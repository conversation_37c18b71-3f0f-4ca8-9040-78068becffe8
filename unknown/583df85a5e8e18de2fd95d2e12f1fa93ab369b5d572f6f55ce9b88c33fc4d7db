{
    /* editor settings */
    "[php]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "junstyle.php-cs-fixer",
        "editor.wordSeparators": "`~!@#%^&*()-=+[{]}\\|;:'\",.<>/?"
    },
    "[python]": {
        "editor.formatOnSave": true,
    },

    /* php settings*/
    "php-cs-fixer.executablePath": "${workspaceFolder}/tools/php-cs-fixer/vendor/bin/php-cs-fixer",
    "php-cs-fixer.executablePathWindows": "${workspaceFolder}\\tools\\php-cs-fixer\\vendor\\bin\\php-cs-fixer.bat",

    /* python settings */
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": [
        "--line-length",
        "120"
    ],
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.flake8Args": [
        "--max-line-length",
        "120"
    ],
    "[sql]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "bradymholt.pgformatter"
    }
}
