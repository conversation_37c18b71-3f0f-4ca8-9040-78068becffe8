#! /bin/sh

# set -e

# Initialize working environment

# For prod/staging composer install is already called on build step.
if [[ "$APP_ENV" == "dev" ]]
then
    composer install --no-scripts
    composer install --working-dir=tools/php-cs-fixer
    pip3 install -r requirements.txt
    npm install
fi

echo "${MAIN_DB_HOST}:${MAIN_DB_PORT}:${MAIN_DB_DATABASE}:${MAIN_DB_USERNAME}:${MAIN_DB_PASSWORD}" > /home/<USER>/.pgpass 
chmod 0600 /home/<USER>/.pgpass

COUNTRIES=$(psql -U $MAIN_DB_USERNAME -h $MAIN_DB_HOST -qtAX -d $MAIN_DB_DATABASE -c "SELECT iso_alpha_2_code FROM countries WHERE active;")

ROLE=${CONTAINER_ROLE:-app}

if [[ ! -d $APP_DIR/storage/files/exports ]]
then
    mkdir $APP_DIR/storage/files/exports
fi

if [[ ! -d $APP_DIR/storage/framework/views ]]
then
    mkdir $APP_DIR/storage/framework/views
fi

# Combines all cache clear commands.
php artisan optimize:clear

for COUNTRY in $COUNTRIES
do
    echo "COUNTRY: $COUNTRY"
    COUNTRY_SMALL=$(echo "$COUNTRY" | tr '[:upper:]' '[:lower:]')
    
    if [ "$APP_ENV" != "prod" ]; then
        echo "Copy Laravel storage"
        mkdir -p /home/<USER>/qnap_storage/storage_$COUNTRY_SMALL
        cp -r storage /home/<USER>/qnap_storage/storage_$COUNTRY_SMALL
        cp -r resources/fonts /home/<USER>/qnap_storage/storage_$COUNTRY_SMALL/
    fi

    echo "Run database migrations..."
    php artisan migrate --path=/database/migrations/local/ --database=$COUNTRY --force
    
    echo "Generate map files..."
    php artisan satellite:generate-map-files $COUNTRY && php artisan satellite:generate-static-map-files $COUNTRY

done

if [ "$ROLE" = "app" ]; then
    php $APP_DIR/artisan horizon:publish
    #start php
    exec php-fpm -F

elif [ "$ROLE" = "queue" ]; then
    echo "Generate supervisor configs"
    envsubst < /home/<USER>/supervisord/conf.d/laravel-horizon.template > /home/<USER>/supervisord/conf.d/laravel-horizon.ini

    exec supervisord -c /home/<USER>/supervisord/supervisord.conf -n
    
elif [ "$ROLE" = "scheduler" ]; then
    echo "Scheduler role"
    while [ true ]
    do
      php $APP_DIR/artisan schedule:run --verbose --no-interaction &
      # Sleep for 1 minute.
      sleep 60
    done
else
    echo "Could not match the container role \"$ROLE\""
    exit 1
fi
