FROM nginx:1.19-alpine

ARG GROUP_ID=1000
ARG USER_ID=1000
ARG USER_NAME=appuser
ARG USER_GROUP=appgroup
ARG APP_DIR=/var/www/html/app

COPY ./.docker/nginx/default.conf.template /etc/nginx/templates/
COPY ./.docker/nginx/cors_support /etc/nginx/
COPY ./.docker/nginx/nginx.conf /etc/nginx/nginx.conf

RUN  addgroup -g ${GROUP_ID} -S ${USER_GROUP} \
    && adduser -u ${USER_ID} -D -S ${USER_NAME} -G ${USER_GROUP}

#COPY App
WORKDIR ${APP_DIR}
COPY --chown=${USER_NAME}:${USER_GROUP} . .

WORKDIR /etc/nginx