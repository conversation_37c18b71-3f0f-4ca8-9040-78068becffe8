user nginx;
worker_processes auto;
pid /run/nginx.pid;

events {
    worker_connections 768;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 2;
    types_hash_max_size 2048;
    client_max_body_size 10M;
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
    ssl_prefer_server_ciphers on;
    access_log /var/log/access.log;
    error_log /var/log/error.log;
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 256;
    gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css text/html application/json  application/vnd.ms-fontobject application/x-font-ttf font/opentype image/svg+xml image/x-icon;
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}