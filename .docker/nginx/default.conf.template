server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root ${APP_DIR}/public/;
    index index.php;

    location /healthcheck.html {
        root ${APP_DIR};
        add_header    X-Health-Check    Ok;
    }

    location ~ ^/images/(.*[a-z]+)/(.*[A-Z]+)/plots/(.+)$ {
      alias /home/<USER>/qnap_storage/storage_$1/storage/plots/$3 ;
    }

    location ~ ^/images/(.*[a-z]+)/(.*[A-Z]+)/meteo/(.+)$ {
      alias /home/<USER>/qnap_storage/storage_$1/storage/meteo/$3 ;
    }

    location ~ ^/images/(.*[a-z]+)/(.*[A-Z]+)/(.+)$ {
      alias /home/<USER>/qnap_storage/storage_$1/storage/ordered_plots/$2/$3 ;
    }

    location / {
        try_files $uri /index.php?$args;
    }

    location ~ ^/index\.php(/|$) {
        include cors_support;
        fastcgi_pass ${CONTAINER_NAME}:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        internal;
    }

    location ~ \.php$ {
        include cors_support;
        return 404;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
}