#!/usr/bin/python
# -*- coding: UTF-8 -*-

import sys, os
from html import escape
from flup.server.fcgi import WSGIServer
import cgi
import shlex
import subprocess
from base64 import b64decode
from json import loads as load_json
from cryptography.hazmat.primitives import serialization
from jwt import decode as jwt_decode
from requests import get
from urllib.parse import parse_qs
import tempfile
import re


def app(environ, start_response):
    query_params = environ["QUERY_STRING"]
    query_params_dict = parse_qs(query_params)
    jwt_token = query_params_dict.get("access_token")

    #    if not jwt_token:
    #        start_response("403 Forbidden", [("Content-Type", "text/html")])
    #        return []
    #    kc_url = "http://keycloak.geoscan.info:8081/realms/geotech"
    #    kc_response = get(kc_url).json()
    #
    #    jwt_pub_key_base64 = kc_response["public_key"]
    #    key_der = b64decode(jwt_pub_key_base64.encode())
    #    jwt_pub_key = serialization.load_der_public_key(key_der)

    #    try:
    #        data = load_json(
    #            jwt_decode(jwt_token, jwt_pub_key, algorithms=["RS256"], audience="account")
    #        )
    #    except:
    #        start_response("403 Forbidden", [("Content-Type", "text/html")])
    #        return []
    process = subprocess.Popen(
        shlex.split(f"/usr/local/bin/mapserv"),
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        env={
            "HTTP_HOST": environ.get("HTTP_HOST", ""),
            "HTTP_USER_AGENT": environ.get("HTTP_USER_AGENT", ""),
            "QUERY_STRING": environ.get("QUERY_STRING", ""),
            "REMOTE_HOST": environ.get("REMOTE_HOST", ""),
            "REMOTE_USER": environ.get("REMOTE_USER", ""),
            "SERVER_NAME": environ.get("SERVER_NAME", ""),
            "SERVER_PORT": environ.get("SERVER_PORT", ""),
            "REQUEST_METHOD": environ.get("REQUEST_METHOD", ""),
            "PATH": os.environ.get("PATH", ""),
            "SCRIPT_NAME": environ.get("SCRIPT_NAME", ""),
            "SCRIPT_FILENAME": environ.get("SCRIPT_FILENAME", ""),
            "MS_ERRORFILE": environ.get("MS_ERRORFILE", ""),
            "MS_MAP_PATTERN": environ.get("MS_MAP_PATTERN", ""),
            "MS_DEBUGLEVEL": environ.get("MS_DEBUGLEVEL", ""),
            "MAPSERVER_CONFIG_FILE": environ.get("MAPSERVER_CONFIG_FILE", ""),
        },
    )

    stdout, stderr = process.communicate()
    regex = r"^Content-Type:(?P<content>[\sa-z\/]+)\n"

    matches = re.finditer(regex, stdout.decode("UTF-8"), re.MULTILINE)

    for matchNum, match in enumerate(matches, start=1):

        print(
            "Match {matchNum} was found at {start}-{end}: {match}".format(
                matchNum=matchNum,
                start=match.start(),
                end=match.end(),
                match=match.group(),
            )
        )

        for groupNum in range(0, len(match.groups())):
            groupNum = groupNum + 1

            print(
                "Group {groupNum} found at {start}-{end}: {group}".format(
                    groupNum=groupNum,
                    start=match.start(groupNum),
                    end=match.end(groupNum),
                    group=match.group(groupNum),
                )
            )
    rc = process.wait()
    start_response("200 OK", [("Content-Type", "text/html")])
    # msgparts = [stdout.decode("utf-8")]
    msgparts = [stdout]
    if rc != 0:
        msgparts = [f"CGI binary terminated with rc={rc}."]
        if stderr:
            msgparts.append(stderr)

    return msgparts


@fastcgi()
def appf():
    query = os.environ["QUERY_STRING"]
    content = sys.stdin.read()
    sys.stdout.write(f"Content-type: text/html\r\n\r\n<html>{content} ; ")
    sys.stdout.write(f"{query}</html>\r\n")


# if __name__ == "__main__":
# WSGIServer(app).run()
