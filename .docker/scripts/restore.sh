#!/bin/bash
#DBS="geoscan_cms gs_main susi_main_v5 susi_main_ro_v5 susi_main_rs_v5"

# Input paramas
DBS=$1
DAY_TO_RESTORE=$2

DAY=$(date "+%a" -d "yesterday")
DW_DST=/root/migrate

DROP_DATABASE=""
KILL_DATABASE=""

if [ $DAY_TO_RESTORE ]; then
    DAY=$DAY_TO_RESTORE
fi

cd $DW_DST

for db in $DBS; do
    echo "Downloading $db"

    docker run --rm -i -v ~/.aws:/root/.aws -v /root/migrate:/aws amazon/aws-cli s3 cp s3://geoscan/databases/$DAY/$db.zip /aws
    unzip -o $db.zip

    /usr/lib/postgresql/10/bin/pg_restore -l /root/migrate/$db.sql >/root/migrate/restore.pgdump.list 2>&1

    db2=$db
    if [ "$db" == "gs_main" ]; then
        db2=gs_main
        #/usr/lib/postgresql/10/bin/pg_restore -l /root/migrate/$db.sql | grep -v 'config_params' | grep -v 'countries' >/root/migrate/restore.pgdump.list 2>&1
    fi
    if [ "$db" == "susi_main_v5" ] || [ "$db" == "susi_main_ro_v5" ]; then
        /usr/lib/postgresql/10/bin/pg_restore -l /root/migrate/$db.sql | grep -v 'global_users_roles' >/root/migrate/restore.pgdump.list 2>&1
    fi

    DROP_DATABASE="DROP DATABASE $db2;"
    CREATE_DATABASE="CREATE DATABASE $db2;"
    KILL_DATABASE="SELECT  pg_terminate_backend(pid)  FROM  pg_stat_activity  WHERE pid <> pg_backend_pid() AND datname = '$db2';"

    /usr/lib/postgresql/10/bin/psql -p 5432 -h 127.0.0.1 -U postgres -d postgres -c "$KILL_DATABASE" && /usr/lib/postgresql/10/bin/psql -p 5432 -h 127.0.0.1 -U postgres -d postgres -c "$DROP_DATABASE"
    /usr/lib/postgresql/10/bin/psql -p 5432 -h 127.0.0.1 -U postgres -d postgres -c "$CREATE_DATABASE"
    /usr/lib/postgresql/10/bin/pg_restore -c -p 5432 -h 127.0.0.1 -U postgres -v -L /root/migrate/restore.pgdump.list -d $db2 /root/migrate/$db.sql >pg_restore.log 2>&1

    if [ "$db" == "gs_main" ]; then
        query="update config_params_values set value = REPLACE(value, 'https://api.geoscan.info', 'http://office.geoscan.info') where cpv.value ilike 'https://api.geoscan.info%' ; update countries set active = false where id not in (1,3);"
        /usr/lib/postgresql/10/bin/psql -p 5432 -h 127.0.0.1 -U postgres -d postgres -c "$query"
    fi
    #rm $db.sql
done

exit 0
