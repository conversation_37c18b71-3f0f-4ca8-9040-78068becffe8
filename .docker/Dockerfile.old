FROM php:7.4-fpm-alpine as backend

ARG MAIN_DB_HOST
ARG MAIN_DB_PORT
ARG MAIN_DB_DATABASE
ARG MAIN_DB_USERNAME
ARG MAIN_DB_PASSWORD

ARG GROUP_ID=1000
ARG USER_ID=1000
ARG USER_NAME=appuser
ARG USER_GROUP=appgroup
ARG APP_DIR=/var/www/html/app

WORKDIR ${APP_DIR}

ARG APP_ENV

RUN apk add --no-cache build-base autoconf libpq unixodbc-dev freetype libpng libjpeg-turbo freetype-dev libpng-dev libjpeg-turbo-dev libzip-dev zip openldap-dev libxml2-dev wget
RUN apk add \
    --repository https://dl-cdn.alpinelinux.org/alpine/v3.15/community \
    --no-cache \
    php7-pear

RUN set -ex && \
    apk --no-cache add postgresql-libs postgresql-dev

RUN docker-php-ext-configure pgsql --with-pgsql=/usr/local/pgsql && \
    docker-php-ext-configure gd --with-jpeg=/usr/lib && \
    docker-php-ext-install pdo pdo_pgsql gd zip

RUN apk del postgresql-dev

RUN docker-php-ext-install exif
RUN docker-php-ext-configure exif \
    --enable-exif
RUN apk add --no-cache \
    icu-dev \
    git \
    autoconf \
    g++ \
    make \
    cmake \
    openssl-dev \
    postgresql-libs \
    postgresql-dev \
    && CPPFLAGS="-DHAVE_SYS_FILE_H" pecl install redis \
    && docker-php-ext-enable redis.so 

RUN apk add --no-cache --update libmemcached-libs zlib
RUN set -xe && \
    cd /tmp/ && \
    apk add --no-cache --update --virtual .phpize-deps $PHPIZE_DEPS && \
    apk add --no-cache --update --virtual .memcached-deps zlib-dev libmemcached-dev cyrus-sasl-dev && \
    pecl install igbinary && \
    ( \
    pecl install --nobuild memcached && \
    cd "$(pecl config-get temp_dir)/memcached" && \
    phpize && \
    ./configure --enable-memcached-igbinary && \
    make -j$(nproc) && \
    make install && \
    cd /tmp/ \
    ) && \
    docker-php-ext-enable igbinary memcached && \
    rm -rf /tmp/* && \
    apk del .memcached-deps .phpize-deps

RUN apk add --no-cache imagemagick imagemagick-dev
RUN printf "\n" | pecl install imagick
RUN docker-php-ext-enable imagick

RUN apk add \
    --repository https://dl-cdn.alpinelinux.org/alpine/v3.6/main \
    --no-cache \
    rabbitmq-c-dev \
    && pecl install amqp \
    && docker-php-ext-enable amqp

RUN  addgroup -g ${GROUP_ID} -S ${USER_GROUP} \
    && adduser -u ${USER_ID} -D -S ${USER_NAME} -G ${USER_GROUP} \
    #change the app folder permissions
    && chown -R ${USER_NAME}:${USER_GROUP} ${APP_DIR} \
    && chmod -R g+rwx ${APP_DIR} \
    && docker-php-ext-install intl opcache mysqli pdo_mysql

RUN apk add --repository=https://dl-cdn.alpinelinux.org/alpine/v3.16/main -u alpine-keys

# Install Mono
RUN apk add --no-cache --repository=http://dl-cdn.alpinelinux.org/alpine/edge/testing mono

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

COPY ./.docker/php/php.ini /usr/local/etc/php/php.ini
COPY ./.docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./.docker/crontab/crontab /etc/crontabs/root
COPY ./.docker/docker-entrypoint.sh /usr/local/bin
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

RUN apk add ttf-ubuntu-font-family --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.13/main

# [END]


RUN apk add postgresql-client --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.8/main
RUN apk add --no-cache python3 python3-dev gdal-dev gdal-tools py3-numpy-dev py3-numpy py3-gdal py3-pip py3-wheel py3-matplotlib py3-pillow py3-rasterio py3-shapely
RUN apk add supervisor gettext nodejs npm chromium && npm install -g puppeteer@21

# K8s Health Check
RUN apk add --no-cache fcgi
COPY ./.docker/php/php-fpm-healthcheck /usr/local/bin/
RUN chmod +x /usr/local/bin/php-fpm-healthcheck
# K8s Health Check End

EXPOSE 9000

#change the user to execute any further composer commands with ${USER_NAME}
USER ${USER_NAME}

RUN mkdir -p /home/<USER>/qnap_storage \
    && mkdir -p /home/<USER>/qnap_storage2

COPY --chown=${USER_NAME}:${USER_GROUP} ./composer.json composer.json
COPY --chown=${USER_NAME}:${USER_GROUP} ./composer.lock composer.lock
COPY --chown=${USER_NAME}:${USER_GROUP} ./requirements.txt requirements.txt
COPY --chown=${USER_NAME}:${USER_GROUP} ./package.json ./package.json
COPY --chown=${USER_NAME}:${USER_GROUP} ./package-lock.json ./package-lock.json
COPY --chown=${USER_NAME}:${USER_GROUP} . .
COPY --chown=${USER_NAME}:${USER_GROUP} ./.docker/php/supervisord /home/<USER>/supervisord

RUN composer install --no-scripts $([ "$APP_ENV" == "prod" ] && echo "--no-dev") && \
    pip3 install -r requirements.txt && \
    npm install $([ "$APP_ENV" == "prod" ] && echo "--production --ignore-scripts")

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]