<?php

namespace App\Helpers;

use Carbon\Carbon;
use DateTimeZone;
use Monolog\DateTimeImmutable;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class SoilSamplesLogger extends Logger
{
    protected $path;

    public function __construct($name = 'soil_sample_log', $level = 'DEBUG', $path = null, array $processors = [])
    {
        $tz = $this->timezone = new DateTimeZone(config('app.timezone'));
        $handlers = [];
        parent::__construct($name, $handlers, $processors);

        $dtObj = Carbon::now($tz);

        if (!$path) {
            $path = $name . '-' . $dtObj->format('Y-m-d') . '.log';
        }

        $path = $this->path = storage_path('logs/' . $path);

        $this->addStream($path, $level);

        return $this;
    }

    public static function getLevelNum($level)
    {
        return static::getLevels()[strtoupper($level)];
    }

    public function addStream($path, $level = 'DEBUG')
    {
        if (!is_int($level)) {
            $level = static::getLevelNum($level);
        }
        $this->pushHandler(new StreamHandler($path, $level));

        return $this;
    }

    public function addRecord(int $level, string $message, array $context = [], ?DateTimeImmutable $datetime = null): bool
    {
        if (!is_int($level)) {
            $level = static::getLevelNum($level);
        }

        return parent::addRecord($level, $message, $context, $datetime);
    }
}
