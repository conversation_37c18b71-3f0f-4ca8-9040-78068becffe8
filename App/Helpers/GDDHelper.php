<?php

namespace App\Helpers;

class GDDHelper
{
    public $GDDSum = 0;
    public $precipitationSum = 0;
    public $crop;
    public $meteoData;

    /**
     * @param array $meteoData period meteo data by days
     */
    public function __construct($meteoData)
    {
        $this->meteoData = $meteoData;
    }

    /**
     * calculateGDD -returns the GDD index and current GDD.
     *
     * @param array $meteoDayData - meteo data for a day
     *
     * @return array
     */
    public function calculateGDD($meteoDayData)
    {
        $currentGDD = ($meteoDayData['temperature_max'] + $meteoDayData['temperature_min']) / 2 - config("crops.{$this->crop}.TBase");
        $currentGDD = ($currentGDD < 0) ? 0 : $currentGDD;
        $this->GDDSum += $currentGDD;

        $GDDResult['GDDIndex'] = $this->GDDSum;

        return $GDDResult;
    }

    public function getMeteoCalculations()
    {
        $result = is_array($this->meteoData) ? array_map(function ($meteoDayData) {
            // calculate GDD
            if ($this->crop) {
                $GDDResult = $this->calculateGDD($meteoDayData);
                $meteoDayData['GDDIndex'] = $GDDResult['GDDIndex'];
            }

            // calculate precipitationSum
            $this->precipitationSum += $meteoDayData['precipitation'];
            $meteoDayData['precipitationSum'] = round($this->precipitationSum, 1);

            return $meteoDayData;
        }, $this->meteoData) : [];

        return $result;
    }
}
