<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class GeneratePlotImgEvent implements IGeneratable
{
    use SerializesModels;

    private $outImage;
    private $width;
    private $height;
    private $plotJson;

    /**
     * GeneratePlotImgEvent constructor.
     *
     * @param string $outImage Path for the output image
     * @param string $plotJson The plot geom represented as json
     * @param int $width Width of the image in px
     * @param int $height Height of the image in px
     */
    public function __construct(string $outImage, string $plotJson, int $width, int $height)
    {
        $this->outImage = $outImage;
        $this->width = $width;
        $this->height = $height;
        $this->plotJson = $plotJson;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $tmpTiff = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid('plot_image_', true) . '.tiff';
        $tmpGeoJson = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid('plot_geojson_', true) . '.json';

        file_put_contents($tmpGeoJson, $this->plotJson);
        $gdalRasterize = "{$gdalBinPath}gdal_rasterize -burn 64 -burn 127 -burn 194 -burn 255 -ot Byte -ts 320 240 -of GTiff {$tmpGeoJson} {$tmpTiff}";
        $gdalTranslate = "{$gdalBinPath}gdal_translate -of PNG -ot Byte";
        $command = " {$gdalRasterize} && {$gdalTranslate}";

        if ($this->width && $this->height) {
            $command .= " -outsize {$this->width} {$this->height} ";
        }
        $command .= "{$tmpTiff} {$this->outImage}";

        if (!File::exists(pathinfo($this->outImage, PATHINFO_DIRNAME))) {
            File::makeDirectory(pathinfo($this->outImage, PATHINFO_DIRNAME));
        }
        $output = null;
        exec($command . ' 2>&1', $output);
    }
}
