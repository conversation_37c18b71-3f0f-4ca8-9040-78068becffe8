<?php

namespace App\Events;

use App\Helpers\Helper;
use App\Models\Order;
use App\Models\Plot;
use App\Models\ServiceProvider;
use App\Models\User;
use App\Services\System\SystemService;
use App\Traits\Map\Layerable;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class GenerateStaticMapFilesEvent implements IGeneratable
{
    use SerializesModels;
    use Layerable;

    private $mapFilesDisk;

    /**
     * Create a new event instance.
     */
    public function __construct()
    {
        $this->mapFilesDisk = Config::get('globals.MAP_FILES_DISK');
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $this->generateGeoScanMapFile();
        $this->generateImagesByDateMapFiles();
        $this->generatePlotsPreviewMapFile();
        $this->generateVraMapFile();
        $this->generateSoilMapFile();
        $this->generateSoilMapFileOld();
        $this->generateWaterPoundsMapFile();
        $this->generateIndexMapFile();
        $this->generateSoilGridMapFile();
        $this->generateOrdersMapFile();
        $this->generateOrdersMapFileOld();
        $this->generatePlotsByCropMapFile();
        $this->generateLayerCmsPlotsMapFile();
        $this->generateLayerTileIdx();
        $this->generateLayerTileIdxMask();
        $this->generateIndexLayerFile();
        $this->generateIndexWaterLayerFile();
        $this->generateLayerUploadPlotsBoundaries();
        $this->generateTmpPlotsBoundaryLayerFile();
        $this->generateTmpPlotsBoundaryOverlapsLayerFile();
        $this->generateExistingPlotsBoundaryLayerFile();
        $this->generateLayerEditPlotsBoundaries();
        $this->generateCadastreMapFile();
    }

    private function generateGeoScanMapFile()
    {
        $data = [
            'base_path' => base_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
            'osm_connection' => Config::get('globals.OSM_CONNECTION'),
        ];

        $mapStr = View::make('maps.static.geo-scan', $data)->render();
        $this->createMapFile('geo_scan.map', $mapStr);
    }

    private function generatePlotsByCropMapFile()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));

        $orderedPolygonsQuery = Plot::selectRaw("
                DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid, 
                su_satellite_plots.name AS plot_name, 
                scc.crop_name_%lang% AS crop, 
                su_satellite_plots.geom, 
                scc.crop_color,
                round((ST_Area(su_satellite_plots.geom) * {$areaCoef})::numeric, 3) AS area
            ")
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->join('su_satellite_orders so', 'so.id', '=', 'sopr.order_id')
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($q) {
                $q->on('sspc.plot_id', '=', 'su_satellite_plots.gid');
                $q->on('sspc.year', DB::raw('%year%'));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->where([
                ['so.type', '\'index\''],
            ])
            ->where(function ($query) {
                $query->where('sspc.is_primary', 'true');
                $query->orWhere('sspc.is_primary', null);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                $query->orWhereIn('su_satellite_plots.farm_id', ['%farm_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%plot_ids%)'));
                $query->orWhereIn('su_satellite_plots.gid', ['%plot_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%year%)'));
                $query->orWhereIn('so.year', ['%year%']);
            })
            ->where(function ($query) {
                $query->where('sspc.crop_id', DB::raw('any(array %crop_ids%::integer[])'));
                $query->orWhere(DB::raw('array_length(array %crop_ids%::integer[], 1)'), null);
                $query->orWhere(DB::raw('(
                    SELECT bool_or(a is null)
                    FROM unnest(array %crop_ids%::integer[]) s(a)
                    WHERE sspc.crop_id = any(array %crop_ids%::integer[])
                    OR sspc.crop_id is null
                )'), 'true');
            })
            ->whereRaw('su.id=%__user_id__%')
            ->groupBy('su_satellite_plots.gid', 'scc.crop_name_%lang%', 'sspc.irrigated', 'scc.crop_color')
            ->orderBy('su_satellite_plots.gid', 'ASC')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_plots_by_crop',
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$orderedPolygonsQuery}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['gid', 'crop_color'],
            'transparency' => '100',
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => 'lang',
                    'regular_expression' => '[a-zA-Z]+',
                ],
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'farm_ids',
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                ],
                [
                    'name' => 'default_farm_ids',
                    'regular_expression' => 'null',
                ],
                [
                    'name' => 'plot_ids',
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                ],
                [
                    'name' => 'default_plot_ids',
                    'regular_expression' => 'null',
                ],
                [
                    'name' => 'crop_ids',
                    'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$', // match array, example '[123, null, 456]'
                ],
                [
                    'name' => 'default_crop_ids',
                    'regular_expression' => '[]',
                ],
                [
                    'name' => 'year',
                    'regular_expression' => '[0-9]+',
                ],
            ],
            'classes' => [
                [
                    'name' => 'layer_satellite_order_ready',
                    'styles' => [
                        [
                            'border_color' => '0 131 201',
                            'color' => '[crop_color]',
                            'width' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();
        $this->createMapFile('layer_plots_by_crop.map', $layerStr);
    }

    private function generateOrdersMapFile()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $orderedPolygonsQuery = Plot::selectRaw("
                DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid, 
                su_satellite_plots.geom,
                JSONB_BUILD_ARRAY(
                        St_Xmin(St_Transform(su_satellite_plots.geom, 3857)), 
                        St_Ymin(St_Transform(su_satellite_plots.geom, 3857)),
                        St_Xmax(St_Transform(su_satellite_plots.geom, 3857)),
                        St_Ymax(St_Transform(su_satellite_plots.geom, 3857))
                    ) as extent,
                su_satellite_plots.name AS name,
                round((ST_Area(su_satellite_plots.geom) * {$areaCoef})::numeric, 3) AS area,
                COALESCE(
                    JSONB_AGG(
                        JSONB_BUILD_OBJECT(
                            'id', sspc.id,
                            'crop_name', scc.crop_name_%lang%,
                            'harvest_date', sspc.to_date,
                            'sowing_date', sspc.from_date,
                            'irrigated', sspc.irrigated,
                            'is_primary', sspc.is_primary
                        )
                    ) FILTER (WHERE sspc.id NOTNULL),
                    '[]'::JSONB
                ) AS crops,
                su_satellite_plots.thumbnail,
                sslp.mean as avg_index,
                JSON_BUILD_OBJECT(
                    'id', sf.id,
                    'name', sf.name
                ) AS farm
            ")
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms AS sf', 'sf.id', '=', 'sfu.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', 'su_satellite_plots.uuid')
            ->join('su_satellite_orders AS so', function ($join) {
                $join->on('so.uuid', '=', 'sopr.order_uuid');
                $join->where('so.type', '=', "'index'");
                $join->where('so.status', '<>', "'canceled'");
            })
            ->leftJoin('su_satellite_layers_plots AS sslp', function ($join) {
                $join->on('sslp.sopr_id', '=', 'sopr.id')
                    ->on('sslp.type', '=', "'index'")
                    ->on('sslp.date', '=', "coalesce(NULLIF('%date%', 'null'), now()::date::text)::date")
                    ->on('sslp.satellite_type ', '=', "'sentinel'");
            })
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) {
                $join->on('sspc.plot_id', '=', 'su_satellite_plots.gid');
                $join->where(function ($query) {
                    $query->whereNull(DB::raw('COALESCE(%year%)'));
                    $query->orWhere('sspc.year', '%year%');
                });
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%plot_ids%)'));
                $query->orWhereIn('su_satellite_plots.gid', ['%plot_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                $query->orWhereIn('su_satellite_plots.farm_id', ['%farm_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%year%)'));
                $query->orWhere('so.year', '%year%');
            })
            ->where(function ($query) {
                $query->where('sspc.crop_id', DB::raw('any(array %crop_ids%::integer[])'));
                $query->orWhere(DB::raw('array_length(array %crop_ids%::integer[], 1)'), null);
                $query->orWhere(DB::raw('(
                    SELECT bool_or(a is null)
                    FROM unnest(array %crop_ids%::integer[]) s(a)
                    WHERE sspc.crop_id = any(array %crop_ids%::integer[])
                    OR sspc.crop_id is null
                )'), 'true');
            })
            ->whereRaw('su.id = %__user_id__%')
            ->groupBy('su_satellite_plots.gid', 'sslp.id', 'sf.id')
            ->orderBy('su_satellite_plots.gid', 'ASC')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_satellite_orders',
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$orderedPolygonsQuery}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['all'],
            'processing' => 'ITEMS=gid,crops,name,area,avg_index,farm,thumbnail,extent',
            'transparency' => '100',
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
            'labelitem' => 'area',
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => 'lang',
                    'regular_expression' => '[a-zA-Z]+',
                ],
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'farm_ids',
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                ],
                [
                    'name' => 'default_farm_ids',
                    'regular_expression' => 'null',
                ],
                [
                    'name' => 'plot_ids',
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                ],
                [
                    'name' => 'default_plot_ids',
                    'regular_expression' => 'null',
                ],
                [
                    'name' => 'crop_ids',
                    'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$', // match array, example '[123, null, 456]'
                ],
                [
                    'name' => 'default_crop_ids',
                    'regular_expression' => '[]',
                ],
                [
                    'name' => 'year',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'date',
                    'regular_expression' => '[0-9]{4}-[0-9]{2}-[0-9]{2}',
                ],
                [
                    'name' => 'default_date',
                    'regular_expression' => 'null',
                ],
            ],
            'classes' => [
                [
                    'name' => 'layer_satellite_order_ready',

                    'labels' => [
                        [
                            'angle' => 'auto',
                            'force' => 'true',
                            'outlinecolor' => '15 15 15',
                            'size' => '12',
                            'minsize' => '10',
                            'minfeaturesize' => '50',
                            'antialias' => 'TRUE',
                            'color' => '255 255 255',
                            'type' => 'truetype',
                            'position' => 'cc',
                            'align' => 'CENTER',
                            'font' => 'arial',
                            'text' => '[name]',
                        ],
                        [
                            'angle' => 'auto',
                            'force' => 'true',
                            'outlinecolor' => '15 15 15',
                            'size' => '8',
                            'minsize' => '8',
                            'minfeaturesize' => '50',
                            'offset' => '0 16',
                            'maxscaledenom' => '25000',
                            'antialias' => 'TRUE',
                            'color' => '255 255 255',
                            'type' => 'truetype',
                            'position' => 'cc',
                            'align' => 'CENTER',
                            'font' => 'arial',
                            'text' => "[area] {$areaLabel}",
                        ],
                    ],
                    'styles' => [
                        [
                            'border_color' => '0 131 201',
                            // "size" => 10,
                            'width' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();
        $this->createMapFile('layer_satellite_orders.map', $layerStr);
    }

    private function generateOrdersMapFileOld()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $orderedPolygonsQuery = Order::selectRaw("
            DISTINCT sp.gid,
            cc.crop_name_%lang% AS culture,
            sp.geom,
            sp.name as plot_name,
            round((ST_Area(sp.geom)*{$areaCoef})::numeric, 3) AS area")
            ->join('su_satellite_orders_plots_rel sopr', 'su_satellite_orders.id', '=', 'sopr.order_id')
            ->join('su_satellite_plots sp', 'sp.gid', '=', 'sopr.plot_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'sp.gid')
                    ->on('spc.year', '=', 'su_satellite_orders.year')
                    ->where('spc.is_primary', '=', 'true');
            })
            ->leftJoin('su_crop_codes cc', 'spc.crop_id', '=', 'cc."id"')
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'sp.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'fu.user_id')
            ->join('su_farms f', 'f.id', '=', 'fu.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->whereRaw("su_satellite_orders.status <> '?'", ['canceled'])
            ->where('o.id', '%organization_id%')
            ->where('fu.is_visible', 'true')
            ->where('su_satellite_orders.year', '%year%')
            ->whereRaw('su.id=%__user_id__%')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_satellite_orders_old',
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$orderedPolygonsQuery}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['gid'],
            'gid' => 'gid',
            'transparency' => '100',
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => 'lang',
                    'regular_expression' => '[a-zA-Z]+',
                ],
                [
                    'name' => 'organization_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'year',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
            ],
            'classes' => [
                [
                    'name' => 'layer_satellite_order_ready',
                    'labels' => [
                        [
                            'angle' => 'auto',
                            'force' => 'true',
                            'outlinecolor' => '15 15 15',
                            'size' => '12',
                            'minsize' => '10',
                            'minfeaturesize' => '50',
                            'antialias' => 'TRUE',
                            'color' => '255 255 255',
                            'type' => 'truetype',
                            'position' => 'cc',
                            'align' => 'CENTER',
                            'font' => 'arial',
                            'text' => '[plot_name]',
                        ],
                        [
                            'angle' => 'auto',
                            'force' => 'true',
                            'outlinecolor' => '15 15 15',
                            'size' => '12',
                            'minsize' => '10',
                            'minfeaturesize' => '50',
                            'offset' => '0 16',
                            'maxscaledenom' => '15000',
                            'antialias' => 'TRUE',
                            'color' => '255 255 255',
                            'type' => 'truetype',
                            'position' => 'cc',
                            'align' => 'CENTER',
                            'font' => 'arial',
                            'text' => "[crop] / [area] {$areaLabel}",
                        ],
                    ],
                    'styles' => [
                        [
                            'border_color' => '0 131 201',
                            'width' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();
        $this->createMapFile('layer_satellite_orders_old.map', $layerStr);
    }

    private function generateSoilMapFile()
    {
        $defaultDb = Config::get('database.default');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} dbname={$mainDB['database']} user={$mainDB['username']} password={$mainDB['password']} port={$mainDB['port']}";

        $data = [
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'main_db_connection' => $mainDBConnectionString,
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'machine' => Config::get('globals.MACHINE'),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.soil', $data)->render();

        $mapStr = $this->layerSoilElement($mapStr);
        $this->createMapFile('soil.map', $mapStr);
    }

    private function generateSoilMapFileOld()
    {
        $defaultDb = Config::get('database.default');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} dbname={$mainDB['database']} user={$mainDB['username']} password={$mainDB['password']} port={$mainDB['port']}";

        $data = [
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'base_path' => base_path(),
            'main_db_connection' => $mainDBConnectionString,
            'storage_path' => storage_path(),
            'machine' => Config::get('globals.MACHINE'),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.soil_old', $data)->render();

        $mapStr = $this->layerSoilElement($mapStr);
        $this->createMapFile('soil_old.map', $mapStr);
    }

    /**
     * @throws GuzzleException
     */
    private function layerSoilElement(string $mapStr): string
    {
        $serviceProviders = ServiceProvider::select('id', 'slug')->get()->toArray();
        $systemService = new SystemService(new Request());

        foreach ($serviceProviders as $provider) {
            $soilData = $systemService->getSoilMapElements($provider['id']);

            if (!$soilData) {
                continue;
            }

            foreach ($soilData as $elementName => $elementData) {
                $elementData = array_map(function ($data) {
                    $data['color'] = Helper::convertHexColorToRGB($data['color']);

                    return $data;
                }, $elementData);

                $layerName = 'soil_by_element_' . $provider['slug'] . '_' . $elementName;
                $layerOpts = [
                    'name' => $layerName,
                    'tile_index' => 'soil_by_element_tile_idx',
                    'scale' => $elementData,
                    'mask' => 'ordered_plots',
                ];

                $mapStr .= View::make('map_soil_by_element_layer', $layerOpts)->render();
                $mapStr .= "\n";
            }
        }

        $mapStr .= "END\r\n";

        return $mapStr;
    }

    private function generateVraMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [
            'base_path' => base_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
        ];

        $mapSoilStr = View::make('maps.static.vra_soil', $data)->render();
        $this->createMapFile('vra_soil.map', $mapSoilStr);
    }

    private function generateImagesByDateMapFiles()
    {
        $defaultDb = Config::get('database.default');

        $data = [
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
        ];

        $mapStrRapideye = View::make('maps.static.rapideye-by-date', $data)->render();
        $this->createMapFile('rapideye_by_date.map', $mapStrRapideye);

        $mapStrSentinel = View::make('maps.static.sentinel-by-date', $data)->render();
        $this->createMapFile('sentinel_by_date.map', $mapStrSentinel);

        $mapStrLandsat = View::make('maps.static.landsat-by-date', $data)->render();
        $this->createMapFile('landsat_by_date.map', $mapStrLandsat);
    }

    private function layerPoints($layerNamePoints, $defaultDb, $tagLabel)
    {
        $layerStr = '';
        $dataPoints = [
            'layername' => $layerNamePoints,
            'type' => 'POINT',
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'projection' => '
                "proj=merc"
                "a=6378137"
                "b=6378137"
                "lat_ts=0.0"
                "lon_0=0.0"
                "x_0=0.0"
                "y_0=0"
                "k=1.0"
                "units=m"
                "nadgrids=@null"
                "wktext"
                "no_defs"
            ',
            'gid' => 'gid',
            'transparency' => '100',
            'labelitem' => $tagLabel,
            'classes' => [
                [
                    'name' => $layerNamePoints . '_ready',
                    'text' => "[{$tagLabel}]",
                    'styles' => [
                        [
                            'symbol' => 'circle',
                            'size' => '10',
                            'color' => '42 236 9',
                        ],
                    ],
                    'labels' => [
                        [
                            'color' => '0 0 0',
                            'size' => 10,
                        ],
                    ],
                ],
            ],
        ];

        $layerStr .= View::make('maps.layer', $dataPoints)->render();
        $layerStr .= "\n";
        $layerStr .= "
            SYMBOL
              NAME 'circle'
              TYPE ellipse
              FILLED false
              POINTS
                5 5
              END
            END\r\n";

        return $layerStr;
    }

    private function generatePlotsPreviewMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [];
        $data['host'] = Config::get('database.connections.' . $defaultDb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultDb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultDb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultDb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultDb . '.port');
        $data['connection'] = Config::get('globals.WMS_SERVER') . '?map=' . Config::get('globals.WMS_MAP_PATH') . 'geo_scan.map&';

        $mapStr = View::make('maps.static.plot-preview', $data)->render();
        $this->createMapFile('plot_preview.map', $mapStr);
    }

    private function generateWaterPoundsMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [];
        $data['base_path'] = base_path();
        $data['host'] = Config::get('database.connections.' . $defaultDb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultDb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultDb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultDb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultDb . '.port');
        $data['wms_server'] = Config::get('globals.WMS_SERVER');

        $mapStr = View::make('maps.static.water-pounds', $data)->render();
        $this->createMapFile('water_pounds.map', $mapStr);
    }

    private function generateIndexMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'machine' => Config::get('globals.MACHINE'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.index', $data)->render();

        // Create Adjustable Layers
        $mapStr = $this->createAdjustableLayers($mapStr);

        $mapStr .= '
        END';

        $this->createMapFile('index.map', $mapStr);
    }

    private function createAdjustableLayers($mapStr)
    {
        $elementClasses = Config::get('globals.ELEMENT_CLASSES');
        $originalClasses = $elementClasses['satellite']['NDVI_N'][10];

        $arrFirstClass = reset($originalClasses);
        $lowestLimitOfLowestClass = reset($arrFirstClass);

        $adjustableParams = Config::get('globals.INDEX_ADJUSTABLE_PARAMS');

        $arrAdjParams = json_decode($adjustableParams, true);

        $minValue = $arrAdjParams['min'];
        $maxValue = $arrAdjParams['max'];
        $step = $arrAdjParams['step'];

        $arrOriginalClasses = array_values($originalClasses);

        $arrDataAdj = [];
        for ($i = $minValue; $i <= $maxValue; $i = $i + $step) {
            $arrRecalc = $this->recalcАlgorithm($i, $originalClasses);

            $arrClasses = [];
            foreach ($arrRecalc as $key => $value) {
                if ($key == $lowestLimitOfLowestClass) {
                    $arrClasses[] = [
                        'firstRange' => $key,
                        'secondRange' => $value,
                        'colorFirst' => $arrOriginalClasses[$key][2],
                        'colorSecond' => $arrOriginalClasses[$key + 1][2],
                    ];

                    continue;
                }

                $colorSecond = $arrOriginalClasses[$key][2];
                if (isset($arrOriginalClasses[$key + 1])) {
                    $colorSecond = $arrOriginalClasses[$key + 1][2];
                }

                $arrClasses[] = [
                    'firstRange' => $arrRecalc[$key - 1],
                    'secondRange' => $arrRecalc[$key],
                    'colorFirst' => $arrOriginalClasses[$key][2],
                    'colorSecond' => $colorSecond,
                ];
            }

            $sliderValue = '_' . abs($i) . '_';
            $sign = 'plus';
            if ($i < 0) {
                $sign = 'minus';
            }

            $layerName = 'adjustable_index' . $sliderValue . $sign;

            $arrDataAdj[] = [
                'layer_name' => $layerName,
                'classes' => $arrClasses,
            ];
        }

        foreach ($arrDataAdj as $key => $arrData) {
            $mapStr .= View::make('maps.static.index-adjustable-layers', $arrData)->render();
        }

        return $mapStr;
    }

    private function recalcАlgorithm($currValue, $originalClasses)
    {
        $arrOut = [];
        switch (true) {
            case ($currValue > 0):
                // горната граница на най-ниския клас (ex. 10+40=50)
                $arrHighetClass = end($originalClasses);
                $highetLimitOfHighetClass = $arrHighetClass[1];

                $arrFirstClass = reset($originalClasses);
                $start = reset($arrFirstClass);

                $highestLimitOfLowestClass = $arrFirstClass[1];
                $changedValue = $highestLimitOfLowestClass + $currValue;

                $step = ($highetLimitOfHighetClass - $changedValue) / (count($originalClasses) - 1);

                foreach ($originalClasses as $key => $value) {
                    if ($start == $key) {
                        $arrOut[] = $changedValue;
                        $start = $changedValue;

                        continue;
                    }

                    $nextValue = $start + $step;
                    $arrOut[] = $nextValue;

                    $start = $nextValue;
                }

                break;
            case ($currValue < 0):
                // долната граница на най-високия клас (ex. 100-20=80)
                $arrFirstClass = reset($originalClasses);
                $start = reset($arrFirstClass);

                $arrHighetClass = end($originalClasses);

                $lowestLimitOfHighetClass = reset($arrHighetClass);
                $highetLimitOfHighetClass = $arrHighetClass[1];

                $changedValue = $lowestLimitOfHighetClass + $currValue;

                $step = $changedValue / (count($originalClasses) - 1);

                foreach ($originalClasses as $key => $value) {
                    $nextValue = $start + $step;

                    if ($key == $lowestLimitOfHighetClass) {
                        $nextValue = $highetLimitOfHighetClass;
                    }

                    $arrOut[] = $nextValue;

                    $start = $nextValue;
                }

                break;
            default:
                // when $currValue is 0
                foreach ($originalClasses as $key => $value) {
                    $arrOut[] = $value[1];
                }

                break;
        }

        return $arrOut;
    }

    private function generateSoilGridMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.soil-grid', $data)->render();
        $this->createMapFile('soil-grid.map', $mapStr);
    }

    private function generateLayerCmsPlotsMapFile()
    {
        $defaultDb = Config::get('database.default');
        $cmsDB = Config::get('database.connections.cms');
        $cmsDBConnectionString = "host={$cmsDB['host']} port={$cmsDB['port']} user={$cmsDB['username']} dbname={$cmsDB['database']} password={$cmsDB['password']}";

        $data = [
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'cms_db_connection' => $cmsDBConnectionString,
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
            'area_unit' => Config::get('globals.AREA_UNIT_LABEL'),
            'area_coef' => Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT')),
        ];

        $mapStr = View::make('maps.static.layer-cms-plots', $data)->render();
        $this->createMapFile('layer_cms_plots.map', $mapStr);
    }

    private function generateLayerTileIdx()
    {
        {
            $tileIdxQuery = User::selectRaw("
                sof.path as location,
                ST_Force2d(st_transform(ST_SetSRID(st_extent(ssp.geom), Find_SRID('public', 'su_satellite_plots', 'geom')), 3857)) as geom
            ")
                ->join('su_farms_users as sfu', 'sfu.user_id', '=', 'su_users.id')
                ->join('su_satellite_orders AS sso', function ($join) {
                    $join->on('sso.organization_id', '=', 'su_users.last_chosen_organization_id')
                        ->on('sso.type', '=', "'index'")
                        ->on('sso.status', '=', "'processed'")
                        ->on('sso.year', '=', '%year%');
                })
                ->join('su_satellite_orders_plots_rel as ssopr', 'ssopr.order_id', '=', 'sso.id')
                ->join('su_satellite_orders_files AS sof', function ($join) {
                    $join->on('sof.order_id', '=', 'sso.id')
                        ->on('sof.date', '=', "'%date%'")
                        ->on('sof.type', '=', "'TIFF'")
                        ->on('sof.satellite_type ', '=', "'sentinel'")
                        ->on('sof.layer_type', '=', "'%type%'");
                })
                ->join('su_satellite_plots AS ssp', function ($join) {
                    $join->on('ssp.gid', '=', 'ssopr.plot_id')
                        ->on('sfu.farm_id', '=', 'ssp.farm_id');
                })
                ->whereRaw('su_users.id=%__user_id__%')
                ->groupBy('sof.path')
                ->toSqlWithBindings();

            $defaultdb = Config::get('database.default');

            $data = [
                'layername' => 'layer_tile_idx',
                'host' => Config::get('database.connections.' . $defaultdb . '.host'),
                'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
                'username' => Config::get('database.connections.' . $defaultdb . '.username'),
                'password' => Config::get('database.connections.' . $defaultdb . '.password'),
                'port' => Config::get('database.connections.' . $defaultdb . '.port'),
                'query' => "({$tileIdxQuery}) AS subquery USING UNIQUE geom USING srid=3857",
                'gml_include_items' => ['geom'],
                'transparency' => '100',
                'epsg_code' => 3857,
                'labelitem' => 'location',
                'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
                'geo_pole' => Config::get('globals.GEO_POLE'),
                'params' => [
                    [
                        'name' => 'date',
                        'regular_expression' => '[0-9-]+',
                    ],
                    [
                        'name' => '__user_id__',
                        'regular_expression' => '[0-9]+',
                    ],
                    [
                        'name' => 'farm_ids',
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                    ],
                    [
                        'name' => 'default_farm_ids',
                        'regular_expression' => 'null',
                    ],
                    [
                        'name' => 'plot_ids',
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                    ],
                    [
                        'name' => 'default_plot_ids',
                        'regular_expression' => 'null',
                    ],
                    [
                        'name' => 'crop_ids',
                        'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$', // match array, example '[123, null, 456]'
                    ],
                    [
                        'name' => 'default_crop_ids',
                        'regular_expression' => '[]',
                    ],
                    [
                        'name' => 'year',
                        'regular_expression' => '[0-9]+',
                    ],
                    [
                        'name' => 'type',
                        'regular_expression' => '[a-zA-Z]+',
                    ],
                ],
                'classes' => [
                    [
                        'name' => 'layer_tile_idx_ready',
                        'styles' => [
                            [
                                'width' => '0.91',
                            ],
                        ],
                    ],
                ],
            ];

            $layerStr = View::make('maps.layer', $data)->render();
            $this->createMapFile('layer_tile_idx.map', $layerStr);
        }
    }

    private function generateLayerTileIdxMask()
    {
        {
            $tileIdxQuery = User::selectRaw("
                sslp.plot_id as plot_gid,
                ST_Force2D(st_transform(ST_SetSRID(ssp.geom, Find_SRID('public', 'su_satellite_plots', 'geom')), 3857)) as geom
            ")
                ->join('su_farms_users as sfu', 'sfu.user_id', '=', 'su_users.id')
                ->join('su_satellite_orders AS sso', function ($join) {
                    $join->on('sso.organization_id', '=', 'su_users.last_chosen_organization_id')
                        ->on('sso.type', '=', "'index'")
                        ->on('sso.status', '=', "'processed'")
                        ->on('sso.year', '=', '%year%');
                })
                ->join('su_satellite_orders_plots_rel as ssopr', 'ssopr.order_id', '=', 'sso.id')
                ->join('su_satellite_plots AS ssp', function ($join) {
                    $join->on('ssp.gid', '=', 'ssopr.plot_id')
                        ->on('sfu.farm_id', '=', 'ssp.farm_id');
                })
                ->join('su_satellite_layers_plots AS sslp', function ($join) {
                    $join->on('sslp.sopr_id', '=', 'ssopr.id')
                        ->on('sslp.type', '=', "'%type%'")
                        ->on('sslp.date', '=', "'%date%'")
                        ->on('sslp.satellite_type ', '=', "'sentinel'");
                })
                ->join('su_satellite_plots_crops AS sspc', function ($join) {
                    $join->on('sspc.plot_id', '=', 'ssp.gid')
                        ->on('sspc.year', '=', '%year%');
                })
                ->where('sfu.is_visible', '=', 'true')
                ->where(function ($query) {
                    $query->where('sspc.is_primary', 'true');
                    $query->orWhere('sspc.is_primary', null);
                })
                ->where(function ($query) {
                    $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                    $query->orWhereIn('ssp.farm_id', ['%farm_ids%']);
                })
                ->where(function ($query) {
                    $query->whereNull(DB::raw('COALESCE(%plot_ids%)'));
                    $query->orWhereIn('ssp.gid', ['%plot_ids%']);
                })
                ->where(function ($query) {
                    $query->where('sspc.crop_id', DB::raw('any(array %crop_ids%::integer[])'));
                    $query->orWhere(DB::raw('array_length(array %crop_ids%::integer[], 1)'), null);
                    $query->orWhere(DB::raw('(
                        SELECT bool_or(a is null)
                        FROM unnest(array %crop_ids%::integer[]) s(a)
                        WHERE sspc.crop_id = any(array %crop_ids%::integer[])
                        OR sspc.crop_id is null
                    )'), 'true');
                })
                ->whereRaw('su_users.id=%__user_id__%')
                ->toSqlWithBindings();

            $defaultdb = Config::get('database.default');

            $data = [
                'layername' => 'layer_tile_idx_mask',
                'host' => Config::get('database.connections.' . $defaultdb . '.host'),
                'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
                'username' => Config::get('database.connections.' . $defaultdb . '.username'),
                'password' => Config::get('database.connections.' . $defaultdb . '.password'),
                'port' => Config::get('database.connections.' . $defaultdb . '.port'),
                'query' => "({$tileIdxQuery}) AS subquery USING UNIQUE geom USING srid=3857",
                'gml_include_items' => ['geom'],
                'transparency' => '100',
                'epsg_code' => 3857,
                'labelitem' => 'plot_gid',
                'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
                'geo_pole' => Config::get('globals.GEO_POLE'),
                'params' => [
                    [
                        'name' => 'date',
                        'regular_expression' => '[0-9-]+',
                    ],
                    [
                        'name' => '__user_id__',
                        'regular_expression' => '[0-9]+',
                    ],
                    [
                        'name' => 'farm_ids',
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                    ],
                    [
                        'name' => 'default_farm_ids',
                        'regular_expression' => 'null',
                    ],
                    [
                        'name' => 'plot_ids',
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                    ],
                    [
                        'name' => 'default_plot_ids',
                        'regular_expression' => 'null',
                    ],
                    [
                        'name' => 'crop_ids',
                        'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$', // match array, example '[123, null, 456]'
                    ],
                    [
                        'name' => 'default_crop_ids',
                        'regular_expression' => '[]',
                    ],
                    [
                        'name' => 'year',
                        'regular_expression' => '[0-9]+',
                    ],
                    [
                        'name' => 'type',
                        'regular_expression' => '[a-zA-Z]+',
                    ],
                ],
                'classes' => [
                    [
                        'name' => 'layer_tile_idx_mask_ready',
                        'styles' => [
                            [
                                'color' => '0 0 0',
                                'width' => '0.91',
                            ],
                        ],
                    ],
                ],
            ];

            $layerStr = View::make('maps.layer', $data)->render();
            $this->createMapFile('layer_tile_idx_mask.map', $layerStr);
        }
    }

    private function generateIndexLayerFile()
    {
        $data = [];

        $mapStr = View::make('maps.static.layer_index', $data)->render();
        $this->createMapFile('layer_index.map', $mapStr);
    }

    private function generateIndexWaterLayerFile()
    {
        $data = [];

        $mapStr = View::make('maps.static.layer_index_water', $data)->render();
        $this->createMapFile('layer_index_water.map', $mapStr);
    }

    private function generateLayerUploadPlotsBoundaries()
    {
        $data = [
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.layer_upload_plots_boundaries', $data)->render();
        $this->createMapFile('layer_upload_plots_boundaries.map', $mapStr);
    }

    private function generateTmpPlotsBoundaryLayerFile()
    {
        $query = DB::table('tmp_satellite_%file_id%')
            ->select('gid', 'geom')
            ->whereRaw('ST_IsValid(geom)')
            ->toSql();
        $query = str_replace(['%', '?', '"'], ['%', '%s', ''], $query);

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_tmp_plots_boundary',
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['geom'],
            'transparency' => '100',
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => 'file_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
            ],
            'classes' => [
                [
                    'name' => 'for_tmp_plots_boundary',
                    'styles' => [
                        [
                            'border_color' => '255 220 46',
                            'color' => '255 220 46',
                            'opacity' => '70',
                            'width' => '0.91',
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        Storage::disk($this->mapFilesDisk)->append('layer_upload_plots_boundaries.map', $layerStr);
    }

    private function generateTmpPlotsBoundaryOverlapsLayerFile()
    {
        $query = DB::table('tmp_satellite_%file_id% AS tmp')
            ->selectRaw('coalesce (ST_Intersection(tmp.geom, ssp.geom), ST_Intersection(tmp.geom, tmp_copy.geom)) as geom')
            ->leftJoin('tmp_satellite_%file_id% AS tmp_copy', function ($join) {
                $join->on(DB::raw('ST_Touches(tmp.geom, tmp_copy.geom)'), DB::raw('false::boolean'))
                    ->on(DB::raw('ST_Intersects(tmp.geom, tmp_copy.geom)'), DB::raw('true::boolean'))
                    ->on('tmp.gid', '<>', 'tmp_copy.gid');
            })
            ->leftJoin('su_farms as sf', 'sf.organization_id', '=', DB::raw('%organization_id%'))
            ->leftJoin('su_satellite_plots AS ssp', function ($join) {
                $join->on(DB::raw('ST_Touches(tmp.geom, ssp.geom)'), DB::raw('false::boolean'))
                    ->on(DB::raw('ST_Intersects(tmp.geom, ssp.geom)'), DB::raw('true::boolean'))
                    ->on('ssp.farm_id', '=', 'sf.id');
            })
            ->leftJoin('su_organizations_users AS sou', 'sou.organization_id', '=', 'sf.organization_id')
            ->where('sou.user_id', '=', DB::raw('%__user_id__%'))
            ->whereRaw('ST_IsValid(tmp.geom)')
            ->whereRaw('(ssp.gid notnull OR tmp_copy.gid notnull)')
            ->orderByRaw('tmp.gid, sf.id, ssp.gid desc nulls last, tmp_copy.gid desc nulls last')
            ->toSql();

        $query = str_replace(['%', '?', '"'], ['%', '%s', ''], $query);

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_tmp_plots_boundary_overlaps',
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE geom USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['geom'],
            'transparency' => '100',
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => 'file_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'organization_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
            ],
            'classes' => [
                [
                    'name' => 'for_tmp_plots_boundary_overlaps',
                    'styles' => [
                        [
                            'border_color' => '255 102 102',
                            'color' => '255 102 102',
                            'opacity' => '50',
                            'width' => '0.91',
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        Storage::disk($this->mapFilesDisk)->append('layer_upload_plots_boundaries.map', $layerStr);
    }

    private function generateExistingPlotsBoundaryLayerFile()
    {
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $query = Plot::select('su_satellite_plots.gid', 'su_satellite_plots.geom', 'su_satellite_plots.name')
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join('su_farms AS f', 'f.id', '=', 'sfu.farm_id')
            ->where('f.organization_id', '=', '%organization_id%')
            ->whereRaw('su.id=%__user_id__%')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_existing_plots_boundary',
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['geom', 'name'],
            'transparency' => '100',
            'labelitem' => 'name',
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => 'file_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'organization_id',
                    'regular_expression' => '[0-9]+',
                ],
            ],
            'classes' => [
                [
                    'name' => 'for_existing_plots_boundary',
                    'styles' => [
                        [
                            'border_color' => '218 232 252',
                            'color' => '218 232 252',
                            'opacity' => '70',
                            'width' => '0.91',
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        Storage::disk($this->mapFilesDisk)->append('layer_upload_plots_boundaries.map', $layerStr);
    }

    private function generateLayerEditPlotsBoundaries()
    {
        $data = [
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.layer_edit_plots_boundaries', $data)->render();
        $this->createMapFile('layer_edit_plots_boundaries.map', $mapStr);

        $this->generateLayerEditableNotEditablePlotsBoundary('layer_editable_plots_boundary', 'true', '218 232 252');
        $this->generateLayerEditableNotEditablePlotsBoundary('layer_not_editable_plots_boundary', 'false', '248 206 204');
    }

    private function generateLayerEditableNotEditablePlotsBoundary(string $layerName, string $isEditablePlot, $styleColor)
    {
        $query = Plot::select('su_satellite_plots.gid', 'su_satellite_plots.geom')
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join('su_farms AS sf', 'sf.id', '=', 'su_satellite_plots.farm_id')
            ->where('sf.organization_id', '=', '%organization_id%')
            ->where('su_satellite_plots.is_editable', '=', $isEditablePlot)
            ->whereBetween(DB::raw('su_satellite_plots.upload_date::date'), ["'%start_date%'", "'%end_date%'"])
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                $query->orWhereIn('su_satellite_plots.farm_id', ['%farm_ids%']);
            })
            ->whereRaw('su.id=%__user_id__%')
            ->groupBy('su_satellite_plots.gid')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => $layerName,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'gml_include_items' => ['geom'],
            'transparency' => '100',
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'params' => [
                [
                    'name' => '__user_id__',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'organization_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'start_date',
                    'regular_expression' => '[0-9-]+',
                ],
                [
                    'name' => 'end_date',
                    'regular_expression' => '[0-9-]+',
                ],
                [
                    'name' => 'farm_ids',
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$', // match comma separated numbers
                ],
                [
                    'name' => 'default_farm_ids',
                    'regular_expression' => 'null',
                ],
            ],
            'classes' => [
                [
                    'name' => 'for_' . $layerName,
                    'styles' => [
                        [
                            'border_color' => $styleColor,
                            'color' => $styleColor,
                            'opacity' => '70',
                            'width' => '0.91',
                        ],
                    ],
                ],
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        Storage::disk($this->mapFilesDisk)->append('layer_edit_plots_boundaries.map', $layerStr);
    }

    private function generateCadastreMapFile()
    {
        $data = [
            'cadastre_wms_server' => Config::get('globals.CADASTRE_WMS_SERVER'),
            'cadastre_wms_layer' => 'wms_cad_immovable',
        ];

        $mapStr = View::make('maps.static.cadastre', $data)->render();
        $this->createMapFile('cadastre.map', $mapStr);
    }

    private function createMapFile($mapFileName, $mapStr)
    {
        Storage::disk($this->mapFilesDisk)->delete($mapFileName);
        Storage::disk($this->mapFilesDisk)->put($mapFileName, $mapStr);
    }
}
