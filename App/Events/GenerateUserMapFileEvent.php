<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use View;

class GenerateUserMapFileEvent implements IGeneratable
{
    use SerializesModels;

    private $wmsMapPath;

    /**
     * Create a new event instance.
     */
    public function __construct($truncateOthers = true)
    {
        $this->truncateOthers = $truncateOthers;
        $this->wmsMapPath = Config::get('globals.WMS_MAP_PATH');
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $this->createOrdersWorkMap();
        $this->createUploadPlotsBoundariesMap();
        $this->createEditPlotsBoundariesMap();

        if ($this->truncateOthers) {
            // create cov_layer if not exist
            $this->createMapFile('layer_satellite_orders.map', '');
            $this->createMapFile('layer_satellite_work.map', '');
            $this->createMapFile('layer_plots_by_crop.map', '');
            $this->createMapFile('layer_upload_plots_boundaries.map', '');
            $this->createMapFile('layer_edit_plots_boundaries.map', '');
        }
    }

    private function getMapHead()
    {
        $data['wmsserver'] = Config::get('globals.WMS_SERVER');
        $data['wmssrs'] = Config::get('globals.WMS_SRS');
        $data['wfsserver'] = Config::get('globals.WMS_SERVER');
        $data['wfssrs'] = Config::get('globals.WMS_SRS');
        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');

        return View::make('maps.head', $data)->render();
    }

    private function createOrdersWorkMap()
    {
        $mapStr = "MAP\r\n";
        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_satellite_orders.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_satellite_orders_old.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_satellite_work.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_plots_by_crop.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_index.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_index_water.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_tile_idx.map'\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_tile_idx_mask.map'\r\n";
        $mapStr .= 'END';

        $this->createMapFile('orders_work.map', $mapStr);
    }

    private function createUploadPlotsBoundariesMap()
    {
        $mapStr = "MAP\r\n";
        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_upload_plots_boundaries.map'\r\n";
        $mapStr .= 'END';

        $this->createMapFile('upload_plots_boundaries.map', $mapStr);
    }

    private function createEditPlotsBoundariesMap()
    {
        $mapStr = "MAP\r\n";
        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";
        $mapStr .= "INCLUDE '{$this->wmsMapPath}layer_edit_plots_boundaries.map'\r\n";
        $mapStr .= 'END';

        $this->createMapFile('edit_plots_boundaries.map', $mapStr);
    }

    private function createMapFile($mapFileName, $mapStr)
    {
        $mapFilesDisk = Config::get('globals.MAP_FILES_DISK');

        Storage::disk($mapFilesDisk)->delete($mapFileName);
        Storage::disk($mapFilesDisk)->put($mapFileName, $mapStr);
    }
}
