<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FarmTrackReportEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public const CREATE_FARM_TRACK_REPORT = 'Create farm track report';
    public const UPDATE_FARM_TRACK_REPORT = 'Update farm track report';

    /**
     * Create a new event instance.
     *
     * @param ?int $integrationReportId
     * @param ?array $params
     * @param ?string $tmpTableName
     * @param ?string $error
     */
    public function __construct(string $farmTrackReportEvent, ?int $integrationReportId, string $reportState, ?array $params = null, ?string $tmpTableName = null, ?string $error = null)
    {
        $this->farmTrackReportEvent = $farmTrackReportEvent;
        $this->integrationReportId = $integrationReportId;
        $this->reportState = $reportState;
        $this->params = $params;
        $this->tmpTableName = $tmpTableName;
        $this->error = $error;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel('farm-track-report');
    }
}
