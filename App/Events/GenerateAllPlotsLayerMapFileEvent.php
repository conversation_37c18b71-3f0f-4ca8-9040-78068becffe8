<?php

namespace App\Events;

use App\Traits\Map\Layerable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class GenerateAllPlotsLayerMapFileEvent implements IGeneratable
{
    use SerializesModels;
    use Layerable;

    /**
     * Create a new event instance.
     */
    public function __construct() {}

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $mapStr = "MAP\r\n";

        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";

        $sql = "select su_satellite_plots.gid, su_satellite_plots.geom, round((ST_Area(su_satellite_plots.geom)*%m2_coef%)::numeric, 3) || ' ' || '%area_unit_label%'  AS area, su_satellite_plots.name from su_satellite_plots 
        join su_farms_users on su_farms_users.farm_id = su_satellite_plots.farm_id
        join su_farms on su_farms.id = su_farms_users.farm_id
        join su_organizations on su_organizations.id = su_farms.organization_id
                where su_farms_users.user_id = %user_id% AND su_organizations.id = %organization_id% AND su_farms_users.is_visible = TRUE AND su_satellite_plots.upload_date::DATE >= '%from_date%' AND su_satellite_plots.upload_date::DATE <= '%to_date%'";

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_all_plots',
            'utf' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'gml_include_items' => ['gid'],
            'transparency' => 100,
            'query' => "({$sql}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '',
            'labelitem' => 'area',
            'params' => [
                [
                    'name' => 'user_id',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'from_date',
                    'regular_expression' => "[0-9\-]+",
                ],
                [
                    'name' => 'to_date',
                    'regular_expression' => "[0-9\-]+",
                ],
                [
                    'name' => 'm2_coef',
                    'regular_expression' => '[0-9]+',
                ],
                [
                    'name' => 'area_unit_label',
                    'regular_expression' => '[а-Яa-zA-Z]+',
                ],
                [
                    'name' => 'organization_id',
                    'regular_expression' => '[0-9]+',
                ],
            ],
            'classes' => [
                [
                    'name' => 'layer_all_plots',
                    'styles' => [
                        [
                            'outlinecolor' => '244 67 54',
                            'width' => 0.91,
                        ],
                    ],
                    'labels' => [
                        array_merge([
                            'size' => 10,
                            'color' => '244 67 54',
                        ], self::$DEFAULT_LABEL_OPTIONS),
                    ],
                ],
            ],
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
        ];

        $mapStr .= View::make('maps.layer', $data)->render();

        $mapStr .= 'END';

        $mapFile = 'layer_all_plots.map';
        $mapFileDisk = Config::get('globals.MAP_FILES_DISK');

        Storage::disk($mapFileDisk)->delete($mapFile);
        Storage::disk($mapFileDisk)->put($mapFile, $mapStr);
    }

    private function getMapHead()
    {
        $data['wmsserver'] = Config::get('globals.WMS_SERVER');
        $data['wmssrs'] = Config::get('globals.WMS_SRS');
        $data['wfsserver'] = Config::get('globals.WMS_SERVER');
        $data['wfssrs'] = Config::get('globals.WMS_SRS');
        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');

        return View::make('maps.head', $data)->render();
    }
}
