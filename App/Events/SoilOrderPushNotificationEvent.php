<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class SoilOrderPushNotificationEvent extends Event
{
    use SerializesModels;

    public $groupId;

    /**
     * Create a new event instance.
     */
    public function __construct($groupId)
    {
        $this->groupId = $groupId;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
