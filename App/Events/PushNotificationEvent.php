<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class PushNotificationEvent extends Event
{
    use SerializesModels;

    public $notyId;

    /**
     * Create a new event instance.
     */
    public function __construct($notyId)
    {
        $this->notyId = $notyId;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
