<?php

namespace App\Events;

use App\Models\Plot;
use App\Traits\Map\Layerable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class GenerateWorkLayerMapFileEvent implements IGeneratable
{
    use SerializesModels;
    use Layerable;

    /**
     * Create a new event instance.
     */
    public function __construct() {}

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $sql = Plot::selectRaw("su_satellite_plots.gid, su_satellite_plots.geom, round((ST_Area(su_satellite_plots.geom)*{$areaCoef})::numeric, 3) || ' {$areaLabel}' AS area, su_satellite_plots.name")
            ->leftJoin('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders AS so', function ($join) {
                $join->on('so.id', '=', 'sopr.order_id')
                    ->where('so.status', '<>', DB::raw("'canceled'"));
            })
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->where('fu.user_id', '%user_id%')
            ->where('o.id', '%organization_id%')
            ->where('fu.is_visible', 'TRUE')
            ->groupBy('gid')
            ->havingRaw('MAX(so.id) IS NULL')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [];
        $data['layername'] = 'layer_satellite_work';
        $data['utf'] = false;
        $data['host'] = Config::get('database.connections.' . $defaultdb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultdb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultdb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultdb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultdb . '.port');
        $data['gml_include_items'] = ['gid'];
        $data['transparency'] = 100;
        $data['query'] = "({$sql}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . '';
        $data['labelitem'] = 'area';

        $data['classes'] = [
            [
                'name' => 'layer_satellite_work',
                'styles' => [
                    [
                        'border_color' => '73 10 61',
                    ],
                ],
                'labels' => [
                    array_merge([
                        'size' => 10,
                        'color' => '73 10 61',
                    ], self::$DEFAULT_LABEL_OPTIONS),
                ],
            ],
        ];

        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');
        $data['validation'] = true;

        $data['params'] = [
            [
                'name' => 'user_id',
                'regular_expression' => '[0-9]+',
            ],
            [
                'name' => 'organization_id',
                'regular_expression' => '[0-9]+',
            ],
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        $mapFile = 'layer_satellite_work.map';
        $mapFilesDisk = Config::get('globals.MAP_FILES_DISK');

        Storage::disk($mapFilesDisk)->delete($mapFile);
        Storage::disk($mapFilesDisk)->put($mapFile, $layerStr);
    }
}
