<?php

namespace App\Classes\LoadData;

use App\Models\File;
use Config;
use DB;
use Illuminate\Support\Facades\File as FileSystem;
use Schema;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class SoilGridProcessingClass extends AbstractLoadDataClass
{
    public $tmpTable = '';

    /**
     * init - init function.
     *
     * @param mixed $config
     */
    public function __construct(File $file)
    {
        parent::__construct($file);

        $this->tmpTable = 'tmp_soil_grid_' . $file->id;
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        return $this->mainProcessing();
    }

    /**
     * finalActions - final actions that we execute.
     */
    public function finalActions()
    {
        if (File::LOADING_FILE_NOW == $this->file->status) {
            $this->copyProcess();
        }
    }

    public function onAfterProcessing()
    {
        $tmpTable = "tmp_soil_grid_{$this->file->id}";
        $crs = Config::get('globals.DEFAULT_DB_CRS');

        $resultTmpTable = DB::table($this->tmpTable)
            ->selectRaw("json_build_object(
                'type', 'FeatureCollection',
                'features', json_agg(
                    json_build_object(
                        'type',       'Feature',
                        'geometry',   ST_AsGeoJSON(st_collect(st_transform(st_setsrid(geom, {$crs}), 3857), st_transform(st_setsrid(ST_PointOnSurface(geom), {$crs}), 3857)))::json,
                        'properties', json_build_object(
                            'sample_id', sampleid::int,
                            'color', concat('#', left(lpad(to_hex((random() * 10000000)::bigint), 6, '0'), 6)),
                            'cell_area', round(st_area(geom)/1000)
                        )
                    )
                )
            ) as geojson")
            ->whereRaw('ST_IsValid(geom)')
            ->pluck('geojson')->first();

        // Drop Tmp table
        Schema::dropIfExists($tmpTable);

        return $resultTmpTable;
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        $this->file->status = File::SUCCESSFULLY_TREATED;
        $this->file->save();

        // remove the unziped dir
        FileSystem::deleteDirectory($this->userSubDir);
    }
}
