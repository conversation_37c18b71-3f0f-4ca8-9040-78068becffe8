<?php

namespace App\Classes\LoadData;

use App\Models\Crop;
use App\Models\File;
use App\Models\Plot;
use App\Models\PlotCrop;
use App\Services\FarmingYear\FarmingYearService;
use DB;
use Exception;
use Illuminate\Support\Facades\File as FileSystem;
use Schema;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ProcessBoundariesClass extends AbstractLoadDataClass
{
    public $tmpTable = '';
    /**
     * @var FarmingYearService
     */
    public $farmingYearService;

    /**
     * ProcessBoundariesClass constructor.
     *
     * @param ?string $fileType
     */
    public function __construct(File $file, FarmingYearService $farmingYearService, ?string $fileType = null)
    {
        parent::__construct($file);

        $this->tmpTable = 'tmp_satellite_' . $file->id;
        $this->fileType = $fileType;
        $this->farmingYearService = $farmingYearService;
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        $this->mainProcessing();
    }

    /**
     * finalActions - final actions that we execute.
     *
     * @throws Exception
     */
    public function finalActions()
    {
        if (($this->shapeFile && $this->dbfFile) || ($this->shapeFlag && $this->dbfFlag)) {
            $this->makeValidGeom();

            $this->tmpCRS = DB::table($this->tmpTable)->selectRaw('ST_SRID(geom)')->value('st_srid');

            if ((int) $this->tmpCRS != (int) $this->file->crs) {
                throw new Exception(__METHOD__ . ' Line:' . __LINE__, File::ERROR_INVALID_CRS);
            }

            try {
                $this->transformColumnCRS();
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), File::ERROR_INVALID_GEOMETRY);
            }

            try {
                $this->dumpGeometries();
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), File::ERROR_INVALID_GEOMETRY);
            }

            $this->file->status = File::ERROR_WAITING_DEFINITION;
            $this->file->save();

            // remove the unziped dir
            FileSystem::deleteDirectory($this->userSubDir);
        } else {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, File::ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * onAfterProcessing - actions that we exucete after processing.
     */
    public function onAfterProcessing()
    {
        if (File::ERROR_WAITING_COPYING == $this->status) {
            $this->copyProcess();
        }
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        if (strlen($this->definition)) {
            // with definition
            $this->copyWithDefinition();
        } else {
            // with no definition
            $this->copyNoDefinition();
        }

        // Drop Tmp table
        Schema::dropIfExists($this->tmpTable);
    }

    private function copyWithDefinition()
    {
        // copy with definition
        $this->definition = unserialize($this->definition);
        $query = DB::table($this->tmpTable)
            ->select(
                DB::raw('ST_Force2D(ST_CollectionExtract(geom, 3)) as geom'),
                DB::raw('ROUND((ST_Area(ST_Force2D(ST_CollectionExtract(geom, 3)))/1000)::numeric, 3) AS area')
            )
            ->whereRaw('ST_IsValid(ST_Force2D(ST_CollectionExtract(geom, 3))) AND NOT ST_IsEmpty(ST_Force2D(ST_CollectionExtract(geom, 3)))');

        $cultures = [];
        if (isset($this->definition['name'])) {
            $query->addSelect(DB::raw($this->definition['name']));
        }

        if (isset($this->definition['crop'])) {
            $query->addSelect(DB::raw($this->definition['crop']));
        }

        $resultTmpTable = $query->get();

        $cultures = [];
        if (isset($this->definition['crop'])) {
            $arrCropTmpTable = $resultTmpTable->pluck($this->definition['crop'])->toArray();
            $cultures = Crop::select('id', 'crop_code', 'crop_name')
                ->whereIn('crop_code', $arrCropTmpTable)
                ->orWhereIn('crop_name', $arrCropTmpTable)
                ->orWhere('id', Crop::UNKNOWN_CROP_ID)
                ->orderBy('id', 'asc')
                ->get()->toArray();
        }

        $newGids = collect();
        for ($i = 0; $i < count($resultTmpTable); $i++) {
            if (isset($this->definition['crop'])) {
                // Plot
                $plot = new Plot();

                $nameValue = '';
                if (isset($this->definition['name'])) {
                    $nameValue = $resultTmpTable[$i]->{$this->definition['name']};
                }

                $plot->area = $resultTmpTable[$i]->area;
                $plot->name = $nameValue;
                $plot->geom = $resultTmpTable[$i]->geom;
                $plot->farm_id = $this->farm->id;
                $plot->save();
                $newGids->push($plot->gid);

                $cropValue = $resultTmpTable[$i]->{$this->definition['crop']};

                if (is_numeric($cropValue)) {
                    $key = array_search($cropValue, array_column($cultures, 'crop_code'));
                } else {
                    $key = array_search($cropValue, array_column($cultures, 'crop_name'));
                }

                $farmYear = $this->farmingYearService->getFarmingYearsByOrganization($this->farm->organization)->where('default', true)->first();
                // PlotCrop
                $plotCrop = new PlotCrop();
                $plotCrop->plot_id = $plot->gid;
                $plotCrop->crop_id = $cultures[$key]['id'];
                $plotCrop->year = $farmYear['year'];
                $plotCrop->from_date = $farmYear['from_date'];
                $plotCrop->to_date = $farmYear['to_date'];
                $plotCrop->is_primary = true;
                $plotCrop->save();
            } else {
                $nameValue = $resultTmpTable[$i]->{$this->definition['name']};

                // Plot
                $plot = new Plot();
                $plot->area = $resultTmpTable[$i]->area;
                $plot->name = $nameValue;
                $plot->geom = $resultTmpTable[$i]->geom;
                $plot->farm_id = $this->farm->id;
                $plot->save();
                $newGids->push($plot->gid);
            }
        }

        // Update Uploaded Date before removeDuplicateGeoms
        $this->updateUploadedDate();

        with(new Plot())->removeDuplicateGeoms($this->farm->id);

        $this->file->status = File::SUCCESSFULLY_TREATED;
        $this->file->save();
    }

    private function copyNoDefinition()
    {
        // copy without definition
        $lastGid = Plot::select('gid')
            ->where('farm_id', $this->farm->id)
            ->orderBy('gid', 'desc')
            ->limit(1)
            ->value('gid');

        DB::statement('INSERT INTO
            su_satellite_plots (geom, area, farm_id, upload_date) 
            SELECT 
                ST_Force2D(ST_CollectionExtract(geom, 3)) as geom,
                ROUND((ST_Area(ST_Force2D(ST_CollectionExtract(geom, 3)))/1000)::numeric, 3) AS area, '
                . $this->farm->id . ' as farm_id, now() from ' . $this->tmpTable
                . ' WHERE ST_IsValid(ST_Force2D(ST_CollectionExtract(geom, 3))) AND NOT ST_IsEmpty(ST_Force2D(ST_CollectionExtract(geom, 3)))
        ');

        // Update Uploaded Date before removeDuplicateGeoms
        $this->updateUploadedDate();

        with(new Plot())->removeDuplicateGeoms($this->farm->id);

        $this->file->status = File::SUCCESSFULLY_TREATED;
        $this->file->save();
    }

    private function updateUploadedDate()
    {
        $oldGids = Plot::select('a.gid')
            ->from(DB::raw('su_satellite_plots AS a, su_satellite_plots AS b'))
            ->whereRaw('a.gid < b.gid')
            ->where('a.farm_id', $this->farm->id)
            ->where('b.farm_id', $this->farm->id)
            ->whereRaw('ST_Equals(a.geom, b.geom)')
            ->get()
            ->pluck('gid');

        Plot::whereIn('gid', $oldGids)->update(['upload_date' => date('Y-m-d H:i:s')]);
    }

    private function dumpGeometries()
    {
        $tmpTable = $this->tmpTable;

        DB::statement("CREATE TABLE {$tmpTable}_new AS 
                    SELECT *, (ST_Dump(geom)).geom AS geom_new FROM {$tmpTable}");

        Schema::dropIfExists($tmpTable);

        Schema::rename($tmpTable . '_new', $tmpTable);

        DB::statement("ALTER TABLE {$tmpTable} DROP COLUMN IF EXISTS geom;");
        DB::statement("ALTER TABLE {$tmpTable} RENAME COLUMN geom_new TO geom;");

        DB::table($tmpTable)
            ->whereRaw('ST_Area(geom) < ' . Plot::MIN_AREA)
            ->delete();
    }
}
