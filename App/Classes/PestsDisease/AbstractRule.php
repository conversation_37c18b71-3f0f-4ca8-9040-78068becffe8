<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\PestsDisease;

abstract class AbstractRule implements ICheckable
{
    protected $minValue;
    protected $maxValue;
    protected $avgValue;
    protected $type;
    protected $valueType;

    public function getMinValue()
    {
        return $this->minValue;
    }

    /**
     * @return AbstractRule
     */
    public function setMinValue($minValue)
    {
        $this->minValue = $minValue;

        return $this;
    }

    public function getMaxValue()
    {
        return $this->maxValue;
    }

    /**
     * @return AbstractRule
     */
    public function setMaxValue($maxValue)
    {
        $this->maxValue = $maxValue;

        return $this;
    }

    public function getAvgValue()
    {
        return $this->avgValue;
    }

    /**
     * @return AbstractRule
     */
    public function setAvgValue($avgValue)
    {
        $this->avgValue = $avgValue;

        return $this;
    }

    public function getType()
    {
        return $this->type;
    }

    /**
     * @return AbstractRule
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    public function getValueType()
    {
        return $this->valueType;
    }

    /**
     * @return AbstractRule
     */
    public function setValueType($valueType)
    {
        $this->valueType = $valueType;

        return $this;
    }
}
