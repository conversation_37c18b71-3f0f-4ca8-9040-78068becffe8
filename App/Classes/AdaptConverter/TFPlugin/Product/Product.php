<?php

namespace App\Classes\AdaptConverter\TFPlugin\Product;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;

class Product extends AbstractBaseModel
{
    private $category;
    private $form;
    private $type;
    private $status;

    public function __construct(int $id, string $name)
    {
        parent::__construct($id, $name);
    }

    public function getCategory(): string
    {
        return $this->category;
    }

    public function setCategory(string $category): void
    {
        $this->category = $category;
    }

    public function getForm(): string
    {
        return $this->form;
    }

    public function setForm(string $form): void
    {
        $this->form = $form;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['Name'] = $this->name;
        $arr['Category'] = $this->category;
        $arr['Form'] = $this->form;
        $arr['Type'] = $this->type;
        $arr['Status'] = $this->status;

        return $arr;
    }
}
