<?php

namespace App\Classes\AdaptConverter\TFPlugin\Guidance;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;

class GuidanceGroup extends AbstractBaseModel
{
    private $boundingPolygon = [];
    private $patterns = [];

    public function __construct(int $id, string $name)
    {
        parent::__construct($id, $name);
    }

    public function getBoundingPolygon(): array
    {
        return $this->boundingPolygon;
    }

    public function setBoundingPolygon(array $boundingPolygon): void
    {
        $this->boundingPolygon = $boundingPolygon;
    }

    public function getPatterns(): array
    {
        return $this->patterns;
    }

    public function setPatterns(array $patterns): void
    {
        $this->patterns = $patterns;
    }

    public function addPattern(Pattern $pattern): void
    {
        $this->patterns[] = $pattern;
    }

    public function removePattern(int $index): void
    {
        if (isset($this->patterns[$index])) {
            unset($this->patterns[$index]);
        }
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['BoundingPolygon'] = $this->boundingPolygon;
        $arr['Patterns'] = $this->patterns;

        return $arr;
    }
}
