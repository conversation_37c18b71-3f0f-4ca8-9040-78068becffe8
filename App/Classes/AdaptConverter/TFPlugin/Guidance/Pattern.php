<?php

namespace App\Classes\AdaptConverter\TFPlugin\Guidance;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;

class Pattern extends AbstractBaseModel
{
    private $shape = [];
    private $type;
    private $shift = 0;

    public function __construct(int $id, string $name)
    {
        parent::__construct($id, $name);
    }

    public function getShape(): array
    {
        return $this->shape;
    }

    public function setShape(array $shape): void
    {
        $this->shape = $shape;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getShift(): int
    {
        return $this->shift;
    }

    public function setShift(int $shift): void
    {
        $this->shift = $shift;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['Shape'] = $this->shape;
        $arr['Type'] = $this->type;
        $arr['Shift'] = $this->shift;

        return $arr;
    }
}
