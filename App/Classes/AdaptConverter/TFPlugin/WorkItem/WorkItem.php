<?php

namespace App\Classes\AdaptConverter\TFPlugin\WorkItem;

use JsonSerializable;

class WorkItem implements JsonSerializable
{
    private $id;
    private $growerId;
    private $farmId;
    private $fieldId;
    private $fieldBoundaryId;
    private $cropZoneId;
    private $referenceLayerIds = [];
    private $guidanceIds = [];
    private $workItemOperations = [];

    public function __construct(int $id)
    {
        $this->id = $id;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getGrowerId(): int
    {
        return $this->growerId;
    }

    public function setGrowerId(int $growerId): void
    {
        $this->growerId = $growerId;
    }

    public function getFarmId(): int
    {
        return $this->farmId;
    }

    public function setFarmId(int $farmId): void
    {
        $this->farmId = $farmId;
    }

    public function getFieldId(): int
    {
        return $this->fieldId;
    }

    public function setFieldId(int $fieldId): void
    {
        $this->fieldId = $fieldId;
    }

    public function getFieldBoundaryId(): int
    {
        return $this->fieldBoundaryId;
    }

    public function setFieldBoundaryId(int $fieldBoundaryId): void
    {
        $this->fieldBoundaryId = $fieldBoundaryId;
    }

    public function getCropZoneId(): int
    {
        return $this->cropZoneId;
    }

    public function setCropZoneId(int $cropZoneId): void
    {
        $this->cropZoneId = $cropZoneId;
    }

    public function getReferenceLayerIds(): array
    {
        return $this->referenceLayerIds;
    }

    public function setReferenceLayerIds(array $referenceLayerIds): void
    {
        $this->referenceLayerIds = $referenceLayerIds;
    }

    public function getGuidanceIds(): array
    {
        return $this->guidanceIds;
    }

    public function setGuidanceIds(array $guidanceIds): void
    {
        $this->guidanceIds = $guidanceIds;
    }

    public function getWorkItemOperations(): array
    {
        return $this->workItemOperations;
    }

    public function setWorkItemOperations(array $workItemOperations): void
    {
        $this->workItemOperations = $workItemOperations;
    }

    public function addWorkItemOperation(WorkItemOperation $workItemOperation): void
    {
        $this->workItemOperations[] = $workItemOperation;
    }

    public function removeWorkItemOperation(int $index): void
    {
        if (isset($this->workItemOperations[$index])) {
            unset($this->workItemOperations[$index]);
        }
    }

    public function jsonSerialize(): array
    {
        return [
            'Id' => $this->id,
            'GrowerId' => $this->growerId,
            'FarmId' => $this->farmId,
            'FieldId' => $this->fieldId,
            'CropZoneId' => $this->cropZoneId,
            'ReferenceLayerIds' => $this->referenceLayerIds,
            'FieldBoundaryId' => $this->fieldBoundaryId,
            'GuidanceIds' => $this->guidanceIds,
            'WorkItemOperations' => $this->workItemOperations,
        ];
    }
}
