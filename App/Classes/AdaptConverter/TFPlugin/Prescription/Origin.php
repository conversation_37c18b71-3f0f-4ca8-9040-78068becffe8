<?php

namespace App\Classes\AdaptConverter\TFPlugin\Prescription;

use JsonSerializable;

class Origin implements JsonSerializable
{
    private $minX;
    private $minY;
    private $z = 0.0;

    public function __construct(float $minX, float $minY, float $z = 0.0)
    {
        $this->minX = $minX;
        $this->minY = $minY;
        $this->z = $z;
    }

    // minX
    public function getMinX(): float
    {
        return $this->minX;
    }

    public function setMinX(float $minX): void
    {
        $this->minX = $minX;
    }

    // minY
    public function getMinY(): float
    {
        return $this->minY;
    }

    public function setMinY(float $minY): void
    {
        $this->minY = $minY;
    }

    // z
    public function getZ(): float
    {
        return $this->z;
    }

    public function setZ(float $z): void
    {
        $this->z = $z;
    }

    public function getCoordinates(): array
    {
        return [
            $this->minX,
            $this->minY,
            $this->z,
        ];
    }

    public function setCoordinates(float $minX, float $minY, float $z = 0.0): void
    {
        $this->minX = $minX;
        $this->minY = $minY;
        $this->z = $z;
    }

    public function jsonSerialize(): array
    {
        return [
            'coordinates' => $this->getCoordinates(),
        ];
    }
}
