<?php

namespace App\Classes\AdaptConverter\TFPlugin\Prescription;

use JsonSerializable;

class BoundingBox implements JsonSerializable
{
    private $maxX;
    private $maxXCode;
    private $maxXDescription;

    private $minX;
    private $minXCode;
    private $minXDescription;

    private $maxY;
    private $maxYCode;
    private $maxYDescription;

    private $minY;
    private $minYCode;
    private $minYDescription;

    public function __construct(
        float $maxX,
        float $minX,
        float $maxY,
        float $minY,
        string $maxXCode = '',
        string $maxXDescription = '',
        string $minXCode = '',
        string $minXDescription = '',
        string $maxYCode = '',
        string $maxYDescription = '',
        string $minYCode = '',
        string $minYDescription = ''
    ) {
        $this->maxX = $maxX;
        $this->maxXCode = $maxXCode;
        $this->maxXDescription = $maxXDescription;

        $this->minX = $minX;
        $this->minXCode = $minXCode;
        $this->minXDescription = $minXDescription;

        $this->maxY = $maxY;
        $this->maxYCode = $maxYCode;
        $this->maxYDescription = $maxYDescription;

        $this->minY = $minY;
        $this->minYCode = $minYCode;
        $this->minYDescription = $minYDescription;
    }

    // maxX
    public function getMaxX(): float
    {
        return $this->maxX;
    }

    public function setMaxX(float $maxX): void
    {
        $this->maxX = $maxX;
    }

    public function getMaxXCode(): string
    {
        return $this->maxXCode;
    }

    public function setMaxXCode(string $maxXCode): void
    {
        $this->maxXCode = $maxXCode;
    }

    public function getMaxXDescription(): string
    {
        return $this->maxXDescription;
    }

    public function setMaxXDescription(string $maxXDescription): void
    {
        $this->maxXDescription = $maxXDescription;
    }

    // minX
    public function getMinX(): float
    {
        return $this->minX;
    }

    public function setMinX(float $minX): void
    {
        $this->minX = $minX;
    }

    public function getMinXCode(): string
    {
        return $this->minXCode;
    }

    public function setMinXCode(string $minXCode): void
    {
        $this->minXCode = $minXCode;
    }

    public function getMinXDescription(): string
    {
        return $this->minXDescription;
    }

    public function setMinXDescription(string $minXDescription): void
    {
        $this->minXDescription = $minXDescription;
    }

    // maxY
    public function getMaxY(): float
    {
        return $this->maxY;
    }

    public function setMaxY(float $maxY): void
    {
        $this->maxY = $maxY;
    }

    public function getMaxYCode(): string
    {
        return $this->maxYCode;
    }

    public function setMaxYCode(string $maxYCode): void
    {
        $this->maxYCode = $maxYCode;
    }

    public function getMaxYDescription(): string
    {
        return $this->maxYDescription;
    }

    public function setMaxYDescription(string $maxYDescription): void
    {
        $this->maxYDescription = $maxYDescription;
    }

    // minY
    public function getMinY(): float
    {
        return $this->minY;
    }

    public function setMinY(float $minY): void
    {
        $this->minY = $minY;
    }

    public function getMinYCode(): string
    {
        return $this->minYCode;
    }

    public function setMinYCode(string $minYCode): void
    {
        $this->minYCode = $minYCode;
    }

    public function getMinYDescription(): string
    {
        return $this->minYDescription;
    }

    public function setMinYDescription(string $minYDescription): void
    {
        $this->minYDescription = $minYDescription;
    }

    public function jsonSerialize(): array
    {
        return [
            'MaxX' => $this->maxX,
            'MaxY' => $this->maxY,
            'MinX' => $this->minX,
            'MinY' => $this->minY,
        ];
    }
}
