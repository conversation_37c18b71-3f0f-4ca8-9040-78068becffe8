<?php

namespace App\Classes\AdaptConverter\TFPlugin\Prescription;

use JsonSerializable;

class RxProductLookup implements JsonSerializable
{
    private $id;
    private $productId;
    private $representation;
    private $unitOfMeasure;

    public function __construct(?int $id, int $productId, string $unitOfMeasure, string $representation = 'vrSolutionRateMass')
    {
        $this->id = $id;
        $this->productId = $productId;
        $this->unitOfMeasure = $unitOfMeasure;
        $this->representation = $representation;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function setProductId(int $productId): void
    {
        $this->productId = $productId;
    }

    public function getUnitOfMeasure(): string
    {
        return $this->unitOfMeasure;
    }

    public function setUnitOfMeasure(string $unitOfMeasure): void
    {
        $this->unitOfMeasure = $unitOfMeasure;
    }

    public function getRepresentation(): string
    {
        return $this->representation;
    }

    public function setRepresentation(string $representation): void
    {
        $this->representation = $representation;
    }

    public function jsonSerialize(): array
    {
        return [
            'Id' => $this->id,
            'ProductId' => $this->productId,
            'Representation' => $this->representation,
            'UnitOfMeasure' => $this->unitOfMeasure,
        ];
    }
}
