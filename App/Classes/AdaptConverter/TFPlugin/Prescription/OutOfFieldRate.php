<?php

namespace App\Classes\AdaptConverter\TFPlugin\Prescription;

use JsonSerializable;

class OutOfFieldRate implements JsonSerializable
{
    private $id;
    private $rate;
    private $code;
    private $description;

    public function __construct(?int $id, float $rate = 0.0, string $code = '1', string $description = 'Description')
    {
        $this->id = $id;
        $this->rate = $rate;
        $this->code = $code;
        $this->description = $description;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getRate(): float
    {
        return $this->rate;
    }

    public function setRate(float $rate): void
    {
        $this->rate = $rate;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function jsonSerialize(): array
    {
        return [
            'Rate' => $this->rate,
            'Code' => $this->code,
            'Description' => $this->description,
        ];
    }
}
