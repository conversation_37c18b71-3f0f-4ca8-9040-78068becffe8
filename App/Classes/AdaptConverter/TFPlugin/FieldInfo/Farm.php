<?php

namespace App\Classes\AdaptConverter\TFPlugin\FieldInfo;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;

class Farm extends AbstractBaseModel
{
    private $growerId;

    public function __construct(int $id, string $name)
    {
        parent::__construct($id, $name);
    }

    public function getGrowerId(): int
    {
        return $this->growerId;
    }

    public function setGrowerId(int $growerId): void
    {
        $this->growerId = $growerId;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();
        $arr['GrowerId'] = $this->growerId;

        return $arr;
    }
}
