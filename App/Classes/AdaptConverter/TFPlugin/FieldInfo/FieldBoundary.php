<?php

namespace App\Classes\AdaptConverter\TFPlugin\FieldInfo;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;

class FieldBoundary extends AbstractBaseModel
{
    private $fieldId;
    private $coordinates = [];

    public function __construct(
        int $id,
        string $name
    ) {
        parent::__construct($id, $name);
    }

    public function getFieldId(): int
    {
        return $this->fieldId;
    }

    public function setFieldId(int $fieldId): void
    {
        $this->fieldId = $fieldId;
    }

    public function getCoordinates(): array
    {
        return $this->coordinates;
    }

    public function setCoordinates(array $coordinates): void
    {
        $this->coordinates = $coordinates;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['FieldId'] = $this->fieldId;
        $arr['SpatialData']['coordinates'] = $this->coordinates;

        return $arr;
    }
}
