<?php

namespace App\Classes\AdaptConverter\TFPlugin\Crop;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;

class CropZone extends AbstractBaseModel
{
    private $fieldId;
    private $cropId;
    private $boundingPolygon = [];

    public function __construct(int $id, string $name)
    {
        parent::__construct($id, $name);
    }

    public function getFieldId(): int
    {
        return $this->fieldId;
    }

    public function setFieldId(int $fieldId): void
    {
        $this->fieldId = $fieldId;
    }

    public function getCropId(): int
    {
        return $this->cropId;
    }

    public function setCropId(int $cropId): void
    {
        $this->cropId = $cropId;
    }

    public function getBoundingPolygon(): array
    {
        return $this->boundingPolygon;
    }

    public function setBoundingPolygon(array $boundingPolygon): void
    {
        $this->boundingPolygon = $boundingPolygon;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['FieldId'] = $this->fieldId;
        $arr['CropId'] = $this->cropId;
        $arr['BoundingPolygon'] = $this->boundingPolygon;

        return $arr;
    }
}
