<?php

namespace App\Classes\Machine\MachineUnitTrack;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class MachineUnitTrackGPXFormatter extends BaseMachineUnitTrackFormatter implements IMachineUnitTrackFormattable
{
    public static function format(int $machineUnitId, string $machineUnitTrackReportData): array
    {
        $dbProj = Config::get('globals.DEFAULT_DB_CRS');
        $machineUnitTrackDataRawQuery = self::getUnitTrackRawDataQuery($machineUnitTrackReportData);

        $machineTrackPointsXmlQuery = DB::table('machine_unit_track_data_raw')
            ->selectRaw("
                XMLELEMENT(
                    name trkpt,
                    XMLATTRIBUTES(
                        ROUND(lat::numeric, 6) AS lat,
                        ROUND(lon::numeric, 6) AS lon
                    ),
                    XMLELEMENT (
                        name time,
                        to_char(datetime, 'YYYY-MM-DDThh:mm:ss')
                    ), 
                    XMLELEMENT(
                        name ele,
                        speed
                    )
                ) AS trkpt,
                speed,
                fuel_consumption,
                point,
                wialon_unit_imei
            ")
            ->orderBy('datetime');

        $trackQuery = DB::table('machine_track_points_xml')
            ->withExpression('machine_unit_track_data_raw', $machineUnitTrackDataRawQuery)
            ->withExpression('machine_track_points_xml', $machineTrackPointsXmlQuery)
            ->selectRaw("
                XMLROOT(
                    XMLELEMENT(
                        name gpx,
                        XMLATTRIBUTES(
                            '1.1' AS \"version\",
                            'Techno Farmm LTD' AS \"creator\",
                            'http://www.w3.org/2001/XMLSchema-instance' AS \"xmlns:xsi\",
                            'http://www.topografix.com/GPX/1/1/gpx.xsd' AS \"xsi:schemaLocation\",
                            'http://www.topografix.com/GPX/1/1' AS \"xmlns\"
                         ),
                         XMLELEMENT(
                            name trk,
                            XMLELEMENT(
                                name extensions,
                                XMLELEMENT(
                                    name \"gpxtpx:distance\",
                                    ROUND(ST_Length(ST_Transform(ST_Makeline(ARRAY_AGG(point)), {$dbProj}))::NUMERIC / 1000, 3) || ' km'
                                ),
                                XMLELEMENT(
                                    name \"gpxtpx:maxSpeed\",
                                    MAX(speed) || ' km/h'
                                ),
                                XMLELEMENT(
                                    name \"gpxtpx:fuelConsumption\",
                                    sum(fuel_consumption) || ' l'
                                ),
                                XMLELEMENT(
                                    name \"gpxtpx:unitId\",
                                    {$machineUnitId}
                                ),
                                XMLELEMENT(
                                    name \"gpxtpx:wialonUnitImei\",
                                    wialon_unit_imei
                                )
                            ),
                            XMLELEMENT(
                                name trkseg,
                                xmlagg(trkpt)
                            )
                        )
                    ),
                    version '1.1'
                ) AS track_gpx
            ")
            ->groupBy('wialon_unit_imei');

        return [
            'data' => $trackQuery->get()->pluck('track_gpx')->first(),
        ];
    }
}
