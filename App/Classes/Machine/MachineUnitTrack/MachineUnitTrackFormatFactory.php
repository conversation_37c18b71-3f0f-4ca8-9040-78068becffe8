<?php

namespace App\Classes\Machine\MachineUnitTrack;

use App\Models\MachineUnit;
use Exception;
use Illuminate\Support\Facades\App;

class MachineUnitTrackFormatFactory
{
    private static $formatTypes = [
        MachineUnit::TRACK_FORMAT_GEOJSON => MachineUnitTrackGeoJsonFormatter::class,
        MachineUnit::TRACK_FORMAT_GPX => MachineUnitTrackGPXFormatter::class,
    ];

    /**
     * @throws Exception
     */
    public static function make(string $type): IMachineUnitTrackFormattable
    {
        if (!isset(self::$formatTypes[$type])) {
            throw new Exception(sprintf("Unsupported format '%s'! Supported formats are: '%s.'", $type, implode(', ', array_keys(self::$formatTypes))));
        }

        return App::make(self::$formatTypes[$type]);
    }
}
