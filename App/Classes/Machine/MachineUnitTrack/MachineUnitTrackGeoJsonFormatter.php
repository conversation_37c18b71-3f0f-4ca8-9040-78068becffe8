<?php

namespace App\Classes\Machine\MachineUnitTrack;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class MachineUnitTrackGeoJsonFormatter extends BaseMachineUnitTrackFormatter implements IMachineUnitTrackFormattable
{
    public static function format(int $machineUnitId, string $machineUnitTrackReportData): array
    {
        $dbProj = Config::get('globals.DEFAULT_DB_CRS');
        $machineUnitTrackDataRawQuery = self::getUnitTrackRawDataQuery($machineUnitTrackReportData);

        $machineUnitTrackDataQuery = DB::table('machine_unit_track_data_raw')
            ->select(
                DB::raw("{$machineUnitId} AS unit_id"),
                'wialon_unit_imei',
                DB::raw('st_makeline(array_agg(point)) AS geom'),
                DB::raw("max(speed) || ' km/h' AS max_speed"),
                DB::raw("sum(fuel_consumption) || ' l' AS fuel_consumption"),
                DB::raw('min(datetime) AS start_time'),
                DB::raw('max(datetime) AS end_time')
            )
            ->groupBy('wialon_unit_imei');

        $trackQuery = DB::table('machine_unit_track_data')
            ->withExpression('machine_unit_track_data_raw', $machineUnitTrackDataRawQuery)
            ->withExpression('machine_unit_track_data', $machineUnitTrackDataQuery)
            ->select(
                DB::raw("
                    json_build_object(
                        'type', 'Feature',
                        'id', unit_id,
                        'geometry', ST_AsGeoJSON(geom)::json,
                        'properties', json_build_object(
                            'unit_id', unit_id,
                            'wialon_unit_imei', wialon_unit_imei,
                            'max_speed', max_speed,
                            'fuel_consumption', fuel_consumption,
                            'distance', round((st_length(st_transform(geom, {$dbProj}))::NUMERIC / 1000), 3) || ' km'
                        )
                    ) AS geojson
                ")
            );

        return [
            'data' => json_decode($trackQuery->pluck('geojson')->first()),
        ];
    }
}
