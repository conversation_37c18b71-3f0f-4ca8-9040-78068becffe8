<?php

namespace App\Classes\Machine\MachineUnitTrack;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class BaseMachineUnitTrackFormatter
{
    public static function getUnitTrackRawDataQuery(string $machineTrackReportData): Builder
    {
        return DB::table(
            DB::raw('json_array_elements_text(?::json) AS track(item)')
        )
            ->addBinding($machineTrackReportData, 'from')
            ->selectRaw("
                ST_SetSRID(
                    ST_MakePoint((track.item::json->'coordinates'->>'x')::float, (track.item::json->'coordinates'->>'y')::float),
                    4326
                ) AS point,
                (track.item::json->'coordinates'->>'x')::float AS lon,
                (track.item::json->'coordinates'->>'y')::float AS lat,
                NULLIF(REGEXP_REPLACE(track.item::json->>'speed', '\D','','g'), '')::numeric AS speed,
                TO_TIMESTAMP((track.item::json->>'time')::bigint) AS datetime,
                (track.item::json->>'time')::bigint AS \"timestamp\",
                (track.item::json->>'value')::numeric AS fuel_consumption,
                (track.item::json->>'wialon_unit_imei' )::bigint AS wialon_unit_imei
            ")
            ->where(DB::raw("length(track.item::json->>'sensor')"), '>', 0);
    }
}
