<?php

namespace App\Classes\CMS;

use Firebase\JWT\JWT;
use Guzzle<PERSON>ttp\Client;
use Guzzle<PERSON>ttp\HandlerStack;
use Illuminate\Http\Request;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Sentry\State\Scope;

class AbstractCmsService
{
    protected $client;
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $stack = HandlerStack::create();
        $stack->push(self::tracingTransactionContext());

        $this->client = new Client(['base_uri' => config('cms.BASE_URI'), 'handler' => $stack]);
    }

    public static function tracingTransactionContext()
    {
        return function (callable $handler) {
            return function (
                RequestInterface $request,
                array $options
            ) use ($handler) {
                $promise = $handler($request, $options);

                return $promise->then(
                    function (ResponseInterface $response) {
                        if (count($response->getHeader('X-Transaction-ID'))) {
                            $transactionId = $response->getHeader('X-Transaction-ID')[0];
                            \Sentry\configureScope(function (Scope $scope) use ($transactionId): void {
                                $scope->setTag('transaction_id', $transactionId);
                            });
                        }

                        return $response;
                    }
                );
            };
        };
    }

    protected function getHeaders(?string $jwt = null)
    {
        $token = $jwt ?? $this->extractAccessTokenFromRequest();

        return [
            'Authorization' => $token,
        ];
    }

    /**
     * JWT Encode.
     *
     * @param array|object $payload - PHP object or array
     */
    protected function jwtEncode($payload)
    {
        $privateKey = file_get_contents(config('globals.JWT_SYSTEM_PRIVATE_KEY'));
        $algorithm = config('globals.JWT_SYSTEM_ALGORITHM');

        return JWT::encode($payload, $privateKey, $algorithm);
    }

    private function extractAccessTokenFromRequest()
    {
        return $this->request->headers->get('authorization');
    }
}
