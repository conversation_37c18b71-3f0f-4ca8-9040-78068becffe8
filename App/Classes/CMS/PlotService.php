<?php

namespace App\Classes\CMS;

use App\Models\Plot;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;

class PlotService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @param $orderUuids
     *
     * @throws GuzzleException
     */
    public function getFilteredPlotsSoilsData($filters)
    {
        $query['filter'] = $filters;

        try {
            $response = $this->client->request('GET', 'api/plots/soils-filter', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getPlotSoilsListData($orderUuids)
    {
        $query['filter']['customer_order_uuids'] = json_encode($orderUuids);

        try {
            $response = $this->client->request('GET', 'api/plots/soils-list', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getPlotABOverviewData($filter, $page, $limit)
    {
        try {
            $response = $this->client->request('POST', 'api/contracts/subscription-package-field/ab-overview', [
                'headers' => $this->getHeaders(),
                'json' => [
                    'filter' => $filter,
                    'page' => $page,
                    'limit' => $limit,
                ],
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $plotsResponseData = json_decode($response->getBody()->getContents(), true);

        $plotUuIds = array_map(function ($plot) {
            return $plot['uuid'];
        }, $plotsResponseData['items']);

        $plots = Plot::select('uuid', 'name')->whereIn('uuid', $plotUuIds)->get()->toArray();
        $plots = array_combine(array_column($plots, 'uuid'), $plots);

        $plotsResponseData['items'] = array_map(function ($raw) use ($plots) {
            $plot = $plots[$raw['uuid']];
            $raw['name'] = $plot['name'];

            return $raw;
        }, $plotsResponseData['items']);

        return $plotsResponseData;
    }

    /**
     * @throws GuzzleException
     */
    public function samplesContentFromCMS(string $plotUuId, string $orderUuid, array $samplingTypeIds)
    {
        try {
            $response = $this->client->request('GET', 'api/analysis/lab-elements/plots/' . $plotUuId . '/soil/samples', [
                'headers' => $this->getHeaders(),
                'query' => [
                    'order_uuid' => $orderUuid,
                    'sampling_type_ids' => $samplingTypeIds,
                ],
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function samplesContentByElementFromCMS(string $plotUuid, string $orderUuid, string $element, array $samplingTypeIds)
    {
        try {
            $response = $this->client->request('GET', "api/analysis/lab-elements/{$element}/plots/{$plotUuid}/soil/samples", [
                'headers' => $this->getHeaders(),
                'query' => [
                    'order_uuid' => $orderUuid,
                    'sampling_type_ids' => $samplingTypeIds,
                ],
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getPackagesByPlot(string $plotUuid, $headerParams)
    {
        try {
            $url = "api/plots/{$plotUuid}/packages";

            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCellsForSamplingWithElements($query)
    {
        try {
            $response = $this->client->request('GET', 'api/analysis/package-grid-points/for-sampling', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function updatePlotFarmInCMS($plotUuid, $farmId)
    {
        try {
            $this->client->request('POST', 'api/contracts/subscription-package-field/update-farm', [
                'headers' => $this->getHeaders(),
                'json' => [
                    'plotUuid' => $plotUuid,
                    'farmId' => $farmId,
                ],
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }
}
