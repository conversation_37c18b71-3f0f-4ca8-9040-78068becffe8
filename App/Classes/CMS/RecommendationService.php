<?php

namespace App\Classes\CMS;

use Auth;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;

class RecommendationService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function getRecommendations(array $headerParams)
    {
        $organizations = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get();
        $organizationIdentityNumbers = array_column($organizations->toArray(), 'identity_number');
        if (isset($headerParams['filter']['customer_identification'])) {
            $identityNumber = $headerParams['filter']['customer_identification'];
            $headerParams['filter']['customer_identification'] = json_encode([$identityNumber]);
        } else {
            $headerParams['filter']['customer_identification'] = json_encode($organizationIdentityNumbers);
        }

        return $this->getRecommendationData($headerParams);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationByPlot(string $plotUuid, array $headerParams)
    {
        $url = "api/recommendations/plot/{$plotUuid}";

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationLabResults(int $subscriptionPackageFieldId, array $samplingTypeIds)
    {
        $url = "api/recommendations/{$subscriptionPackageFieldId}/lab-results";

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => [
                    'sampling_type_ids' => $samplingTypeIds,
                ],
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function storeRecommendation(array $params)
    {
        $url = 'api/recommendations/store';

        try {
            $response = $this->client->request('POST', $url, [
                'headers' => $this->getHeaders(),
                'json' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param string $status
     *
     * @throws GuzzleException
     */
    public function getRecommendationsByLastChosenOrganization(array $headerParams, $status = 'Delivered')
    {
        $headerParams['filter']['customer_identification'] = json_encode([
            Auth::user()->lastChosenOrganization->identity_number,
        ]);

        if (isset($headerParams['filter']['year'])) {
            $headerParams['filter']['year'] = (((int)$headerParams['filter']['year']) - 1) . '/' . $headerParams['filter']['year'];
        }
        $headerParams['filter']['status'] = json_encode([$status]);

        return $this->getRecommendationData($headerParams);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationById(int $id)
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/' . $id, [
                'headers' => $this->getHeaders(),
                'query' => [],
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationVra(int $recommendationId)
    {
        try {
            $params = [];

            $response = $this->client->request('GET', 'api/recommendations/' . $recommendationId . '/vra-orders', [
                'headers' => $this->getHeaders(),
                'query' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCalculations(int $subscriptionPackageFieldId, array $params)
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/' . $subscriptionPackageFieldId . '/calculations', [
                'headers' => $this->getHeaders(),
                'query' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationModels()
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/models', [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function update(int $id, array $params)
    {
        try {
            $response = $this->client->request('PUT', 'api/recommendations/' . $id . '/update', [
                'headers' => $this->getHeaders(),
                'json' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param ?string $declineReason
     *
     * @throws GuzzleException
     */
    public function updateStatus(int $id, string $status, ?string $declineReason = null)
    {
        try {
            $params = ['status' => $status];

            if (isset($declineReason)) {
                $params['decline_reason'] = $declineReason;
            }

            $response = $this->client->request('PUT', 'api/recommendations/' . $id . '/update-status', [
                'headers' => $this->getHeaders(),
                'json' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationData($headerParams)
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationReport($headerParams)
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/report', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function getRecommendationsSoilAnalyzesData(array $params = [])
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/soil-analyzes-data', [
                'headers' => $this->getHeaders(),
                'query' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }
}
