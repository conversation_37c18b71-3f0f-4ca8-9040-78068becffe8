<?php

namespace App\Classes\CMS;

use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;

class ProtocolService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function listProtocols($requestData, $id = null)
    {
        $query = [
            'customer_identification' => $requestData['organization_identity_number'],
        ];

        if (isset($requestData['page']) && !empty($requestData['page'])) {
            $query['page'] = (int) $requestData['page'];
        }

        if (isset($requestData['limit']) && !empty($requestData['limit'])) {
            $query['limit'] = (int) $requestData['limit'];
        }

        $url = 'api/protocols/';
        if ($id) {
            $url .= "{$id}";
        } else {
            $url .= 'list';
        }

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }
}
