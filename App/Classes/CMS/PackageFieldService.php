<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\CMS;

use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;

class PackageFieldService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return float|int
     */
    public function cntAllSubscriptionFields($organizations)
    {
        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');

        $query = [
            'customer_identification' => json_encode($organizationIdentityNumbers),
        ];

        try {
            $subscriptionPackageFields = $this->client->request('GET', 'api/contracts/subscription-package-field/count', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($subscriptionPackageFields->getBody()->getContents(), true);
    }
}
