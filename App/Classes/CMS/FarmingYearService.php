<?php

namespace App\Classes\CMS;

use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class FarmingYearService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function getFarmingYearsByOrganization(int $organizationId): Collection
    {
        try {
            $response = $this->client->request('GET', "api/farming-years/organization/{$organizationId}", [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $farmingYears = json_decode($response->getBody()->getContents(), true);

        return collect($farmingYears);
    }
}
