<?php

namespace App\Classes\CMS;

use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class ResponsibleUsersService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function findByOrderUuid(string $orderUuid): Collection
    {
        try {
            $response = $this->client->request('GET', "api/responsible_users/order/{$orderUuid}", [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        return collect($data);
    }
}
