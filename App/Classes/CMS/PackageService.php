<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\CMS;

use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;

class PackageService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function list(array $queryParams = [])
    {
        try {
            $response = $this->client->request('GET', 'api/packages', [
                'headers' => $this->getHeaders(),
                'query' => $queryParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getPackage(int $packageId)
    {
        try {
            $response = $this->client->request('GET', 'api/packages/' . $packageId, [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return array
     */
    public function countPackagesByCustomerIdentifications($organizations)
    {
        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');

        $response = [];
        $response['countAvailablePackages'] = $this->cntAvailablePackages($organizationIdentityNumbers);
        $response['countAllPackages'] = $this->cntAllPackages($organizationIdentityNumbers);

        return $response;
    }

    public function stationAdded(int $contractId)
    {
        try {
            $response = $this->client->request('POST', 'api/contracts/subscription-package/contract/' . $contractId . '/station-added', [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return float|int
     */
    private function cntAllPackages($organizationIdentityNumbers)
    {
        $query = [
            'customer_identification' => json_encode($organizationIdentityNumbers),
            'has_packages' => 'true',
        ];

        try {
            $subscriptionPackage = $this->client->request('GET', 'api/contracts/subscription-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);

            $serviceResponse = $this->client->request('GET', 'api/contracts/service-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $subsResult = json_decode($subscriptionPackage->getBody()->getContents(), true);
        $serviceResult = json_decode($serviceResponse->getBody()->getContents(), true);

        return array_sum([$subsResult['countPackages'], $serviceResult['countPackages']]);
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return float|int
     */
    private function cntAvailablePackages($organizationIdentityNumbers)
    {
        $query = [
            'customer_identification' => json_encode($organizationIdentityNumbers),
            'has_packages' => 'true',
            'type_count' => 'available',
        ];

        try {
            $subscriptionPackage = $this->client->request('GET', 'api/contracts/subscription-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);

            $serviceResponse = $this->client->request('GET', 'api/contracts/service-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $subsResult = json_decode($subscriptionPackage->getBody()->getContents(), true);
        $serviceResult = json_decode($serviceResponse->getBody()->getContents(), true);

        return array_sum([$subsResult['countPackages'], $serviceResult['countPackages']]);
    }
}
