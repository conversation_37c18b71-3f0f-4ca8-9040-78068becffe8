<?php

namespace App\Classes\CMS;

use App\Models\Farm;
use App\Models\Plot;
use App\Models\PlotCrop;
use App\Services\Exports\ExportFactory;
use Arr;
use Auth;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AnalysisReportService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @throws GuzzleException
     */
    public function getAnalysesReport(array $queryParams, bool $forExport = false)
    {
        $farmIds = Farm::where('organization_id', '=', Auth::user()->lastChosenOrganization->id)->pluck('id');
        $queryParams['filter']['farm_ids'] = json_encode($farmIds);

        $plotIds = json_decode(Arr::get($queryParams['filter'], 'plot_ids', '{}'), true);

        if (count($plotIds)) {
            $plotUuids = Plot::whereIn('gid', $plotIds)->get()->pluck('uuid');
            $queryParams['filter']['plot_uuids'] = json_encode($plotUuids);
            unset($queryParams['filter']['plot_ids']);
        }

        $params = $this->getAnalysesReportFromCMS($queryParams);
        $rowsCollection = collect($params['rows']);
        $plotUuids = $rowsCollection->pluck('plot_uuid')->unique()->toArray();
        $orderUuids = $rowsCollection->pluck('order_uuid')->unique()->toArray();

        $plotWithSoilPointsAndFarms = Plot::getPlotsWithSoilGridPointsAndFarms(
            Auth::user()->lastChosenOrganization->id,
            Auth::user()->id,
            $plotUuids,
            $orderUuids
        )->keyBy('plot_uuid');

        $farmYears = json_decode(Arr::get($queryParams['filter'], 'farm_years', '{}'), true);
        $lang = Arr::get($queryParams, 'lang', 'en');
        $cropsData = PlotCrop::getCropDataByPlotsForAnalysis($plotIds, $farmYears, $lang);

        $params['rows'] = array_map(function ($row) use ($plotWithSoilPointsAndFarms, $forExport, $cropsData) {
            $plotData = $plotWithSoilPointsAndFarms[$row['plot_uuid']];
            $cropId = $plotData['plot_id'] . '_' . $row['period'];

            $row['farm_name'] = $plotData['farm_name'];
            $row['plot_name'] = $plotData['plot_name'];
            $row['sampling_date'] = $plotData['sampling_date'];
            $row['sample_area'] = $plotData['soil_grid_points_area'][$row['sample_id']];
            $row['crop_name'] = $cropsData[$cropId]['crop_name'] ?? null;

            if ($forExport) {
                $row['longitude'] = (float)$plotData['soil_grid_points_coordinates'][$row['sample_id']]['longitude'];
                $row['latitude'] = (float)$plotData['soil_grid_points_coordinates'][$row['sample_id']]['latitude'];
                $row['organization'] = Auth::user()->lastChosenOrganization->name;
            }

            return $row;
        }, $params['rows']);

        $columns = Config('reports.ANALYSES_COLUMNS');
        foreach ($params['header'] as $element) {
            array_push($columns, ['name' => $element, 'label' => $element, 'width' => '100px', 'visible' => true]);
        }

        if ($forExport) {
            $columns = array_merge($columns, [
                ['name' => 'longitude', 'label' => 'Longitude', 'width' => '120px', 'visible' => true],
                ['name' => 'latitude', 'label' => 'Latitude', 'width' => '120px', 'visible' => true],
                ['name' => 'organization', 'label' => 'Organization', 'width' => '180px', 'visible' => true],
                ['name' => 'plot_uuid', 'label' => 'Plot ID', 'width' => '180px', 'visible' => true],
            ]);
        }

        $params['header'] = $columns;

        return $params;
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function exportXLSAnalysesReport($queryParams): string
    {
        $reportData = $this->getAnalysesReport($queryParams, true);

        if (0 === $reportData['total']) {
            throw new Exception('No data', 404);
        }

        $fileName = uniqid('analyses_report_', true) . '.xls';
        $columns = array_column($reportData['header'], 'name');
        $columnsMap = $this->translateColumns($columns);

        $finalReportData = array_map(function ($row) use ($columnsMap) {
            return $this->getRowData((array) $row, $columnsMap);
        }, $reportData['rows']);

        $exporter = ExportFactory::make('xls');
        $exporter->export(collect($finalReportData), $columns, $fileName, [], 'reportAnalysis', 'report_analysis');

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return $fileName;
    }

    /**
     * @throws GuzzleException
     */
    private function getAnalysesReportFromCMS($queryParams)
    {
        $response = $this->client->request('GET', 'api/analysis/lab-elements/report', [
            'headers' => $this->getHeaders(),
            'query' => $queryParams,
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    private function translateColumns(array $columns): array
    {
        $columnsMap = [];
        foreach ($columns as $value) {
            $columnsMap[$value] = trans('reportAnalysis.' . $value);
        }

        return $columnsMap;
    }

    private function getRowData(array $row, array $columnsMap): array
    {
        $rowData = [];

        foreach ($columnsMap as $key => $v) {
            if (isset($row[$key]) && strlen($row[$key]) > 0) {
                if ('package_name' === $key) {
                    $rowData[$key] = $row[$key] . ' ' . $row['period'];

                    continue;
                }

                $rowData[$key] = $row[$key];
            } elseif (isset($row['elements']) && array_key_exists($key, $row['elements'])) {
                $rowData[$key] = $row['elements'][$key];
            } else {
                $rowData[$key] = '';
            }
        }

        return $rowData;
    }
}
