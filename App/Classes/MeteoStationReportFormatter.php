<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 7/2/2020
 * Time: 2:45 PM.
 */

namespace App\Classes;

use Carbon\Carbon;

class MeteoStationReportFormatter
{
    public const REPORT_PROPERTY_MAX_CNT = 6;
    public const PRECIPITATION_SUM_ABOVE = 5;

    private static $sensorReportMap = [
        'precipitationSum' => 'Precipitation',
        'precipitationSumAbove' => 'Precipitation',
        'airTemperatureMax' => 'Air temperature max',
        'airTemperatureMin' => 'Air temperature min',
        'soilTemperatureMin' => 'Soil temperature min',
        'soilTemperatureMax' => 'Soil temperature max',
        'soilMoistureMin' => 'Field capacity',
        'soilMoistureMax' => 'Field capacity',
    ];

    /**
     * @return array
     */
    public function format($data, $stationName, bool $includeRowChildren = true)
    {
        $report = [];
        $report['stationName'] = $stationName;
        $report['precipitationSum'] = $this->precipitationSum($data);
        $report['precipitationSumAbove'] = $this->precipitationSumAbove(self::PRECIPITATION_SUM_ABOVE, $data);
        $airTemperature = $this->airTemperature($data);
        $report['airTemperatureMax'] = $airTemperature['Max'];
        $report['airTemperatureMin'] = $airTemperature['Min'];

        $soilTemperature = $this->getSoilMinMaxData('Soil temperature', 'soilTemperature', $data);
        foreach ($soilTemperature as $key => $value) {
            $report[$key] = $value;
        }

        $fieldCapacity = $this->getMinMaxData('Field capacity', 'soilMoisture', $data);
        foreach ($fieldCapacity as $key => $value) {
            $report[$key] = $value;
        }

        if (true === $includeRowChildren) {
            $this->setChildren($report, $data);
        }

        return $report;
    }

    /**
     * @return null|array|float|\Illuminate\Contracts\Translation\Translator|string
     */
    private function precipitationSum($data)
    {
        $data = $this->findData('Precipitation', $data);

        $noData = trans('general.no_data');
        if ($data === $noData) {
            return $noData;
        }

        $sum = round(array_sum(array_column($data, 'value')), 1);
        $unit = array_first($data)['unit'];

        return ['value' => $sum, 'unit' => $unit];
    }

    /**
     * @return null|array|float|\Illuminate\Contracts\Translation\Translator|string
     */
    private function precipitationSumAbove($value, $data)
    {
        $data = $this->findData('Precipitation', $data);

        $noData = trans('general.no_data');
        if ($data === $noData) {
            return $noData;
        }

        $result = array_filter($data, function ($valuePrecipitation) use ($value) {
            return $valuePrecipitation['value'] > $value;
        });

        $sum = round(array_sum(array_column($result, 'value')), 1);
        $unit = array_first($data)['unit'];

        return ['value' => $sum, 'unit' => $unit];
    }

    /**
     * @param bool $strict
     *
     * @return null|array|data|\Illuminate\Contracts\Translation\Translator|mixed|string
     */
    private function findData($param, $data, $strict = true)
    {
        $noData = trans('general.no_data');
        if (!isset($data['series'])) {
            return $noData;
        }

        $found = array_filter($data['series'], function ($value) use ($param, $strict) {
            if ($strict) {
                return $value['name'] === trans('general.' . $param);
            }

            return (false !== strpos($value['name'], trans('general.' . $param)));
        });

        if (!$found) {
            return $noData;
        }

        if (!$strict) {
            return $found;
        }

        $found = reset($found);

        return $found['data'];
    }

    /**
     * @return array
     */
    private function airTemperature($data)
    {
        $dataMin = $this->findData('Air temperature min', $data);
        $dataMax = $this->findData('Air temperature max', $data);
        $noData = trans('general.no_data');

        return ['Max' => is_array($dataMax) ? max($dataMax) : $noData, 'Min' => is_array($dataMin) ? min($dataMin) : $noData];
    }

    /**
     * @return array
     */
    private function getMinMaxData($param, $property, $data)
    {
        $dataValues = $this->findData($param, $data, false);

        $noData = trans('general.no_data');
        $minMaxData = [];
        for ($i = 1; $i <= self::REPORT_PROPERTY_MAX_CNT; ++$i) {
            $minMaxData[$property . 'Max' . $i] = $noData;
            $minMaxData[$property . 'Min' . $i] = $noData;
        }

        if ($dataValues === $noData) {
            return $minMaxData;
        }

        $index = 1;
        foreach ($dataValues as $value) {
            if (!isset($value['data'])) {
                $minMaxData[$property . 'Max' . $index] = $noData;
                $minMaxData[$property . 'Min' . $index] = $noData;
                $index = $index + 1;

                continue;
            }

            $values = $value['data'];
            $minMaxData[$property . 'Max' . $index] = max($values);
            $minMaxData[$property . 'Min' . $index] = min($values);
            $index = $index + 1;
        }

        return $minMaxData;
    }

    /**
     * @return array
     */
    private function getSoilMinMaxData($param, $property, $data)
    {
        $dataValuesMin = $this->findData($param . ' min', $data, false);
        $dataValuesMax = $this->findData($param . ' max', $data, false);

        $noData = trans('general.no_data');
        $minMaxData = [];
        for ($i = 1; $i <= self::REPORT_PROPERTY_MAX_CNT; ++$i) {
            $minMaxData[$property . 'Max' . $i] = $noData;
            $minMaxData[$property . 'Min' . $i] = $noData;
        }

        if ($dataValuesMin === $noData || $dataValuesMax === $noData) {
            return $minMaxData;
        }

        $index = 1;
        foreach ($dataValuesMin as $value) {
            if (!isset($value['data'])) {
                $minMaxData[$property . 'Min' . $index] = $noData;
                $index = $index + 1;

                continue;
            }

            $values = $value['data'];
            $minMaxData[$property . 'Min' . $index] = min($values);
            $index = $index + 1;
        }

        $index = 1;
        foreach ($dataValuesMax as $value) {
            if (!isset($value['data'])) {
                $minMaxData[$property . 'Max' . $index] = $noData;
                $index = $index + 1;

                continue;
            }

            $values = $value['data'];
            $minMaxData[$property . 'Max' . $index] = max($values);
            $index = $index + 1;
        }

        return $minMaxData;
    }

    /**
     * Sets report row childre by day.
     */
    private function setChildren(array &$report, array $data): void
    {
        $noData = trans('general.no_data');
        $report['children'] = [];

        foreach (self::$sensorReportMap as $sensorKey => $reportName) {
            $findStrict = true;
            if ('Soil temperature min' == $reportName || 'Soil temperature max' == $reportName || 'Field capacity' == $reportName) {
                $findStrict = false;
            }

            $sensorData = $this->findData($reportName, $data, $findStrict);

            foreach ($data['periods'] as $periodKey => $miliseconds) {
                $report['children'][$periodKey]['date'] = Carbon::createFromTimestamp($miliseconds / 1000)->format('d-m-Y');
                $report['children'][$periodKey]['stationName'] = $report['children'][$periodKey]['date'];
                $report['children'][$periodKey]['key'] = uniqid();
                if (true === $findStrict) {
                    if ($report[$sensorKey] === $noData) {
                        $report['children'][$periodKey][$sensorKey] = $noData;

                        continue;
                    }
                    if ('precipitationSumAbove' == $sensorKey) {
                        $report['children'][$periodKey][$sensorKey] = ($sensorData[$periodKey]['value'] > self::PRECIPITATION_SUM_ABOVE) ? $sensorData[$periodKey] : 0;

                        continue;
                    }
                    $report['children'][$periodKey][$sensorKey] = $sensorData[$periodKey];
                } else {
                    $cnt = 1;
                    if (!is_array($sensorData)) {
                        $report['children'][$periodKey][$sensorKey . $cnt] = $noData;

                        continue;
                    }
                    foreach ($sensorData as $value) {
                        $newKey = '';
                        $newKey = $sensorKey . $cnt;

                        if ($report[$newKey] === $noData) {
                            $report['children'][$periodKey][$newKey] = $noData;
                            $cnt++;

                            continue;
                        }

                        $report['children'][$periodKey][$newKey] = $value['data'][$periodKey];
                        $cnt++;
                    }
                }
            }
        }
    }
}
