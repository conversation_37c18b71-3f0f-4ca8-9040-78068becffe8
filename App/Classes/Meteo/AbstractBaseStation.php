<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 7/2/2020
 * Time: 4:58 PM.
 */

namespace App\Classes\Meteo;

use App\Classes\Echarts\IEChartsFormattable;
use App\Classes\Heap;
use App\Classes\MeteoStationReportFormatter;
use App\Helpers\Helper;
use App\Models\UserStation;
use Cache;
use DateTime;
use DateTimeZone;
use Exception;

abstract class AbstractBaseStation implements IEChartsFormattable, IStation
{
    public const DEFAULT_CONVERSION_COEFFICIENT = 1;
    public const DEFAULT_VALUE_FORMAT_DECIMALS = 2;

    protected $stationModel;
    protected $sensorCodesMaping;
    private $heap;

    public function __construct(UserStation $stationModel, Heap $heap)
    {
        $this->stationModel = $stationModel;
        $this->heap = $heap;
    }

    /**
     * Retrieves the station report as an EChart.
     *
     * @param string $feed the feed name
     * @param string $period the period of the report
     * @param array $sensors the array of sensors to include in the report
     * @param string $fromDate the starting date of the report
     * @param string $toDate the ending date of the report
     * @param bool $isTimestamp (optional) Indicates whether the dates are timestamps
     *
     * @throws Exception
     *
     * @return array
     */
    public function getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false)
    {
        if (!$this->stationModel) {
            throw new Exception('User station not found.', 404);
        }

        $cacheMinutes = now()->addMinutes(15);
        // update last communication
        $stationInfo = Cache::remember('stationInfo/' . $this->stationModel->name, $cacheMinutes, function () {
            return $this->getStationData();
        });

        if (!is_array($stationInfo) || !isset($stationInfo['dates']) || !isset($stationInfo['position'])) {
            throw new Exception('User station not found.', 404);
        }

        $timeZone = $this->getTimeZone();
        $dateRange = $this->dateRange($period, $this->stationModel->install_date, $fromDate, $toDate, $timeZone, $isTimestamp);

        $from = $dateRange['timestampFrom'];
        $to = $dateRange['timestampTo'];

        if (!$from) {
            throw new Exception('Start date is required!', 400);
        }
        $totalExpextedCount = $this->totalExpectedCount($feed, $from, $to);

        $allowedRecordsCount = config('echarts.stations.allowedRecordsCount');
        if ($totalExpextedCount > $allowedRecordsCount) {
            throw new Exception('You have selected too much data, please increase the reporting time or reduce the preview time.', 416);
        }

        $chartData = $this->getHistoryDataEChartsFormatted(
            $cacheMinutes,
            $stationInfo,
            $from,
            $to,
            $feed,
            $sensors,
            function () {}
        );
        if (!is_array($chartData)) {
            throw new Exception('Station not found.', 500);
        }

        return $chartData;
    }

    /**
     * @param bool $isTimestamp
     *
     * @throws Exception
     *
     * @return array
     */
    public function getStationReport($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false, bool $includeRowChildren = true)
    {
        $chartData = $this->getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp);

        $formatter = new MeteoStationReportFormatter();
        $stationName = $this->stationModel->custom_name ?? $this->stationModel->name;

        return $formatter->format($chartData, $stationName, $includeRowChildren);
    }

    protected function dateRange(string $period, string $installDate, ?string $fromDate, ?string $toDate, string $timeZone, bool $isTimestamp): array
    {
        if ($isTimestamp) {
            return ['timestampFrom' => $fromDate, 'timestampTo' => $toDate];
        }

        $timestampFrom = null;
        $timestampTo = null;

        if ('custom' !== $period) {
            $now = new DateTime('now', (new DateTimeZone($timeZone)));
            $periodStart = clone $now;
            $periodStart->modify('-' . $period);
            $stationInstallDate = DateTime::createFromFormat('Y-m-d', $installDate, (new DateTimeZone($timeZone)));

            if ($stationInstallDate > $periodStart) {
                $periodStart = $stationInstallDate;
            }

            $periodStart->setTime(0, 0, 0);
            $timestampFrom = $periodStart->getTimestamp();
            $timestampTo = null;

            return ['timestampFrom' => $timestampFrom, 'timestampTo' => $timestampTo];
        }

        if (!$fromDate || !$toDate) {
            return ['timestampFrom' => $timestampFrom, 'timestampTo' => $timestampTo];
        }

        if (!Helper::isValidDate($fromDate) || !Helper::isValidDate($toDate)) {
            throw new Exception('Invalid date format', 403);
        }

        $periodStart = DateTime::createFromFormat('Y-m-d', $fromDate, (new DateTimeZone($timeZone)))->setTime(0, 0, 0);
        $timestampFrom = $periodStart->getTimestamp();

        $periodEnd = DateTime::createFromFormat('Y-m-d', $toDate, (new DateTimeZone($timeZone)))->setTime(0, 0, 0);
        $periodEnd->modify('+1 day');
        $timestampTo = $periodEnd->getTimestamp();

        return ['timestampFrom' => $timestampFrom, 'timestampTo' => $timestampTo];
    }

    /**
     * @throws Exception
     *
     * @return float|int
     */
    protected function totalExpectedCount($feed, $from, $to)
    {
        $now = new DateTime();
        $date = clone $now;
        $date->setTimestamp($from);

        if ($to) {
            $now->setTimestamp($to);
        }

        $diff = $date->diff($now);
        $days = intval($diff->format('%a'));

        switch ($feed) {
            case 'hourly':
                return $days * 24;
            case 'raw':
                return $days * 24 * 4;
            default:
                return $days;
        }
    }

    /**
     * Format sensor Value.
     *
     * @param float $value
     * @param float $coeff
     */
    protected function formatSensorValue($value, $coeff = self::DEFAULT_CONVERSION_COEFFICIENT, $decimals = self::DEFAULT_VALUE_FORMAT_DECIMALS)
    {
        $transformedValue = round(($value * $coeff), 2, PHP_ROUND_HALF_UP);

        return number_format($transformedValue, $decimals, '.', '');
    }

    /**
     * Get conversion coefficietn.
     *
     * @param string $configKey
     */
    protected function getConversionCoefficient(?string $configKey): float
    {
        $config = $this->sensorCodesMaping;
        if (!$configKey) {
            return self::DEFAULT_CONVERSION_COEFFICIENT;
        }
        if (!array_key_exists($configKey, $config)) {
            return self::DEFAULT_CONVERSION_COEFFICIENT;
        }

        return $config[$configKey]['unit_conversion_coefficient'];
    }

    /**
     * Undocumented function.
     *
     * @param string $configKey
     */
    protected function getConversionUnit(?string $configKey): ?string
    {
        $config = $this->sensorCodesMaping;
        if (!$configKey) {
            return null;
        }

        if (!array_key_exists($configKey, $config)) {
            return null;
        }

        return $config[$configKey]['conversion_unit'];
    }
}
