<?php

namespace App\Classes\Meteo;

use App\Classes\Heap;
use App\Helpers\Helper;
use Cache;
use Carbon\Carbon;
use Config;
use Exception;
use GuzzleHttp\Client;
use Request;

/**
 * A class for Meteo Cache.
 */
class MeteoCache
{
    private $configMeteoBlue;
    private $baseUriImg;
    private $defaultQueryMeteoBlue = [];
    private $clientMeteoBlue;
    private $clientMeteoBlueImg;

    private $heap;

    /**
     * @param string meteoCache MeteoBlue config
     */
    public function __construct($configMeteoBlue, Heap $heap)
    {
        $this->configMeteoBlue = $configMeteoBlue;

        $this->baseUriImg = $configMeteoBlue['METEO_URI_IMG'];

        $this->defaultQueryMeteoBlue = [
            'apikey' => $configMeteoBlue['METEO_APIKEY'],
            'tz' => $configMeteoBlue['METEO_TZ'],
            'temperature' => 'C',
            'windspeed' => 'ms-1',
            'winddirection' => 'degree',
            'precipitationamount' => 'mm',
            'timeformat' => 'iso8601',
            'format' => 'json',
            'lang' => $configMeteoBlue['METEO_LANG'],
        ];

        $this->clientMeteoBlue = new Client(['base_uri' => $configMeteoBlue['METEO_BASE_URI']]);
        $this->clientMeteoBlueImg = new Client(['base_uri' => $this->baseUriImg]);

        $this->heap = $heap;
    }

    /**
     * Retrieve the MeteoBlue data from the cache or store it in the cache if not available.
     *
     * @param array $arrQuery the query parameters for the MeteoBlue API
     * @param string $feedТype the type of MeteoBlue feed
     * @param Carbon $cacheMinutes the number of minutes to cache the data
     * @param callable $callback the callback function to process the retrieved data
     *
     * @return mixed the processed data from the cache or an empty array if an exception occurs
     */
    public function getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes, $callback)
    {
        $key = $this->configMeteoBlue['METEO_BASE_URI'] . $feedТype . '?' . http_build_query($arrQuery);

        $arrQuery = array_merge($this->defaultQueryMeteoBlue, $arrQuery);

        // Retrieve or Store from the Cache
        return Cache::remember($key, $cacheMinutes, function () use ($arrQuery, $feedТype, $callback) {
            try {
                $response = $this->clientMeteoBlue->request('GET', $feedТype, [
                    'query' => $arrQuery,
                ]);
                $content = json_decode($response->getBody()->getContents(), true);

                return $callback($content);
            } catch (Exception $e) {
                return $callback([]);
            }
        });
    }

    /**
     * Get Meteo Blue From Cache.
     *
     * @param  arrQuery
     * @param  feedТype
     */
    public function requestMeteoBlueImg($arrQuery, $feedТype, $gid, $lang, $userId, $zoneGid)
    {
        $arrQuery = array_merge($this->defaultQueryMeteoBlue, $arrQuery);

        // File check
        $fileName = $gid . '_' . $lang . '_' . $feedТype . '.png';
        $filePath = config('meteo.METEO_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . $userId . DIRECTORY_SEPARATOR . $fileName;

        if (file_exists($filePath)) {
            return ['fileName' => $fileName];
        }

        $this->heap->track($feedТype, [
            'Language' => $lang,
            'Zone GID' => $zoneGid,
        ]);

        // Make request to MeteoBlue API - controlled by cron image deleting
        $response = $this->clientMeteoBlueImg->request('GET', $feedТype, [
            'query' => $arrQuery,
        ]);
        $contentImg = $response->getBody()->getContents();

        @mkdir(dirname($filePath), 0777, true);

        if (!file_put_contents($filePath, $contentImg)) {
            return Helper::errorResponseMeteoBlue('No file saved.');
        }

        return ['fileName' => $fileName];
    }
}
