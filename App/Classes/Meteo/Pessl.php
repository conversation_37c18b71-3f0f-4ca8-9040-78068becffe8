<?php

namespace App\Classes\Meteo;

use App\Classes\Echarts\Meteo\EChartsMeteoDataFormatter;
use App\Classes\Heap;
use App\Models\UserStation;
use Cache;
use Carbon\Carbon;
use Config;
use DateTime;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

/**
 * A class for Pessl API.
 */
class Pessl extends AbstractBaseStation
{
    public const TIME_ZONE = 'UTC';

    private $clientAuth;
    private $clientApi;
    private $clientId;
    private $clientSecret;
    private $oAuthUrl;
    private $apiUrl;
    private $sensorCodes;
    private $codeTemperature;
    private $codeWindSpeed;

    /**
     * Pessl constructor.
     */
    public function __construct($config, UserStation $stationModel, Heap $heap)
    {
        parent::__construct($stationModel, $heap);

        $this->clientId = $config['client_id'];
        $this->clientSecret = $config['client_secret'];
        $this->oAuthUrl = $config['oauth_url'];
        $this->apiUrl = $config['api_url'];
        $this->sensorCodes = $config['sensor_codes'];
        $this->codeTemperature = $config['code_temperature'];
        $this->codeWindSpeed = $config['code_wind_speed'];
        $this->sensorCodesMaping = $config['sensor_codes_mapping'];

        $this->clientAuth = new Client([
            'base_uri' => $this->oAuthUrl,
        ]);

        $this->clientApi = new Client([
            'base_uri' => $this->apiUrl,
        ]);
    }

    /**
     * Get History Data 1 year history period from today.
     *
     * @throws Exception
     */
    public function getHistoryData($cacheMinutes, $callback)
    {
        $now = new DateTime();
        $yearAgo = clone $now;
        $yearAgo->modify('-1 year');
        $stationInstallDate = DateTime::createFromFormat('Y-m-d', $this->stationModel->install_date);
        $feed = 'daily';

        $period = $yearAgo;
        if ($stationInstallDate > $yearAgo) {
            $period = $stationInstallDate;
        }

        $historyPeriod = mktime(0, 0, 0, $period->format('m'), $period->format('d'), $period->format('Y'));

        return $this->historyDataCommon($historyPeriod, $cacheMinutes, $feed, $callback);
    }

    /**
     * Get History Data From Period.
     */
    public function getHistoryDataFrom($cacheMinutes, $period, $feed, $callback)
    {
        return $this->historyDataCommon($period, $cacheMinutes, $feed, $callback);
    }

    public function getCurrentTemperature($cacheMinutes)
    {
        $headers = $this->getHeaders();

        $callPath = 'data/' . $this->stationModel->name . '/raw/last/1';
        $sensCode = config('pessl')['code_temperature'];

        return Cache::remember(
            'getCurrentTemperature/' . $callPath . '_' . $sensCode,
            $cacheMinutes,
            function () use ($callPath, $headers) {
                $response = $this->clientApi->request('GET', $callPath, [
                    'headers' => $headers,
                ]);

                $content = json_decode($response->getBody()->getContents(), true);

                $dateTime = $content['data'][0]['date'];
                $date = explode(' ', $dateTime)[0];

                $now = new DateTime();
                $today = DateTime::createFromFormat('Y-m-d', $date);

                $diff = $today->diff($now);

                if ($diff->days > 1) {
                    return false;
                }

                $sensorCodes = [
                    '506' => 'avg', // air temperature
                ];

                $sensorValues = $this->getSensorValues($content, $sensorCodes);

                return empty($sensorValues) ? null : $sensorValues[0]['current_value'];
            }
        );
    }

    /**
     * Returns the current wind speed of the station.
     *
     * @return bool|float
     */
    public function getCurrentWindSpeed($cacheMinutes)
    {
        $headers = $this->getHeaders();

        $callPath = 'data/' . $this->stationModel->name . '/raw/last/1';
        $sensCode = config('pessl')['code_wind_speed'];

        return Cache::remember(
            'getCurrentWindSpeed/' . $callPath . '_' . $sensCode,
            $cacheMinutes,
            function () use ($callPath, $headers) {
                $response = $this->clientApi->request('GET', $callPath, [
                    'headers' => $headers,
                ]);

                $content = json_decode($response->getBody()->getContents(), true);

                $dateTime = $content['data'][0]['date'];
                $date = explode(' ', $dateTime)[0];

                $now = new DateTime();
                $today = DateTime::createFromFormat('Y-m-d', $date);

                $diff = $today->diff($now);

                if ($diff->days > 1) {
                    return false;
                }

                $sensorCodes = [
                    '5' => 'avg', // wind speed
                ];

                $sensorValues = $this->getSensorValues($content, $sensorCodes);

                return empty($sensorValues) ? null : $sensorValues[0]['current_value'];
            }
        );
    }

    /**
     * @param bool $updateStationModel Update the station model with the result. Defaults to true.
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return array|mixed
     */
    public function getStationData(bool $updateStationModel = true)
    {
        $headers = $this->getHeaders();
        $callPath = 'station/' . $this->stationModel->name . '';

        try {
            $response = $this->clientApi->request('GET', $callPath, [
                'headers' => $headers,
            ]);
        } catch (RequestException $e) {
            throw new Exception($e->getMessage(), 403);
        }

        $content = json_decode($response->getBody()->getContents(), true);

        $stationTs = $content['dates']['last_communication'];

        $dt = new DateTime($stationTs);

        $stationUTC = $dt->format('Y-m-d H:i:s');

        $content['dates']['last_communication'] = $stationUTC;

        $lastCommunication = $content['dates']['last_communication'];
        $latitude = $content['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];
        $longitude = $content['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];

        if ($updateStationModel) {
            $this->stationModel->update([
                'last_communication' => $lastCommunication,
                'latitude' => round($latitude, UserStation::PRECISION),
                'longitude' => round($longitude, UserStation::PRECISION),
            ]);
        }

        return $content;
    }

    /**
     * Get History Data for last n hours.
     */
    public function historyDataHourlyCommon($numberOfHours, $cacheMinutes, $callback)
    {
        $headers = $this->getHeaders();

        $callPath = 'data/' . $this->stationModel->name . '/hourly/last/' . $numberOfHours . 'h';

        return Cache::remember(
            'historyDataHourlyCommon/' . $callPath,
            $cacheMinutes,
            function () use ($callPath, $headers, $callback) {
                try {
                    $response = $this->clientApi->request('GET', $callPath, [
                        'headers' => $headers,
                    ]);
                } catch (RequestException $e) {
                    $response = 'error';
                    $message = $e->getMessage();
                }

                if ('error' == $response) {
                    return ['error' => $message, 'http_code' => 403];
                }

                $callback();

                $content = json_decode($response->getBody()->getContents(), true);

                $this->sensorCodes['battery'] = '7_last';
                $this->sensorCodes['solarPanel'] = '30_last';

                return $this->commonArrContent($content, 'hourly');
            }
        );
    }

    /**
     * Get Current Data.
     */
    public function getCurrentSensorValues($cacheMinutes, $callback)
    {
        $headers = $this->getHeaders();

        $callPath = 'data/' . $this->stationModel->name . '/raw/last/1';

        return Cache::remember(
            'getCurrentSensorValues/' . $callPath,
            $cacheMinutes,
            function () use ($callPath, $headers, $callback) {
                try {
                    $response = $this->clientApi->request('GET', $callPath, [
                        'headers' => $headers,
                    ]);
                } catch (RequestException $e) {
                    $response = 'error';
                    $message = $e->getMessage();
                }

                if ('error' == $response) {
                    return ['error' => $message, 'http_code' => 403];
                }

                $callback();

                $content = json_decode($response->getBody()->getContents(), true);

                $sensorCodes = [
                    '600' => 'avg',        // radiation
                    '6' => 'sum',
                    '5' => 'avg',        // wind speed
                    '7' => 'last',        // battery
                    '4' => 'time',        // leaf wetness
                    '506' => 'avg',        // air temperature
                    '507' => 'avg',        // humidity
                    '18449' => 'avg',    // pressure //not sure
                    '16' => 'avg',        // soil temperature
                ];

                return $this->getSensorValues($content, $sensorCodes);
            }
        );
    }

    public function getDailySensorValues($cacheMinutes)
    {
        $sensors = [];
        $todayStartTs = (new DateTime())->setTime(0, 0)->getTimestamp();
        $dailyPrecipitation = $this->getDailyDataByStationAndDate((new DateTime()), 'precipitation_sum');
        $dailyPrecipitationData = [
            'name' => 'Precipitation',
            'current_value' => $dailyPrecipitation,
            'unit' => 'l/m²',
        ];

        array_push($sensors, $dailyPrecipitationData);

        return [
            'sensors' => $sensors,
            'start_from_ts' => $todayStartTs,
        ];
    }

    public function getHistoryDataEChartsFormatted(
        $cacheMinutes,
        $stationInfo,
        $from,
        $to,
        $feed,
        $sensors,
        $callback
    ) {
        $stationName = $this->stationModel->name;
        $headers = $this->getHeaders();
        $callPath = 'data/normal/' . $stationName . '/' . $feed . '/from/' . $from . '';

        if ($from && $to) {
            $callPath = 'data/normal/' . $stationName . '/' . $feed . '/from/' . $from . '/to/' . $to . '';
        }

        $chartData = new EChartsMeteoDataFormatter();

        $sensorCodesMappingArray = config('pessl')['sensor_codes_mapping'];
        $mappedSensors = $chartData->mapSensors($sensors, $sensorCodesMappingArray, 'sensor_group');

        $lang = Config::get('app.locale');

        $arrContent = Cache::remember(
            'getHistoryDataEChartsFormatted/' . trim($lang) . $callPath,
            ($cacheMinutes),
            function () use ($callPath, $headers, $callback, $mappedSensors, $stationInfo) {
                try {
                    $response = $this->clientApi->request('GET', $callPath, [
                        'headers' => $headers,
                    ]);
                } catch (RequestException $e) {
                    throw new Exception($message = $e->getMessage(), 403);
                }

                if (204 === $response->getStatusCode()) {
                    throw new Exception($response->getReasonPhrase(), $response->getStatusCode());
                }

                $callback();

                $content = json_decode($response->getBody()->getContents(), true);

                return $this->mapData($mappedSensors, $content, $stationInfo);
            }
        );

        $allowedRecordsCount = config('echarts.stations.allowedRecordsCount');
        if (isset($arrContent['xAxisData']) && count($arrContent['xAxisData']) > $allowedRecordsCount) {
            throw new Exception('You have selected too much data, please increase the reporting time or reduce the preview time.', 416);
        }

        if ((isset($arrContent['xAxisData']) && !count($arrContent['xAxisData'])) || isset($arrContent['error'])) {
            return [];
        }

        $chartData = new EChartsMeteoDataFormatter();

        $arrContent['periods'] = $arrContent['xAxisData'];
        $axisData = $chartData->setAxisIndexes($arrContent);
        $arrContent = $chartData->createTitle($arrContent);
        $arrContent = $chartData->createGrid($arrContent);
        $arrContent = $chartData->createXAxis($arrContent, $axisData);
        $arrContent = $chartData->createYAxis($arrContent, $axisData);
        $arrContent = $chartData->createDataZoom($arrContent);

        return $chartData->setSeriesAxisIndexes($arrContent, $axisData);
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return array|bool|float|mixed
     */
    public function getDailyDataByStationAndDate(DateTime $date, $sensor)
    {
        $value = false;

        $headers = $this->getHeaders();
        $date->setTime(0, 0, 0);
        $from = $date->getTimestamp();
        $to = clone $date;
        $to->setTime(23, 59, 59);
        $to = $to->getTimestamp();
        $callPath = 'data/normal/' . $this->stationModel->name . '/daily/from/' . $from . '/to/' . $to . '';

        try {
            $response = $this->clientApi->request('GET', $callPath, [
                'headers' => $headers,
            ]);
        } catch (RequestException $e) {
            $message = $e->getMessage();

            return ['error' => $message, 'http_code' => 403];
        }

        if (204 === $response->getStatusCode()) {
            return ['error' => $response->getReasonPhrase(), 'http_code' => $response->getStatusCode()];
        }

        $content = json_decode($response->getBody()->getContents(), true);

        $stationSensors = [];
        $cumulativeSensors = [];

        $sensorCodesMappingArray = config('pessl')['sensor_codes_mapping'];
        $sensorName = substr($sensor, 0, strrpos($sensor, '_'));
        if (array_key_exists($sensorName, $sensorCodesMappingArray) && !empty($sensorCodesMappingArray[$sensorName])) {
            $sensorValue = $sensorCodesMappingArray[$sensorName];
            $sensorCodes[$sensor] = $sensorValue['codes'];
        }

        foreach ($sensorCodes as $key => $sensor) {
            $stationSensors[$key] = [];
            if (strpos($key, 'cumulative')) {
                $cumulativeSensors[$key] = 0;
            }
            foreach ($sensor as $each) {
                $stationSensor = array_filter($content['sensors'], function ($elem) use ($each) {
                    return (bool) ($elem['code'] == $each);
                });

                if (!empty($stationSensor)) {
                    $stationSensor = reset($stationSensor);
                    $stationSensors[$key][] = $stationSensor;
                }
            }
        }

        foreach ($content['data'] as $each) {
            foreach ($stationSensors as $key => $stationSensor) {
                foreach ($stationSensor as $item) {
                    $sensorDataKey = $item['ch'] . '_' . $item['mac'] . '_' . $item['serial'] . '_' . $item['code'] . '_' . substr(
                        $key,
                        strrpos($key, '_') + 1
                    );

                    if (array_key_exists($sensorDataKey, $each)) {
                        $value = $each[$sensorDataKey];
                        if (strpos($key, 'cumulative')) {
                            $cumulativeSensors[$key] += $each[$sensorDataKey];
                            $value = $cumulativeSensors[$key];
                        }
                        $value = round($value, 2, PHP_ROUND_HALF_UP);

                        break;
                    }
                }
            }
        }

        return $value;
    }

    public function getTimeZone()
    {
        return self::TIME_ZONE;
    }

    /**
     * Get Common History Data.
     */
    private function historyDataCommon($period, $cacheMinutes, $feed, $callback)
    {
        $headers = $this->getHeaders();

        $callPath = 'data/normal/' . $this->stationModel->name . '/' . $feed . '/from/' . $period . '';

        return Cache::remember(
            'historyDataCommon/' . $callPath,
            $cacheMinutes,
            function () use ($callPath, $headers, $callback, $feed) {
                try {
                    $response = $this->clientApi->request('GET', $callPath, [
                        'headers' => $headers,
                    ]);
                } catch (RequestException $e) {
                    $message = $e->getMessage();

                    return ['error' => $message, 'http_code' => 403];
                }

                if (204 === $response->getStatusCode()) {
                    return ['error' => $response->getReasonPhrase(), 'http_code' => $response->getStatusCode()];
                }

                $callback();

                $content = json_decode($response->getBody()->getContents(), true);

                $arrContent = $this->commonArrContent($content, $feed);

                // Alternative search by sensor name that contains 'temperature'
                if (!count(array_column($arrContent, 'temperature_max'))) {
                    $sensorsNames = array_column($content['sensors'], 'name');

                    $foundKey = null;
                    foreach ($sensorsNames as $key => $value) {
                        if (false !== stripos($value, 'temperature')) {
                            $foundKey = $key;
                        }
                    }

                    if (null !== $foundKey) {
                        $this->sensorCodes['temperature_max'] = $content['sensors'][$foundKey]['code'] . '_max';
                        $this->sensorCodes['temperature_min'] = $content['sensors'][$foundKey]['code'] . '_min';
                    }

                    $arrContent = $this->commonArrContent($content, $feed);
                }

                return $arrContent;
            }
        );
    }

    /**
     * @param string $feed
     *
     * @return array
     */
    private function commonArrContent($content, $feed = 'daily')
    {
        $arrContent = [];
        foreach ($content['data'] as $index => $data) {
            foreach ($data as $key => $value) {
                if ('date' == $key) {
                    $arrContentKey = date('Y-m-d', strtotime($data['date']));
                    $arrContentValue = date('Y-m-d', strtotime($value));
                    if ('daily' != $feed) {
                        $arrContentKey = date('Y-m-d H:i:s', strtotime($data['date']));
                        $arrContentValue = date('Y-m-d H:i:s', strtotime($value));
                    }
                    $arrContent[$arrContentKey]['time'] = $arrContentValue;
                }

                foreach ($this->sensorCodes as $name => $code) {
                    if (false !== strpos($key, $code)) {
                        $arrContentKey = date('Y-m-d', strtotime($data['date']));
                        if ('daily' != $feed) {
                            $arrContentKey = date('Y-m-d H:i:s', strtotime($data['date']));
                        }
                        $arrContent[$arrContentKey][$name] = $value;
                    }
                }
            }
        }

        return $arrContent;
    }

    private function findSensor($arrSearch, $key)
    {
        $foundArr = [];

        $lastPrefix = strrchr($key, '_');
        $keyValue = str_replace($lastPrefix, '', $key);

        $arrSensorsConfig = config('pessl')['sensor_codes_mapping'];
        $arrCode = $arrSensorsConfig[$keyValue]['codes'];
        $code = reset($arrCode);

        $foundArr = array_filter($arrSearch, function ($value) use ($code) {
            return $value['code'] == $code;
        });

        return reset($foundArr);
    }

    private function mapData($sensors, $data, $stationInfo)
    {
        $result = $this->mapStationSensors($sensors, $data);

        $mappedData = $result['mappedData'];
        $stationSensors = $result['stationSensors'];
        $cumulativeSensors = $result['cumulativeSensors'];

        $timeZone = config('app.timezone');
        if (isset($stationInfo['position']['timezoneCode'])) {
            $timeZone = $stationInfo['position']['timezoneCode'];
        }

        foreach ($data['data'] as $each) {
            $date = (new Carbon($each['date'], $timeZone))->getTimestamp() * 1000;
            $mappedData['xAxisData'][] = $date;
            foreach ($stationSensors as $key => $sensor) {
                $pos = strrpos($key, '_');
                $aggr = substr($key, $pos + 1);
                $configKey = substr($key, 0, $pos);
                $conversionCoeff = $this->getConversionCoefficient($configKey);

                // Process nodes
                if (isset($sensor['nodes'])) {
                    $nodes = $sensor['nodes'];

                    foreach ($nodes as $keyNode => $arrNode) {
                        foreach ($arrNode as $keyArrNode => $valueArrNode) {
                            $channel = $valueArrNode['ch'];
                            $sensorDataKey = $channel . '_' . $valueArrNode['mac'] . '_' . $valueArrNode['serial'] . '_' . $valueArrNode['code'] . '_' . $aggr;

                            if (array_key_exists($sensorDataKey, $each)) {
                                $value = $each[$sensorDataKey];
                                $mappedData['seriesData'][$key]['nodes'][$keyNode][$channel][] = $this->formatSensorValue($value, $conversionCoeff);
                            }
                        }
                    }

                    continue;
                }

                if (!isset($sensor['ch'])) {
                    continue;
                }

                // Process normal sensors (not nodes)
                $sensorDataKey = $sensor['ch'] . '_' . $sensor['mac'] . '_' . $sensor['serial'] . '_' . $sensor['code'] . '_' . $aggr;
                if (array_key_exists($sensorDataKey, $each)) {
                    $value = $each[$sensorDataKey];
                    if (strpos($key, 'cumulative')) {
                        $cumulativeSensors[$key] += $each[$sensorDataKey];
                        $value = $cumulativeSensors[$key];
                    }

                    $mappedData['seriesData'][$key][] = $this->formatSensorValue($value, $conversionCoeff);
                }
            }
        }

        $mappedData['stationSensors'] = $stationSensors;

        $chartData = new EChartsMeteoDataFormatter();

        $sensorCodesMappingArray = config('pessl')['sensor_codes_mapping'];

        $arrResultData = $chartData->setCommonData($mappedData, $sensorCodesMappingArray);

        if (isset($arrResultData['stationSensors'])) {
            unset($arrResultData['stationSensors']);
        }

        return $arrResultData;
    }

    /**
     * @return array
     */
    private function mapStationSensors($sensors, $data)
    {
        $mappedData['seriesData'] = [];
        $mappedData['xAxisData'] = [];
        $stationSensors = [];
        $cumulativeSensors = [];

        foreach ($sensors as $key => $sensorGroup) {
            $stationSensors[$key] = [];
            $mappedData['seriesData'][$key] = [];
            if (strpos($key, 'cumulative')) {
                $cumulativeSensors[$key] = 0;
            }

            $stationSensor = array_filter($data['sensors'], function ($elem) use ($sensorGroup) {
                return (bool) ($elem['group'] == $sensorGroup);
            });

            if (empty($stationSensor)) {
                continue;
            }

            // If we have nodes (Mac or serails)
            if (count($stationSensor) > 1) {
                $stationSensor = array_values($stationSensor);

                $arrNode = [];
                foreach ($stationSensor as $keySensor => $itemSensor) {
                    if ('X' != $itemSensor['serial']) {
                        $arrNode[$itemSensor['serial']][] = $itemSensor;
                    }

                    if ('X' != $itemSensor['mac']) {
                        $arrNode[$itemSensor['mac']][] = $itemSensor;
                    }
                }

                // Add as node
                if (!empty($arrNode)) {
                    $stationSensors[$key]['nodes'] = $arrNode;

                    continue;
                }

                // Add as normal (if it is in the same group but is not node)
                $arrFound = $this->findSensor($stationSensor, $key);
                $stationSensors[$key] = $arrFound;

                continue;
            }

            // Add as normal
            $arrToShift = $stationSensor;
            $shifted = array_shift($arrToShift);
            $stationSensors[$key] = $shifted;
        }

        return [
            'mappedData' => $mappedData,
            'stationSensors' => $stationSensors,
            'cumulativeSensors' => $cumulativeSensors,
        ];
    }

    /**
     * @return array
     */
    private function getHeaders()
    {
        $params = [
            'grant_type' => 'client_credentials',
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
        ];

        // Cache minutes for Pessl token
        $cacheMinutes = now()->addMinutes(30);

        $content = Cache::remember('pessl-token', $cacheMinutes, function () use ($params) {
            $response = $this->clientAuth->request('POST', 'token', [
                'form_params' => $params,
            ]);

            return json_decode($response->getBody()->getContents(), true);
        });

        $headers = [
            'Accept' => 'application/json',
        ];

        if ($content['access_token']) {
            $headers['Authorization'] = 'Bearer ' . $content['access_token'];
        }

        return $headers;
    }

    /**
     * @return array
     */
    private function getSensorValues($content, $sensorCodes)
    {
        $sensors = $content['sensors'];

        $data = $content['data'][0];
        $arrContent = [];

        foreach ($sensors as $sensorKey => $sensor) {
            if (!in_array($sensor['code'], array_keys($sensorCodes))) {
                continue;
            }
            $key = $sensor['ch'] . '_' . $sensor['mac'] . '_' . $sensor['serial'] . '_' . $sensor['code'] . '_' . $sensorCodes[$sensor['code']];
            $configKey = $this->getSensorConfigKey($sensor['ch'], $sensor['code']);

            $sensorValue = $data[$key];
            $unit = $sensor['unit'];
            if ($configKey) {
                $sensorValue = $this->formatSensorValue(
                    $sensorValue,
                    $this->getConversionCoefficient($configKey)
                );
                $unit = $this->getConversionUnit($configKey);
            }

            $arrContent[] = [
                'name' => config('pessl')[$sensor['group']]['name'],
                'unit' => $unit,
                'current_value' => $sensorValue,
            ];
        }

        return array_values($arrContent);
    }

    /**
     * Get sensor config key.
     */
    private function getSensorConfigKey($ch, $code)
    {
        $data = array_filter(config('pessl.sensor_codes_mapping'), function ($v, $k) use ($ch, $code) {
            return $v['sensor_group'] == $ch && in_array($code, $v['codes']);
        }, ARRAY_FILTER_USE_BOTH);

        return $key = array_key_first($data);
    }
}
