<?php

namespace App\Classes\Meteo;

use DateTime;

interface IStation
{
    public function getHistoryData($cacheMinutes, $callback);

    public function getHistoryDataFrom($cacheMinutes, $period, $feed, $callback);

    public function getHistoryDataEChartsFormatted($cacheMinutes, $stationInfo, $from, $to, $feed, $sensors, $callback);

    public function getCurrentTemperature($cacheMinutes);

    public function getCurrentWindSpeed($cacheMinutes);

    public function getStationData(bool $updateStationModel = true);

    public function getCurrentSensorValues($cacheMinutes, $callback);

    public function getDailySensorValues($cacheMinutes); // This might be developed further in future.

    public function getDailyDataByStationAndDate(DateTime $date, $sensor);

    public function getStationReport($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false, bool $includeRowChildren = true);

    public function getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false);

    public function getTimeZone();
}
