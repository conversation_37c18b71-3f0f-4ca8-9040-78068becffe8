<?php

namespace App\Classes\Meteo;

use App\Classes\Echarts\Meteo\EChartsMeteoDataFormatter;
use App\Classes\Heap;
use App\Models\UserStation;
use App\Services\Wialon\ReportService;
use App\Services\Wialon\WialonService;
use Cache;
use Config;
use DateTime;
use DateTimeZone;
use DB;
use Exception;
use GuzzleHttp\Exception\RequestException;
use Schema;

/**
 * A class for OnSite2 API.
 */
class OnSite2 extends AbstractBaseStation
{
    public const TIME_ZONE = 'Europe/Sofia';

    /**
     * @var WialonService
     */
    private $wialonService;
    /**
     * @var ReportService
     */
    private $reportService;
    /**
     * @var string
     */
    private $tmpTable;
    /**
     * @var array
     */
    private $stationConfig;
    /**
     * @var null|mixed
     */
    private $sensorAirTemperature;
    /**
     * @var null|mixed
     */
    private $sensorWindSpeed;

    public function __construct($config, UserStation $stationModel, Heap $heap, WialonService $wialonService, ReportService $reportService)
    {
        parent::__construct($stationModel, $heap);

        $this->stationConfig = $config;
        $this->wialonService = $wialonService;
        $this->reportService = $reportService;
        $this->sensorCodesMaping = $config['sensor_codes_mapping'];

        $this->sensorAirTemperature = $this->sensorCodesMaping['air_temperature'] ?? null;
        $this->sensorWindSpeed = $this->sensorCodesMaping['wind_speed'] ?? null;
    }

    /**
     * @param bool $updateStationModel Update the station model with the result. Defaults to true.
     *
     * @throws Exception
     */
    public function getStationData(bool $updateStationModel = true): array
    {
        try {
            $this->wialonService->login($this->stationConfig['wialon_token'], $this->stationConfig['wialon_base_url']);
            $searchStationParams = $this->stationConfig['stations_search_items'];
            $searchStationParams['params']['spec']['propValueMask'] = $this->stationModel->name;

            $response = $this->wialonService->makeRequest($searchStationParams['svc'], json_encode($searchStationParams['params']));

            if (isset($response['error']) || !isset($response['items'])) {
                throw new Exception('error: ' . $response['error'] . ', svc: ' . $searchStationParams['svc'] . ' , params: ' . $searchStationParams['params'] . ', url: ' . $this->stationConfig['wialon_base_url']);
            }
        } catch (Exception $e) {
            throw $e;
        }

        $stationData = array_first($response['items']);
        $wialonUnitLastMessage = $stationData['lmsg'];
        $dt = new DateTime('now', new DateTimeZone('Europe/Sofia'));
        $dt->setTimestamp($wialonUnitLastMessage['t']);
        $lastCommunication = $dt->format('Y-m-d H:i:s');
        $latitude = isset($wialonUnitLastMessage['pos']['y']) && strlen($wialonUnitLastMessage['pos']['y']) > 0 ? $wialonUnitLastMessage['pos']['y'] : null;
        $longitude = isset($wialonUnitLastMessage['pos']['x']) && strlen($wialonUnitLastMessage['pos']['x']) > 0 ? $wialonUnitLastMessage['pos']['x'] : null;

        if ($updateStationModel) {
            $this->stationModel->update([
                'last_communication' => $lastCommunication,
                'latitude' => round($latitude, UserStation::PRECISION),
                'longitude' => round($longitude, UserStation::PRECISION),
            ]);
        }

        return [
            'params' => ['wialon_id' => $stationData['id']],
            'dates' => [
                'last_communication' => $lastCommunication,
            ],
            'position' => [
                'geo' => [
                    'coordinates' => [
                        0 => $longitude,
                        1 => $latitude,
                    ],
                ],
            ],
        ];
    }

    public function getCurrentSensorValues($cacheMinutes, $callback)
    {
        $url = $this->stationConfig['wialon_base_url'];
        $token = $this->stationConfig['wialon_token'];
        $params = $this->stationConfig['report_current'];
        $stationParams = json_decode($this->stationModel->params, true);
        $params['exec_params']['reportObjectId'] = $stationParams['wialon_id'];
        $sensorsOrderMap = config('onsite2')['sensors_order_map'];
        $callPath = "data/{$this->stationModel->name}/onSite2/currentSensorValues";

        $cachedResult = Cache::remember(
            "getCurrentSensorValues/{$callPath}",
            $cacheMinutes,
            function () use ($url, $params, $token, $sensorsOrderMap) {
                try {
                    $tmpTableName = uniqid('tmp_report_');
                    $this->reportService->integrationReport($url, $params, null, $token, $tmpTableName);

                    if (!Schema::hasTable($tmpTableName)) {
                        return [
                            'sensorsData' => [],
                            'tmpTable' => $tmpTableName,
                        ];
                    }

                    $sensorsData = DB::table($tmpTableName)
                        ->selectRaw("
                        jsonb_agg(jsonb_build_object(
                            'current_value', value,
                            'name', sensor,
                            'unit', regexp_replace(formatted_value , '.*?\s', '')
                        )) as sensors
                    ")
                        ->whereRaw("\"time\" = (select max(time) from {$tmpTableName})")
                        ->whereIn('sensor', $sensorsOrderMap)
                        ->get()->pluck('sensors')->first();

                    // Call raw history report to take sum precipitation for last 15 min.
                    $today = date('Y-m-d');
                    $timeZone = $this->getTimeZone();
                    $dateRange = $this->dateRange('custom', $this->stationModel->install_date, $today, $today, $timeZone, false);
                    $from = $dateRange['timestampFrom'];
                    $to = $dateRange['timestampTo'];
                    $historyReportTmpTable = $this->getHistoryDataByInterval($from, $to, 'raw');
                    $sumPrecipitationForLast15Min = DB::table($historyReportTmpTable)
                        ->selectRaw("
                            jsonb_build_object(
                                'name', 'Precipitation',
                                'unit', 'l/m²',
                                'current_value', sum_precipitation
                            ) as sum_precipitation
                        ")
                        ->orderByRaw("to_timestamp(grouping, '(DD.MM.YYYY) hh24:mi') desc")
                        ->limit(1)
                        ->get()->pluck('sum_precipitation')->first();

                    Schema::dropIfExists($historyReportTmpTable);

                    return [
                        'sensorsData' => array_merge(json_decode($sensorsData), [json_decode($sumPrecipitationForLast15Min)]),
                        'tmpTable' => $tmpTableName,
                    ];
                } catch (RequestException $e) {
                    throw new Exception($e->getMessage(), 403);
                }
            }
        );

        $this->tmpTable = $cachedResult['tmpTable'];
        $sensorsData = array_combine(array_column($cachedResult['sensorsData'], 'name'), $cachedResult['sensorsData']);

        $orderedSensorsData = [];
        foreach ($sensorsOrderMap as $sensorName) {
            if (!isset($sensorsData[$sensorName])) {
                continue;
            }

            $orderedSensorsData[] = $sensorsData[$sensorName];
        }

        return $orderedSensorsData;
    }

    public function getDailySensorValues($cacheMinutes)
    {
        $todayStartTs = (new DateTime())->setTime(0, 0)->getTimestamp();
        $callPath = "data/{$this->stationModel->name}/onSite2/dailySensorValues";
        $tmpTable = $this->tmpTable;

        if (!Schema::hasTable($tmpTable)) {
            return [
                'sensors' => [],
                'start_from_ts' => $todayStartTs,
            ];
        }

        $cachedResult = Cache::remember(
            "getDailySensorValues/{$callPath}",
            $cacheMinutes,
            function () use ($tmpTable) {
                $sensorsData = DB::table($tmpTable)
                    ->selectRaw("
                        jsonb_agg(jsonb_build_object(
                            'current_value', value,
                            'name', sensor,
                            'unit', regexp_replace(formatted_value , '.*?\s', '')
                        )) as sensors
                    ")
                    ->whereRaw("\"time\" = (select max(time) from {$tmpTable})")
                    ->whereIn('sensor', ['Total Precipitation'])
                    ->get()->pluck('sensors')->first();

                Schema::dropIfExists($tmpTable);

                return [
                    'sensorsData' => json_decode($sensorsData),
                    'tmpTable' => $tmpTable,
                ];
            }
        );

        $this->tmpTable = $cachedResult['tmpTable'];

        return [
            'sensors' => $cachedResult['sensorsData'],
            'start_from_ts' => $todayStartTs,
        ];
    }

    public function getHistoryDataEChartsFormatted($cacheMinutes, $stationInfo, $from, $to, $feed, $sensors, $callback)
    {
        $sensorCodesMappingArray = config('onsite2')['sensor_codes_mapping'];

        $lang = Config::get('app.locale');
        $callPath = "data/{$this->stationModel->name}/onSite2/{$feed}/from/{$from}/to/{$to}";

        return Cache::remember(
            "getHistoryDataEChartsFormatted/{$lang}{$callPath}",
            ($cacheMinutes),
            function () use ($sensors, $sensorCodesMappingArray, $feed, $from, $to) {
                $tmpTable = $this->getHistoryDataByInterval($from, $to, $feed);

                // Since there is no way to get a date from a farm track, we use the grouping column to be able to format the string to a timestamp.
                // There is a difference in the string format depending on whether the period is for raw, hourly or daily. So we use from config structure to parse data.
                $dateFormat = $this->stationConfig['date_format'][$feed];
                $sensors = $this->mapSensors($sensors, $sensorCodesMappingArray);
                $seriesData = '';
                foreach ($sensors as $sensorName => $dbColumnName) {
                    if (array_key_last($sensors) === $sensorName) {
                        $seriesData .= "'{$sensorName}', jsonb_agg({$dbColumnName} order by to_timestamp(grouping, '{$dateFormat}'))";

                        continue;
                    }
                    $seriesData .= "'{$sensorName}', jsonb_agg({$dbColumnName} order by to_timestamp(grouping, '{$dateFormat}')),";
                }

                $sensorsNameForQuery = implode(',', array_diff(array_values($sensors), ['sum_precipitation_cumulative']));
                $sensorsWithPrecipitationWithAccumulation = DB::table($tmpTable)
                    ->selectRaw("
                        grouping,
                        {$sensorsNameForQuery},
                        SUM(sum_precipitation::numeric) over (
                        order by to_timestamp(grouping, '{$dateFormat}')) as sum_precipitation_cumulative
                    ");

                $sensorsData = DB::table('sensors_with_precipitation_with_accumulation_data')
                    ->withExpression('sensors_with_precipitation_with_accumulation_data', $sensorsWithPrecipitationWithAccumulation)
                    ->selectRaw("
                        jsonb_agg((extract(epoch from to_timestamp(grouping, '{$dateFormat}'))*1000)::BIGINT order by to_timestamp(grouping, '{$dateFormat}'))::jsonb as \"xAxisData\",
                        jsonb_build_object(
                                {$seriesData}
                        ) as \"seriesData\"
                    ")->first();
                Schema::dropIfExists($tmpTable);

                $sensorsData = array_map(function ($row) {
                    return json_decode($row, true);
                }, json_decode(json_encode($sensorsData), true));

                $orderedSensorsData = [];
                foreach ($sensors as $sensorName => $sensorData) {
                    if (!isset($sensorsData['seriesData'][$sensorName])) {
                        continue;
                    }

                    $orderedSensorsData[$sensorName] = $sensorsData['seriesData'][$sensorName];
                }
                $sensorsData['seriesData'] = $orderedSensorsData;

                $chartData = new EChartsMeteoDataFormatter();
                $arrContent = $chartData->setCommonData($sensorsData, $sensorCodesMappingArray);
                $allowedRecordsCount = config('echarts.stations.allowedRecordsCount');

                if (isset($arrContent['xAxisData']) && count($arrContent['xAxisData']) > $allowedRecordsCount) {
                    throw new Exception('You have selected too much data, please increase the reporting time or reduce the preview time.', 416);
                }

                if (isset($arrContent['xAxisData']) && !count($arrContent['xAxisData'])) {
                    return [];
                }

                $arrContent['periods'] = $arrContent['xAxisData'];
                $axisData = $chartData->setAxisIndexes($arrContent);
                $arrContent = $chartData->createTitle($arrContent);
                $arrContent = $chartData->createGrid($arrContent);
                $arrContent = $chartData->createXAxis($arrContent, $axisData);
                $arrContent = $chartData->createYAxis($arrContent, $axisData);
                $arrContent = $chartData->createDataZoom($arrContent);

                return $chartData->setSeriesAxisIndexes($arrContent, $axisData);
            }
        );
    }

    /**
     * @deprecated
     */
    public function getHistoryData($cacheMinutes, $callback)
    {
        throw new Exception('Method is not implemented!', 501);
    }

    /**
     * @deprecated
     */
    public function getHistoryDataFrom($cacheMinutes, $period, $feed, $callback)
    {
        $stationParams = json_decode($this->stationModel->params, true);
        $url = $this->stationConfig['wialon_base_url'];
        $token = $this->stationConfig['wialon_token'];
        $params = $this->stationConfig["report_{$feed}"];
        $params['exec_params']['reportObjectId'] = $stationParams['wialon_id'];
        $params['exec_params']['interval']['from'] = $period;

        $lang = Config::get('app.locale');
        $callPath = "data/{$this->stationModel->name}/onSite2/from/{$period}";

        return Cache::remember(
            "getHistoryDataFrom/{$lang}{$callPath}",
            ($cacheMinutes),
            function () use ($url, $params, $token) {
                $tmpTableName = uniqid('tmp_report_');
                $this->reportService->integrationReport($url, $params, null, $token, $tmpTableName);

                if (!Schema::hasTable($tmpTableName)) {
                    return ['data' => []];
                }

                $sensorsData = DB::table($tmpTableName)
                    ->selectRaw("
                        json_object_agg(TO_DATE(grouping,'DD/MM/YYYY'), 
                        jsonb_build_object(
                            'time', TO_DATE(grouping,'DD/MM/YYYY'), 
                            'precipitation', sum_precipitation::decimal, 
                            'temperature_max', max_air_temperature::decimal, 
                            'temperature_min', min_air_temperature::decimal
                        )) as data
                    ")->pluck('data')->first();
                Schema::dropIfExists($tmpTableName);

                return json_decode($sensorsData, true);
            }
        );
    }

    public function getCurrentTemperature($cacheMinutes)
    {
        if (!isset($this->sensorAirTemperature['name'])) {
            return;
        }

        $currentSensorValues = $this->getCurrentSensorValues($cacheMinutes, null);
        Schema::dropIfExists($this->tmpTable);
        $airTemperature = $this->getSensorCurrentValueByName($currentSensorValues, $this->sensorAirTemperature['name']);

        return (float)$airTemperature;
    }

    public function getCurrentWindSpeed($cacheMinutes)
    {
        if (!isset($this->sensorWindSpeed['name'])) {
            return;
        }

        $currentSensorValues = $this->getCurrentSensorValues($cacheMinutes, null);
        Schema::dropIfExists($this->tmpTable);
        $windSpeed = $this->getSensorCurrentValueByName($currentSensorValues, $this->sensorWindSpeed['name']);

        return (float)$windSpeed;
    }

    public function getDailyDataByStationAndDate(DateTime $date, $sensor)
    {
        throw new Exception('Method is not implemented!', 501);
    }

    public function getTimeZone()
    {
        return self::TIME_ZONE;
    }

    protected function getHistoryDataByInterval($from, $to, $feed)
    {
        $stationParams = json_decode($this->stationModel->params, true);
        $url = $this->stationConfig['wialon_base_url'];
        $token = $this->stationConfig['wialon_token'];
        $params = $this->stationConfig["report_{$feed}"];
        $params['exec_params']['reportObjectId'] = $stationParams['wialon_id'];
        $params['exec_params']['interval']['from'] = $from;
        $params['exec_params']['interval']['to'] = $to;
        $tmpTableName = uniqid('tmp_report_');

        $this->reportService->integrationReport($url, $params, null, $token, $tmpTableName);

        return $tmpTableName;
    }

    private function mapSensors($sensors, $sensorCodesMappingArray): array
    {
        $mappedSensor = [];

        if (in_array('all', $sensors)) {
            foreach ($sensorCodesMappingArray as $key => $value) {
                if ('wind_speed_max' === $key) {
                    // remove max after wind_speed
                    $key = substr($key, 0, -4);
                    $mappedSensor["{$key}_{$value['default']}_{$value['default']}"] = "{$value['default']}_{$key}";

                    continue;
                }
                $mappedSensor["{$key}_{$value['default']}"] = "{$value['default']}_{$key}";
            }

            return $mappedSensor;
        }

        foreach ($sensors as $sensor) {
            $sensorName = substr($sensor, 0, strrpos($sensor, '_'));
            $dataType = substr($sensor, -3);
            $dataType = ('min' === $dataType || 'max' === $dataType) ? $dataType : '';
            if (array_key_exists($sensorName, $sensorCodesMappingArray) && !empty($sensorCodesMappingArray[$sensorName])) {
                $sensorValue = $sensorCodesMappingArray[$sensorName];
                $value = strlen($dataType) > 0 ? "{$dataType}_{$sensorName}" : "{$sensorValue['default']}_{$sensorName}";
                $mappedSensor[$sensor] = $value;
            }
        }

        return $mappedSensor;
    }

    private function getSensorCurrentValueByName(array $currentSensorValues, string $sensorName)
    {
        $sensorIndex = array_search($sensorName, array_column($currentSensorValues, 'name'));

        if (!$sensorIndex) {
            return;
        }

        $sensorData = $currentSensorValues[$sensorIndex] ?? null;

        return $sensorData->current_value ?? null;
    }
}
