<?php

namespace App\Classes\Meteo;

use App\Classes\Heap;
use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Models\UserStation;
use Cache;
use Carbon\Carbon;
use DateTime;
use Exception;
use Request;
use Validator;

/**
 * A class for Virtual Stations.
 */
class Virtual extends AbstractBaseStation
{
    public const TIME_ZONE = 'UTC';

    private $meteoCache;

    /**
     * Virtual constructor.
     */
    public function __construct($config, MeteoCache $meteoCache, UserStation $stationModel, Heap $heap)
    {
        parent::__construct($stationModel, $heap);

        $this->meteoCache = $meteoCache;
    }

    /**
     * @return array|string
     */
    public function getHistoryData($cacheMinutes, $callback)
    {
        $historyPeriod = 1; // 1 year history period from today
        $prevYear = mktime(0, 0, 0, date('m'), date('d'), date('Y') - $historyPeriod);

        $arrQuery = [
            'lat' => $this->stationModel->latitude,
            'lon' => $this->stationModel->longitude,
            'startdate' => date('Y-m-d', $prevYear),
            'enddate' => date('Y-m-d'),
        ];

        $feedType = 'historybasic-day';

        // Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache(
            $arrQuery,
            $feedType,
            $cacheMinutes,
            function ($content) {
                return $this->mapDataHistory($content, 'history_day');
            }
        );

        $lastCommunication = Carbon::now();
        $this->stationModel->last_communication = $lastCommunication;
        $this->stationModel->save();

        // this is out of cache
        return Helper::filterMeteoDataHistory($arrContent, $prevYear);
    }

    /**
     * @return array
     */
    public function getHistoryDataFrom($cacheMinutes, $period, $feed, $callback)
    {
        return [];
    }

    /**
     * @return float|int
     */
    public function getCurrentTemperature($cacheMinutes)
    {
        $arrQuery = [
            'lat' => $this->stationModel->latitude,
            'lon' => $this->stationModel->longitude,
        ];

        $feedType = 'current';

        $cacheMinutes = now()->addMinutes(config('meteo.METEO_CACHE_MINUTES_CURRENT'));

        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedType, $cacheMinutes, function ($content) {
            return $content;
        });

        $carbonTime = new Carbon($arrContent['data_current']['time'], config('app.timezone'));
        $this->stationModel->last_communication = $carbonTime->toDateTimeString();
        $this->stationModel->save();

        $temp = round($arrContent['data_current']['temperature']);

        if (-0 == $temp) {
            $temp = abs($temp);
        }

        return $temp;
    }

    /**
     * Returns the current wind speed of the station.
     *
     * @return bool|float
     */
    public function getCurrentWindSpeed($cacheMinutes)
    {
        $arrQuery = [
            'lat' => $this->stationModel->latitude,
            'lon' => $this->stationModel->longitude,
        ];

        $feedType = 'current';

        $cacheMinutes = now()->addMinutes(config('meteo.METEO_CACHE_MINUTES_CURRENT'));

        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedType, $cacheMinutes, function ($content) {
            return $content;
        });

        $carbonTime = new Carbon($arrContent['data_current']['time'], config('app.timezone'));
        $this->stationModel->last_communication = $carbonTime->toDateTimeString();
        $this->stationModel->save();

        $temp = round($arrContent['data_current']['windspeed']);

        if (-0 == $temp) {
            $temp = abs($temp);
        }

        return $temp;
    }

    /**
     * @param bool $updateStationModel Update the station model with the result. Defaults to true.
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getStationData(bool $updateStationModel = true)
    {
        $organizationId = Request::get('organization_id');
        $station = UserStation::where('name', $this->stationModel->name);
        if ($organizationId) {
            $station = $station->where('organization_id', $organizationId);
        }
        $station = $station->first();

        if (null != $station) {
            return $this->mapStationData($station);
        }

        $validator = Validator::make(Request::all(), [
            'lon' => 'required|numeric',
            'lat' => 'required|numeric',
        ]);

        $lon = Request::get('lon');
        $lat = Request::get('lat');

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $result = [
            'dates' => [
                'last_communication' => Carbon::now(config('app.timezone')),
            ],
            'position' => [
                'geo' => [
                    'coordinates' => [
                        0 => $lon,
                        1 => $lat,
                    ],
                ],
            ],
        ];

        $lastCommunication = $result['dates']['last_communication'];
        $latitude = $result['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];
        $longitude = $result['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];

        if ($updateStationModel) {
            $this->stationModel->update([
                'last_communication' => $lastCommunication,
                'latitude' => round($latitude, UserStation::PRECISION),
                'longitude' => round($longitude, UserStation::PRECISION),
            ]);
        }

        return $result;
    }

    /**
     * @param bool $isTimestamp
     *
     * @throws Exception
     *
     * @return array|void
     */
    public function getStationReport($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false, bool $includeRowChildren = true)
    {
        throw new Exception('Method is not implemented!', 501);
    }

    /**
     * @return array
     */
    public function historyDataHourlyCommon($numberOfHours, $cacheMinutes, $callback)
    {
        $today = Carbon::today()->toDateString();

        return [
            $today => [
                'time' => $today,
                'battery' => 6,
                'solarPanel' => 6,
            ],
        ];
    }

    /**
     * @return array
     */
    public function getCurrentSensorValues($cacheMinutes, $callback)
    {
        $sensors = [];
        $sensors[] = [
            'current_value' => '6',
            'name' => 'Battery',
            'unit' => 'V',
        ];

        return $sensors;
    }

    /**
     * @return array
     */
    public function getDailySensorValues($cacheMinutes)
    {
        return [
            'sensors' => [],
            'start_from_ts' => null,
        ];
    }

    /**
     * @throws Exception
     */
    public function getHistoryDataEChartsFormatted($cacheMinutes, $stationInfo, $from, $to, $feed, $sensors, $callback)
    {
        return [];
    }

    public function getDailyDataByStationAndDate(DateTime $date, $sensor)
    {
        throw new NotImplementedException();
    }

    public function getTimeZone()
    {
        return self::TIME_ZONE;
    }

    /**
     * @return array
     */
    private function mapStationData($data)
    {
        $arrData = [];

        if (!$data || !$data->last_communication || !$data->latitude || !$data->longitude) {
            return $arrData;
        }

        return [
            'dates' => [
                'last_communication' => $data->last_communication,
            ],
            'position' => [
                'geo' => [
                    'coordinates' => [
                        0 => $data->longitude,
                        1 => $data->latitude,
                    ],
                ],
            ],
        ];
    }

    /**
     * @return array|string
     */
    private function mapDataHistory($content, $type)
    {
        if (!is_array($content[$type]['time']) && !strlen($content[$type]['time'])) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        $arrContent = [];
        if (is_array($content[$type]['time'])) {
            foreach ($content[$type]['time'] as $index => $date) {
                foreach ($content[$type] as $key => $value) {
                    $arrContent[$date][$key] = $value[$index];
                }
            }
        } else {
            $index = 0;
            $date = $content[$type]['time'];

            $arrContent[$date]['time'] = $date;

            foreach ($content[$type] as $key => $value) {
                if ('time' != $key) {
                    $arrContent[$date][$key] = $value[$index];
                }
            }
        }

        return $arrContent;
    }
}
