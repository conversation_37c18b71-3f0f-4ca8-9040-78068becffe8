<?php

namespace App\Classes\Meteo;

use App\Classes\Echarts\Meteo\EChartsMeteoDataFormatter;
use App\Classes\Heap;
use App\Models\StationsGS\SensorAvgData;
use App\Models\StationsGS\SensorData;
use App\Models\StationsGS\SensorLastData;
use App\Models\StationsGS\Station;
use App\Models\UserStation;
use DateTime;
use Exception;

/**
 * A class for OnSite API.
 */
class OnSite extends AbstractBaseStation
{
    public const TIME_ZONE = 'UTC';

    private $radius;

    /**
     * OnSite constructor.
     */
    public function __construct($config, UserStation $stationModel, Heap $heap)
    {
        parent::__construct($stationModel, $heap);

        $this->radius = $config['radius'];
        $this->sensorPrecipitation = $config['sensor_precipitation'];
        $this->sensorAirTemperature = $config['sensor_air_temperature'];
        $this->sensorAirTemperature2 = $config['sensor_air_temperature_2'];
        $this->sensorSoilTemperature = $config['sensor_soil_temperature'];
        $this->sensorSoilTemperature2 = $config['sensor_soil_temperature_2'];
        $this->sensorAirHumidity = $config['sensor_air_humidity'];
        $this->sensorAirPressure = $config['sensor_air_pressure'];
        $this->sensorSolarRadiation = $config['sensor_solar_radiation'];
        $this->sensorWindSpeed = $config['sensor_wind_speed'];
        $this->sensorWindSpeedMax = $config['sensor_wind_speed_max'];
        $this->sensorLeafMoisture = $config['sensor_leaf_moisture'];
        $this->sensorBatteryVoltage = $config['sensor_battery_voltage'];
        $this->sensorSolarPanelVoltage = $config['sensor_panel_voltage'];
        $this->sensorFieldCapacity = $config['sensor_field_capacity'];
        $this->sensorFieldCapacity2 = $config['sensor_field_capacity_2'];
        $this->sensorCodesMaping = $config['sensor_codes_mapping'];
    }

    /**
     * Get History Data 1 year history period from today.
     *
     * @throws Exception
     *
     * @return array
     */
    public function getHistoryData($cacheMinutes, $callback)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        if (!$station) {
            return ['error' => 'OnSite station not found.', 'http_code' => 403];
        }

        $now = new DateTime();
        $yearAgo = clone $now;
        $yearAgo->modify('-1 year');
        $stationInstallDate = DateTime::createFromFormat('Y-m-d', $this->stationModel->install_date);

        $period = $yearAgo;
        if ($stationInstallDate > $yearAgo) {
            $period = $stationInstallDate;
        }

        $historyPeriod = mktime(0, 0, 0, $period->format('m'), $period->format('d'), $period->format('Y'));

        $resultPrecipitation = SensorAvgData::getPrecipitationData(
            $station->id,
            $this->sensorPrecipitation,
            $historyPeriod
        )->get()->toArray();

        $resultTemperature = SensorAvgData::getSensorData(
            $station->id,
            $this->sensorAirTemperature,
            $historyPeriod
        )->get()->toArray();

        return $this->commonArrContent($resultPrecipitation, $resultTemperature);
    }

    /**
     * Get History Data From Perion.
     *
     * @return array
     */
    public function getHistoryDataFrom($cacheMinutes, $period, $feed, $callback)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        if (!$station) {
            return ['error' => 'OnSite station not found.', 'http_code' => 404];
        }

        $class = SensorAvgData::class;
        if ('daily' != $feed) {
            $class = SensorData::class;
        }

        $resultPrecipitation = $class::getPrecipitationData(
            $station->id,
            $this->sensorPrecipitation,
            $period,
            $feed
        )->get()->toArray();
        $resultTemperature = $class::getSensorData(
            $station->id,
            $this->sensorAirTemperature,
            $period,
            $feed
        )->get()->toArray();

        return $this->commonArrContent($resultPrecipitation, $resultTemperature);
    }

    /**
     * @return bool|float
     */
    public function getCurrentTemperature($cacheMinutes)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        if (!$station) {
            // temperature 0
            return false;
        }

        $objTemperature = SensorLastData::getSensorData($station->id, $this->sensorAirTemperature)->first();

        if (!$objTemperature) {
            // temperature 0
            return false;
        }

        return floatval($objTemperature->value * $objTemperature->multiplier);
    }

    /**
     * Returns the current wind speed of the station.
     *
     * @return bool|float
     */
    public function getCurrentWindSpeed($cacheMinutes)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        if (!$station) {
            // windspeed 0
            return false;
        }

        $objWindSpeed = SensorLastData::getSensorData($station->id, $this->sensorWindSpeed)->first();

        if (!$objWindSpeed) {
            // windspeed 0
            return false;
        }

        return floatval($objWindSpeed->value * $objWindSpeed->multiplier);
    }

    /**
     * Get Current Data.
     *
     * @return array|bool
     */
    public function getCurrentSensorValues($cacheMinutes, $callback)
    {
        $station = Station::stationId($this->stationModel->name)->first();
        if (!$station) {
            return false;
        }

        $sensors = [
            $this->sensorPrecipitation,
            $this->sensorAirTemperature,
            $this->sensorAirTemperature2,
            $this->sensorSoilTemperature,
            $this->sensorSoilTemperature2,
            $this->sensorAirHumidity,
            $this->sensorAirPressure,
            $this->sensorSolarRadiation,
            $this->sensorWindSpeed,
            $this->sensorWindSpeedMax,
            $this->sensorLeafMoisture,
            $this->sensorBatteryVoltage,
            $this->sensorFieldCapacity,
            $this->sensorFieldCapacity2,
        ];
        $lastData = SensorLastData::getAllSensorsData($station->id, $sensors)->get()->toArray();
        if (!$lastData) {
            // no last data
            return false;
        }

        $lastPrecipitation = SensorLastData::getPrecipitationData($station->id, $this->sensorPrecipitation)->first();

        $callback();

        // add names and units to the values
        $mapped = array_map(function ($element) use ($lastPrecipitation) {
            $element['code'] = $element['name'];
            $element['name'] = config('onsite')[$element['code']]['name'];

            if ($element['sens_id'] == $this->sensorPrecipitation) {
                $element['current_value'] = $lastPrecipitation->precipitation;
            }

            $configKey = $this->getSensorConfigKey($element['sens_id']);
            $coeff = $this->getConversionCoefficient($configKey);
            $conversionUnit = $this->getConversionUnit($configKey);
            $element['unit'] = null !== $conversionUnit ? $conversionUnit : config('onsite')[$element['code']]['unit'];

            $element['current_value'] = $this->formatSensorValue(
                $element['current_value'],
                $coeff,
                config('onsite')[$element['code']]['decimal_points']
            );

            unset($element['owner'], $element['recieved'], $element['sens_id'], $element['stat_id'], $element['ts'], $element['value'], $element['code'], $element['multiplier']);

            return $element;
        }, $lastData);

        usort($mapped, function ($sensor1, $sensor2) {
            return $sensor1['name'] <=> $sensor2['name'];
        });

        return array_values($mapped);
    }

    /**
     * @throws Exception
     *
     * @return array|bool
     */
    public function getDailySensorValues($cacheMinutes)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        if (!$station) {
            return false;
        }

        $sensors = [];
        $todayStartTs = (new DateTime())->setTime(0, 0)->getTimestamp();

        $precipitation = SensorLastData::getDailyPrecipitationData(
            $station->id,
            $this->sensorPrecipitation,
            $todayStartTs
        )->first();

        if ($precipitation) {
            $precipitationData = [
                'name' => 'Precipitation',
                'current_value' => $precipitation->precipitation,
                'unit' => config('onsite')['R']['unit'],
            ];

            array_push($sensors, $precipitationData);
        }

        return [
            'sensors' => $sensors,
            'start_from_ts' => $todayStartTs,
        ];
    }

    /**
     * @param bool $updateStationModel Update the station model with the result. Defaults to true.
     *
     * @throws Exception
     */
    public function getStationData(bool $updateStationModel = true): array
    {
        try {
            $result = Station::getInformation($this->stationModel->name)->first();
            $result = $this->mapStationData($result);
        } catch (Exception $e) {
            throw $e;
        }

        $lastCommunication = $result['dates']['last_communication'];
        $latitude = $result['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];
        $longitude = $result['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];

        if ($updateStationModel) {
            $this->stationModel->update([
                'last_communication' => $lastCommunication,
                'latitude' => round($latitude, UserStation::PRECISION),
                'longitude' => round($longitude, UserStation::PRECISION),
            ]);
        }

        return $result;
    }

    /**
     * Get History Data for last n hours
     * This is pretty much a dummy method (imaginary station don't have real batteries).
     *
     * @param $stationName
     *
     * @return array
     */
    public function historyDataHourlyCommon($numberOfHours, $cacheMinutes, $callback)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        if (!$station) {
            return ['error' => 'OnSite station not found.', 'http_code' => 404];
        }

        $objBatteryVoltage = SensorLastData::getSensorData($station->id, $this->sensorBatteryVoltage)->first();
        $objSolarPanelVoltage = SensorLastData::getSensorData($station->id, $this->sensorSolarPanelVoltage)->first();

        $batteryVoltage = 0;
        $solarPanelVoltage = 0;
        $mvToVCoefficient = 0.001;

        if ($objBatteryVoltage) {
            // batteryVoltage != 0
            $batteryVoltage = floatval($objBatteryVoltage->value * $objBatteryVoltage->multiplier * $mvToVCoefficient);
        }

        if ($objSolarPanelVoltage) {
            // solarPanelVoltage != 0
            $solarPanelVoltage = floatval($objSolarPanelVoltage->value * $objSolarPanelVoltage->multiplier * $mvToVCoefficient);
        }

        return [
            $this->stationModel->last_communication => [
                'time' => $this->stationModel->last_communication,
                'battery' => $batteryVoltage,
                'solarPanel' => $solarPanelVoltage,
            ],
        ];
    }

    /**
     * @throws Exception
     *
     * @return ECharts|mixed
     */
    public function getHistoryDataEChartsFormatted($cacheMinutes, $stationInfo, $from, $to, $feed, $sensors, $callback)
    {
        $station = Station::stationId($this->stationModel->name)->first();
        if (!$station) {
            throw new Exception('OnSite station not found.', 404);
        }

        $chartData = new EChartsMeteoDataFormatter();

        $sensorCodesMappingArray = config('onsite')['sensor_codes_mapping'];
        $mappedSensors = $chartData->mapSensors($sensors, $sensorCodesMappingArray);

        /** @var SensorAvgData|SensorData $class
         */
        $class = SensorData::class;

        $sensorsString = '';
        $sensorsList = [];
        foreach ($mappedSensors as $mappedSensor) {
            $sensorsList[] = implode(',', $mappedSensor);
        }

        $sensorsString .= implode(',', $sensorsList);

        $data = $class::getSensorsData($station->id, $sensorsString, $from, $to, $feed)->get()->toArray();
        $arrContent = $this->mapData($mappedSensors, $data);

        $allowedRecordsCount = config('echarts.stations.allowedRecordsCount');
        if (isset($arrContent['xAxisData']) && count($arrContent['xAxisData']) > $allowedRecordsCount) {
            throw new Exception('You have selected too much data, please increase the reporting time or reduce the preview time.', 416);
        }

        if (isset($arrContent['xAxisData']) && !count($arrContent['xAxisData'])) {
            return [];
        }

        $arrContent['periods'] = $arrContent['xAxisData'];
        $axisData = $chartData->setAxisIndexes($arrContent);
        $arrContent = $chartData->createTitle($arrContent);
        $arrContent = $chartData->createGrid($arrContent);
        $arrContent = $chartData->createXAxis($arrContent, $axisData);
        $arrContent = $chartData->createYAxis($arrContent, $axisData);
        $arrContent = $chartData->createDataZoom($arrContent);

        return $chartData->setSeriesAxisIndexes($arrContent, $axisData);
    }

    /**
     * @return avg_value|int|max_value|min_value|mixed|string|sum_value
     */
    public function getDailyDataByStationAndDate(DateTime $date, $sensor)
    {
        $station = Station::stationId($this->stationModel->name)->first();

        $sensorCodesMappingArray = config('onsite')['sensor_codes_mapping'];
        $sensorName = substr($sensor, 0, strrpos($sensor, '_'));
        if (array_key_exists($sensorName, $sensorCodesMappingArray) && !empty($sensorCodesMappingArray[$sensorName])) {
            $sensorValue = $sensorCodesMappingArray[$sensorName];
            $sensorCodes[$sensor] = $sensorValue['codes'];
        }

        /** @var SensorAvgData|SensorData $class
         */
        $class = SensorData::class;

        $sensorsString = '';
        $sensorsList = [];
        foreach ($sensorCodes as $mappedSensor) {
            $sensorsList[] = implode(',', $mappedSensor);
        }

        $sensorsString .= implode(',', $sensorsList);

        $date->setDate(2018, 8, 30);

        $from = clone $date;
        $from->setTime(0, 0, 0);
        $to = clone $date;
        $to->setTime(23, 59, 59);

        $data = $class::getSensorsData(
            $station->id,
            $sensorsString,
            $from->getTimestamp(),
            $to->getTimestamp(),
            'daily'
        )->get()->toArray();

        $cumulativeSensors = [];

        foreach ($sensorCodes as $key => $sensor) {
            // If there is NO data for the sensor - REMOVE it from the sensors
            $foundSensorData = [];
            foreach ($sensor as $sensorCode) {
                $foundSensor = array_filter($data, function ($elem) use ($sensorCode) {
                    return $elem['sens_id'] == $sensorCode;
                });

                if (empty($foundSensor)) {
                    continue;
                }
                $foundSensorData[] = $foundSensor;
            }

            if (empty($foundSensorData)) {
                unset($sensorCodes[$key]);

                continue;
            }

            if (strpos($key, 'cumulative')) {
                $cumulativeSensors[$key] = 0;
            }
        }

        $datesArray = [];
        foreach ($data as $each) {
            $datesArray[] = $each['date'];
        }

        $datesArray = array_unique($datesArray);

        foreach ($datesArray as $item) {
            foreach ($sensorCodes as $key => $sensor) {
                $aggregation = substr($key, strrpos($key, '_') + 1);

                foreach ($sensor as $each) {
                    $element = array_filter($data, function ($elem) use ($item, $each) {
                        return $elem['date'] == $item && $elem['sens_id'] == $each;
                    });

                    $value = '';
                    if (!empty($element)) {
                        $element = reset($element);
                        $value = 0;
                        switch ($aggregation) {
                            case 'min':
                                $value = $element['min_value'];

                                break;
                            case 'max':
                                $value = $element['max_value'];

                                break;
                            case 'sum':
                                $value = $element['sum_value'];

                                break;
                            case 'avg':
                            case 'last':
                                $value = $element['avg_value'];

                                break;
                        }

                        if (strpos($key, 'cumulative')) {
                            $cumulativeSensors[$key] += $value;
                            $value = $cumulativeSensors[$key];
                        }

                        $value = round($value, 2, PHP_ROUND_HALF_UP);
                    }
                }
            }
        }

        return $value;
    }

    public function getTimeZone()
    {
        return self::TIME_ZONE;
    }

    private function mapData($sensors, $data)
    {
        $mappedData['seriesData'] = [];
        $mappedData['xAxisData'] = [];
        $cumulativeSensors = [];

        foreach ($sensors as $key => $sensor) {
            // If there is NO data for the sensor - REMOVE it from the sensors
            $foundSensorData = [];
            foreach ($sensor as $sensorCode) {
                $foundSensor = array_filter($data, function ($elem) use ($sensorCode) {
                    return $elem['sens_id'] == $sensorCode;
                });

                if (empty($foundSensor)) {
                    continue;
                }
                $foundSensorData[] = $foundSensor;
            }

            if (empty($foundSensorData)) {
                unset($sensors[$key]);

                continue;
            }

            $mappedData['seriesData'][$key] = [];
            if (strpos($key, 'cumulative')) {
                $cumulativeSensors[$key] = 0;
            }
        }

        $datesArray = [];
        foreach ($data as $each) {
            $datesArray[] = $each['date'];
        }

        $datesArray = array_unique($datesArray);

        foreach ($datesArray as $item) {
            $mappedData['xAxisData'][] = $item;
            foreach ($sensors as $key => $sensor) {
                $aggregation = substr($key, strrpos($key, '_') + 1);

                foreach ($sensor as $each) {
                    $configKey = $this->getSensorConfigKey($each);
                    $coeff = $this->getConversionCoefficient($configKey);

                    $element = array_filter($data, function ($elem) use ($item, $each) {
                        return $elem['date'] == $item && $elem['sens_id'] == $each;
                    });

                    $value = '';
                    if (!empty($element)) {
                        $element = reset($element);
                        $value = 0;
                        switch ($aggregation) {
                            case 'min':
                                $value = $element['min_value'];

                                break;
                            case 'max':
                                $value = $element['max_value'];

                                break;
                            case 'sum':
                                $value = $element['sum_value'];

                                break;
                            case 'avg':
                            case 'last':
                                $value = $element['avg_value'];

                                break;
                        }

                        if (strpos($key, 'cumulative')) {
                            $cumulativeSensors[$key] += $value;
                            $value = $cumulativeSensors[$key];
                        }

                        $value = $this->formatSensorValue($value, $coeff);
                    }

                    $mappedData['seriesData'][$key][] = $value;
                }
            }
        }

        $chartData = new EChartsMeteoDataFormatter();

        $sensorCodesMappingArray = config('onsite')['sensor_codes_mapping'];

        return $chartData->setCommonData($mappedData, $sensorCodesMappingArray);
    }

    /**
     * Map Station Data.
     *
     * @throws Exception
     *
     * @return array
     */
    private function mapStationData($data)
    {
        $arrData = [];

        if (!$data || !$data->last_communication || !$data->lat_gps || !$data->lon_gps) {
            return $arrData;
        }

        $stationTs = $data->last_communication;

        $dt = new DateTime();
        $dt->setTimestamp($stationTs);
        $stationUTC = $dt->format('Y-m-d H:i:s');

        return [
            'dates' => [
                'last_communication' => $stationUTC,
            ],
            'position' => [
                'geo' => [
                    'coordinates' => [
                        0 => $data->lon_gps,
                        1 => $data->lat_gps,
                    ],
                ],
            ],
        ];
    }

    /**
     * @return array
     */
    private function commonArrContent($resultPrecipitation, $resultTemperature)
    {
        $precipitationDates = array_column($resultPrecipitation, 'date');
        $temperatureDates = array_column($resultTemperature, 'dt');

        $dates = array_unique(array_merge($precipitationDates, $temperatureDates));

        $arrContent = [];
        foreach ($dates as $date) {
            $precipitationKey = array_search($date, array_column($resultPrecipitation, 'date'));
            $temperatureKey = array_search($date, array_column($resultTemperature, 'dt'));
            $precipitation = floatval(is_int($precipitationKey) ? $resultPrecipitation[$precipitationKey]['precipitation'] : 0);
            $temperatureMax = is_int($temperatureKey) ? floatval($resultTemperature[$temperatureKey]['max_value'] * $resultTemperature[$temperatureKey]['multiplier']) : '';
            $temperatureMin = is_int($temperatureKey) ? floatval($resultTemperature[$temperatureKey]['min_value'] * $resultTemperature[$temperatureKey]['multiplier']) : '';

            $arrContent[$date] = [
                'time' => $date,
                'precipitation' => round($precipitation, 1),
                'temperature_max' => $temperatureMax,
                'temperature_min' => $temperatureMin,
            ];
        }

        return $arrContent;
    }

    /**
     * Get sensor config key.
     *
     * @param $code
     */
    private function getSensorConfigKey(int $sensId)
    {
        $data = array_filter(config('onsite.sensor_codes_mapping'), function ($v, $k) use ($sensId) {
            return in_array($sensId, $v['codes']);
        }, ARRAY_FILTER_USE_BOTH);

        if (empty($data)) {
            return;
        }

        return $key = array_key_first($data);
    }
}
