<?php

namespace App\Classes;

use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\Plot;
use App\Models\SoilGrid;
use App\Models\SoilGridParams;
use App\Models\SoilPoints;
use Auth;
use Config;
use DB;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PlotShape
{
    private $gdalBinPath;

    public function __construct($gdalBinPath)
    {
        $this->gdalBinPath = $gdalBinPath;
    }

    /**
     * Generate .shp, .dbf., shx files for plot's boundary, soil grid and soil points.
     *
     * @param int $plotId The gid of the plot
     * @param int $orderId The id of the order
     *
     * @return string The path where files are saved
     */
    public function generateShpFiles($plotId, $orderId)
    {
        $sopr = OrderPlotRel::where([['plot_id', $plotId], ['order_id', $orderId]])->first();

        if (!$sopr) {
            throw new NotFoundHttpException('OrderPlotRel not found!');
        }

        $sg = SoilGrid::where('sopr_id', $sopr->id)->first();

        if (!$sg) {
            throw new NotFoundHttpException('SoilGrid not found!');
        }

        $sp = SoilPoints::where('sopr_id', $sopr->id)->first();

        if (!$sp) {
            throw new NotFoundHttpException('SoilGrid not found!');
        }

        $reportsPath = Config::get('reports.REPORT_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id;
        $workDir = $reportsPath . DIRECTORY_SEPARATOR . 'plots' . DIRECTORY_SEPARATOR . $plotId . DIRECTORY_SEPARATOR;

        if (!is_dir($workDir)) {
            mkdir($workDir, 0700, true);
        }

        $boundaryShpPath = $this->generateBoundaryFiles($workDir, $plotId);
        $soilGridShpPath = $this->generateSoilGridFiles($workDir, $plotId, $orderId);
        $soilPointsShpPath = $this->generateSoilPointsFiles($workDir, $plotId, $orderId);

        if (!is_file($boundaryShpPath) || !is_file($soilGridShpPath) || !is_file($soilPointsShpPath)) {
            throw new Exception('Error generating files');
        }

        return $workDir;
    }

    public function generateGridByPlotId(int $plotId, string $type = '', ?int $customGridCellArea = null)
    {
        $plot = Plot::findOrFail($plotId);
        $celData = $this->calculateCellData($plot->area, $type, $customGridCellArea);

        $query = $this->gridQueryBuilderWithNewGridPoints($plot->uuid, $celData['cellCount'], $celData['gridCellSize']);
        $query->select(DB::raw("json_build_object(
                'type', 'FeatureCollection',
                'features', json_agg(
                    json_build_object(
                        'type',       'Feature',
                        'geometry',   ST_AsGeoJSON(st_collect(st_transform(st_setsrid(cell, " . Config::get('globals.DEFAULT_DB_CRS') . '), 3857), st_transform(st_setsrid(point, ' . Config::get('globals.DEFAULT_DB_CRS') . "), 3857)))::json,
                        'properties', json_build_object(
                            'sample_id', sample_id,
                            'color', concat('#', left(lpad(to_hex((random() * 10000000)::bigint), 6, '0'), 6)),
                            'plot_id', plot_id
                        )
                    )
                )
            ) as geojson"));

        return $query->get()[0]->geojson;
    }

    public function getDataWithNewGridPoints(string $plotUuId, string $orderUuid, string $type = '', ?int $customCellArea = null)
    {
        $plot = Plot::where('uuid', $plotUuId)->first();
        $celData = $this->calculateCellData($plot->area, $type, $customCellArea);
        $soprQuery = $this->soprQuery($orderUuid);

        $query = $this->gridQueryBuilderWithNewGridPoints($plotUuId, $celData['cellCount'], $celData['gridCellSize'], 'sopr_data');
        $query->withExpression('sopr_data', $soprQuery);
        $query->select('order_id', 'plot_id', 'sopr_id', 'sample_id', 'color', 'point', 'cell')
            ->groupBy('sample_id', 'color', 'plot_id', 'sopr_id', 'point', 'cell', 'order_id');

        return $query->get()->toArray();
    }

    /**
     * @param ?int $customCellArea
     */
    public function getDataWithOldGridPoints(string $plotUuId, string $orderUuid, string $overlappedPlotUuId, string $overlappedOrderUuId, string $type = '', ?int $customCellArea = null)
    {
        $plot = Plot::where('uuid', $plotUuId)->first();
        $celData = $this->calculateCellData($plot->area, $type, $customCellArea);

        $soprQuery = $this->soprQuery($orderUuid);

        $query = $this->gridQueryBuilderWithOldGridPoints($plotUuId, $overlappedPlotUuId, $overlappedOrderUuId, $celData['gridCellSize'], 'sopr_data');
        $query->withExpression('sopr_data', $soprQuery);
        $query->select('order_id', 'plot_id', 'sopr_id', 'sample_id', 'color', 'point', 'cell')
            ->groupBy('sample_id', 'color', 'plot_id', 'sopr_id', 'point', 'cell', 'order_id');

        return $query->get()->toArray();
    }

    public function soprQuery(string $orderUuid)
    {
        return DB::table('result_final as res_fin')
            ->select('res_fin.sample_id', 'res_fin.color', 'res_fin.point', 'res_fin.cell', 'res_fin.plot_id', 'sopr.id as sopr_id', 'sopr.order_id')
            ->leftJoin('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', 'res_fin.plot_id')
            ->where('sopr.order_uuid', '=', $orderUuid)
            ->groupBy('sopr_id', 'res_fin.sample_id', 'res_fin.color', 'res_fin.point', 'res_fin.cell', 'res_fin.plot_id');
    }

    public function getExistingGridsAsGeoJSON(array $plotsIds, int $orderId)
    {
        $data = DB::table('su_satellite_plots as p')
            ->select(
                'ssg.geom as geom',
                'ssp.geom as point',
                'ssg.sample_id',
                'ssg.color',
                'p.gid'
            )
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'p.gid')
            ->join('su_satellite_soil_grid as ssg', 'ssg.sopr_id', '=', 'sopr.id')
            ->join('su_satellite_soil_points as ssp', function ($join) {
                $join->on('ssp.sopr_id', '=', 'sopr.id')
                    ->on('ssp.sample_id', '=', 'ssg.sample_id');
            })
            ->where('sopr.order_id', $orderId)
            ->whereIn('p.gid', $plotsIds)
            ->groupBy('ssg.geom', 'ssg.sample_id', 'ssg.color', 'ssp.geom', 'p.gid');

        $geojsonsArray = DB::table('data')
            ->select(DB::raw("
                json_build_object(
                'type', 'FeatureCollection',
                'features', json_agg(
                    json_build_object(
                        'type',       'Feature',
                        'geometry',   ST_AsGeoJSON(st_collect(st_transform(st_setsrid(geom , " . Config::get('globals.DEFAULT_DB_CRS') . '), 3857), st_transform(st_setsrid(geom, ' . Config::get('globals.DEFAULT_DB_CRS') . "), 3857)))::json,
                        'properties', json_build_object(
                            'sample_id', sample_id ,
                            'color', color ,
                            'plot_id', gid 
                        )
                    )
                )
            ) as geojson"))
            ->groupBy('gid');
        $query = DB::table('geojsonsarr')
            ->withExpression('data', $data)
            ->withExpression('geojsonsarr', $geojsonsArray)
            ->select(DB::raw('json_agg(geojson)  as geojsons '));

        return $query->get()[0]->geojsons;
    }

    /**
     * Generate .shp, .dbf, .shx files for plot's boundary.
     *
     * @param int $plotId The gid of the plot
     * @param string $workDir The directory where the files are stored
     *
     * @return string The path of the .shp boundary file
     */
    private function generateBoundaryFiles($workDir, $plotId)
    {
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $dbConnectionString = "host={$currentDatabase['host']} port={$currentDatabase['port']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']}";
        $boundaryFilePath = $workDir . $plotId . '_boundary.shp';
        $boundarySql = "
            SELECT gid, area, name, geom
            FROM su_satellite_plots
            WHERE gid = {$plotId}
        ";

        $generateBoundaryCmd = "{$this->gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$boundaryFilePath} PG:\"{$dbConnectionString}\" -sql \"{$boundarySql}\"";
        exec($generateBoundaryCmd);

        return $boundaryFilePath;
    }

    /**
     * Generate .shp, .dbf, .shx files for plot's soil grid.
     *
     * @param int $plotId The gid of the plot
     * @param int $orderId The id of the order
     * @param string $workDir The directory where the files are stored
     *
     * @return string The path of the .shp soil grid file
     */
    private function generateSoilGridFiles($workDir, $plotId, $orderId)
    {
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $dbConnectionString = "host={$currentDatabase['host']} port={$currentDatabase['port']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']}";
        $soilGridFilePath = $workDir . $plotId . '_cells.shp';
        $soilGridSql = "
            SELECT ssg.gid, ssg.geom, ssg.sample_id, sopr.order_id
            FROM su_satellite_soil_grid as ssg
            JOIN su_satellite_orders_plots_rel  as sopr ON sopr.id = ssg.sopr_id
            WHERE sopr.plot_id = {$plotId}
            AND sopr.order_id = {$orderId}
        ";

        $generateSoilGridCmd = "{$this->gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$soilGridFilePath} PG:\"{$dbConnectionString}\" -sql \"{$soilGridSql}\"";
        exec($generateSoilGridCmd);

        return $soilGridFilePath;
    }

    /**
     * Generate .shp, .dbf, .shx files for plot's soil points.
     *
     * @param int $plotId The gid of the plot
     * @param int $orderId The id of the order
     * @param string $workDir The directory where the files are stored
     *
     * @return string The path of the .shp soil points file
     */
    private function generateSoilPointsFiles($workDir, $plotId, $orderId)
    {
        $soilPointsFilePath = $workDir . $plotId . '_points.shp';
        $soilPointsSql = "
            SELECT ssp.gid, ssp.geom, ssp.sample_id, sopr.order_id
            FROM su_satellite_soil_points as ssp
            JOIN su_satellite_orders_plots_rel  as sopr ON sopr.id = ssp.sopr_id
            WHERE sopr.plot_id = {$plotId}
            AND sopr.order_id = {$orderId}
        ";
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $dbConnectionString = "host={$currentDatabase['host']} port={$currentDatabase['port']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']}";
        $generateSoilPointsCmd = "{$this->gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$soilPointsFilePath} PG:\"{$dbConnectionString}\" -sql \"{$soilPointsSql}\"";
        exec($generateSoilPointsCmd);

        return $soilPointsFilePath;
    }

    private function gridQueryBuilderWithNewGridPoints(string $plotsUuId, int $cellCount, int $gridCellSize, string $tableName = 'result_final')
    {
        // Grid Plots
        $queryGridPlots = DB::table('su_satellite_plots as plots')
            ->select(
                DB::raw('(st_dump(makegrid(geom, 10))).geom points'),
                'plots.geom as plot',
                'gid as plot_id'
            )
            ->where('uuid', $plotsUuId);

        // Clusters
        $queryClusters = DB::table('grid_points')
            ->select(
                'points',
                DB::raw("ST_ClusterKMeans(points, {$cellCount}) over () as cluster_id"),
                'plot',
                'plot_id'
            );

        $queryCentroids = DB::table('clusters')
            ->select(
                DB::raw('st_Centroid(ST_collect(clusters.points))  centroid'),
                'cluster_id',
                'plot',
                'plot_id'
            )
            ->groupBy('clusters.cluster_id', 'plot', 'plot_id');

        $queryGridCells = $this->gridCellsQuery();
        $queryForUnion = $this->forUnionQuery($gridCellSize);
        $queryOverlapped = $this->overlappedQuery();
        $queryResult = $this->resultQuery();
        $queryResultFinal = $this->resultFinalQuery(false);

        return DB::table($tableName)
            ->withExpression('grid_points', $queryGridPlots)
            ->withExpression('clusters', $queryClusters)
            ->withExpression('centroids', $queryCentroids)
            ->withExpression('grid_cells', $queryGridCells)
            ->withExpression('for_union', $queryForUnion)
            ->withExpression('overlapped', $queryOverlapped)
            ->withExpression('result', $queryResult)
            ->withExpression('result_final', $queryResultFinal);
    }

    private function gridQueryBuilderWithOldGridPoints(
        string $plotUuId,
        string $overlappedPlotUuId,
        string $overlappedOrderUuId,
        int $gridCellSize,
        string $tableName = 'result_final'
    ) {
        // Grid points from overlapped plot
        $overlappedData = DB::table('su_satellite_plots as ssp')
            ->select(
                'sssp.geom as centroid',
                'sssp.sample_id as cluster_id',
                'ssp.geom as overlaped_plot',
                'ssp.farm_id'
            )
            ->join('su_satellite_orders_plots_rel as ssopr', 'ssp.uuid', 'ssopr.plot_uuid')
            ->join('su_satellite_soil_points as sssp', 'sssp.sopr_id', 'ssopr.id')
            ->join('su_satellite_orders as sso', 'sso.uuid', 'ssopr.order_uuid')
            ->where('ssp.uuid', '=', $overlappedPlotUuId)
            ->where('sso.uuid', '=', $overlappedOrderUuId);

        // Old grid points with new plot data
        $newPlotWithOldPoints = DB::table('overlapped_data')
            ->select(
                'overlapped_data.centroid',
                'overlapped_data.cluster_id',
                'ssp.geom as plot',
                'ssp.gid as plot_id'
            )
            ->join('su_satellite_plots as ssp', 'ssp.farm_id', 'overlapped_data.farm_id')
            ->where('ssp.uuid', '=', $plotUuId);

        $queryGridCells = $this->gridCellsQuery();
        $queryForUnion = $this->forUnionQuery($gridCellSize);
        $queryOverlapped = $this->overlappedQuery();
        $queryResult = $this->resultQuery();
        $queryResultFinal = $this->resultFinalQuery(true, $overlappedPlotUuId, $overlappedOrderUuId);

        return DB::table($tableName)
            ->withExpression('overlapped_data', $overlappedData)
            ->withExpression('centroids', $newPlotWithOldPoints)
            ->withExpression('grid_cells', $queryGridCells)
            ->withExpression('for_union', $queryForUnion)
            ->withExpression('overlapped', $queryOverlapped)
            ->withExpression('result', $queryResult)
            ->withExpression('result_final', $queryResultFinal);
    }

    private function gridCellsQuery()
    {
        $unionGridCells = DB::table('centroids')
            ->select(
                DB::raw('(ST_Dump(ST_Intersection((ST_Dump(ST_VoronoiPolygons(ST_collect(centroids.centroid), 0, plot))).geom, plot))).geom'),
                'plot_id'
            )
            ->groupBy('plot', 'plot_id');

        return DB::query()->fromSub(function ($query) use ($unionGridCells) {
            $query->select(
                DB::raw('(case when count(centroids.centroid)<=1 then centroids.plot end) grid_cell'),
                'plot_id'
            );
            $query->from('centroids');
            $query->groupBy('plot', 'plot_id');
            $query->union($unionGridCells);
        }, 'a')
            ->select(DB::raw('row_number () over () cid'), 'grid_cell', 'plot_id');
    }

    private function forUnionQuery(int $gridCellSize)
    {
        $forUnionLeftJoin = DB::table('grid_cells')
            ->select('grid_cell')
            ->where(DB::raw('st_area(grid_cell)'), '<=', $gridCellSize);

        return DB::table('grid_cells as a')
            ->select(
                DB::raw('distinct(a.grid_cell) big'),
                'a.cid',
                DB::raw('b.grid_cell small'),
                DB::raw('ST_Length(ST_CollectionExtract(ST_Intersection(a.grid_cell, b.grid_cell), 2)) lng'),
                'plot_id'
            )
            ->leftJoinSub($forUnionLeftJoin, 'b', DB::raw('ST_Touches(a.grid_cell, b.grid_cell)'), DB::raw('true'))
            ->where(DB::raw('st_area(a.grid_cell)'), '>', $gridCellSize);
    }

    private function overlappedQuery()
    {
        $overlappedUnion = DB::table('for_union')
            ->select('cid', DB::raw('big cell'), 'plot_id')
            ->groupBy('cid', 'big', 'plot_id');

        $overlappedJoin = DB::table('for_union as a')
            ->select('small', DB::raw('max(lng) blng'))
            ->whereNotNull('small')
            ->groupBy('small');

        return DB::query()->fromSub(function ($query) use ($overlappedUnion, $overlappedJoin) {
            $query->select('a.cid', DB::raw('st_union(a.big, b.small) cell'), 'plot_id');
            $query->from('for_union as a');
            $query->joinSub($overlappedJoin, 'b', 'b.blng', 'a.lng');
            $query->union($overlappedUnion);
        }, 'a')
            ->select('cid', 'cell', 'plot_id');
    }

    private function resultQuery()
    {
        return DB::table('overlapped')
            ->select(DB::raw('st_union(cell) cell'), 'plot_id')
            ->groupBy('cid', 'plot_id');
    }

    private function resultFinalQuery(bool $oldGridData, ?string $overlappedPlotUuId = null, ?string $overlappedOrderUuId = null)
    {
        if (!$oldGridData) {
            return DB::table('result')
                ->select(DB::raw('row_number () over() sample_id'), DB::raw("concat('#', left(lpad(to_hex((random() * 10000000)::bigint), 6, '0'), 6)) color"), DB::raw('st_centroid(cell) point'), 'cell', 'plot_id');
        }

        return DB::table('result')
            ->select('sssp.sample_id', DB::raw("concat('#', left(lpad(to_hex((random() * 10000000)::bigint), 6, '0'), 6)) color"), 'sssp.geom as point', 'result.cell', 'result.plot_id')
            ->join('su_satellite_soil_points as sssp', function ($join) {
                $join->where(DB::raw('ST_Intersects(sssp.geom, ST_ConvexHull(cell))'), '=', DB::raw('true'));
            })
            ->join('su_satellite_orders_plots_rel as ssopr', 'sssp.sopr_id', 'ssopr.id')
            ->where('ssopr.plot_uuid', '=', $overlappedPlotUuId)
            ->where('ssopr.order_uuid', '=', $overlappedOrderUuId);
    }

    /**
     * @param ?int $customCellArea
     *
     * @return array|void
     */
    private function calculateCellData(int $plotArea, string $type = '', ?int $customCellArea = 0)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaCoefHa = Config::get('globals.DKA_HA');
        $gridCellSizes = Config::get('globals.GRID_CELL_SIZES');

        if ('2ha' == $type) {
            $cellCount = intval(round(($plotArea * $areaCoefHa) / 2, 0));
        } elseif ('5ha' == $type) {
            $cellCount = intval(round(($plotArea * $areaCoefHa) / 5, 0));
        } elseif ('vra' == $type) {
            if (round($plotArea * $areaCoefHa) <= 6) {
                $cellCount = 2;
            } else {
                $cellCount = intval(($plotArea * $areaCoefHa) / 3);
            }
        } elseif ('iso' == $type) {
            switch ($a = round($plotArea * $areaCoefHa)) {
                case $a <= 2:
                    $cellCount = $gridCellSizes['0-2'];

                    break;
                case $a > 2 && $a <= 5:
                    $cellCount = $gridCellSizes['2-5'];

                    break;
                case $a > 5 && $a <= 10:
                    $cellCount = $gridCellSizes['5-10'];

                    break;
                case $a > 10 && $a <= 15:
                    $cellCount = $gridCellSizes['10-15'];

                    break;
                case $a > 15 && $a <= 20:
                    $cellCount = $gridCellSizes['15-20'];

                    break;
                case $a > 20 && $a <= 30:
                    $cellCount = $gridCellSizes['20-30'];

                    break;
                default:
                    $cellCount = intval(round(1 + sqrt($plotArea * $areaCoefHa), 0));
            }
        } elseif ((SoilGridParams::CUSTOM === $type || SoilGridParams::CUSTOM_GRID === $type) && $customCellArea) {
            $cellCount = intval(round((($plotArea * $areaCoef) / $customCellArea), 0));
        } else {
            throw new Exception('Invalid grid type');
        }

        if ($cellCount < 1) {
            $cellCount = 1;
        }

        $gridCellSize = ($plotArea * 1000) / (4 * $cellCount);

        return ['cellCount' => $cellCount, 'gridCellSize' => $gridCellSize];
    }
}
