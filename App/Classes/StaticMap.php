<?php

namespace App\Classes;

use App\Models\Country;
use App\Models\Plot;
use Auth;
use Config;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\File;

class StaticMap
{
    /**
     * Constructor.
     *
     * @param {String} url Bing static map base url
     * @param {String} apiKey Bing static map api key
     */
    public function __construct($url, $apiKey)
    {
        $this->url = $url;
        $this->apiKey = $apiKey;

        $this->client = new Client([
            'base_uri' => $url,
            'timeout' => 2.0,
            'http_errors' => false,
        ]);
    }

    public function downloadPNG($plotId, $lang)
    {
        $plot = Plot::find($plotId);

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $plotArea = $plot->area * $areaCoef;

        $country = Country::find(Auth::user()->globalUser()->country);
        $countryCode = $country->iso_alpha_2_code;
        $crop = $plot->getCropNameByDate(date('Y-m-d'), $lang)->first();

        $cropName = 'No Crop';

        if ($crop) {
            $cropName = $crop->crop_name;
        }

        $path3dViews = "plots/{$countryCode}/3dviews";
        $storePNGPath = Config::get('globals.STATIC_MAP_PNG_PATH') . "/plots/{$countryCode}/3dviews";
        $pngPath = $storePNGPath . "/{$plotId}.png";
        $pngWebPath = $path3dViews . "/{$plotId}.png";

        if (is_file($pngPath)) {
            return response()->json(['imageUrl' => $pngWebPath, 'plotName' => $plot->name, 'plotArea' => $plotArea, 'cropName' => $cropName]);
        }

        $plotCoords = Plot::getPlotCoords($plotId);
        $latitude = $plotCoords->latitude;
        $longitude = $plotCoords->longitude;

        $plotSize = Plot::getPlotSize($plotId);
        $plotWidth = $plotSize->extent_width_3857 + 30;
        $plotHeight = $plotSize->extent_height_3857 + 30;

        $mapType = 'Aerial';

        // $scale is measured in (meters/pixel)
        $scale = 4.78;
        $zoomLevel = 15;

        if (($plotWidth >= 1190 && $plotWidth <= 2390) && ($plotHeight >= 1190 && $plotHeight <= 2390)) {
            $zoomLevel = 16;
            $scale = 4.39;
        }

        if ($plotWidth < 1190 && $plotHeight < 1190) {
            $zoomLevel = 17;
            $scale = 1.19;
        }

        $plotWidth = round($plotWidth / $scale, 0);
        $plotHeight = round($plotHeight / $scale, 0);
        $mapSize = "{$plotWidth},{$plotHeight}";

        $apiKey = $this->apiKey;
        $pngUrl = "/REST/V1/Imagery/Map/{$mapType}/{$latitude}%2C{$longitude}/{$zoomLevel}";

        $response = $this->client->request('GET', $pngUrl, [
            'query' => [
                'mapSize' => $mapSize,
                'format' => 'png',
                'key' => $apiKey,
            ],
        ]);

        if (200 != $response->getStatusCode()) {
            throw new Exception('Error: ' . $response->getStatusCode() . ', ' . $response->getReasonPhrase());
        }

        if (!is_dir($storePNGPath)) {
            mkdir($storePNGPath, 755, true);
        }

        $content = $response->getBody()->getContents();
        File::put($pngPath, $content);

        return response()->json(['imageUrl' => $pngWebPath, 'plotName' => $plot->name, 'plotArea' => $plotArea, 'cropName' => $cropName]);
    }
}
