<?php

namespace App\Classes\APN;

class APN
{
    /*
      Class to send push notifications using Apple Push Notifications for iOS

      Example usage
      -----------------------
      $push = new GCM();
      $push->setDevices($devices);
      $push->send($title, $message [, $data]);
      -----------------------

      $url APN server url
      $certificate Path to file(.pem) with correct certificate
      $passphrase Passphrase for the certificate file
      $devices An array or string of registered device tokens

     */

    protected $url = 'ssl://gateway.push.apple.com:2195';
    protected $certificate = 'GSPushDistributionCK.pem';
    protected $passphrase = '9QWinqqqIGWif';
    protected $devices;

    public function __construct()
    {
        $this->certificate = app_path() . DIRECTORY_SEPARATOR . 'Classes' . DIRECTORY_SEPARATOR . 'APN' . DIRECTORY_SEPARATOR . 'GSPushDistributionCK.pem';
    }

    /*
      Set the devices to send to
      @param $deviceIds array of device tokens to send to
    */

    public function setDevices($deviceIds)
    {
        if (is_array($deviceIds)) {
            $this->devices = $deviceIds;
        } else {
            $this->devices = [$deviceIds];
        }
    }

    /*
      Send the message to the device
      @param $message The message to send
      @param $data Array of data to accompany the message
     */

    public function send($title, $message, $data = false)
    {
        if (!is_array($this->devices) || 0 == count($this->devices)) {
            return $this->error('No devices set');
        }

        // //////////////////////////////////////////////////////////////////////////////

        $ctx = stream_context_create();
        stream_context_set_option($ctx, 'ssl', 'local_cert', $this->certificate);
        stream_context_set_option($ctx, 'ssl', 'passphrase', $this->passphrase);

        // Open a connection to the APNS server
        $fp = stream_socket_client($this->url, $err, $errstr, 60, STREAM_CLIENT_CONNECT | STREAM_CLIENT_PERSISTENT, $ctx);

        if (!$fp) {
            return $this->error("Failed to connect: {$err} {$errstr}" . PHP_EOL);
        }

        // Create the payload body
        $body['aps'] = [
            'alert' => [
                'title' => $title,
                'body' => $message,
            ],
            'sound' => 'default',
            'badge' => 1,
        ];

        // Encode the payload as JSON
        $payload = json_encode($body);

        for ($i = 0; $i < count($this->devices); $i++) {
            // Build the binary notification
            $msg = chr(0) . pack('n', 32) . pack('H*', $this->devices[$i]) . pack('n', strlen($payload)) . $payload;

            // Send it to the server
            $result = fwrite($fp, $msg, strlen($msg));

            if (!$result) {
                echo 'Message to ' . $this->devices[$i] . ' not delivered' . PHP_EOL;
            } else {
                echo 'Message to ' . $this->devices[$i] . ' successfully delivered' . PHP_EOL;
            }
        }

        // Close the connection to the server
        fclose($fp);
    }

    private function error($msg)
    {
        return "Apple send notification failed with error:\t" . $msg . PHP_EOL;
    }
}
