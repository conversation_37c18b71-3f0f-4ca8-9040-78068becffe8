<?php

namespace App\Classes;

use Auth;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 * A simple helper class for sending track requests to Heap Analytics.
 */
class Heap
{
    protected $headers;
    private $appId;
    private $client;

    /**
     * @param string $appId the app_id corresponding to one of Heap projects
     * @param string $identity username
     */
    public function __construct($appId, $trackURL)
    {
        $this->appId = $appId;
        $this->client = new Client([
            'base_uri' => $trackURL,
            'timeout' => 2.0,
        ]);
        $this->headers = [
            'Content-type' => 'application/json',
        ];
    }

    /**
     * Tracks a custom server side event.
     *
     * @param string $event The name of the server-side event. Limited to 1024 characters.
     * @param array $properties An object with key-value properties you want associated with the user.
     *                          Each key and property must either be a number or string with fewer than 1024 characters.
     */
    public function track($event, array $properties = [])
    {
        $json = [
            'app_id' => $this->appId,
            'identity' => Auth::user()->username,
            'event' => $event,
            'timestamp' => date('c'),
        ];

        if ($properties) {
            $json['properties'] = $properties;
        }

        try {
            $this->client->post('/api/track', [
                'headers' => $this->headers,
                'json' => $json,
                'timeout' => 6,
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }

        return true;
    }

    public function addUserProperties(array $properties)
    {
        try {
            $this->client->post('/api/add_user_properties', [
                'json' => [
                    'app_id' => $this->appId,
                    'identity' => Auth::user()->username,
                    'properties' => $properties,
                ],
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }

        return true;
    }
}
