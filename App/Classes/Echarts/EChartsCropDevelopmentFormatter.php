<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 7/9/2020
 * Time: 5:03 PM.
 */

namespace App\Classes\Echarts;

class EChartsCropDevelopmentFormatter implements IEChartsDataFormatter
{
    private $xAxis;
    private $series;

    public function getXAxis()
    {
        return $this->xAxis;
    }

    /**
     * @return EChartsIndexDataFormatter
     */
    public function setXAxis($xAxis)
    {
        $this->xAxis = $xAxis;

        return $this;
    }

    public function getSeries()
    {
        return $this->series;
    }

    /**
     * @return EChartsIndexDataFormatter
     */
    public function setSeries($series)
    {
        $this->series = $series;

        return $this;
    }

    public function formatCropDevelopment(array $chartsData, array $dataAvg)
    {
        $xAxis = config('echarts.crop-development.options.xAxis');
        $series = config('echarts.crop-development.options.series');
        $xAxis['data'] = $chartsData[0]['xAxis'][0]['data'] ?? [];

        foreach ($xAxis['data'] as $dateKey => $date) {
            $precipitation = [];
            $minTemp = [];
            $maxTemp = [];
            foreach ($chartsData as $chart) {
                $precipitation = $this->findData(trans('general.Precipitation'), $chart);
                $minTemp = $this->findData(trans('general.Air temperature min'), $chart);
                $maxTemp = $this->findData(trans('general.Air temperature max'), $chart);

                $precipitation[] = !is_null($precipitation) ? $precipitation[$dateKey] : $precipitation;
                $minTemp[] = !is_null($minTemp) ? $minTemp[$dateKey] : $minTemp;
                $maxTemp[] = !is_null($maxTemp) ? $maxTemp[$dateKey] : $maxTemp;
            }
            $series['rainfall']['data'][$dateKey] = round(array_sum($precipitation) / count($precipitation), 2);
            $series['minAir']['data'][$dateKey] = round(array_sum($minTemp) / count($minTemp), 2);
            $series['maxAir']['data'][$dateKey] = round(array_sum($maxTemp) / count($maxTemp), 2);
            $series['avgAir']['data'][$dateKey] = round(array_sum([$series['maxAir']['data'][$dateKey], $series['minAir']['data'][$dateKey]]) / 2, 2);
        }

        $series['avgNdvi']['data'] = $dataAvg['avg_mean'];
        $series['avgCloudsPercent']['data'] = $dataAvg['avg_clouds_percent'];
        $xAxis['data'] = count($xAxis['data']) > 0 ? $xAxis['data'] : $dataAvg['dates'];
        $this->setXAxis($xAxis);
        $this->setSeries(array_values($series));

        return $this->toJson();
    }

    private function findData($param, $data, $strict = true)
    {
        if (!isset($data['series'])) {
            return;
        }

        $found = array_filter($data['series'], function ($value) use ($param) {
            return $value['name'] === $param;
        });

        if (!$found) {
            return;
        }

        if (!$strict) {
            return $found;
        }

        $found = reset($found);

        return $found['data'];
    }

    private function toJson()
    {
        return [
            'xAxis' => $this->xAxis,
            'series' => $this->series,
        ];
    }
}
