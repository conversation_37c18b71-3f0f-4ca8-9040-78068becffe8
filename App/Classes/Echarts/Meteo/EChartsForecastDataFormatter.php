<?php

namespace App\Classes\Echarts\Meteo;

use App\Classes\Echarts\IEChartsDataFormatter;
use Exception;
use JsonSerializable;

class EChartsForecastDataFormatter implements IEChartsDataFormatter, JsonSerializable
{
    private $temperature;
    private $wind;
    private $spraying;

    public function getTemperature(): EChartsMeteoDataFormatter
    {
        return $this->temperature;
    }

    /**
     * @return EChartsForecastDataFormatter
     */
    public function setTemperature(EChartsMeteoDataFormatter $temperature)
    {
        $this->temperature = $temperature;

        return $this;
    }

    public function getWind(): EChartsMeteoDataFormatter
    {
        return $this->wind;
    }

    /**
     * @return EChartsForecastDataFormatter
     */
    public function setWind(EChartsMeteoDataFormatter $wind)
    {
        $this->wind = $wind;

        return $this;
    }

    public function getSpraying(): EChartsMeteoDataFormatter
    {
        return $this->spraying;
    }

    /**
     * @return EChartsForecastDataFormatter
     */
    public function setSpraying(EChartsMeteoDataFormatter $spraying)
    {
        $this->spraying = $spraying;

        return $this;
    }

    public function loadFromTemplate(array $dataMap = []): EChartsForecastDataFormatter
    {
        if (
            0 === count(array_keys($dataMap))
        ) {
            throw new Exception("
                Forecast echart dataMap: The fields 'days', 'temperature', 'pictocode', 'precipitation', 'precipitationProbability', 'windspeed' and 'spraying' are required
            ");
        }

        $forecastEchartsTemplate = new EChartsForecastDataTemplate(
            $dataMap['days'],
            $dataMap['temperature'] ?? [],
            $dataMap['pictocode'] ?? [],
            $dataMap['precipitation'] ?? [],
            $dataMap['precipitationProbability'] ?? [],
            $dataMap['windspeed'] ?? [],
            $dataMap['spraying'] ?? []
        );
        $echartData = $forecastEchartsTemplate->getEchartsData();

        $this->validateForecastEchartData($echartData);

        $this->temperature = new EChartsMeteoDataFormatter();
        $this->temperature->setTitle($echartData['temperature']['title']);
        $this->temperature->setGrid($echartData['temperature']['grid']);
        $this->temperature->setTooltip($echartData['temperature']['tooltip']);
        $this->temperature->setXAxis($echartData['temperature']['xAxis']);
        $this->temperature->setYAxis($echartData['temperature']['yAxis']);
        $this->temperature->setSeries($echartData['temperature']['series']);

        $this->wind = new EChartsMeteoDataFormatter();
        $this->wind->setTitle($echartData['wind']['title']);
        $this->wind->setGrid($echartData['wind']['grid']);
        $this->wind->setTooltip($echartData['wind']['tooltip']);
        $this->wind->setXAxis($echartData['wind']['xAxis']);
        $this->wind->setYAxis($echartData['wind']['yAxis']);
        $this->wind->setSeries($echartData['wind']['series']);

        $this->spraying = new EChartsMeteoDataFormatter();
        $this->spraying->setTitle($echartData['spraying']['title']);
        $this->spraying->setGrid($echartData['spraying']['grid']);
        $this->spraying->setTooltip($echartData['spraying']['tooltip']);
        $this->spraying->setXAxis($echartData['spraying']['xAxis']);
        $this->spraying->setYAxis($echartData['spraying']['yAxis']);
        $this->spraying->setSeries($echartData['spraying']['series']);

        return $this;
    }

    /**
     * @param array $data The filled template data
     */
    public function validateForecastEchartData(array $data)
    {
        $columnsToValidate = [
            'title',
            'grid',
            'tooltip',
            'xAxis',
            'xAxis',
            'series',
        ];

        if (!isset($data['temperature']) || !isset($data['wind']) || !isset($data['spraying'])) {
            throw new Exception("Missing forecast data. The fields 'temperature', 'wind' and 'spraying' are required");
        }

        $temperatureFieldsDiff = array_diff_key(array_flip($columnsToValidate), $data['temperature']);
        if (count($temperatureFieldsDiff) > 0) {
            $missingColumns = "'" . implode("', '", array_keys($temperatureFieldsDiff)) . "'";

            throw new Exception("Missing temperature forecast data. The fields: {$missingColumns} are required.");
        }

        $windFieldsDiff = array_diff_key(array_flip($columnsToValidate), $data['wind']);
        if (count($windFieldsDiff) > 0) {
            $missingColumns = "'" . implode("', '", array_keys($windFieldsDiff)) . "'";

            throw new Exception("Missing wind forecast data. The fields: {$missingColumns} are required.");
        }

        $sprayingFieldsDiff = array_diff_key(array_flip($columnsToValidate), $data['spraying']);
        if (count($sprayingFieldsDiff) > 0) {
            $missingColumns = "'" . implode("', '", array_keys($sprayingFieldsDiff)) . "'";

            throw new Exception("Missing spraying forecast data. The fields: {$missingColumns} are required.");
        }
    }

    public function jsonSerialize()
    {
        return [
            'temperature' => $this->temperature,
            'wind' => $this->wind,
            'spraying' => $this->spraying,
        ];
    }
}
