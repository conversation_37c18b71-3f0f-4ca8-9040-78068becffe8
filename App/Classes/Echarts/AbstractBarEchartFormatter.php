<?php

namespace App\Classes\Echarts;

use stdClass;

abstract class AbstractBarEchartFormatter implements IEChartsDataFormatter
{
    protected $grid;
    protected $xAxis;
    protected $yAxis;
    protected $series;
    protected $legend;

    public function getXAxis()
    {
        return $this->xAxis;
    }

    public function setXAxis($xAxis): AbstractBarEchartFormatter
    {
        $this->xAxis = $xAxis;

        return $this;
    }

    public function getYAxis()
    {
        return $this->yAxis;
    }

    public function setYAxis($yAxis): AbstractBarEchartFormatter
    {
        $this->yAxis = $yAxis;

        return $this;
    }

    public function getSeries()
    {
        return $this->series;
    }

    public function getGrid()
    {
        return $this->grid;
    }

    public function setGrid($grid): AbstractBarEchartFormatter
    {
        $this->grid = $grid;

        return $this;
    }

    public function getLegend()
    {
        return $this->legend;
    }

    public function setLegend($legend): AbstractBarEchartFormatter
    {
        $this->legend = $legend;

        return $this;
    }

    public function setSeries($series): AbstractBarEchartFormatter
    {
        $this->series = $series;

        return $this;
    }

    public function createSeries(array $dataForSeries, array $colors = [], bool $setSeries = true): array
    {
        $series = [];

        for ($i = 0; $i < count($dataForSeries); $i++) {
            $dataForSeries[$i] = [
                'name' => $dataForSeries[$i]['name'],
                'type' => 'bar',
                'stack' => 'total',
                'label' => [
                    'show' => false,
                ],
                'emphasis' => [
                    'focus' => 'none',
                ],
                'data' => [
                    $dataForSeries[$i]['value'],
                ],
                'itemStyle' => $dataForSeries[$i]['itemStyle'] ?? [],
            ];

            if (count($colors)) {
                $dataForSeries[$i]['itemStyle']['color'] = $colors[$dataForSeries[$i]['name']];
            }

            if (0 === $i) {
                $dataForSeries[$i]['itemStyle']['borderRadius'] = [5, 0, 0, 5];
            }

            if ($i === count($dataForSeries) - 1) {
                $dataForSeries[$i]['itemStyle']['borderRadius'] = [0, 5, 5, 0];
            }

            if (0 === $i && $i === count($dataForSeries) - 1) {
                $dataForSeries[$i]['itemStyle']['borderRadius'] = [5, 5, 5, 5];
            }

            $series[] = $dataForSeries[$i];
        }

        if ($setSeries) {
            $this->setSeries($series);
        }

        return $series;
    }

    public function toJson()
    {
        $properties = $this->getProperties();
        $object = new stdClass();
        foreach ($properties as $name => $value) {
            $object->$name = $value;
        }

        return json_encode($object);
    }

    public function jsonSerialize(): array
    {
        $data = [];

        if (isset($this->grid)) {
            $data['grid'] = $this->grid;
        }

        if (isset($this->xAxis)) {
            $data['xAxis'] = $this->xAxis;
        }

        if (isset($this->yAxis)) {
            $data['yAxis'] = $this->yAxis;
        }

        if (isset($this->series)) {
            $data['series'] = $this->series;
        }

        if (isset($this->legend)) {
            $data['legend'] = $this->legend;
        }

        return $data;
    }

    private function getProperties(): array
    {
        return get_object_vars($this);
    }
}
