<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 5/15/2020
 * Time: 6:14 PM.
 */

namespace App\Classes\Echarts;

use Config;

class EChartsIndexDataFormatter implements IEChartsDataFormatter
{
    private $title;
    private $xAxis;
    private $yAxis;
    private $series;

    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @return EChartsIndexDataFormatter
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    public function getXAxis()
    {
        return $this->xAxis;
    }

    /**
     * @return EChartsIndexDataFormatter
     */
    public function setXAxis($xAxis)
    {
        $this->xAxis = $xAxis;

        return $this;
    }

    public function getYAxis()
    {
        return $this->yAxis;
    }

    /**
     * @return EChartsIndexDataFormatter
     */
    public function setYAxis($yAxis)
    {
        $this->yAxis = $yAxis;

        return $this;
    }

    public function getSeries()
    {
        return $this->series;
    }

    /**
     * @return EChartsIndexDataFormatter
     */
    public function setSeries($series)
    {
        $this->series = $series;

        return $this;
    }

    public function formatIndexAnalysis(array $avgData)
    {
        $xAxis = [];
        $series = [];
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        foreach ($avgData[0] as $key => $value) {
            $area = 0;
            if (isset($value)) {
                $area = round($value * $areaCoef, 3);
            }

            $xAxis['data'][] = $key;
            $series['data'][] = ['name' => $areaLabel, 'value' => $area];
        }

        $this->setXAxis($xAxis);
        $this->setSeries($series);

        return $this->toJson();
    }

    private function toJson()
    {
        return [
            'xAxis' => $this->xAxis,
            'series' => $this->series,
        ];
    }

    private function getProperties()
    {
        return get_object_vars($this);
    }
}
