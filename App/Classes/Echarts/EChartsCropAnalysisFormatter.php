<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 7/9/2020
 * Time: 5:03 PM.
 */

namespace App\Classes\Echarts;

use JsonSerializable;

class EChartsCropAnalysisFormatter extends AbstractBarEchartFormatter implements IEChartsDataFormatter, JsonSerializable
{
    private $title;
    private $tooltip;
    private $dataZoom;

    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @return EChartsCropAnalysisFormatter
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    public function getTooltip()
    {
        return $this->tooltip;
    }

    /**
     * @return EChartsCropAnalysisFormatter
     */
    public function setTooltip($tooltip)
    {
        $this->tooltip = $tooltip;

        return $this;
    }

    public function getDataZoom()
    {
        return $this->dataZoom;
    }

    /**
     * @return EChartsCropAnalysisFormatter
     */
    public function setDataZoom($dataZoom)
    {
        $this->dataZoom = $dataZoom;

        return $this;
    }

    /**
     * @param $cropAnalysisData = [
     *                          'name'      => string,
     *                          'value'     => int,
     *                          'crop_id'   => int,
     *                          'itemStyle' => [
     *                          'color' => string
     *                          ]
     *                          ]
     * @param bool $setSeries Apply the created series to the series property
     *
     * @return $series = [
     *      'name' => string,
     *      'type' => string,
     *      'stack' => string',
     *      'itemStyle' => [
     *          'borderRadius' => int[]
     *          'color' => string
     *      ],
     *      'label' => [
     *          'show' => bool
     *      ],
     *      'emphasis' => [
     *          'focus' => string
     *      ],
     *      'data' => int[]
     * ]
     */
    public function createSeries(array $dataForSeries, array $colors = [], bool $setSeries = true): array
    {
        $series = [];
        for ($i = 0; $i < count($dataForSeries); $i++) {
            $dataForSeries[$i] = [
                'name' => $dataForSeries[$i]['name'],
                'type' => 'bar',
                'stack' => 'total',
                'label' => [
                    'show' => false,
                ],
                'emphasis' => [
                    'focus' => 'none',
                ],
                'id' => $dataForSeries[$i]['crop_id'],
                'data' => [
                    $dataForSeries[$i]['value'],
                ],
                'itemStyle' => (array)$dataForSeries[$i]['itemStyle'],
            ];

            if (0 === $i) {
                $dataForSeries[$i]['itemStyle']['borderRadius'] = [5, 0, 0, 5];
            }

            if ($i === count($dataForSeries) - 1) {
                $dataForSeries[$i]['itemStyle']['borderRadius'] = [0, 5, 5, 0];
            }

            $series[] = $dataForSeries[$i];
        }

        if ($setSeries) {
            $this->setSeries($series);
        }

        return $series;
    }

    public function jsonSerialize(): array
    {
        $data = [];

        if (isset($this->title)) {
            $data['title'] = $this->title;
        }

        if (isset($this->grid)) {
            $data['grid'] = $this->grid;
        }

        if (isset($this->xAxis)) {
            $data['xAxis'] = $this->xAxis;
        }

        if (isset($this->yAxis)) {
            $data['yAxis'] = $this->yAxis;
        }

        if (isset($this->series)) {
            $data['series'] = $this->series;
        }

        if (isset($this->tooltip)) {
            $data['tooltip'] = $this->tooltip;
        }

        if (isset($this->legend)) {
            $data['legend'] = $this->legend;
        }

        if (isset($this->dataZoom)) {
            $data['dataZoom'] = $this->dataZoom;
        }

        return $data;
    }
}
