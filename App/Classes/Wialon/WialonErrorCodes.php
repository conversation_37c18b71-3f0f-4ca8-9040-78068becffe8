<?php

namespace App\Classes\Wialon;

abstract class WialonErrorCodes
{
    public const INVALID_SESSION = 1;
    public const INVALID_SERVICE_NAME = 2;
    public const INVALID_RESULT = 3;
    public const INVALID_INPUT = 4;
    public const PERFORM_REQUEST = 5;
    public const UNKNOWN = 6;
    public const ACCESS_DENIED = 7;
    public const TOKEN_USER_NOT_FOUND = 8;
    public const AUTH_SERVER_UNAVAILABLE = 9;
    public const CONCURRENT_REQUESTS_LIMIT = 10;
    public const PASSWORD_RESET = 11;
    public const BILLING = 14;
    public const NO_MESSAGES_FOR_INTERVAL = 1001;
    public const ITEM_PROPERTY_EXISTS = 1002;
    public const ONE_REQUEST_ALLOWED = 1003;
    public const MESSAGES_LIMIT = 1004;
    public const EXECUTION_TIME_LIMIT = 1005;
    public const TWO_FACTOR_AUTH_LIMIT = 1006;
    public const SESSION_EXPIRED = 1011;
    public const CANNOT_TRANSFER_UNIT = 2006;
    public const NO_UNIT_ACCESS = 2008;
    public const USER_CANNOT_BOUND_TO_NEW_ACCOUNT = 2014;
    public const SENSOR_DELETE_FORBIDDEN = 2015;
}
