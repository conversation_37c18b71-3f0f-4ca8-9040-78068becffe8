<?php

namespace App\Classes\GCM;

class GCM
{
    /*
      Class to send push notifications using Google Cloud Messaging for Android

      Example usage
      -----------------------
      $push = new GCM();
      $push->setDevices($devices);
      $push->send($title, $message [, $data]);
      -----------------------

      $apiKey GCM api key
      $devices An array or string of registered device tokens
      $message The mesasge to push out

     */

    protected $url = 'https://android.googleapis.com/gcm/send';
    protected $serverApiKey = 'AIzaSyA3io6mZrTpFnVCt-2sxZn-PzB_JuWGDbw';
    protected $devices;

    /*
      Set the devices to send to
      @param $deviceIds array of device tokens to send to
    */

    public function setDevices($deviceIds)
    {
        if (is_array($deviceIds)) {
            $this->devices = $deviceIds;
        } else {
            $this->devices = [$deviceIds];
        }
    }

    /*
      Send the message to the device
      @param $message The message to send
      @param $data Array of data to accompany the message
     */

    public function send($title, $message, $data = false)
    {
        if (!is_array($this->devices) || 0 == count($this->devices)) {
            return $this->error('No devices set');
        }

        if (strlen($this->serverApiKey) < 8) {
            return $this->error('Server API Key not set');
        }

        $fields = [
            'registration_ids' => $this->devices,
            'data' => [
                'title' => $title,
                'message' => $message,
            ],
        ];

        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $fields['data'][$key] = $value;
            }
        }

        $headers = [
            'Authorization: key=' . $this->serverApiKey,
            'Content-Type: application/json',
        ];

        // Open connection
        $curl = curl_init();

        // Set the url, number of POST vars, POST data
        curl_setopt($curl, CURLOPT_URL, $this->url);

        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($fields));

        // Avoids problem with https certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        // Execute post
        $result = curl_exec($curl);

        // Close connection
        curl_close($curl);

        return $result;
    }

    private function error($msg)
    {
        return "Android send notification failed with error:\t" . $msg . PHP_EOL;
    }
}
