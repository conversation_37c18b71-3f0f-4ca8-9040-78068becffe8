<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes;

use App\Classes\Meteo\IStation;
use App\Classes\Meteo\MeteoBlue;
use App\Helpers\Helper;
use App\Models\Plot;
use App\Models\UserStation;
use DateTime;

class MeteoService
{
    public function __construct(Heap $heap)
    {
        $this->heap = $heap;
    }

    public function getDailyDataByPlotAndDate(DateTime $date, Plot $plot, $sensor)
    {
        if ($plot->station) {
            /** @var IStation $stationObject */
            $stationObject = UserStation::where('id', $plot->station->id)->first()->getStationApi();
        }

        if (!$plot->station || !$data = $stationObject->getDailyDataByStationAndDate($date, $sensor)) {
            /** @var MeteoBlue $meteoB<PERSON>Service */
            $meteoBlueService = Helper::resolveObject(MeteoBlue::class);
            $meteoLocation = Plot::findMeteoLocation($plot->gid);
            $data = $meteoBlueService->getDailyDataByCoordinatesAndDate(
                $meteoLocation['latitude'],
                $meteoLocation['longitude'],
                $date,
                $sensor
            );
        }

        return $data;
    }

    public function formatSimple(array $chart_data)
    {
        $series = $chart_data['series'];
        $xAxis = $chart_data['xAxis'];
        $yAxis = $chart_data['yAxis'];

        $arrResult = [];
        foreach ($series as $serie) {
            $foundXAxis = array_filter($xAxis, function ($value) use ($serie) {
                return $value['gridIndex'] == $serie['xAxisIndex'];
            });
            $foundXAxis = reset($foundXAxis);

            $foundYAxis = array_filter($yAxis, function ($value) use ($serie) {
                return $value['gridIndex'] == $serie['xAxisIndex'];
            });
            $foundYAxis = reset($foundYAxis);

            $unit = $foundYAxis['axisLabel']['formatter'] ?? '';
            $unit = str_replace('{value}', '', $unit);
            $unit = trim($unit);

            $arrData = [];
            foreach ($serie['data'] as $key => $value) {
                $timestamp = $foundXAxis['data'][$key] ?? '';

                $arrData[] = [
                    'timestamp' => $timestamp,
                    'value' => $value,
                    'unit' => $unit,
                ];
            }

            $arrResult[$serie['name']]['data'] = $arrData;
        }

        return $arrResult;
    }
}
