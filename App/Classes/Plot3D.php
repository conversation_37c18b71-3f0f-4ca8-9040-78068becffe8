<?php

namespace App\Classes;

use GuzzleHttp\Client;

class Plot3D
{
    public function __construct($url)
    {
        $this->url = $url;

        $this->client = new Client([
            'base_uri' => $url,
            'timeout' => 300.0,
        ]);
    }

    public function downloadBinFile($geojson)
    {
        $response = $this->client->request('POST', '/api/generateBin', [
            'form_params' => ['geojson' => $geojson],
        ]);

        return $response->getBody()->getContents();
    }
}
