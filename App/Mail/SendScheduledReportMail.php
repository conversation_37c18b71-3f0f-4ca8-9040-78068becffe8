<?php

namespace App\Mail;

use App\Models\Organization;
use App\Models\ScheduledReport;
use App\Models\ServiceProvider;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendScheduledReportMail extends Mailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * @var ScheduledReport
     */
    public $scheduledReport;

    /**
     * @var ServiceProvider
     */
    public $serviceProvider;

    /**
     * @var Organization
     */
    public $organization;

    /**
     * @var string
     */
    public $reportFile;

    /**
     * Create a new message instance.
     */
    public function __construct(
        ScheduledReport $scheduledReport,
        ServiceProvider $serviceProvider,
        Organization $organization,
        string $reportFile
    ) {
        $this->scheduledReport = $scheduledReport;
        $this->serviceProvider = $serviceProvider;
        $this->organization = $organization;
        $this->reportFile = $reportFile;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): SendScheduledReportMail
    {
        return $this
            ->subject($this->scheduledReport->subject)
            ->view('emails.scheduled-report.send')
            ->attach($this->reportFile)
            ->withSwiftMessage(function ($message) {
                $message->mailableClass = __CLASS__;
            });
    }
}
