<?php

namespace App\Jobs;

use App\Models\Country;
use App\Models\ScheduledReport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PrepareScheduledReports implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct() {}

    /**
     * Execute the job.
     */
    public function handle()
    {
        Country::where('active', true)->each(function (Country $country) {
            ScheduledReport::on(strtoupper($country->iso_alpha_2_code))
                ->chunkById('500', function ($scheduledReports) {
                    /** @var ScheduledReport $scheduledReport */
                    foreach ($scheduledReports as $scheduledReport) {
                        if (
                            $scheduledReport->isADailyReport()
                            && $scheduledReport->dailyDeliveryDaysIncludeToday()
                            && ($scheduledReport->hasNeverBeenScheduledToSend() || $scheduledReport->scheduledToSendAtHasPassed())
                            && ($scheduledReport->isNotLastSentToday() || $scheduledReport->hasNeverBeenSentUntilNow())
                        ) {
                            GenerateAndSendScheduledReport::dispatch($scheduledReport);
                        }

                        if (
                            $scheduledReport->isAWeeklyReport()
                            && $scheduledReport->weeklyDeliveryDayIsToday()
                            && ($scheduledReport->hasNeverBeenScheduledToSend() || $scheduledReport->scheduledToSendAtHasPassed())
                            && ($scheduledReport->isNotLastSentThisWeek() || $scheduledReport->hasNeverBeenSentUntilNow())
                        ) {
                            GenerateAndSendScheduledReport::dispatch($scheduledReport);
                        }

                        if (
                            $scheduledReport->isAMonthlyReport()
                            && $scheduledReport->monthlyDeliveryDayIsToday()
                            && ($scheduledReport->hasNeverBeenScheduledToSend() || $scheduledReport->scheduledToSendAtHasPassed())
                            && ($scheduledReport->isNotLastSentThisMonth() || $scheduledReport->hasNeverBeenSentUntilNow())
                        ) {
                            GenerateAndSendScheduledReport::dispatch($scheduledReport);
                        }
                    }
                });
        });
    }
}
