<?php

namespace App\Jobs;

use App\Models\MachineEvent;
use App\Models\MachineTask;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class MatchEventAndTask extends AbstractCountryAwareJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $serverName;
    private $organizationId;

    public function __construct(string $serverName, int $organizationId)
    {
        $this->serverName = $serverName;
        $this->organizationId = $organizationId;
    }

    public function handle()
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $machineEventStageProposed = MachineEvent::PROPOSED;

        $eventsForMatch = DB::table('su_machine_tasks AS smt')
            ->select(
                'smt.id as task_id',
                'sme.id as event_id',
                'sme.start_date',
                'sme.end_date',
                DB::raw("
                    case
                        when sme.plot_id isnull then null 
                        when sme.machine_implement_id notnull 
                            and smi.width > 0 
                            and sme.geom_cultivated notnull 
                            then ROUND(((ST_Area(sme.geom_cultivated) / 1000) * {$areaCoef})::numeric, 3)
                        else 0
                    end as cultivated_area
                "),
            )
            ->join('su_machine_events as sme', function ($join) use ($machineEventStageProposed) {
                $join->on(function ($query) {
                    $query->where('smt.plot_id', '=', DB::raw('sme.plot_id'))
                        ->orWhere(function ($query) {
                            $query->whereNull('smt.plot_id')
                                ->whereNull('sme.plot_id');
                        });
                })
                    ->on('smt.machine_unit_id', '=', 'sme.machine_id')
                    ->on('smt.start_date', '<=', 'sme.start_date')
                    ->on('smt.end_date', '>=', 'sme.end_date')
                    ->on('smt.work_operation_ids', '=', 'sme.work_operation_ids')
                    ->on('smt.machine_implement_id', '=', 'sme.machine_implement_id')
                    ->on('smt.driver', '=', 'sme.driver')
                    ->where('sme.stage', $machineEventStageProposed);
            })
            ->leftJoin('su_machines_implements as smi', 'smi.id', '=', 'sme.machine_implement_id')
            ->where('smt.organization_id', $this->organizationId)
            ->whereIn('smt.state', MachineTask::getAvailableStateToMatchTaskToEvent());

        DB::table('su_machine_tasks')
            ->withExpression('events_for_match', $eventsForMatch)
            ->join('events_for_match AS efm', 'efm.task_id', '=', 'su_machine_tasks.id')
            ->updateFrom([
                'state' => MachineTask::STATE_DONE_PROPOSED,
                'machine_event_id' => DB::raw('efm.event_id'),
                'start_date' => DB::raw('efm.start_date'),
                'completion_date' => DB::raw('efm.end_date'),
                'covered_area' => DB::raw('efm.cultivated_area'),
            ]);
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
