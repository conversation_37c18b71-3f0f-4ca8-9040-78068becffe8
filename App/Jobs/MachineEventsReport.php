<?php

namespace App\Jobs;

use App\Services\Machine\MachineEventService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Sentry;

class MachineEventsReport extends AbstractCountryAwareJob implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $serverName;
    private $tmpTableName;
    private $organizationId;
    private $wialonUnitId;
    private $date;

    /**
     * IrrigationPerDay constructor.
     *
     * MachineEventsReport constructor.
     *
     * @param ?int $wialonUnitId
     * @param ?string $date
     */
    public function __construct(
        string $serverName,
        string $tmpTableName,
        int $organizationId,
        ?string $date,
        ?int $wialonUnitId
    ) {
        $this->serverName = $serverName;
        $this->tmpTableName = $tmpTableName;
        $this->organizationId = $organizationId;
        $this->wialonUnitId = $wialonUnitId;
        $this->date = $date;
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle()
    {
        try {
            DB::transaction(function () {
                $machineEventService = app(MachineEventService::class);
                if ($this->date) {
                    $machineEventService->detachEventsFromTasks($this->organizationId, $this->date, $this->wialonUnitId);
                    $machineEventService->deleteEvents($this->organizationId, $this->date, $this->wialonUnitId);
                }
                $machineEventService->storeMachineEventsReport($this->serverName, $this->tmpTableName, $this->organizationId);
            });
        } catch (Exception $e) {
            Sentry::captureException($e);

            throw $e;
        }
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
