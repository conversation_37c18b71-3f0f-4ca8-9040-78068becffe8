<?php

namespace App\Jobs;

use App\Classes\CMS\PlotService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class UpdateFieldFarmIdInCMS implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    /**
     * The number of times the  job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $plotUuid;
    private $farmId;

    public function __construct(string $plotUuid, int $farmId)
    {
        $this->plotUuid = $plotUuid;
        $this->farmId = $farmId;
    }

    /**
     * Execute the job.
     */
    public function handle(PlotService $plotService)
    {
        $plotService->updatePlotFarmInCMS($this->plotUuid, $this->farmId);
    }
}
