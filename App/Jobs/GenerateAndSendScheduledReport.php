<?php

namespace App\Jobs;

use App\Mail\SendScheduledReportMail;
use App\Models\Country;
use App\Models\Organization;
use App\Models\ScheduledReport;
use App\Services\Irrigation\IrrigationReportService;
use App\Services\Machine\MachineProductsReportService;
use App\Services\Machine\MachineReportService;
use App\Traits\Country\TenantCountryAware;
use App\Traits\Mail\ServiceProviderConfigurableMail;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class GenerateAndSendScheduledReport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use TenantCountryAware;
    use ServiceProviderConfigurableMail;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * @var ScheduledReport
     */
    private $scheduledReport;

    /**
     * Create a new job instance.
     */
    public function __construct(ScheduledReport $scheduledReport)
    {
        $this->scheduledReport = $scheduledReport;
        $this->countryISOAlpha2Code = $scheduledReport->getConnectionName();
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle()
    {
        $country = Country::where('iso_alpha_2_code', strtoupper($this->countryISOAlpha2Code))->first();
        $serviceProvider = $this->scheduledReport->owner->globalUser()->serviceProvider;
        $organization = $this->scheduledReport->owner->lastChosenOrganization;
        $this->configureServiceProviderMail($serviceProvider);
        $this->loadCountrySpecificConfigs($country);
        $this->setDatabaseConnection($country);
        App::setLocale($locale = $this->scheduledReport->getLocale());

        try {
            $reportFile = $this->generateAndGetReportFileFor($this->scheduledReport);
            $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        } catch (Exception $exception) {
            logger()->info(
                sprintf(
                    'At %s the requested report with ID %s faild with the following error: %s',
                    now()->toDateTimeString(),
                    $this->scheduledReport->getKey(),
                    $exception->getMessage()
                )
            );

            $this->scheduledReport->update([
                'last_sent_at' => now(),
            ]);

            $this->delete();

            return;
        }

        Mail::to($this->scheduledReport->recipient_emails)
            ->locale($locale)
            ->later(
                $sendAt = $this->calculateWhenToSendAtFor($this->scheduledReport),
                new SendScheduledReportMail($this->scheduledReport, $serviceProvider, $organization, $reportFileFullPath)
            );

        $this->scheduledReport->update([
            'scheduled_to_send_at' => $sendAt,
        ]);
    }

    /**
     * @throws Exception
     */
    private function generateAndGetReportFileFor(ScheduledReport $scheduledReport): string
    {
        if (!isset(ScheduledReport::$reportTypes[$scheduledReport->type])) {
            throw new Exception("Unsupported report type {$scheduledReport->type}");
        }

        /** @var Organization $organization */
        $organization = $scheduledReport->owner->lastChosenOrganization;

        return $this
            ->getReportClass($scheduledReport->type)
            ->generateReport(
                $scheduledReport->file_type,
                $organization->getKey(),
                $scheduledReport->getParametersForFilter(),
                $scheduledReport->getParameters('groupBy'),
                $scheduledReport->getParameters('orderBy'),
                $scheduledReport->getParameters('selectedColumns'),
            );
    }

    /**
     * @return IrrigationReportService|MachineProductsReportService|MachineReportService
     */
    private function getReportClass(string $type): object
    {
        return App::make(ScheduledReport::$reportTypes[$type]);
    }

    private function calculateWhenToSendAtFor(ScheduledReport $scheduledReport): \Illuminate\Support\Carbon
    {
        // @var Organization $organization
        // $organization = $scheduledReport->owner->lastChosenOrganization;

        // TODO: Add conversion to organization's timezone at some point
        return now()
//            ->setTimezone($organization->time_zone)
            ->setTimeFromTimeString($scheduledReport->delivery_time);
    }
}
