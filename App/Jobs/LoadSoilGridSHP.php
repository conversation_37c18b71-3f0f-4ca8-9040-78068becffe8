<?php

namespace App\Jobs;

use App\Classes\LoadData\SoilGridProcessingClass;
use App\Models\File;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class LoadSoilGridSHP implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    protected $file;

    /**
     * Create a new job instance.
     */
    public function __construct(File $file)
    {
        $this->file = $file;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $processor = new SoilGridProcessingClass($this->file);

        return $processor->startProcessing();
    }
}
