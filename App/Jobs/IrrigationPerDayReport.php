<?php

namespace App\Jobs;

use App\Services\Irrigation\IrrigationDataRawService;
use App\Services\Irrigation\IrrigationEventsService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class IrrigationPerDayReport extends AbstractCountryAwareJob implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $serverName;
    private $tmpTableName;
    private $organizationId;
    private $date;
    private $wialonUnitId;

    public function __construct(
        string $serverName,
        string $tmpTableName,
        int $organizationId,
        string $date,
        ?int $wialonUnitId
    ) {
        $this->serverName = $serverName;
        $this->tmpTableName = $tmpTableName;
        $this->organizationId = $organizationId;
        $this->date = $date;
        $this->wialonUnitId = $wialonUnitId;
    }

    /**
     * * Execute the job.
     *
     * @throws Exception
     */
    public function handle()
    {
        DB::transaction(function () {
            $irrigationDataRawService = app(IrrigationDataRawService::class);

            if ($this->date) {
                $irrigationDataRawService->deleteIrrDataRaw($this->date, $this->wialonUnitId);
                IrrigationEventsService::deleteEvents($this->organizationId, $this->date, $this->wialonUnitId);
            }

            $irrigationDataRawService->addContent($this->tmpTableName, $this->organizationId);
            $newIrrigationEventIds = $irrigationDataRawService->addIrrigationEvents();
            $irrigationDataRawService->addContentEventsPlots($newIrrigationEventIds);
        });
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
