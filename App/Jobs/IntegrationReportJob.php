<?php

namespace App\Jobs;

use App\Factories\WialonReportFactory;
use App\Services\Wialon\ReportService;
use Illuminate\Bus\Batchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class IntegrationReportJob extends AbstractCountryAwareJob
{
    use InteractsWithQueue;
    use SerializesModels;
    use Batchable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $serverName;
    private $tmpTableName;
    private $reportId;
    private $reportName;
    private $reportParams;
    private $reportUrl;
    private $reportToken;
    private $organizationId;
    private $date;
    private $wialonUnitId;

    public function __construct(string $serverName, string $tmpTableName, int $reportId, string $reportName, array $reportParams, string $reportUrl, string $reportToken, ?int $organizationId = null, ?string $date = null, $wialonUnitId = null)
    {
        $this->serverName = $serverName;
        $this->tmpTableName = $tmpTableName;
        $this->reportId = $reportId;
        $this->reportName = $reportName;
        $this->reportParams = $reportParams;
        $this->reportUrl = $reportUrl;
        $this->reportToken = $reportToken;
        $this->organizationId = $organizationId;
        $this->date = $date;
        $this->wialonUnitId = $wialonUnitId;
    }

    public function handle()
    {
        $reportData = app(ReportService::class)->integrationReport(
            $this->reportUrl,
            $this->reportParams,
            $this->reportId,
            $this->reportToken,
            $this->tmpTableName
        );

        if (empty($reportData['columns'])) {
            return;
        }

        dispatch(WialonReportFactory::createJob(
            $this->serverName,
            $this->tmpTableName,
            $this->reportName,
            $this->organizationId,
            $this->date,
            $this->wialonUnitId
        ));
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
