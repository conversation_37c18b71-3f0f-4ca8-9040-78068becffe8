<?php

namespace App\Jobs;

use App\Contracts\CountryAwareJob;
use App\Jobs\Middleware\UseCountrySpecificConfigs;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

abstract class AbstractCountryAwareJob implements CountryAwareJob, ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public function middleware()
    {
        return [new UseCountrySpecificConfigs()];
    }
}
