<?php

// app/Jobs/Middleware/UseCountrySpecificConfigs.php

namespace App\Jobs\Middleware;

use App\Contracts\CountryAwareJob;
use App\Models\Country;
use Closure;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

class UseCountrySpecificConfigs
{
    public function handle($job, Closure $next)
    {
        if (!$job instanceof CountryAwareJob) {
            throw new Exception('Job must implement CountryAwareJob interface.');
        }

        $countryIsoAlpha2Code = $job->getCountryIsoAlpha2Code();
        $this->loadCountrySpecificConfigs($countryIsoAlpha2Code);

        return $next($job);
    }

    protected function loadCountrySpecificConfigs($countryIsoAlpha2Code)
    {
        $country = Country::where('iso_alpha_2_code', strtoupper($countryIsoAlpha2Code))->first();

        $this->loadConfigParams($country);
        $this->setDBConnection($country);
    }

    private function loadConfigParams($country)
    {
        // @var ConfigParamValue[] $configParams
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain . '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));
    }

    private function setDBConnection($country)
    {
        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));
    }
}
