<?php

namespace App\Jobs;

use App\Classes\Wialon\WialonErrorCodes;
use App\Models\Integration;
use App\Services\Wialon\WialonService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class DeactivateIntegrationJob extends AbstractCountryAwareJob implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    public $retryAfter = 60;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;
    private $serverName;

    private $integration;
    private $exception;

    public function __construct(
        string $serverName,
        Integration $integration
    ) {
        $this->serverName = $serverName;
        $this->integration = $integration;
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle()
    {
        try {
            app(WialonService::class)->login($this->integration->token, $this->integration->integrationAddress->getBaseUrl());
        } catch (Throwable $exception) {
            if (
                WialonErrorCodes::TOKEN_USER_NOT_FOUND === $exception->getCode()
                || WialonErrorCodes::ACCESS_DENIED === $exception->getCode()
            ) {
                throw new Exception('Deactivating integration due to Wialon login error. Integration id : ' . $this->integration->id, $exception->getCode(), $exception);
            }
        }
    }

    public function failed(Throwable $exception)
    {
        $this->integration->deactivate()->save();

        \Sentry\captureException($exception);
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
