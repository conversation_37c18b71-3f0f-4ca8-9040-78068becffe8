<?php

namespace App\Jobs;

use App\Models\MachineTask;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class UpdateTaskStateToOngoing extends AbstractCountryAwareJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $serverName;
    private $organizationId;

    public function __construct(string $serverName, int $organizationId)
    {
        $this->serverName = $serverName;
        $this->organizationId = $organizationId;
    }

    public function handle()
    {
        $machinesCurrentDataQuery = DB::table('su_current_machines_data')
            ->select(
                DB::raw("
                    current_data_feature->'properties'->>'unit_id' as machine_unit_id,
                    current_data_feature->'properties'->>'plot_id' as plot_id,
                    current_data_feature->'properties'->>'last_communication' as date,
                    array_agg(
                        distinct swo.id
                    ) as work_operation_ids 
                ")
            )
            ->crossJoin(DB::raw("jsonb_array_elements((su_current_machines_data.geojson->'features')::JSONB) current_data_feature"))
            ->leftJoin('su_machines_implements AS smi', function ($join) {
                $join->on('smi.organization_id', '=', 'su_current_machines_data.organization_id')
                    ->on('smi.name', '=', DB::raw("current_data_feature->'properties'->>'trailer'"));
            })
            ->leftJoin('su_machines_implements_work_operations AS smiwo', 'smiwo.implement_id', '=', 'smi.id')
            ->join('su_work_operations AS swo', function ($join) {
                $join->on('swo.id', '=', 'smiwo.work_operation_id')
                    ->orWhere(function ($query) {
                        $query->whereNull('smiwo.work_operation_id')
                            ->where('swo.name', DB::raw("'Unknown'::work_operations_types_enum"));
                    });
            })
            ->where('su_current_machines_data.organization_id', $this->organizationId)
            ->whereRaw("current_data_feature->'properties'->>'status' = 'Moving'")
            ->groupBy('current_data_feature', 'smi.id');

        $onGoingTaskQuery = DB::table('su_machine_tasks as smt')
            ->select('smt.id as task_id')
            ->join('machines_current_data as mcd', function ($join) {
                $join->on('smt.machine_unit_id', '=', DB::raw('mcd.machine_unit_id::int'))
                    ->where('smt.work_operation_ids', '<@', DB::raw('mcd.work_operation_ids'))
                    ->whereBetweenColumns(DB::raw('mcd.date::date'), [DB::raw('smt.start_date::date'), DB::raw('smt.end_date::date')]) //  whereBetween
                    ->where(function ($query) {
                        $query->where('smt.plot_id', '=', DB::raw('mcd.plot_id::int'))
                            ->orWhere(function ($query) {
                                $query->where('smt.plot_id', null);
                                $query->where(DB::raw('mcd.plot_id'), null);
                            });
                    });
            });

        DB::table('su_machine_tasks')
            ->withExpression('machines_current_data', $machinesCurrentDataQuery)
            ->withExpression('task_for_ongoing', $onGoingTaskQuery)
            ->join('task_for_ongoing AS tfo', 'tfo.task_id', '=', 'su_machine_tasks.id')
            ->updateFrom([
                'su_machine_tasks.state' => MachineTask::STATE_ONGOING,
            ]);
    }

    public function getCountryIsoAlpha2Code()
    {
        return $this->serverName;
    }
}
