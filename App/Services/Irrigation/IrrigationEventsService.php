<?php

namespace App\Services\Irrigation;

use App\Classes\Echarts\Irrigation\EChartIrrigationFormatter;
use App\Jobs\IntegrationReportJob;
use App\Models\Country;
use App\Models\Integration;
use App\Models\IntegrationReportsTypes;
use App\Models\IrrigationEvent;
use App\Services\Wialon\ReportService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class IrrigationEventsService
{
    private $reportService;
    private $irrigationDataRawService;

    public function __construct(ReportService $reportService, IrrigationDataRawService $irrigationDataRawService)
    {
        $this->reportService = $reportService;
        $this->irrigationDataRawService = $irrigationDataRawService;
    }

    public function getEventsCountByType($organizationId, $filter): array
    {
        return IrrigationEvent::getEventsCountByTypeQuery($organizationId, $filter)
            ->select(
                'iet.type AS name',
                DB::raw('COALESCE(events_count.count, 0) AS value')
            )
            ->get()
            ->toArray();
    }

    public function getMobileEchartEventsCountByType($organizationId, $filter)
    {
        $eventsCountByType = IrrigationEvent::getEventsCountByTypeQuery($organizationId, $filter)->select(
            'iet.type AS name',
            DB::raw('COALESCE(events_count.count, 0) AS value')
        )->get()->toArray();

        $chartOptions = Config::get('echarts.mobile.irrigation-events-count-by-type.options');
        $chartColors = Config::get('echarts.mobile.irrigation-events-count-by-type.state_colors');

        return $this->createEchart($chartOptions, $eventsCountByType, $chartColors);
    }

    public function getEventsNameByType($organizationId, $filter): array
    {
        return IrrigationEvent::getEventsCountByTypeQuery($organizationId, $filter)
            ->select(
                'iet.type AS name'
            )
            ->pluck('name')->toArray();
    }

    public function scheduleIrrigationPerDateReport(int $organizationId, ?int $wialonUnitId, int $toDate, int $fromDate): void
    {
        $country = Country::find(Auth::user()->globalUser()->country);

        $report = IntegrationReportsTypes::getIntegrationReportsQuery(
            Integration::ACTIVE,
            IntegrationReportsTypes::SCHEDULED,
            $organizationId,
            IntegrationReportsTypes::IRRIGATION_PER_DAY
        )->firstOrFail();

        $params = json_decode($report->params, true);

        if ($wialonUnitId) {
            $params['exec_params']['reportObjectId'] = $wialonUnitId;
        }

        $params['exec_params']['interval']['flags'] = IntegrationReportsTypes::REPORT_FLAG_BY_DATE;
        $params['exec_params']['interval']['to'] = $toDate;
        $params['exec_params']['interval']['from'] = $fromDate;

        $date = Carbon::createFromTimestamp($toDate)->format('Y-m-d');

        dispatch(new IntegrationReportJob(
            $country->iso_alpha_2_code,
            $tmpTableName = uniqid('tmp_report_'),
            $report->id,
            $report->name,
            $params,
            $report->url,
            $report->token,
            $report->organization_id,
            $date,
            $wialonUnitId
        ));
    }

    public static function deleteEvents(int $organizationId, string $date, ?int $wialonUnitId)
    {
        return IrrigationEvent::deleteEvents($organizationId, $date, $wialonUnitId);
    }

    private function createEchart(array $chartOptions, array $seriesData, ?array $colors = []): array
    {
        $chartData = new EChartIrrigationFormatter();
        $chartData->setXAxis($chartOptions['xAxis']);
        $chartData->setYAxis($chartOptions['yAxis']);
        $chartData->setGrid($chartOptions['grid']);
        $chartData->setLegend($chartOptions['legend']);
        $chartData->createSeries($seriesData, $colors);

        return $chartData->jsonSerialize();
    }
}
