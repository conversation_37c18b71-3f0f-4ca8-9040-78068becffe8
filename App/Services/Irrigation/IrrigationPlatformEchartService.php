<?php

namespace App\Services\Irrigation;

use App\Classes\Echarts\Irrigation\EChartIrrigationFormatter;
use App\Models\CurrentIrrigationPlatform;
use Illuminate\Support\Facades\Config;

class IrrigationPlatformEchartService
{
    public function __construct() {}

    public function getMobileEchartIrrigationPlatformByState(int $organizationId, array $platformIds, array $states, array $farmIds, array $plotIds): array
    {
        $irrigationStateData = CurrentIrrigationPlatform::getPlatformCurrentDataState($organizationId, $platformIds, $states, $farmIds, $plotIds);
        $chartOptions = Config::get('echarts.mobile.irrigation-platform-state.options');
        $chartColors = Config::get('echarts.mobile.irrigation-platform-state.state_colors');

        return $this->createEchart($chartOptions, $irrigationStateData, $chartColors);
    }

    public function getMobileEchartPivotByState(int $organizationId, array $platformIds, array $states, array $farmIds, array $plotIds): array
    {
        $pivotStateData = CurrentIrrigationPlatform::getPivotCurrentDataStateEchart($organizationId, $platformIds, $states, $farmIds, $plotIds);
        $chartOptions = Config::get('echarts.mobile.pivot-state.options');
        $chartColors = Config::get('echarts.mobile.pivot-state.state_colors');

        return $this->createEchart($chartOptions, $pivotStateData, $chartColors);
    }

    private function createEchart(array $chartOptions, array $seriesData, ?array $colors = []): array
    {
        $chartData = new EChartIrrigationFormatter();
        $chartData->setXAxis($chartOptions['xAxis']);
        $chartData->setYAxis($chartOptions['yAxis']);
        $chartData->setGrid($chartOptions['grid']);
        $chartData->setLegend($chartOptions['legend']);
        $chartData->createSeries($seriesData, $colors);

        return $chartData->jsonSerialize();
    }
}
