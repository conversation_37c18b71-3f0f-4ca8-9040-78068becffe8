<?php

namespace App\Services\Irrigation;

use App\Classes\Wialon\WialonErrorCodes;
use App\Jobs\DeactivateIntegrationJob;
use App\Models\CurrentIrrigationPlatform;
use App\Models\Integration;
use App\Models\IntegrationReportsTypes;
use App\Models\IrrigationUnit;
use App\Services\Wialon\WialonService;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Sentry;

class IrrigationUnitService
{
    private $wialonService;

    public function __construct(WialonService $wialonService)
    {
        $this->wialonService = $wialonService;
    }

    /**
     * @throws Exception
     */
    public function syncUnits($organizationId, $svcType, $jsonParams)
    {
        $integrations = Integration::getIntegrationsDataForSync(
            $organizationId,
            Integration::PACKAGE_SLUG_SHORT_IM,
            Integration::ACTIVE,
            [IntegrationReportsTypes::IRRIGATION_UNITS]
        )->toArray();
        $currentUnits = IrrigationUnit::where('organization_id', $organizationId)->get()->toArray();
        $currentUnits = array_column($currentUnits, 'wialon_unit_imei');

        if (!$integrations) {
            throw new Exception('No active integrations found.');
        }

        $allUnits = [];
        foreach ($integrations as $integration) {
            $token = $integration['token'];
            $url = parse_url($integration['integration_address']['url']);
            $baseUrl = $url['scheme'] . '://' . $url['host'];

            try {
                $this->wialonService->login($token, $baseUrl);
            } catch (Exception $e) {
                if (
                    WialonErrorCodes::TOKEN_USER_NOT_FOUND === $e->getCode()
                    || WialonErrorCodes::ACCESS_DENIED === $e->getCode()
                ) {
                    $integration = Integration::find($integration['id']);

                    dispatch(new DeactivateIntegrationJob(
                        $integration->getConnectionName(),
                        $integration,
                    ));
                }

                throw $e;
            }

            $arrReport = $this->wialonService->makeRequest($svcType, $jsonParams);

            if (isset($arrReport['error']) || !isset($arrReport['items'])) {
                throw new Exception('error: ' . $arrReport['error'] . ', svc: ' . $svcType . ' , params: ' . $jsonParams . ', url: ' . $baseUrl);
            }

            $reportItems = $arrReport['items'];
            $newUnits = array_filter($reportItems, function ($reportItem) use ($currentUnits) {
                return isset($reportItem['uid']) && strlen($reportItem['uid']) > 0 && !in_array($reportItem['uid'], $currentUnits);
            });
            $newUnits = array_values($newUnits);

            $newUnitsToInsert = array_map(function ($unit) use ($organizationId) {
                $length = 0;

                $lengthSensor = $this->getUnitSensor($unit, 'custom', 'lenght');

                if (isset($lengthSensor, $lengthSensor['p'])) {
                    $matches = [];
                    preg_match('/[0-9]+/', $lengthSensor['p'], $matches);

                    if (count($matches) > 0) {
                        $length = intval(current($matches));
                    }
                }

                return [
                    'organization_id' => $organizationId,
                    'name' => $unit['nm'],
                    'wialon_unit_imei' => $unit['uid'],
                    'wialon_unit_id' => $unit['id'],
                    'type' => 'pivot',
                    'length' => $length,
                ];
            }, $newUnits);

            $allUnits = array_merge($allUnits, $newUnitsToInsert);
        }

        $allUnits = array_unique($allUnits, SORT_REGULAR);
        DB::table('su_irrigation_units')->insert($allUnits);
    }

    /**
     * @throws Exception
     */
    public function storeFormattedIrrigationUnitReport(string $tmpTableName, int $organizationId)
    {
        DB::beginTransaction();

        try {
            $tmpTableExists = Schema::hasTable($tmpTableName);

            if (!$tmpTableExists) {
                return;
            }

            $currentData = CurrentIrrigationPlatform::formatCurrentIrrigationUnitReportByState($tmpTableName)->get()->pluck('current_data')->first();
            $geoJsonData = CurrentIrrigationPlatform::formatCurrentIrrigationUnitReportByPlatformGeoJSON($tmpTableName, $organizationId);

            $geoJsonData = $geoJsonData->get()->map(function ($item) {
                return (array) $item;
            })->toArray();

            $geoJsonData = reset($geoJsonData);
            $geoJson = $geoJsonData['geojson'];

            CurrentIrrigationPlatform::updateOrCreate(
                ['organization_id' => $organizationId],
                ['geojson' => json_decode($geoJson), 'current_data' => $currentData]
            );

            DB::statement('DROP TABLE public.' . $tmpTableName);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            DB::statement('DROP TABLE public.' . $tmpTableName);

            Sentry::captureException($e);

            throw $e;
        }
    }

    private function getUnitSensor($unit, $sensorType, $sensorName)
    {
        $sensor = null;

        if (!isset($unit['sens']) || (isset($unit['sens']) && 0 === count($unit['sens']))) {
            return $sensor;
        }

        $sensors = $unit['sens'];

        $sensor = array_filter($sensors, function ($sensor) use ($sensorType, $sensorName) {
            if (!isset($sensor['t']) || !isset($sensor['n'])) {
                return false;
            }

            return (bool) ($sensor['t'] == $sensorType && $sensor['n'] == $sensorName);
        });

        if (isset($sensor) && count($sensor) > 0) {
            $sensor = current($sensor);
        }

        return $sensor;
    }
}
