<?php

namespace App\Services\Irrigation;

use App\Models\IrrigationDataRaw;
use App\Models\IrrigationEvent;
use App\Models\IrrigationEventPlot;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class IrrigationDataRawService
{
    /**
     * @throws Exception
     */
    public function addContent(string $tmpTableName, int $organizationId)
    {
        DB::beginTransaction();

        try {
            $tmpTableExists = Schema::hasTable($tmpTableName);

            if (!$tmpTableExists) {
                return;
            }

            $query = $this->getContentQuery($tmpTableName, $organizationId);
            IrrigationDataRaw::insertIrrigationDataRaw($query);
            DB::statement("DROP TABLE IF EXISTS {$tmpTableName}");
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            DB::statement("DROP TABLE IF EXISTS {$tmpTableName}");

            throw $e;
        }
    }

    /**
     * @throws Exception
     */
    public function addIrrigationEvents(): array
    {
        DB::beginTransaction();

        try {
            $lastIrrigationEventId = IrrigationEvent::query()
                ->orderBy('id', 'desc')
                ->pluck('id')
                ->first();

            IrrigationDataRaw::createIrrigationEventsFromRawData();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            throw $e;
        }

        if ($lastIrrigationEventId) {
            return IrrigationEvent::query()
                ->where('id', '>', $lastIrrigationEventId)
                ->pluck('id')
                ->toArray();
        }

        return IrrigationEvent::all()
            ->pluck('id')
            ->toArray();
    }

    /**
     * @throws Exception
     */
    public function addContentEventsPlots(array $irrigationEventIds = [])
    {
        DB::beginTransaction();

        try {
            $eventsPlotsQuery = IrrigationEvent::getIrrigationEventsPlotsQuery($irrigationEventIds);
            IrrigationEventPlot::insertIrrigationEventPlot($eventsPlotsQuery);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            throw $e;
        }
    }

    public function deleteIrrDataRaw(string $date, ?int $wialonUnitId)
    {
        return IrrigationDataRaw::deleteIrrDataRaw($date, $wialonUnitId);
    }

    private function getContentQuery(string $tmpTableName, int $organizationId): Builder
    {
        $sqlRawStates = 'select
            ct.time,
            ct.unit::bigint unit,
            (ct.speed::numeric)::int speed,
            round(((ct.pressure::numeric)::numeric  / 14.503773773),1) pressure,
            case when ct.state::numeric = 1 and ct.angle notnull and ct.angle::numeric > 0 and ct.angle::numeric < 360 and ct.coordinates notnull 
                then ceil(round(ct.angle::numeric,0) / 5) 
                when ct.state::numeric = 0 and ct.alarm::numeric = 1 and ct.angle notnull and ct.angle::numeric > 0 and ct.angle::numeric < 360 and ct.coordinates notnull
                then ceil(round(ct.angle::numeric,0) / 5) /*show MISALIGNMENT_ALARM for valid angle and state Off*/
            else 1 /*show MISALIGNMENT_ALARM even if angle is not valid - assign artificial value to angle*/
                end segment_id,
            ct.angle::numeric angle,
            case
                when ct.alarm::numeric = 1 then \'' . IrrigationDataRaw::MISALIGNMENT_ALARM . '\'
                when ct.state::numeric = 1 and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1) and ct.pressure notnull and ct.pressure::numeric / 14.503773773 > 0 and ct.pressure::numeric / 14.503773773 < 0.7  
                     and (ct.speed::numeric)::int > 0 and ct.alarm::numeric = 0 and ct.angle::numeric between 0 and 360 and ST_IsValid(ct.coordinates) then \'' . IrrigationDataRaw::PRESSURE_ALARM . '\'
                when ct.state::numeric = 1 and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1) and ct.pressure notnull and ct.pressure::numeric / 14.503773773 = 0 
                     and (ct.speed::numeric)::int > 0 and ct.alarm::numeric = 0 and ct.angle::numeric between 0 and 360 and ST_IsValid(ct.coordinates) then \'' . IrrigationDataRaw::MOVEMENT . '\'
                when ct.state::numeric = 1 and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1) and ct.pressure notnull and ct.pressure::numeric / 14.503773773 >= 0.7 
                     and (ct.speed::numeric)::int > 0 and ct.alarm::numeric = 0 and ct.angle::numeric between 0 and 360 and ST_IsValid(ct.coordinates) then \'' . IrrigationDataRaw::IRRIGATION . '\'
            else \'' . IrrigationDataRaw::WARNING . '\'
            end state,
            
            case
                when ct.state::numeric = 1 and (ct.angle is null or ct.angle::numeric not between 0 and 360) then \'' . IrrigationDataRaw::WARNING_ANGLE . '\'
                when ct.state::numeric = 1 and (ct.speed is null or ct.speed = \'-----\') then \'' . IrrigationDataRaw::WARNING_SPEED . '\'
                when ct.state::numeric = 1 and (ct.pressure is null or ct.pressure = \'-----\') then  \'' . IrrigationDataRaw::WARNING_PRESSURE . '\'
                when ct.state::numeric = 1 and (ct.coordinates is null or not ST_IsValid(ct.coordinates)) then \'' . IrrigationDataRaw::WARNING_COORDINATES . '\'
                when ct.state::numeric = 1 and ((ct.fwd is null or ct.fwd = \'-----\' or ct.fwd::numeric = 0) and (ct.rwd is null or ct.rwd = \'-----\' or ct.rwd ::numeric = 0)) then \'' . IrrigationDataRaw::WARNING_DIRECTION . '\'
                when ct.state::numeric = 1 and ((ct.speed::numeric)::int = 0 and ct.pressure::numeric > 0) then \'' . IrrigationDataRaw::WARNING_READING_SENSORS . '\'
                when ct.state::numeric = 1 and ((ct.speed::numeric)::int = 0 and ct.pressure::numeric = 0 and ct.angle::numeric between 0 and 360) then \'' . IrrigationDataRaw::WARNING_READING_SENSORS . '\'
            end state_description_type,
            sip.centre centre,
            sip.id platform_id,
            ct.length::numeric, 
            case when
                ct.state::numeric = 1 
                and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1)
                and ct.pressure notnull 
                and ct.pressure::numeric / 14.503773773 >= 0.7 
                and (ct.speed::numeric)::int > 0
                and ct.alarm::numeric = 0
                and ct.angle::numeric between 0 and 360
                and ST_IsValid(ct.coordinates) then round((ct.rate::numeric*10))
            else null end as rate,
            ct.fwd::numeric as forward,
            ct.rwd::numeric as rearward,
            ct.alarm
        from
            crosstab($$
            select
                (time::text || wialon_unit_imei::text) rowid, "time", "coordinates", "wialon_unit_imei", "sensor" , "value"
            from
                ' . $tmpTableName . ' ps
            where
                sensor notnull
            order by
                2, 3 $$, $$
            values (\'pressure\'), (\'angle\'), (\'timer_speed\'), (\'state\'), (\'lenght\'), (\'rate\'), (\'fwd\'), (\'rev\'), (\'alarm\')$$) as 
            ct(rowid varchar, time timestamp, coordinates geometry, unit varchar, pressure varchar, angle varchar, speed varchar, state varchar, length varchar, rate varchar,
            fwd varchar, rwd varchar, alarm varchar)
            join su_irrigation_platforms sip on st_intersects(centre_buff, ct.coordinates) and sip.status = true
            where (ct.state::numeric = 0 and ct.alarm::numeric = 1) or ct.state::numeric = 1 /* - no Off state any more */
        order by
            ct.unit,
            ct.time';

        $sqlGroupData = 'SELECT 
            platform_id, 
           	centre, 
            state, 
            unit, 
            round(avg(pressure),1) pressure, 
            avg(speed)::int unit_speed,
            avg(angle)::int angle,
            segment_id, 
            avg(length) length,
            round(avg(rate)) rate, 
            MIN(time) AS start_time, 
            MAX(time) AS end_time,
            forward,
            rearward,
            state_description_type
            FROM (SELECT state, unit, speed, segment_id, pressure, angle, centre, platform_id, time, length, rate, forward, rearward, state_description_type, row_number() over(order by  unit, time, platform_id, segment_id) 
                             - row_number() over (partition by  unit,platform_id, segment_id, state order by  unit, time,platform_id, segment_id)as grp
              FROM (select *, abs(segment_id - lag(segment_id) over (order by  unit, time,platform_id, segment_id)) prev from raw_states) P where P.prev in (null,0,1)  /*Remove spikes from angle sensor*/
              ) T /* In entire subquery ordering should be same - unit, time,platform_id, segment_id */
            group by state, unit, platform_id, segment_id, centre, grp, forward, rearward, state_description_type
            order by unit, min(time)';

        return IrrigationDataRaw::from('group_data')
            ->withExpression('raw_states', $sqlRawStates)
            ->withExpression('group_data', $sqlGroupData)
            ->select(
                DB::raw('iu.id as irrigation_unit_id'),
                'start_time',
                'end_time',
                'platform_id',
                'unit_speed',
                'pressure',
                'angle',
                DB::raw('rate as water_rate'),
                DB::raw('a.geom as segment'),
                DB::raw('state::irrigation_events_types_enum as type'),
                'forward',
                'rearward',
                DB::raw('state_description_type::irrigation_events_state_description_type_enum')
            )
            ->join(DB::raw('gs_make_pivot_segments(centre , group_data.length::int) as a'), 'a.id', '=', 'segment_id')
            ->join('su_irrigation_units as iu', 'iu.wialon_unit_imei', '=', 'unit')
            ->where('iu.organization_id', '=', $organizationId)
            ->orderBy('unit')
            ->orderBy('start_time');
    }
}
