<?php

namespace App\Services\Irrigation;

use App;
use App\Exceptions\NotFoundException;
use App\Models\IrrigationEvent;
use App\Models\Organization;
use App\Services\Exports\ExportFactory;
use Exception;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class IrrigationReportService
{
    public function getReport(int $organizationId, array $filter, string $groupBy, array $orderBy): array
    {
        $query = $this->getReportQuery($organizationId, $filter, $groupBy, $orderBy);
        $report = $query->paginate($filter['limit']);
        $report->getCollection()->transform(function ($value) {
            return $value->report_json;
        });

        $firstColumnKey = $this->getGroupByMapped($groupBy)['jsonKey'];
        $columns = config('reports.IRRIGATION_COLUMNS');
        $firstColumn = array_splice($columns, array_search($firstColumnKey, array_column($columns, 'name')), 1);

        return [
            'total' => $report->total(),
            'rows' => $report->items(),
            'header' => array_merge($firstColumn, $columns),
            'footer' => $this->getReportTotalsQuery($organizationId, $filter)->get()->first()->toArray(),
        ];
    }

    /**
     * @param int $organizationId Organization Id
     * @param array $filter = array(
     *                      from        => int,
     *                      to          => int,
     *                      farmIds     => int[],
     *                      plotIds     => int[],
     *                      cropIds     => int[],
     *                      platformIds => int[],
     *                      types       => string[],
     *                      )
     * @param string $exportType The export file type ('pdf' or 'xls')
     * @param string $groupBy The name of the column to group by
     * @param array $orderBy Associative array where key is the column to be sorted and value is the sort order ('asc' or 'desc')
     *
     * @throws Exception
     *
     * @return string $tmpFilePath The path of the created report file
     */
    public function generateReport(string $exportType, int $organizationId, array $filter, string $groupBy, array $orderBy, array $columns = []): string
    {
        $reportData = $this->getReportQuery($organizationId, $filter, $groupBy, $orderBy)->pluck('report_json');

        if (0 === count($reportData)) {
            throw new NotFoundException('No data');
        }

        $firstColumn = $this->getGroupByMapped($groupBy)['jsonKey'];
        $fileName = uniqid('irrigation_report_', true) . '.' . $exportType;
        $reportTotalData = $this->getReportTotalsQuery($organizationId, $filter)->first()->toArray();
        $reportTotalData['total_' . $firstColumn] = trans('reportIrrigation.Total');

        if (!count($columns)) {
            $columns = array_column(config('reports.IRRIGATION_COLUMNS'), 'name');
        }

        $firstColumn = array_splice($columns, array_search($firstColumn, $columns), 1);
        $columns = array_merge($firstColumn, $columns);
        $organization = Organization::findOrFail($organizationId);
        $serviceProvider = $organization->serviceProvider;
        $serviceProviderLogo = $serviceProvider->logo;

        $exporter = ExportFactory::make($exportType);
        $exporter->export($reportData, $columns, $fileName, $reportTotalData, 'reportIrrigation', 'report_events', $serviceProviderLogo);

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return $fileName;
    }

    private function getReportQuery(int $organizationId, array $filter, string $groupBy, array $orderBy = []): EloquentBuilder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $lang = App::getLocale();

        $groupByConfig = $this->getGroupByMapped($groupBy);
        $groupColumn = $groupByConfig['groupColumn'];
        $groupContent = $groupByConfig['groupContent'];
        $groupFieldNullable = $groupByConfig['nullable'];

        $query = IrrigationEvent::getListQuery($organizationId, $filter)->groupBy($groupContent);

        if ($groupFieldNullable) {
            // This type of where in nullable db fields
            $query->whereNotNull($groupColumn);
        }

        $mainFields = $this->getMainFields($groupBy);

        $query->select(
            DB::RAW('COALESCE(round(sum(((ST_Area(su_irrigation_events.coverage)/ 1000)* 0.1)::NUMERIC), 3), 0)::int AS covered_area'),
            DB::raw("to_char(COALESCE(sum(date_trunc('minute', su_irrigation_events.end_time::timestamp + interval '30 seconds') - date_trunc('minute', su_irrigation_events.start_time::timestamp + interval '30 seconds')), '0 minutes')::INTERVAL, 'HH24:MI') AS duration"),
            DB::raw('avg(su_irrigation_events.water_rate)::int AS rate'),
            DB::raw('COALESCE(sum((su_irrigation_events.water_rate * (st_area(su_irrigation_events.coverage))/ 10000))::NUMERIC, 0)::int AS water_amount'),
            DB::raw(
                "json_build_object(
                'key', ROW_NUMBER () OVER ( ORDER BY {$groupColumn})::TEXT,
                {$mainFields},
                'covered_area', coalesce(round(sum((
                    CASE WHEN su_irrigation_events.type IN ('Irrigation', 'PressureAlarm')
                        THEN (iep.event_area)*{$areaCoef}
                        ELSE 0
                    END    
                )::numeric), 3), 0)::numeric,
		        'duration', to_char(COALESCE(sum(date_trunc('minute', su_irrigation_events.end_time::timestamp + interval '30 seconds') - date_trunc('minute', su_irrigation_events.start_time::timestamp + interval '30 seconds')), '0 minutes')::INTERVAL, 'HH24:MI'),
		        'rate', avg(
                    CASE WHEN su_irrigation_events.type = 'Irrigation' 
                        THEN COALESCE(su_irrigation_events.water_rate, 0) 
                        ELSE 0
                    END    
		        )::int,
		        'water_amount', coalesce(sum(( (
                    CASE WHEN su_irrigation_events.type = 'Irrigation' 
                        THEN COALESCE(su_irrigation_events.water_rate, 0) 
                        ELSE 0
                    END    
		        ) * (st_area(su_irrigation_events.coverage))/10000))::numeric, 0)::int,
                'children', 
                    json_agg(
                        json_build_object(
                            'key', concat(su_irrigation_events.platform_id, '-', su_irrigation_events.id),
                            'event_id', su_irrigation_events.id,
                            'event_plot_id', iep.id,
                            'date', su_irrigation_events.date,
                            'platform', ip.name,
                            'pivot', iu.name,
                            'farm', coalesce(f.name, ''),
                            'plot', coalesce(sp.name, ''),
                            'crop', coalesce(scc.crop_name_{$lang}, ''),
                            'type', su_irrigation_events.type,
                            'start_time', su_irrigation_events.start_time::time,
                            'end_time', su_irrigation_events.end_time::time,
                            'duration', to_char(COALESCE((date_trunc('minute', su_irrigation_events.end_time::timestamp + interval '30 seconds') - date_trunc('minute', su_irrigation_events.start_time::timestamp + interval '30 seconds')), '0 minutes')::interval, 'HH24:MI'),
                            'speed', coalesce(su_irrigation_events.unit_speed, 0),
                            'rate', (
                                CASE WHEN su_irrigation_events.type = 'Irrigation' 
                                    THEN COALESCE(su_irrigation_events.water_rate, 0) 
                                    ELSE 0
                                END    
                            ),
                            'pressure', coalesce(su_irrigation_events.pressure, 0),
                            'covered_area', coalesce(round((
                                CASE WHEN su_irrigation_events.type IN ('Irrigation', 'PressureAlarm')
                                    THEN (iep.event_area) * {$areaCoef}
                                    ELSE 0
                                END    
                            )::numeric, 3), 0),
                            'water_amount', coalesce((
                                CASE WHEN su_irrigation_events.type = 'Irrigation' 
                                    THEN COALESCE(su_irrigation_events.water_rate, 0) 
                                    ELSE 0
                                END    
                                 * (st_area(iep.geom))/10000
                            )::numeric(10,2), 0)
                        ) order by su_irrigation_events.date desc, su_irrigation_events.start_time desc
                    )
                ) as report_json"
            )
        );

        foreach ($orderBy as $column => $order) {
            $query->orderBy($column, $order);
        }

        return $query;
    }

    private function getReportTotalsQuery(int $organizationId, array $filter): EloquentBuilder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = IrrigationEvent::getListQuery($organizationId, $filter);

        $query->select(
            DB::raw("coalesce(round(sum(((iep.event_area)*{$areaCoef})::numeric), 3), 0)::numeric as total_covered_area"),
            DB::raw("to_char(COALESCE(sum(date_trunc('minute', su_irrigation_events.end_time::timestamp + interval '30 seconds') - date_trunc('minute', su_irrigation_events.start_time::timestamp + interval '30 seconds')), '0 minutes')::INTERVAL, 'HH24:MI') as total_duration"),
            DB::raw('coalesce(avg(su_irrigation_events.water_rate)::int, 0) as total_rate'),
            DB::raw('coalesce(sum((su_irrigation_events.water_rate * (st_area(su_irrigation_events.coverage))/10000))::numeric, 0)::int as total_water_amount')
        );

        return $query;
    }

    private function getGroupByMapped(string $GroupByFromUI): array
    {
        $platformGroupByConfig = ['groupColumn' => 'su_irrigation_events.platform_id', 'groupContent' => ['su_irrigation_events.platform_id'], 'nullable' => false, 'jsonKey' => 'platform'];
        $plotGroupByConfig = ['groupColumn' => 'iep.plot_id', 'groupContent' => ['iep.plot_id'], 'nullable' => true, 'jsonKey' => 'plot'];
        $cropGroupByConfig = ['groupColumn' => 'spc.crop_id', 'groupContent' => ['spc.crop_id'], 'nullable' => true, 'jsonKey' => 'crop'];
        $farmGroupByConfig = ['groupColumn' => 'f.id', 'groupContent' => ['f.id'], 'nullable' => true, 'jsonKey' => 'farm'];

        $groupByConfig = [
            'platform' => $platformGroupByConfig,
            'plot' => $plotGroupByConfig,
            'crop' => $cropGroupByConfig,
            'farm' => $farmGroupByConfig,
        ];

        return $groupByConfig[$GroupByFromUI] ?? $platformGroupByConfig;
    }

    private function getMainFields(string $groupBy): string
    {
        $mainFieldsTemplate = "
            'date', '', 
            'platform', '', 
            'pivot', '', 
            'farm', '', 
            'plot', '', 
            'crop', '', 
            'type', '', 
            'start_time', '',
            'end_time', '',
            'speed', '',
            'pressure', ''
        ";

        $mainFields = str_replace("'platform', ''", "'platform', max(ip.name)", $mainFieldsTemplate);

        if ('plot' === $groupBy) {
            $mainFields = str_replace("'plot', ''", "'plot', max(sp.name)", $mainFieldsTemplate);
        }
        if ('crop' === $groupBy) {
            $mainFields = str_replace("'crop', ''", "'crop', max(scc.crop_name)", $mainFieldsTemplate);
        }
        if ('farm' === $groupBy) {
            $mainFields = str_replace("'farm', ''", "'farm', max(f.name)", $mainFieldsTemplate);
        }

        return $mainFields;
    }
}
