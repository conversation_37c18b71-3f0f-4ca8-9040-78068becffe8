<?php

namespace App\Services\Irrigation;

use App\Models\IrrigationPlatform;
use Illuminate\Support\Facades\Response;

class IrrigationPlatformService
{
    public static function downloadCsvFile()
    {
        $fileName = 'irrigation_platform_report';

        $headers = [
            'Content-Encoding' => 'UTF-8',
            'Content-type' => 'text/csv',
            'charset' => 'utf-8',
            'Content-Disposition' => 'attachment; filename=' . $fileName . '.csv',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $reports = IrrigationPlatform::irrigationReport()->get()->toArray();

        if (!$reports) {
            return Response('No data', 204, $headers);
        }

        $translatedColumns = self::getTranslatedColumns();

        $callback = function () use ($reports, $translatedColumns) {
            $file = fopen('php://output', 'w');
            fputs($file, "\xEF\xBB\xBF");
            fputcsv($file, $translatedColumns);
            foreach ($reports as $report) {
                fputcsv($file, [
                    $report->farm_name,
                    $report->plot_name,
                    $report->crop_name,
                    $report->area,
                    $report->covered_area,
                    $report->total_water,
                    $report->pivot_name,
                    $report->avg_rate,
                    $report->dates,
                ]);
            }
            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    private static function getTranslatedColumns(): array
    {
        $columns = [
            'farmName',
            'plotName',
            'cropName',
            'area',
            'irrigatedArea',
            'totalWater',
            'pivotName',
            'avgRate',
            'irrigationDates',
        ];

        $translatedColumns = [];
        foreach ($columns as $column) {
            $translatedColumns[] = trans('irrigationReport.' . $column);
        }

        return $translatedColumns;
    }
}
