<?php

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Services\GuidanceLines;

use App\Exceptions\NotFoundException;
use App\Models\GuidanceLine;
use App\Services\Common\TFCommonService;
use Exception;
use Illuminate\Support\Facades\DB;

class GuidanceLineService
{
    private $tfCommonService;

    public function __construct(TFCommonService $tfCommonService)
    {
        $this->tfCommonService = $tfCommonService;
    }

    public function exportGuidanceLines(int $organizationId, array $guidanceLineIds)
    {
        $params = $this->getGuidanceLinesExportParams($organizationId, $guidanceLineIds);
        $response = $this->tfCommonService->exportForTrimble($params);

        $tmpfilePath = sys_get_temp_dir() . '/' . uniq<PERSON>('guidance_lines_', true) . '.zip';

        if (!file_put_contents($tmpfilePath, $response)) {
            throw new Exception('Error saving guidance lines file for export!');
        }

        return $tmpfilePath;
    }

    private function getGuidanceLinesExportParams(int $organizationId, array $guidanceLineIds = [])
    {
        $plotGuidanceLines = GuidanceLine::from('su_guidance_line as gl')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'gl.plot_id')
            ->join('su_farms AS sf', 'sf.id', '=', 'sp.farm_id')
            ->join('su_organizations AS so', 'so.id', '=', 'sf.organization_id')
            ->select(
                DB::raw('cyrillic_to_latin(so.name) as client_name'),
                DB::raw('cyrillic_to_latin(sf.name) as farm_name'),
                DB::raw("JSONB_BUILD_OBJECT(
                    'name', cyrillic_to_latin(sp.\"name\"),
                    'geojson',  JSONB_BUILD_OBJECT(
                        'type', 'Feature',
                        'properties', JSONB_BUILD_OBJECT(
                            'id', sp.gid,
                            'name', cyrillic_to_latin(sp.\"name\"),
                            'area', round(sp.\"area\"::numeric, 3)
                        ),
                        'geometry', ST_AsGeoJson(ST_Transform(sp.geom, 4326), 9, 2)::JSONB -- always add crs
                     ),
                    'ab_lines_geojson', JSONB_BUILD_OBJECT(
                        'type', 'FeatureCollection',
                        'features',COALESCE(
                            JSONB_AGG(
                                JSONB_BUILD_OBJECT(
                                    'type', 'Feature',
                                    'properties', JSONB_BUILD_OBJECT(
                                        'id', gl.gid,
                                        'name', cyrillic_to_latin(gl.\"name\")
                                    ),
                                    'geometry', ST_AsGeoJson(
                                        ST_Transform(
                                            CASE WHEN
                                                90 <= degrees(ST_Azimuth(ST_StartPoint(st_transform(gl.geom, 4326)),  ST_EndPoint(st_transform(gl.geom, 4326)))) AND
                                                270 >= degrees(ST_Azimuth(ST_StartPoint(st_transform(gl.geom, 4326)),  ST_EndPoint(st_transform(gl.geom, 4326))))
                                            THEN
                                                CASE WHEN gl.shift < 0
                                                THEN
                                                    ST_OffsetCurve(gl.geom, ABS(gl.shift), 'quad_segs=4 join=round') -- shift right 
                                                ELSE
                                                    st_reverse(ST_OffsetCurve(gl.geom, ABS(gl.shift) * -1, 'quad_segs=4 join=round')) -- shift left, negative value => reverse
                                                END
                                            ELSE
                                                CASE WHEN gl.shift < 0
                                                THEN
                                                    ST_OffsetCurve(gl.geom, ABS(gl.shift), 'quad_segs=4 join=round') -- shift left
                                                ELSE
                                                    st_reverse(ST_OffsetCurve(gl.geom, ABS(gl.shift) * -1, 'quad_segs=4 join=round')) -- shift right, negative value => reverse 
                                                END
                                            END -- shifted ab line, check ST_OffsetCurve() docs
                                        , 4326), 9, 2)::JSONB -- always add crs
                                )
                            ) FILTER (WHERE gl.\"type\" = 'AB line'),
                            '[]'::JSONB
                        )
                    ),
                    'headlands_geojson', JSONB_BUILD_OBJECT(
                        'type', 'FeatureCollection',
                        'features',COALESCE(
                            JSONB_AGG(
                                JSONB_BUILD_OBJECT(
                                    'type', 'Feature',
                                    'properties', JSONB_BUILD_OBJECT(
                                        'id', gl.gid,
                                        'name', cyrillic_to_latin(gl.\"name\")
                                    ),
                                    'geometry', ST_AsGeoJson(ST_Transform(gl.geom, 4326), 9, 2)::JSONB -- always add crs
                                )
                            ) FILTER (WHERE gl.\"type\" = 'Headland'),
                            '[]'::JSONB
                        )
                    )
                ) AS guidance_lines")
            )
            ->where('so.id', $organizationId);

        if (isset($guidanceLineIds) && count($guidanceLineIds) > 0) {
            $plotGuidanceLines->whereIn('gl.gid', $guidanceLineIds);
        }

        $plotGuidanceLines->groupBy(
            'so.id',
            'sf.id',
            'sp.gid'
        );

        $farmGuidanceLines = GuidanceLine::from('plot_guidance_lines AS pgl')
            ->select(
                'pgl.client_name',
                DB::raw("
                    JSONB_BUILD_OBJECT(
                        'farm', pgl.farm_name,
                        'plots', JSONB_AGG(pgl.guidance_lines)
                    ) AS guidance_lines
                ")
            )
            ->groupBy(
                'pgl.client_name',
                'pgl.farm_name'
            );

        $query = GuidanceLine::from('farm_guidance_lines AS fgl')
            ->withExpression('plot_guidance_lines', $plotGuidanceLines)
            ->withExpression('farm_guidance_lines', $farmGuidanceLines)
            ->select(
                'fgl.client_name',
                DB::raw('JSONB_AGG(fgl.guidance_lines)::JSONB AS guidance_lines')
            )
            ->groupBy('fgl.client_name');

        $guidanceLines = $query->first();

        if (!$guidanceLines) {
            throw new NotFoundException('No guidance lines found!');
        }

        $guidanceLines = $guidanceLines->toArray();
        $guidanceLines['guidance_lines'] = json_decode($guidanceLines['guidance_lines'], true);

        return $guidanceLines;
    }
}
