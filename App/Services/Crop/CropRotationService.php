<?php

namespace App\Services\Crop;

use App\Models\Order;
use App\Models\Plot;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class CropRotationService
{
    public function getCropRotationData($user, $filters, $sort, $page, $limit): array
    {
        $getPlotsForCropRotation = $this->getPlotsForCropRotationQuery($filters);
        $farmingYearsWithOrdersQuery = $this->getFarmingYearsQuery($filters)->select(
            'su_satellite_orders.year as farm_year',
            'su_satellite_orders.id as order'
        );
        $groupPlotsByFarmingYearOrdersAndCrops = $this->groupPlotsByFarmingYearOrdersAndCrops();
        $filteredUuid = $this->filteredUuids($filters);
        $cropsByPlots = $this->cropsByPlots($sort);

        $rows = DB::query()->fromSub(function ($query) use ($page, $limit) {
            $query->select(
                'gid',
                'uuid',
                'name',
                'geom_json',
                'area',
                'farm_id',
                'farm_years'
            )
                ->from('crops_by_plots')
                ->forPage($page, $limit);
        }, 'cbp')->selectRaw('jsonb_agg(cbp) as "crop_rotation_result"');

        $total = DB::table('crops_by_plots')->selectRaw('count(crops_by_plots.gid)');

        $results = Plot::from('rows')
            ->withExpression('plots', $getPlotsForCropRotation)
            ->withExpression('farming_years_with_orders', $farmingYearsWithOrdersQuery)
            ->withExpression('group_plots_by_year_orders_and_crops', $groupPlotsByFarmingYearOrdersAndCrops)
            ->withExpression('filtered_uuids', $filteredUuid)
            ->withExpression('crops_by_plots', $cropsByPlots)
            ->withExpression('rows', $rows)
            ->withExpression('total', $total)
            ->selectRaw('rows.crop_rotation_result, total.count as total')
            ->crossJoin('total')
            ->first()->toArray();

        $farmingYearsQuery = $this->getFarmingYearsQuery($filters)->selectRaw(
            'distinct su_satellite_orders.year as farm_year'
        );
        $cropDataForChart = $this->getCropsEChartDataForCropRotationQuery($user, $filters);

        $groupCropDataForChartByFarmYear = DB::table('crop_data_for_chart')
            ->select(
                'name',
                DB::raw('round(sum(area)) as area'),
                'crop_id',
                'itemStyle',
                'farm_year'
            )
            ->groupBy(
                'name',
                'crop_id',
                'itemStyle',
                'farm_year'
            );

        $seriesChartData = DB::table('group_crop_data_for_chart_by_farm_year')
            ->select(
                'farm_year',
                DB::raw("
                    json_build_object(
                        'name', name,
                        'type', 'bar',
                        'stack', 'total',
                        'label', jsonb_build_object(
                                'show', false	
                                ),
                        'emphasis', jsonb_build_object(
                                'focus', 'none'
                                ),
                        'data', jsonb_agg(area),
                        'id', crop_id,
                        'itemStyle', \"itemStyle\"
                    ) as group_chart_data
                ")
            )
            ->groupBy(
                'farm_year',
                'name',
                'crop_id',
                'itemStyle'
            )
            ->orderBy('name', 'desc');

        $plannedCrops = $this->getPrimaryOrAlternativeCropsForChartFilterQuery($filters, true);
        $alternativeCrops = $this->getPrimaryOrAlternativeCropsForChartFilterQuery($filters, false);

        $header = Plot::from('farming_years')
            ->withExpression('farming_years', $farmingYearsQuery)
            ->withExpression('crop_data_for_chart', $cropDataForChart)
            ->withExpression('group_crop_data_for_chart_by_farm_year', $groupCropDataForChartByFarmYear)
            ->withExpression('series_chart_data', $seriesChartData)
            ->withExpression('planned_crops', $plannedCrops)
            ->withExpression('alternative_crops', $alternativeCrops)
            ->select(
                'farming_years.farm_year',
                DB::raw('jsonb_agg(series_chart_data.group_chart_data) filter(where series_chart_data.farm_year notnull) as chart_series'),
                DB::raw("jsonb_build_object('planned_crops', pc.crops, 'alternative_crops',ac.crops) as crops_filter")
            )
            ->leftJoin('series_chart_data', 'series_chart_data.farm_year', '=', 'farming_years.farm_year')
            ->leftJoin('planned_crops AS pc', 'pc.farm_year', '=', 'series_chart_data.farm_year')
            ->leftJoin('alternative_crops AS ac', 'ac.farm_year', '=', 'series_chart_data.farm_year')
            ->groupBy('farming_years.farm_year', 'pc.crops', 'ac.crops')
            ->orderBy('farming_years.farm_year')
            ->get();

        return [
            'total' => $results['total'],
            'header' => $header,
            'rows' => $results['crop_rotation_result'] ?? [],
        ];
    }

    private function getCropsEChartDataForCropRotationQuery($user, $filters)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $cropFieldName = Config::get('globals.CROP_NAME');
        $noCropTxt = trans('general.no_crop');

        $cropQuery = Plot::select(
            DB::raw("
                CASE WHEN spc.crop_id NOTNULL
                    THEN 
                        cc.{$cropFieldName}
                    ELSE
                        '{$noCropTxt}'
                    END
                AS name
            "),
            DB::raw("su_satellite_plots.area*{$areaCoef} AS area"),
            'spc.crop_id',
            DB::raw("
                JSONB_BUILD_OBJECT(
                    'color', CASE WHEN spc.crop_id NOTNULL
                        THEN 
                            cc.crop_color
                        ELSE
                            '#D9D9D9'
                        END
                ) AS \"itemStyle\",
                so.year as farm_year
            ")
        )
            ->join('su_farms_users AS fu', 'fu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms AS f', 'f.id', '=', 'fu.farm_id')
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid');
                $join->on('spc.year', '=', 'so.year');
            })
            ->leftJoin('su_crop_codes AS cc', 'cc.id', '=', 'spc.crop_id')
            ->where([
                ['fu.user_id', $user->id],
                ['f.organization_id', $user->lastChosenOrganization->id],
                ['so.type', Order::TYPE_INDEX],
            ])
            ->whereIn('so.status', [Order::STATUS_PROCESSED, Order::STATUS_PAID, Order::STATUS_NO_TILE, Order::STATUS_PROCESSING]);

        $this->filterQueries($filters, $cropQuery);

        return $cropQuery;
    }

    private function getPlotsForCropRotationQuery(array $filters)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $queryBuilder = Plot::selectRaw(
            "su_satellite_plots.gid,
            su_satellite_plots.uuid,
            su_satellite_plots.name,
            su_satellite_plots.farm_id,
            ROUND((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area,
            ST_AsGeoJSON(ST_Transform(su_satellite_plots.geom,4326)) as geom_json"
        )
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->where('su.last_chosen_organization_id', '=', Auth::user()->lastChosenOrganization->id)
            ->where('su.id', '=', Auth::user()->id);

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $queryBuilder->whereIn('su_satellite_plots.gid', $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $queryBuilder->whereIn('su_satellite_plots.farm_id', $filters['farm_ids']);
        }

        $queryBuilder
            ->groupBy('su_satellite_plots.gid', 'su_satellite_plots.uuid', 'su_satellite_plots.name', 'su_satellite_plots.farm_id');

        return $queryBuilder;
    }

    private function getFarmingYearsQuery(array $filters)
    {
        $queryBuilder = Order::where('su_satellite_orders.organization_id', Auth::user()->lastChosenOrganization->id)
            ->whereIn('su_satellite_orders.status', [Order::STATUS_PROCESSED, Order::STATUS_PAID, Order::STATUS_NO_TILE, Order::STATUS_PROCESSING])
            ->where('su_satellite_orders.type', '=', Order::TYPE_INDEX)
            ->orderBy('su_satellite_orders.year');

        if (isset($filters['farm_years']) && $filters['farm_years']) {
            $queryBuilder->whereIn('su_satellite_orders.year', $filters['farm_years']);
        }

        return $queryBuilder;
    }

    private function groupPlotsByFarmingYearOrdersAndCrops()
    {
        $cropFieldName = Config::get('globals.CROP_NAME');
        $queryBuilder = DB::table('farming_years_with_orders')
            ->select(
                'farming_years_with_orders.farm_year',
                DB::raw("coalesce (jsonb_agg(distinct sopr.order_id) filter(where sopr.order_id notnull),'[]'::jsonb) as subscriptions"),
                'plots.gid',
                'plots.uuid',
                'plots.name',
                'plots.geom_json',
                'plots.farm_id',
                'plots.area',
                'sspc_prime.crop_id as crop_prime_id',
                DB::raw("
                    case
                        when sspc_prime.crop_id is null then
                            null
                    else
                        jsonb_build_object(
                            'id', sspc_prime.crop_id,
                            'plot_crop_rel_id', sspc_prime.id,
                            'crop_name', scc_prime.{$cropFieldName},
                            'color', scc_prime.crop_color,
                            'category_id', scct_prime.id,
                            'category_name', scct_prime.category,
                            'hybrid_id', sch_prime.id,
                            'hybrid_name', sch_prime.name,
                            'sowing_date', sspc_prime.from_date,
                            'harvest_date', sspc_prime.to_date,
                            'is_primary', sspc_prime.is_primary,
                            'irrigated', sspc_prime.irrigated,
                            'days_since_sowing', now()::date-sspc_prime.from_date::date
                        )
                    end as crop_prime
                "),
                DB::raw("
                     coalesce (jsonb_agg(distinct jsonb_build_object(
                                        'id', sspc_alt.crop_id,
                                        'plot_crop_rel_id', sspc_alt.id,
                                        'crop_name', scc_alt.{$cropFieldName},
                                        'color', scc_alt.crop_color,
                                        'category_id', scct_alt.id,
                                        'category_name', scct_alt.category,
                                        'hybrid_id', sch_alt.id,
                                        'hybrid_name', sch_alt.name,
                                        'sowing_date', sspc_alt.from_date,
                                        'harvest_date', sspc_alt.to_date,
                                        'is_primary', sspc_alt.is_primary,
                                        'irrigated', sspc_alt.irrigated,
                                        'days_since_sowing', now()::date-sspc_alt.from_date::date
                                    )
                                )
                    filter (where(sspc_alt.crop_id notnull)),'[]'::jsonb) as crops_alt
                "),
                DB::raw('array_agg(distinct sspc_alt.crop_id) filter (where (sspc_alt.crop_id notnull)) as crops_alt_ids')
            )
            ->crossJoin('plots')
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', function ($join) {
                $join->on('sopr.plot_id', '=', 'plots.gid')
                    ->on('sopr.order_id', '=', 'farming_years_with_orders.order');
            })
            ->leftJoin('su_satellite_plots_crops AS sspc_prime', function ($join) {
                $join->on('sspc_prime.plot_id', '=', 'plots.gid')
                    ->on('sspc_prime.year', '=', 'farming_years_with_orders.farm_year')
                    ->whereRaw('sspc_prime.is_primary');
            })
            ->leftJoin('su_crop_codes AS scc_prime', 'scc_prime.id', '=', 'sspc_prime.crop_id')
            ->leftJoin('su_crop_categories as scct_prime', 'scct_prime.id', '=', 'sspc_prime.category_id')
            ->leftJoin('su_crop_hybrid as sch_prime', 'sch_prime.id', '=', 'sspc_prime.hybrid_id')
            ->leftJoin('su_satellite_plots_crops AS sspc_alt', function ($join) {
                $join->on('sspc_alt.plot_id', '=', 'plots.gid')
                    ->on('sspc_alt.year', '=', 'farming_years_with_orders.farm_year')
                    ->whereRaw('not sspc_alt.is_primary');
            })
            ->leftJoin('su_crop_codes AS scc_alt', 'scc_alt.id', '=', 'sspc_alt.crop_id')
            ->leftJoin('su_crop_categories as scct_alt', 'scct_alt.id', '=', 'sspc_alt.category_id')
            ->leftJoin('su_crop_hybrid as sch_alt', 'sch_alt.id', '=', 'sspc_alt.hybrid_id')
            ->groupBy(
                'farming_years_with_orders.farm_year',
                'plots.gid',
                'plots.uuid',
                'plots.name',
                'plots.geom_json',
                'plots.farm_id',
                'plots.area',
                'sspc_prime.crop_id',
                'sspc_prime.is_primary',
                "scc_prime.{$cropFieldName}",
                'scc_prime.crop_color',
                'scct_prime.id',
                'scct_prime.category',
                'sch_prime.id',
                'sch_prime.name',
                'sspc_prime.from_date',
                'sspc_prime.to_date',
                'sspc_prime.is_primary',
                'sspc_prime.irrigated',
                'sspc_prime.id'
            )
            ->orderBy('farming_years_with_orders.farm_year');

        return $queryBuilder;
    }

    private function filteredUuids(array $filters)
    {
        $queryBuilder = DB::table('group_plots_by_year_orders_and_crops')
            ->select('group_plots_by_year_orders_and_crops.uuid');

        if ($filters['crops_filter']) {
            foreach ($filters['crops_filter'] as $farmYearIndex => $filter) {
                $queryBuilder->join("group_plots_by_year_orders_and_crops AS {$farmYearIndex}", function ($join) use ($farmYearIndex, $filter) {
                    if (0 === $farmYearIndex) {
                        $join->on("{$farmYearIndex}.uuid", '=', 'group_plots_by_year_orders_and_crops.uuid')
                            ->where("{$farmYearIndex}.farm_year", '=', $filter['farm_year']);
                    } else {
                        $prevFarmYearIndex = $farmYearIndex - 1;
                        $join->on("{$farmYearIndex}.uuid", '=', "{$prevFarmYearIndex}.uuid")
                            ->where("{$farmYearIndex}.farm_year", '=', $filter['farm_year']);
                    }

                    if (isset($filter['crops']['planned_crops']) && $filter['crops']['planned_crops']) {
                        $cropIdsNullValueIdx = array_search(null, $filter['crops']['planned_crops']);

                        if (false !== $cropIdsNullValueIdx) {
                            unset($filter['crops']['planned_crops'][$cropIdsNullValueIdx]);

                            $join->where(function ($plannedCropsJoin) use ($farmYearIndex, $filter) {
                                $plannedCropsJoin->whereIn("{$farmYearIndex}.crop_prime_id", $filter['crops']['planned_crops'])
                                    ->orWhere("{$farmYearIndex}.crop_prime_id", null);
                            });
                        } else {
                            $join->whereIn("{$farmYearIndex}.crop_prime_id", $filter['crops']['planned_crops']);
                        }
                    }

                    if (isset($filter['crops']['alternative_crops']) && $filter['crops']['alternative_crops']) {
                        $alternativeCrops = implode(',', $filter['crops']['alternative_crops']);
                        $cropIdsNullValueIdx = array_search(null, $filter['crops']['alternative_crops']);

                        if (false !== $cropIdsNullValueIdx) {
                            unset($filter['crops']['alternative_crops'][$cropIdsNullValueIdx]);

                            $join->where(function ($plannedCropsJoin) use ($farmYearIndex, $alternativeCrops) {
                                $plannedCropsJoin->whereRaw("'{" . $alternativeCrops . "}'::int[] && \"{$farmYearIndex}\".\"crops_alt_ids\"")
                                    ->orWhere("{$farmYearIndex}.crops_alt_ids", null);
                            });
                        } else {
                            $join->whereRaw("'{" . $alternativeCrops . "}'::int[] && \"{$farmYearIndex}\".\"crops_alt_ids\"");
                        }
                    }
                });
            }
        }

        return $queryBuilder->groupBy('group_plots_by_year_orders_and_crops.uuid');
    }

    private function cropsByPlots(array $sort)
    {
        $queryBuilder = DB::table('group_plots_by_year_orders_and_crops')
            ->select(
                'gid',
                'uuid',
                'name',
                DB::raw('geom_json::json'),
                'area',
                'farm_id',
                DB::raw("
                jsonb_agg(
                        jsonb_build_object(
                            'farm_year', farm_year,
                            'crops', crop_prime || crops_alt,
                            'subscriptions', subscriptions
                        ) 
                   order by farm_year ) farm_years
                ")
            )
            ->whereIn('uuid', function ($query) {
                $query->select('uuid')
                    ->from('filtered_uuids');
            })
            ->groupBy(
                'gid',
                'uuid',
                'name',
                'geom_json',
                'area',
                'area',
                'farm_id'
            );

        if (!count($sort)) {
            $queryBuilder->orderBy('gid');
        }

        foreach ($sort as $column => $order) {
            $queryBuilder->orderBy($column, $order);
        }

        return $queryBuilder;
    }

    private function getPrimaryOrAlternativeCropsForChartFilterQuery(array $filters, bool $isPrimary)
    {
        $cropFieldName = Config::get('globals.CROP_NAME');
        $noCropTxt = trans('general.no_crop');

        $query = Plot::select(
            'so.year as farm_year',
            DB::raw("coalesce(
                        jsonb_agg(distinct
                            case when sspc.crop_id notnull
                                then 
                                    jsonb_build_object(
                                        'id', sspc.crop_id,
                                        'crop_name', {$cropFieldName},
                                        'color', scc.crop_color,
                                        'is_primary', sspc.is_primary
                                    )
                            else 
                                jsonb_build_object(
                                    'id', null,
                                    'crop_name', '{$noCropTxt}',
                                    'color', '#D9D9D9',
                                    'is_primary', '{$isPrimary}'
                                )
                        end),
                        '[]'
                ) as crops")
        )
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', 'su_satellite_plots.uuid')
            ->leftJoin('su_satellite_orders AS so', function ($join) {
                $join->on('so.uuid', '=', 'sopr.order_uuid');
                $join->where('so.type', '=', Order::TYPE_INDEX);
                $join->whereRaw('so.organization_id=su.last_chosen_organization_id');
            })
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) use ($isPrimary) {
                $join->on('sspc.plot_id', '=', 'su_satellite_plots.gid');
                $join->whereRaw('sspc.year = so.year');
                $join->where('sspc.is_primary', (int)$isPrimary);
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->where([
                ['su.last_chosen_organization_id', Auth::user()->lastChosenOrganization->id],
                ['su.id', Auth::user()->id],
            ])
            ->whereIn('so.status', [Order::STATUS_PROCESSED, Order::STATUS_PAID, Order::STATUS_NO_TILE, Order::STATUS_PROCESSING]);

        $this->filterQueries($filters, $query);

        $query->groupBy('so.year')
            ->orderBy('so.year');

        return $query;
    }

    private function filterQueries($filters, $cropQuery): void
    {
        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $cropQuery->whereIn('su_satellite_plots.gid', $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $cropQuery->whereIn('su_satellite_plots.farm_id', $filters['farm_ids']);
        }

        if (isset($filters['farm_years']) && $filters['farm_years']) {
            $cropQuery->whereIn('so.year', $filters['farm_years']);
        }
    }
}
