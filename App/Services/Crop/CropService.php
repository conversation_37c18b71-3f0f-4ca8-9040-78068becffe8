<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 5/29/2020
 * Time: 8:25 AM.
 */

namespace App\Services\Crop;

use App\Models\Plot;
use App\Models\PlotCrop;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class CropService
{
    private $plot;

    public function __construct(Plot $plot)
    {
        $this->plot = $plot;
    }

    public function getCrops($user, $year, $farmIds, $plotIds)
    {
        $cropFieldName = Config::get('globals.CROP_NAME');
        $noCropTxt = trans('general.no_crop');

        $cropQuery = $this->plot->cropQuery($user, $year, $farmIds, $plotIds);

        return $cropQuery->select(
            'spc.crop_id as id',
            DB::raw("
                CASE WHEN spc.crop_id NOTNULL
                    THEN 
                        cc.{$cropFieldName}
                    ELSE
                        '{$noCropTxt}'
                    END
                AS name
            "),
            'cc.crop_code AS code'
        )->orderBy('name', 'DESC')->distinct()->get()->toArray();
    }

    public function getEChartCropAnalyses($user, $year, $farmIds, $plotIds)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $cropFieldName = Config::get('globals.CROP_NAME');
        $noCropTxt = trans('general.no_crop');

        $cropQuery = $this->plot->cropQuery($user, $year, $farmIds, $plotIds);
        $chartDataQuery = $cropQuery->select(
            DB::raw("
                CASE WHEN spc.crop_id NOTNULL
                    THEN 
                        cc.{$cropFieldName}
                    ELSE
                        '{$noCropTxt}'
                    END
                AS name
            "),
            DB::raw("sp.area*{$areaCoef} AS area"),
            'spc.crop_id',
            DB::raw("
                JSONB_BUILD_OBJECT(
                    'color', CASE WHEN spc.crop_id NOTNULL
                        THEN 
                            cc.crop_color
                        ELSE
                            '#D9D9D9'
                        END
                ) AS \"itemStyle\"
            ")
        );

        return Plot::from('chart_data')
            ->withExpression('chart_data', $chartDataQuery)
            ->select(
                'name',
                DB::raw('round(sum(area)) as value'),
                'crop_id',
                'itemStyle'
            )
            ->groupBy(
                'name',
                'crop_id',
                'itemStyle'
            )
            ->get()->toArray();
    }

    public function setIrrigatedFromPlatform(array $param)
    {
        $arrPlotIds = Plot::getIrrigatedPlotsQueryBy($param)->select(DB::raw('distinct su_satellite_plots.gid'))->get()->pluck('gid')->toArray();
        if (0 === count($arrPlotIds)) {
            return;
        }
        $arrParam['plot_ids'] = $arrPlotIds;
        PlotCrop::setIrrigatedBy($arrParam, true);
    }

    /**
     * Get crop history by plot.
     *
     * @param int $plotId The plot's id
     * @param string $lang The crop language (e.g. 'en', 'bg' etc.)
     */
    public function getCropHistoryByPlot(int $plotId, string $lang = 'en'): array
    {
        return PlotCrop::getCropHistoryByPlot($plotId, $lang);
    }
}
