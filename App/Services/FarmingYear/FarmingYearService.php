<?php

namespace App\Services\FarmingYear;

use App\Classes\CMS\FarmingYearService as CMSFarmingYearService;
use App\Models\Organization;
use App\Models\StaticModels\FarmingYear;
use Illuminate\Support\Collection;

class FarmingYearService
{
    /**
     * @var CMSFarmingYearService
     */
    private $cmsFarmingYearService;

    public function __construct(CMSFarmingYearService $cmsFarmingYearService)
    {
        $this->cmsFarmingYearService = $cmsFarmingYearService;
    }

    public function getFarmingYearsByOrganization(Organization $organization): Collection
    {
        $month = (int)date('m');
        $currentYear = (int)date('Y');

        if ($month > 9) {
            $currentYear++;
        }

        $farmingYearsFromCMS = $this->cmsFarmingYearService->getFarmingYearsByOrganization($organization->id);
        $farmingYearsRangeResults = FarmingYear::byOrganization($organization, $currentYear);
        $farmingYearsRange = $farmingYearsFromCMS->merge($farmingYearsRangeResults)->uniqueStrict('year');

        if (!$farmingYearsRange->count()) {
            return collect([[
                'title' => $currentYear,
                'id' => $currentYear,
                'default' => true,
                'year' => $currentYear,
                'from_date' => ($currentYear - 1) . '-10-01',
                'to_date' => $currentYear . '-09-30',
                'farming_year' => ($currentYear - 1) . '/' . $currentYear,
            ]]);
        }

        $farmingYearsRange = $farmingYearsRange->sortBy('year')->values();
        if (!$farmingYearsRange->where('default', true)->first()) {
            $last = $farmingYearsRange->pop();
            $last['default'] = true;
            $farmingYearsRange->push($last);
        }

        return $farmingYearsRange;
    }
}
