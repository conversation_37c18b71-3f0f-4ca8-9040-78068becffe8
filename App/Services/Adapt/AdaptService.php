<?php

namespace App\Services\Adapt;

use App\Classes\AdaptConverter\TFPlugin\FieldInfo\Farm;
use App\Classes\AdaptConverter\TFPlugin\FieldInfo\Field;
use App\Classes\AdaptConverter\TFPlugin\FieldInfo\FieldBoundary;
use App\Classes\AdaptConverter\TFPlugin\FieldInfo\Grower;
use App\Classes\AdaptConverter\TFPlugin\Prescription\BoundingBox;
use App\Classes\AdaptConverter\TFPlugin\Prescription\Origin;
use App\Classes\AdaptConverter\TFPlugin\Prescription\OutOfFieldRate;
use App\Classes\AdaptConverter\TFPlugin\Prescription\Prescription;
use App\Classes\AdaptConverter\TFPlugin\Prescription\RxProductLookup;
use App\Classes\AdaptConverter\TFPlugin\Product\Product;
use App\Classes\AdaptConverter\TFPlugin\Product\ProductCategoryEnum;
use App\Classes\AdaptConverter\TFPlugin\Product\ProductFormEnum;
use App\Classes\AdaptConverter\TFPlugin\Product\ProductStatusEnum;
use App\Classes\AdaptConverter\TFPlugin\Product\ProductTypeEnum;
use App\Classes\AdaptConverter\TFPlugin\TFPlugin;
use App\Classes\AdaptConverter\TFPlugin\WorkItem\WorkItem;
use App\Classes\AdaptConverter\TFPlugin\WorkItem\WorkItemOperation;
use App\Helpers\Helper;
use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use App\Services\Order\OrderVraService;
use Auth;
use Config;
use Exception;

class AdaptService
{
    public const FORMAT_TF = 'TF';
    public const FORMAT_TRIMBLEAGDATA = 'TrimbleAgData';
    public const FORMAT_TRIMBLEAGGPS = 'TrimbleAgGPS';
    public const FORMAT_ADM = 'ADM';
    public const FORMAT_ISOV4PLUGIN = 'ISOv4Plugin';
    public const FORMAT_ISOPLUGIN = 'ISOPlugin';
    public const FORMAT_DEERE_GS4_4600 = 'Deere-GS4_4600';
    public const FORMAT_DEERE_GS3_2630 = 'Deere-GS3_2630';
    public const FORMAT_GS2_1800 = 'GS2_1800';
    public const FORMAT_GS2_COMMANDCENTER = 'GS2_CommandCenter';
    public const FORMAT_GS2_2600 = 'GS2_2600';

    public function __construct() {}

    public function getTFPluginData(int $orderId, string $orderType = OrderVraService::VRA_SOIL_ORDER_TYPE, int $cellSize = 5): TFPlugin
    {
        $currentUser = Auth::user();
        $userId = $currentUser->id;
        $serviceProviderId = $currentUser->globalUser()->serviceProvider->id;
        $lastChosenOrganizationId = $currentUser->lastChosenOrganization->id;

        // Generate random unique numbers to use as ids for objects that dont have
        $autoGeneratedIds = Helper::getRandomUniqueNumbers(1, 100, 6);
        $fieldBoundaryId = $autoGeneratedIds[0];
        $outOfFieldRateId = $autoGeneratedIds[1];
        $productId = $autoGeneratedIds[2];
        $rxProductLookupId = $autoGeneratedIds[3];
        $workItemOperationId = $autoGeneratedIds[4];
        $workItemId = $autoGeneratedIds[5];

        $tfPluginData = null;
        if (OrderVraService::VRA_SOIL_ORDER_TYPE === $orderType) {
            $tfPluginData = OrderSoilVra::getVraDataForTFPlugin($userId, $lastChosenOrganizationId, $serviceProviderId, $orderId, $cellSize, $rxProductLookupId);
        } elseif (OrderVraService::VRA_SATELLITE_ORDER_TYPE === $orderType) {
            $tfPluginData = OrderSatelliteVra::getVraDataForTFPlugin($userId, $lastChosenOrganizationId, $serviceProviderId, $orderId, $cellSize, $rxProductLookupId);
        } else {
            throw new Exception('Invalid order type');
        }

        if (!isset($tfPluginData)) {
            throw new Exception('No data found for tf plugin.');
        }

        $tfPluginData = $tfPluginData->toArray();

        // Set FieldInfo data

        $fieldBoundary = new FieldBoundary($fieldBoundaryId, 'Default');
        $fieldBoundary->setFieldId($tfPluginData['plot_id']);
        $fieldBoundary->setCoordinates($tfPluginData['plot_geom_coords']);

        $farm = new Farm($tfPluginData['farm_id'], $tfPluginData['farm_name']);
        $farm->setGrowerId($tfPluginData['grower_id']);

        $grower = new Grower($tfPluginData['grower_id'], $tfPluginData['grower_name']);

        $field = new Field($tfPluginData['plot_id'], $tfPluginData['plot_name']);
        $field->setValue($tfPluginData['plot_area']);
        $field->setFarm($farm);
        $field->setGrower($grower);
        $field->setFieldBoundaryId($fieldBoundary->getId());

        // Set Presccription data

        $bbox = new BoundingBox(
            (float)$tfPluginData['bbox_max_x'],
            (float)$tfPluginData['bbox_min_x'],
            (float)$tfPluginData['bbox_max_y'],
            (float)$tfPluginData['bbox_min_y']
        );

        $origin = new Origin($bbox->getMinX(), $bbox->getMinY());

        $outOfFieldRate = new OutOfFieldRate($outOfFieldRateId);

        $product = new Product($productId, 'Urea 46');
        $product->setCategory(ProductCategoryEnum::ADDITIVE);
        $product->setForm(ProductFormEnum::GAS);
        $product->setType(ProductTypeEnum::GENERIC);
        $product->setStatus(ProductStatusEnum::ACTIVE);

        $rxProductLookup = new RxProductLookup($rxProductLookupId, $product->getId(), '');

        $prescription = new Prescription($tfPluginData['vra_order_id'], $tfPluginData['vra_name']);
        $prescription->setBoundingBox($bbox);
        $prescription->setRates($tfPluginData['rates']);
        $prescription->setFieldId($field->getId());
        $prescription->setOrigin($origin);
        $prescription->setOutOfFieldRate($outOfFieldRate);
        $prescription->setCellSize($cellSize, $cellSize);
        $prescription->setPointsGridSize($tfPluginData['col_count'], $tfPluginData['row_count']);
        $prescription->addRxProductLookup($rxProductLookup);
        $prescription->setProductIds([$product->getId()]);

        // Set WorkItem data

        $workItemOperation = new WorkItemOperation($workItemOperationId);
        $workItemOperation->setPrescriptionId($prescription->getId());
        $workItemOperation->setPrescriptionId($prescription->getId());

        $workItem = new WorkItem($workItemId);
        $workItem->setGrowerId($grower->getId());
        $workItem->setFarmId($farm->getId());
        $workItem->setFieldId($field->getId());
        $workItem->setFieldBoundaryId($fieldBoundary->getId());
        $workItem->addWorkItemOperation($workItemOperation);

        // Create TFPlugin object with all data

        $tfPlugin = new TFPlugin();
        $tfPlugin->setFields([$field]);
        $tfPlugin->setFieldBoundaries([$fieldBoundary]);
        $tfPlugin->setPrescriptions([$prescription]);
        $tfPlugin->setProducts([$product]);
        $tfPlugin->setWorkItems([$workItem]);

        return $tfPlugin;
    }

    public function saveTFPluginData(TFPlugin $data, string $dir, bool $multipleFiles = false): void
    {
        if (!$multipleFiles) {
            $filePath = $dir . '/' . uniqid('data_', true) . '.tf';
            $contents = json_encode($data, \JSON_PRETTY_PRINT);
            \file_put_contents($filePath, $contents);

            return;
        }

        \file_put_contents($dir . '/1TF_FieldInfo.tf', json_encode(['Fields' => $data->getFields()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/2TF_FieldBoundaries.tf', json_encode(['FieldBoundaries' => $data->getFieldBoundaries()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/3TF_Guidance.tf', json_encode(['Guidance' => $data->getGuidance()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/4TF_LoggedData.tf', json_encode(['LoggedData' => $data->getLoggedData()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/5TF_Prescriptions.tf', json_encode(['Prescriptions' => $data->getPrescriptions()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/6TF_Products.tf', json_encode(['Products' => $data->getProducts()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/8TF_Crop.tf', json_encode(['Crops' => $data->getCropZones()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/9TF_WorkItems.tf', json_encode(['WorkItems' => $data->getWorkItems()], \JSON_PRETTY_PRINT));
        \file_put_contents($dir . '/91TF_Reference.tf', json_encode(['ReferenceLayers' => $data->getReferenceLayers()], \JSON_PRETTY_PRINT));
    }

    public function convert(string $srcDir, string $dstDir, string $fromFormat = self::FORMAT_TF, string $toFormat = self::FORMAT_ISOV4PLUGIN)
    {
        if (!is_dir($srcDir)) {
            throw new Excepton("Cannot find dir: {$srcDir}");
        }

        if (!is_dir($dstDir)) {
            mkdir($dstDir, 0700, true);
        }

        $adaptConverterPath = Config::get('globals.ADAPT_CONVERTER_EXE_PATH');
        $cmd = "mono {$adaptConverterPath} --input {$srcDir} --output {$dstDir} --from {$fromFormat} --to {$toFormat} --version 3";
        exec($cmd);
    }
}
