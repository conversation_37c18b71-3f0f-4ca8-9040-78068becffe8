<?php

namespace App\Services\Auth\Exceptions;

use Exception;
use Illuminate\Support\Arr;

class KeycloakGuardException extends Exception
{
    protected $code = 401;

    public function render()
    {
        return response(config('app.debug') ? [
            'error' => 'Keycloak Guard', // TODO: Remove after proper implementation of the exceptions.
            'error_description' => $this->getMessage(), // TODO: Remove after proper implementation of the exceptions.
            'message' => $this->getMessage(),
            'exception' => get_class($this),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => collect($this->getTrace())->map(function ($trace) {
                return Arr::except($trace, ['args']);
            })->all(),
        ] : [
            'error' => 'Keycloak Guard',
            'error_description' => $this->getMessage(),
        ], $this->code);
    }
}
