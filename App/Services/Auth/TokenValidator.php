<?php

namespace App\Services\Auth;

use DomainException;
use Exception;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use Firebase\JWT\SignatureInvalidException;
use Illuminate\Http\Request;
use InvalidArgumentException;
use League\OAuth2\Server\Exception\OAuthServerException;
use Psr\Http\Message\ServerRequestInterface;
use UnexpectedValueException;

class TokenValidator
{
    protected $publicKey;
    protected $introspectTokenCallable;

    /**
     * @param [type] $key
     */
    public function setPublicKey($key)
    {
        $this->publicKey = $key;
    }

    public function setIntrospectTokenCallable(callable $callable)
    {
        $this->introspectTokenCallable = $callable;
    }

    public function verifyToken(Request $request): KeycloakToken
    {
        if (!$request->bearerToken()) {
            throw OAuthServerException::accessDenied('Missing "Authorization" header');
        }

        $accesstToken = $this->introspectToken(
            $this->introspectTokenCallable,
            $request->bearerToken()
        );

        if (false === $accesstToken->active) {
            throw OAuthServerException::accessDenied('Invalid token');
        }

        return $accesstToken;
    }

    public function introspectToken(callable $introspectTokenCallable, string $token): KeycloakToken
    {
        $decodedToken = $introspectTokenCallable($token);
        $decodedToken->access_token = $token;

        return new KeycloakToken(
            $decodedToken
        );
    }

    public function validateAuthorization(ServerRequestInterface $request)
    {
        // when all users has roles and scopes we can validate allowed resource
    }

    /**
     * Validates and verifies a JWT token.
     *
     * @param $publicKey
     *
     * @return object Std
     */
    private function decode(string $token, $jwk, int $leeway = 0): object
    {
        JWT::$leeway = $leeway;

        try {
            $decoded = JWT::decode($token, JWK::parseKeySet($jwk));
            $decoded->access_token = $token;

            return $token ? $decoded : null;
        } catch (InvalidArgumentException $e) {
            throw $e;
            // provided key/key-array is empty or malformed.
        } catch (DomainException $e) {
            throw $e;
            // provided algorithm is unsupported OR
            // provided key is invalid OR
            // unknown error thrown in openSSL or libsodium OR
            // libsodium is required but not available.
        } catch (SignatureInvalidException $e) {
            throw $e;
            // provided JWT signature verification failed.
        } catch (BeforeValidException $e) {
            throw $e;
            // provided JWT is trying to be used before "nbf" claim OR
            // provided JWT is trying to be used before "iat" claim.
        } catch (ExpiredException $e) {
            throw $e;
            // provided JWT is trying to be used after "exp" claim.
        } catch (UnexpectedValueException $e) {
            throw $e;
            // provided JWT is malformed OR
            // provided JWT is missing an algorithm / using an unsupported algorithm OR
            // provided JWT algorithm does not match provided key OR
            // provided key ID in key/key-array is empty or invalid.
        } catch (Exception $e) {
            throw $e;
        }
    }
}
