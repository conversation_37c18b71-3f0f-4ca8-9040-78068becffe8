<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 1/12/2021
 * Time: 8:46 AM.
 */

namespace App\Services\Implement;

use App\Models\WorkOperation;

class WorkOperationService
{
    public function getWorkOperationsNameFilteredData(string $forInstance, array $filters)
    {
        switch ($forInstance) {
            case WorkOperation::MACHINE_TASKS:
                $query = WorkOperation::getWorkOperationsForMachineTasks($filters);

                break;
            case WorkOperation::MACHINE_EVENTS:
                $query = WorkOperation::getWorkOperationsForMachineEvents($filters);

                break;
        }

        $query->selectRaw("
            COALESCE(
                JSONB_AGG(DISTINCT
                    JSONB_BUILD_OBJECT(
                        'id', su_work_operations.id,
                        'name', su_work_operations.name,
                        'color', su_work_operations.color
                    )
                ),
                '[]'::jsonb
            ) as work_operations
        ");

        return $query->pluck('work_operations')->first();
    }
}
