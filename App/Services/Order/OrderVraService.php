<?php

namespace App\Services\Order;

use App\Exceptions\NotFoundException;
use App\Helpers\Helper;
use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use App\Models\Plot;
use App\Services\Adapt\AdaptService;
use DateTime;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class OrderVraService
{
    public const VRA_SOIL_ORDER_TYPE = 'soil_vra';
    public const VRA_SATELLITE_ORDER_TYPE = 'vra';

    private $adaptService;

    public function __construct(AdaptService $adaptService)
    {
        $this->adaptService = $adaptService;
    }

    public function getVraOrders(array $filters, ?bool $withPagination, ?int $limit, ?int $page): array
    {
        $query = OrderSoilVra::getFilteredOrdersQuery($filters)->unionAll(OrderSatelliteVra::getFilteredOrdersQuery($filters));

        if ($withPagination) {
            $listOfOrders = $query->paginate($limit, ['*'], 'page', $page);
            $listOfOrdersTotal = $listOfOrders->count();
            $listOfOrdersItems = $listOfOrders->items();
        } else {
            $listOfOrders = $query;
            $listOfOrdersTotal = $listOfOrders->count();
            $listOfOrdersItems = $listOfOrders->get();
        }

        return [
            'total' => $listOfOrdersTotal,
            'rows' => $listOfOrdersItems,
        ];
    }

    public function exportVraMaps(array $vraOrders, string $format, string $outputFilePath): string
    {
        try {
            $vraOrdersForExport = $this->mapVraOrdersForExport($vraOrders);

            $workDir = sys_get_temp_dir()
                . DIRECTORY_SEPARATOR
                . uniqid('vra_export_');

            if (!file_exists($workDir)) {
                mkdir($workDir, 0700, true);
            }

            switch ($format) {
                case 'john_deere':
                    $this->exportVraMapForJohnDeere($vraOrdersForExport, $workDir, $outputFilePath);

                    break;
                case 'trimble':
                    $this->exportVraMapsForTrimble($vraOrdersForExport, $workDir, $outputFilePath);

                    break;
                case 'shp':
                    $this->exportVraMapShp($vraOrdersForExport, $workDir, $outputFilePath);

                    break;
                    // case 'isoxml':
                    //     TODO: Update method exportVraMapISOXML to work with multiple vra orders. Setup the SmartConverter.
                    //     $this->exportVraMapISOXML($vraOrders, $workDir, $outputFilePath);
                    //     break;
            }
        } catch (Exception $e) {
            throw $e;
        } finally {
            $deleteWorkDir = "rm -rf {$workDir}";
            exec($deleteWorkDir);
        }

        return $outputFilePath;
    }

    public function saveContentToFile($dstPath, $content)
    {
        $file = fopen($dstPath, 'w');
        fwrite($file, $content);
        fclose($file);
    }

    private function exportVraMapForJohnDeere(Collection $vraOrders, string $workDir, string $outputFilePath)
    {
        $jdDir = $workDir . DIRECTORY_SEPARATOR . 'john_deere';
        $jdrxDir = $jdDir . DIRECTORY_SEPARATOR . 'rx';
        if (!is_dir($jdrxDir)) {
            mkdir($jdrxDir, 0700, true);
        }

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');

        foreach ($vraOrders as $vraOrder) {
            $layerDate = $vraOrder->layerPlot->date;
            $plotName = Helper::convertToLatin($vraOrder->plot->name);
            $plotName = preg_replace('/[^a-zA-Z0-9\-\_]/', '', $plotName);
            $plotIdentifier = ('' != $plotName ? $plotName : $vraOrder->plot->id);
            $spreadingFileName = "{$plotIdentifier}_VRA_{$vraOrder->id}_{$vraOrder->layerPlot->element}_{$layerDate}";
            $tmpGeoJsonFileName = $workDir . DIRECTORY_SEPARATOR . $vraOrder->id . '_' . strtotime('now') . '.geojson';

            // Save vra order vector data to temp geojson file
            $this->saveContentToFile($tmpGeoJsonFileName, $vraOrder->vector_data);

            $jdrxPrescriptionFilePath = $jdrxDir . DIRECTORY_SEPARATOR . "{$spreadingFileName}.shp";
            $jdrxPrescriptionShapeCmd = "{$gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$jdrxPrescriptionFilePath} {$tmpGeoJsonFileName}";
            exec($jdrxPrescriptionShapeCmd);
        }

        $zipCmd = "cd {$jdDir};zip -r {$outputFilePath} rx";
        exec($zipCmd);

        if (!is_file($outputFilePath)) {
            throw new Exception('Error exporting VRA map. Zip file not found.');
        }

        return $outputFilePath;
    }

    private function exportVraMapsForTrimble(Collection $vraOrders, string $workDir, string $outputFilePath)
    {
        $aggpsPrescriptionsDir = $workDir . DIRECTORY_SEPARATOR . 'AgGPS' . DIRECTORY_SEPARATOR . 'Prescriptions';
        if (!is_dir($aggpsPrescriptionsDir)) {
            mkdir($aggpsPrescriptionsDir, 0700, true);
        }

        $agdataPrescriptionsDir = $workDir . DIRECTORY_SEPARATOR . 'AgData' . DIRECTORY_SEPARATOR . 'Prescriptions';
        if (!is_dir($agdataPrescriptionsDir)) {
            mkdir($agdataPrescriptionsDir, 0700, true);
        }

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $machine = Config::get('globals.MACHINE');

        foreach ($vraOrders as $vraOrder) {
            $layerDate = $vraOrder->layerPlot->date;
            $plotName = Helper::convertToLatin($vraOrder->plot->name);
            $plotName = preg_replace('/[^a-zA-Z0-9\-\_]/', '', $plotName);
            $plotIdentifier = ('' != $plotName ? $plotName : $vraOrder->plot->id);
            $spreadingFileName = "{$plotIdentifier}_VRA_{$vraOrder->id}_{$vraOrder->layerPlot->element}_{$layerDate}";
            $tmpGeoJsonFileName = $workDir . DIRECTORY_SEPARATOR . $vraOrder->id . '_' . strtotime('now') . '.geojson';

            // Save vra order vector data to temp geojson file
            $this->saveContentToFile($tmpGeoJsonFileName, $vraOrder->vector_data);
            $dateObj = DateTime::createFromFormat('Y-m-d', $layerDate);

            $aggpsBoundaryDir = $workDir . DIRECTORY_SEPARATOR . 'AgGPS'
                . DIRECTORY_SEPARATOR . 'Data' . DIRECTORY_SEPARATOR . Auth::user()->username
                . DIRECTORY_SEPARATOR . $dateObj->format('Y') . DIRECTORY_SEPARATOR . $plotIdentifier;
            $boundaryFilePath = "\"{$aggpsBoundaryDir}" . DIRECTORY_SEPARATOR . 'Boundary.shp"';

            if (!is_dir($aggpsBoundaryDir)) {
                mkdir($aggpsBoundaryDir, 0700, true);
            }

            if (!is_file($boundaryFilePath)) {
                $dbHost = Config::get("database.connections.{$machine}.host");
                $dbUser = Config::get("database.connections.{$machine}.username");
                $dbPort = Config::get("database.connections.{$machine}.port");
                $dbName = Config::get("database.connections.{$machine}.database");
                $dbPass = Config::get("database.connections.{$machine}.password");
                $connStr = "postgresql://{$dbUser}:{$dbPass}@{$dbHost}:{$dbPort}/{$dbName}";

                $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
                $boundaryQuery = Plot::where('gid', $vraOrder->plot_id)
                    ->select(
                        'gid as Id',
                        'name as Name',
                        DB::raw("round((area * {$areaCoef})::numeric, 3) as \"Area\""),
                        DB::raw('st_transform(geom, 4326) as geom')
                    )->toSqlWithBindings();

                $boundaryShapeCmd = "{$gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" {$boundaryFilePath} PG:\"{$connStr}\" -a_srs EPSG:4326 -sql \"{$boundaryQuery}\"";
                exec($boundaryShapeCmd);

                $boundaryCentroid = Plot::where('gid', $vraOrder->plot_id)
                    ->select(
                        DB::raw('st_astext(st_centroid(st_transform(geom, 4326))) as centroid')
                    )->pluck('centroid')->first();

                $boundaryCentroid = str_replace('POINT(', '', $boundaryCentroid);
                $boundaryCentroid = str_replace(')', '', $boundaryCentroid);

                $boundaryCentroid = explode(' ', $boundaryCentroid);
                $boundaryCentroid[0] = $boundaryCentroid[0] . 'E';
                $boundaryCentroid[1] = $boundaryCentroid[1] . 'N';

                $boundaryCentroid = implode('', $boundaryCentroid);
                $centroidFile = fopen($aggpsBoundaryDir . DIRECTORY_SEPARATOR . $boundaryCentroid . '.pos', 'w');
                fclose($centroidFile);
            }

            $agdataPrescriptionFilePath = $agdataPrescriptionsDir . DIRECTORY_SEPARATOR . "{$spreadingFileName}.shp";
            $agdataPrescriptionShapeCmd = "{$gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$agdataPrescriptionFilePath} {$tmpGeoJsonFileName}";
            exec($agdataPrescriptionShapeCmd);

            $aggpsPrescriptionFilePath = $aggpsPrescriptionsDir . DIRECTORY_SEPARATOR . "{$spreadingFileName}.shp";
            $aggpsPrescriptionShapeCmd = "{$gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$aggpsPrescriptionFilePath} {$tmpGeoJsonFileName}";
            exec($aggpsPrescriptionShapeCmd);
        }

        $zipCmd = "cd {$workDir};zip -r {$outputFilePath} AgGPS AgData ";
        exec($zipCmd);

        if (!is_file($outputFilePath)) {
            throw new Exception('Error exporting VRA map. Zip file not found.');
        }

        return $outputFilePath;
    }

    private function exportVraMapShp(Collection $vraOrders, string $workDir, string $outputFilePath)
    {
        $shpDir = $workDir . DIRECTORY_SEPARATOR . 'shp';
        if (!is_dir($shpDir)) {
            mkdir($shpDir, 0700, true);
        }

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');

        foreach ($vraOrders as $vraOrder) {
            $layerDate = $vraOrder->layerPlot->date;
            $plotName = Helper::convertToLatin($vraOrder->plot->name);
            $plotName = preg_replace('/[^a-zA-Z0-9\-\_]/', '', $plotName);
            $plotIdentifier = ('' != $plotName ? $plotName : $vraOrder->plot->id);
            $spreadingFileName = "{$plotIdentifier}_VRA_{$vraOrder->id}_{$vraOrder->layerPlot->element}_{$layerDate}";
            $tmpGeoJsonFileName = $workDir . DIRECTORY_SEPARATOR . $vraOrder->id . '_' . strtotime('now') . '.geojson';

            // Save vra order vector data to temp geojson file
            $this->saveContentToFile($tmpGeoJsonFileName, $vraOrder->vector_data);

            $shpPrescriptionFilePath = $shpDir . DIRECTORY_SEPARATOR . "{$spreadingFileName}.shp";
            $shpPrescriptionShapeCmd = "{$gdalBinPath}ogr2ogr -f \"ESRI Shapefile\" -a_srs EPSG:4326 {$shpPrescriptionFilePath} {$tmpGeoJsonFileName}";
            exec($shpPrescriptionShapeCmd);
        }

        $zipCmd = "cd {$shpDir};zip -j {$outputFilePath} *.*";
        exec($zipCmd);

        if (!is_file($outputFilePath)) {
            throw new Exception('Error exporting VRA map. Zip file not found.');
        }

        return $outputFilePath;
    }

    private function exportVraMapISOXML(int $vraOrderId, string $vraOrderType, string $exportPath, string $zipFilePath)
    {
        // TODO: Update this method to work with multiple vra orders
        $tmpDir = $exportPath . uniqid('tmp_tf_plugin_data_');

        mkdir($tmpDir, 0700, true);

        $tfPluginData = $this->adaptService->getTFPluginData($vraOrderId, $vraOrderType);
        $this->adaptService->saveTFPluginData($tfPluginData, $tmpDir, false);
        $this->adaptService->convert($tmpDir, $tmpDir, AdaptService::FORMAT_TF, AdaptService::FORMAT_ISOV4PLUGIN);

        if (!is_file($tmpDir . '/compress_directory.zip')) {
            throw new Exception("Cannot find file '{$tmpDir} . '/compress_directory.zip''");
        }

        $moveResultFile = "mv {$tmpDir}/compress_directory.zip {$zipFilePath}";
        exec($moveResultFile);

        $deleteTmpDir = "rm -rf {$tmpDir}";
        exec($deleteTmpDir);
    }

    /**
     * Validate the vra orders and add 'order', 'plot' and 'lyerPlot' data to them.
     *
     * @param array $vraOrders ["type" => string, "id" => int]
     */
    private function mapVraOrdersForExport(array $vraOrders): Collection
    {
        $satelliteVraOrderIds = array_column(array_filter($vraOrders, function ($vraOrder) {
            return self::VRA_SATELLITE_ORDER_TYPE == $vraOrder['type'];
        }), 'id');
        $soilVraOrderIds = array_column(array_filter($vraOrders, function ($vraOrder) {
            return self::VRA_SOIL_ORDER_TYPE == $vraOrder['type'];
        }), 'id');

        $satelliteVraOrders = VraOrderFactory::make(self::VRA_SATELLITE_ORDER_TYPE)
            ->with([
                'order' => function ($order) {
                    return $order->where('status', 'processed');
                },
                'plot',
                'layerPlot',
            ])
            ->whereIn('id', $satelliteVraOrderIds)
            ->get();

        $soilVraOrders = VraOrderFactory::make(self::VRA_SOIL_ORDER_TYPE)
            ->with([
                'order' => function ($order) {
                    return $order->where('status', 'processed');
                },
                'plot',
                'layerPlot',
            ])
            ->whereIn('id', $soilVraOrderIds)
            ->get();

        $nonExistingSatelliteOrders = array_diff($satelliteVraOrderIds, $satelliteVraOrders->pluck('id')->toArray());
        $nonExistingSoilOrders = array_diff($soilVraOrderIds, $soilVraOrders->pluck('id')->toArray());

        if (count($nonExistingSatelliteOrders) > 0) {
            $nonExistingSatelliteOrdersStr = implode(', ', $nonExistingSatelliteOrders);

            throw new NotFoundException("Cannot find satellite vra orders with ids: {$nonExistingSatelliteOrdersStr}");
        }

        if (count($nonExistingSoilOrders) > 0) {
            $nonExistingSoilOrdersStr = implode(', ', $nonExistingSoilOrders);

            throw new NotFoundException("Cannot find soil vra orders with ids: {$nonExistingSoilOrdersStr}");
        }

        return $satelliteVraOrders->concat($soilVraOrders);
    }
}
