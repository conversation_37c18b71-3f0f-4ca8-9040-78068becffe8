<?php

namespace App\Services\Order;

use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use Exception;
use ReflectionClass;

class VraOrderFactory
{
    private static $orderTypes = [
        OrderVraService::VRA_SOIL_ORDER_TYPE => OrderSoilVra::class,
        OrderVraService::VRA_SATELLITE_ORDER_TYPE => OrderSatelliteVra::class,
    ];

    public function __construct() {}

    /**
     * Get VRA order model by type.
     *
     * @return (OrderSatelliteVra|OrderSoilVra)
     */
    public static function make(string $type)
    {
        if (!isset(self::$orderTypes[$type])) {
            $errMsg = "Unsupported VRA order type '" . $type . "'! Supported types are: '" . implode("', '", array_keys(self::$orderTypes)) . "'.";

            throw new Exception($errMsg);
        }

        $ref = new ReflectionClass(self::$orderTypes[$type]);

        return $ref->newInstance();
    }
}
