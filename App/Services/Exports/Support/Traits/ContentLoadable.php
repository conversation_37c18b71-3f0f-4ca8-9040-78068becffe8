<?php

namespace App\Services\Exports\Support\Traits;

use App\Services\Exports\Pdf;
use InvalidArgumentException;
use Spatie\Browsershot\Browsershot;

trait ContentLoadable
{
    /**
     * Renders and loads a given view browsershot.
     *
     * @param ?array $data
     * @param ?array $mergeData
     */
    public function loadView(string $view, ?array $data = [], ?array $mergeData = []): Pdf
    {
        $html = view($view, $data, $mergeData)->render();

        $this->browsershot()->setHtml($html);

        return $this;
    }

    /**
     * Sets a valid html string.
     */
    public function loadHtml(string $html): Pdf
    {
        $this->browsershot()->setHtml($html);

        return $this;
    }

    /**
     * Loads the given url to browsershot.
     */
    public function loadUrl(string $url): Pdf
    {
        if (!$this->validUrl($url)) {
            throw new InvalidArgumentException('The url: ' . $url . ' is not valid');
        }

        $this->browsershot()->setUrl($url);

        return $this;
    }

    /**
     * Checks whether the given url is valid or not.
     */
    protected function validUrl(string $url): bool
    {
        return (bool) filter_var($url, FILTER_VALIDATE_URL);
    }

    abstract protected function browsershot(): Browsershot;
}
