<?php

namespace App\Services\Exports;

use Exception;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Excel;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class LaravelExcelBaseStrategy extends BaseExport implements
    FromArray,
    IExportable,
    ShouldAutoSize,
    WithEvents,
    WithHeadings,
    WithMapping,
    WithStyles,
    WithTitle
{
    use Exportable;

    /** @var array */
    protected $data;

    /** @var array */
    protected $columns;

    /** @var array */
    protected $translatedColumnsMap;

    /**
     * @var string
     */
    private $writerType = Excel::XLSX;

    /**
     * @var string
     */
    private $fileName;

    /**
     * Sheet Title.
     *
     * @var string
     */
    private $title;

    /**
     * @var string
     */
    private $translationsFile;

    /**
     * @var array
     */
    private $totalsData;

    /**
     * @var bool
     */
    private $columnsMap = true;

    /**
     * @param ?string $serviceProviderLogo
     * @param ?string $translateFile
     * @param ?string $pdfTemplateName
     *
     * @throws Exception
     */
    public function export(
        Collection $reportData,
        array $columns,
        string $tmpFileName,
        array $reportTotalData,
        ?string $translateFile,
        ?string $pdfTemplateName,
        ?string $serviceProviderLogo = null
    ): void {
        if (0 === count($reportData)) {
            throw new Exception('No data', 404);
        }

        $this->data = $reportData->toArray();
        $this->totalsData = $reportTotalData;
        $this->columns = $columns;
        $this->translationsFile = $translateFile;

        if ($this->columnsMap) {
            $this->translatedColumnsMap = $this->translateColumns($columns, $translateFile);
        }

        $this->title = trans($translateFile . '.sheet_title');

        \Maatwebsite\Excel\Facades\Excel::store($this, $tmpFileName, 'qnap_storage');
    }

    public function headings(): array
    {
        return array_values(
            $this->columnsMap
                ? $this->translatedColumnsMap
                : $this->columns
        );
    }

    public function title(): string
    {
        return $this->title;
    }

    public function withoutColumnsMapping(): bool
    {
        return $this->columnsMap = false;
    }

    public function array(): array
    {
        return array_flatten(
            array_map(
                function ($row) {
                    if (is_object($row) && property_exists($row, 'children')) {
                        return array_merge([$row], $row->children);
                    }

                    return [$row];
                },
                $this->data
            ),
            1
        );
    }

    public function map($row): array
    {
        if (is_object($row)) {
            $row = (array)$row;
        }

        return $this->getRowData($row, $this->translatedColumnsMap, $this->translationsFile);
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $rowDataTotal = $this->getRowDataTotal($this->totalsData, $this->columns);

                $row = $event->sheet->getDelegate()->getHighestRow() + 1;
                $col = $event->sheet->getDelegate()->getHighestColumn();
                $footerAddress = "A{$row}:{$col}{$row}";
                $event->sheet->appendRows([
                    $rowDataTotal,
                ], $event);

                $event->sheet->getStyle($footerAddress)->getFill()->applyFromArray(['fillType' => 'solid', 'rotation' => 0, 'color' => ['rgb' => 'CDECFD']]);
            },
        ];
    }

    public function styles(Worksheet $sheet): array
    {
        $style = [
            1 => [
                'font' => ['bold' => true],
            ],
        ];

        $rows = $this->array();
        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            $row = $rows[$i];

            if (!isset($row->children)) {
                continue;
            }

            $style[$i + 2] = [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E7EAEE'],
                ],
            ];
        }

        return $style;
    }
}
