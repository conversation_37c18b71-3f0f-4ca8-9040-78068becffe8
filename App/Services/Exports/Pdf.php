<?php

namespace App\Services\Exports;

use App\Traits\Exports\ContentLoadable;
use BadMethodCallException;
use Error;
use <PERSON><PERSON>\Browsershot\Browsershot;
use Throwable;

/**
 * // * @method string base64pdf()
 * // * @method void savePdf(string $targetPath)
 *
 * @see \Spatie\Browsershot\Browsershot
 */
class Pdf
{
    use ContentLoadable;

    /**
     * Browsershot base class to generate PDFs.
     *
     * @var \Spatie\Browsershot\Browsershot
     */
    protected $browsershot;

    /**
     * @throws Throwable
     */
    public function __construct()
    {
        $browsershot = new Browsershot();
        $browsershot
            ->noSandbox()
            ->showBackground()
            ->waitUntilNetworkIdle()
            ->paperSize('794', '1122', 'px')
            ->margins(1, 1, 1, 1, 'cm');

        $this->browsershot = $browsershot;
    }

    /**
     * Delegates the call of methods to underlying Browsershot.
     */
    public function __call(string $name, array $arguments): self
    {
        try {
            $this->browsershot()->$name(...$arguments);

            return $this;
        } catch (Error $e) {
            throw new BadMethodCallException('Method ' . static::class . '::' . $name . '() does not exists');
        }
    }

    public function base64pdf(): string
    {
        return $this->browsershot()->base64pdf();
    }

    /**
     * Access underlying browsershot instance.
     */
    public function browsershot(): Browsershot
    {
        return $this->browsershot;
    }
}
