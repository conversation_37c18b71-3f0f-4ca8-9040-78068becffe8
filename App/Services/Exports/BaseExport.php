<?php

namespace App\Services\Exports;

use App\Models\IrrigationEvent;
use App\Models\MachineEvent;
use Illuminate\Support\Facades\Config;

abstract class BaseExport
{
    /**
     * @param array $columns The table columns
     *
     * @return array The result is associative array where the key matches the key from $columns and the value is translated column name
     */
    public function translateColumns(array $columns, string $translateFile): array
    {
        $areaLabel = trans('general.' . Config::get('globals.AREA_UNIT_LABEL'));

        $columnsMap = [];
        foreach ($columns as $value) {
            if (in_array($value, ['plot_area', 'covered_area'])) {
                $columnsMap[$value] = trans($translateFile . '.' . $value) . "({$areaLabel})";

                continue;
            }

            if ('yield' === $value) {
                $columnsMap[$value] = trans($translateFile . '.' . $value) . ' (' . trans('general.kg') . "/{$areaLabel})";

                continue;
            }

            $columnsMap[$value] = trans($translateFile . '.' . $value);
        }

        return $columnsMap;
    }

    /**
     * @param array $row The row data - associative array (column => value)
     * @param array $columnsMap The columns map - assoc array where the key matches the key from $row and the value is translation
     * @param bool $withoutFirst If set to true returns data for all columns except the first
     * @param ?string $translateFile
     *
     * @return array The result is an ordered associative array (The order is specified by $columnsMap)
     */
    public function getRowData(array $row, ?array $columnsMap = [], ?string $translateFile, bool $withoutFirst = false): array
    {
        if (empty($columnsMap)) {
            return $row;
        }

        $rowData = [];
        foreach ($columnsMap as $key => $value) {
            if ($withoutFirst) {
                $rowData[$key] = ' ';
                $withoutFirst = false;

                continue;
            }

            if ((MachineEvent::TASK === $key || IrrigationEvent::TYPE === $key) && isset($row[$key])) {
                if (is_array($row[$key]) && count($row[$key]) > 0) {
                    $translatedItems = array_map(function ($item) use ($translateFile) {
                        return trans("{$translateFile}.{$item}");
                    }, $row[$key]);

                    $rowData[$key] = implode(', ', $translatedItems);

                    continue;
                }

                if (is_string($row[$key]) && strlen($row[$key]) > 0) {
                    $rowData[$key] = trans("{$translateFile}.{$row[$key]}");

                    continue;
                }
            }

            if (isset($row[$key]) && !is_object($row[$key]) && strlen($row[$key]) > 0) {
                $rowData[$key] = $row[$key];

                continue;
            }

            if (isset($row[$key]) && is_object($row[$key]) && property_exists($row[$key], 'value') && strlen($row[$key]->value) > 0) {
                $rowData[$key] = $row[$key]->value;

                continue;
            }

            if (isset($row[$key]) && is_object($row[$key]) && property_exists($row[$key], 'short_name') && strlen($row[$key]->short_name) > 0) {
                $rowData[$key] = $row[$key]->short_name;

                continue;
            }

            $rowData[$key] = ' ';
        }

        return $rowData;
    }

    /**
     * @param array $row The total row data - associative array (column => value). The columns may have prefix 'total_'
     * @param array $columnsMap The columns map - assoc array where the key matches the key from $row and the value is translation
     *
     * @return array The result is an ordered associative array (The order is specified by $columnsMap)
     */
    protected function getRowDataTotal(array $row, array $columnsMap): array
    {
        $rowData = [];

        foreach ($columnsMap as $key => $value) {
            $totalKey = 'total_' . $value;
            $rowKey = (isset($row[$totalKey]) && strlen($row[$totalKey]) > 0) ? $totalKey : $key;
            $rowData[$key] = (isset($row[$rowKey]) && strlen($row[$rowKey]) > 0) ? $row[$rowKey] : ' ';
        }

        return $rowData;
    }
}
