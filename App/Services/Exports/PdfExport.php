<?php

namespace App\Services\Exports;

use App\Services\Station\ReportService;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Throwable;

class PdfExport extends BaseExport implements IExportable
{
    /**
     * @param Collection $reportData The data for report
     * @param array $columns The table columns
     * @param string $tmpFilePath The path of the result file
     * @param array $reportTotalData Report total data
     * @param string $translateFile Translation file name
     * @param string $pdfTemplateName Template name
     * @param ?string $serviceProviderLogo The service proviter logo svg text
     *
     * @throws Throwable
     */
    public function export(Collection $reportData, array $columns, string $tmpFilePath, array $reportTotalData, ?string $translateFile, ?string $pdfTemplateName, ?string $serviceProviderLogo = null): void
    {
        if (0 === count($reportData)) {
            throw new Exception('No data', 404);
        }

        $translatedColumns = $this->translateColumns($columns, $translateFile);
        $this->generatePdf($reportData, $translatedColumns, $tmpFilePath, $reportTotalData, $translateFile, $pdfTemplateName, $serviceProviderLogo);
    }

    /**
     * @param Collection $reportData The data for report
     * @param array $columnsMap This array is used to map the column key with the translated column name
     * @param ?string $serviceProviderLogo The service proviter logo svg text
     *
     * @throws Throwable
     */
    private function generatePdf(Collection $reportData, array $columnsMap, string $file, array $reportTotalData, string $translateFile, string $pdfTemplateName, ?string $serviceProviderLogo): void
    {
        $rows = [];
        foreach ($reportData as $row) {
            $rowData = $this->getRowData((array)$row, $columnsMap, $translateFile);
            $hasSubrows = (isset($row->children) && count($row->children) > 0);
            $rowData['main'] = $hasSubrows;
            $withoutFirst = (ReportService::PDF_TEMLATE_NAME === $pdfTemplateName) ? false : true;

            $rows[] = $rowData;
            if ($hasSubrows) {
                foreach ($row->children as $subrow) {
                    $subrowData = $this->getRowData((array)$subrow, $columnsMap, $translateFile, $withoutFirst);
                    $subrowData['main'] = false;
                    $rows[] = $subrowData;
                }
            }
        }

        $data = [
            'columns' => $columnsMap,
            'title' => trans($translateFile . '.title'),
            'rows' => $rows,
            'totalData' => $reportTotalData,
            'serviceProviderLogo' => $serviceProviderLogo ?? '',
        ];

        $fullPath = Storage::disk('qnap_storage')->path($file);
        (new Pdf())
            ->loadView('templates.' . $pdfTemplateName, $data)
            ->margins(1, 1, 1, 1, 'cm')
            ->format('a4')
            ->scale(0.8)
            ->landscape()
            ->browsershot()
            ->save($fullPath);
    }
}
