<?php

namespace App\Services\Exports;

use Exception;
use Illuminate\Support\Facades\App;

class ExportFactory
{
    private static $exportTypes = [
        'xls' => LaravelExcelBaseStrategy::class,
        'pdf' => PdfExport::class,
    ];

    /**
     * @throws Exception
     */
    public static function make(string $type): IExportable
    {
        if (!isset(self::$exportTypes[$type])) {
            throw new Exception(sprintf("Unsupported export type '%s'! Supported types are: '%s.'", $type, implode(', ', array_keys(self::$exportTypes))));
        }

        return App::make(self::$exportTypes[$type]);
    }
}
