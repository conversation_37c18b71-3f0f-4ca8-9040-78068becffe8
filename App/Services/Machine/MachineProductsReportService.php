<?php

namespace App\Services\Machine;

use App\Models\MachineTask;
use App\Models\Organization;
use App\Models\Products\Product;
use App\Services\Exports\ExportFactory;
use App\Services\Wialon\ReportService;
use Exception;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class MachineProductsReportService
{
    private ReportService $reportService;
    private MachineEventService $machineEventService;

    public function __construct(ReportService $reportService, MachineEventService $machineEventService)
    {
        $this->reportService = $reportService;
        $this->machineEventService = $machineEventService;
    }

    /**
     * Returns paginated report data.
     *
     * @param int $organizationId OrganizationId
     * @param array $filter = array(
     *                      from        => int,
     *                      to          => int,
     *                      farmIds     => int[],
     *                      plotIds     => int[],
     *                      cropIds     => int[],
     *                      productIds  => int[],
     *                      machineIds  => int[],
     *                      implements  => int[],
     *                      types       => string[],
     *                      stages      => string[],
     *                      )
     * @param string $groupBy The name of the column to group by
     * @param array $orderBy Associative array where key is the column to be sorted and value is the sort order ('asc' or 'desc')
     */
    public function getReport(int $organizationId, array $filter, string $groupBy, array $orderBy = []): array
    {
        $groupByConfig = $this->getGroupByMapped($groupBy);

        // Get report pagination list
        $query = $this->getReportQuery($organizationId, $filter, $groupByConfig, $orderBy);
        $report = $query->paginate($filter['limit']);
        $report->getCollection()->transform(function ($value) {
            return $value->report_json;
        });

        $firstColumnKey = $this->getGroupByMapped($groupBy)['jsonKey'];
        $columns = config('reports.MACHINE_PRODUCTS_COLUMNS');
        $firstColumn = array_splice($columns, array_search($firstColumnKey, array_column($columns, 'name')), 1);

        return [
            'total' => $report->total(),
            'rows' => $report->items(),
            'header' => array_merge($firstColumn, $columns),
            'footer' => $this->getReportTotalsQuery($organizationId, $filter, $groupByConfig)->get()->first()->toArray(),
        ];
    }

    /**
     * @param int $organizationId OrganizationId
     * @param array $filter = array(
     *                      from        => int,
     *                      to          => int,
     *                      farmIds     => int[],
     *                      plotIds     => int[],
     *                      cropIds     => int[],
     *                      productIds  => int[],
     *                      machineIds  => int[],
     *                      implements  => int[],
     *                      types       => string[],
     *                      stages      => string[],
     *                      )
     * @param string $type The report file type ('pdf' or 'xls')
     * @param string $groupBy The name of the column to group by
     * @param array $orderBy Associative array where key is the column to be sorted and value is the sort order ('asc' or 'desc')
     *
     * @throws Exception
     *
     * @return string $tmpFilePath The path of the created report file
     */
    public function generateReport(string $type, int $organizationId, array $filter, string $groupBy, array $orderBy = [], array $columns = []): string
    {
        $groupByConfig = $this->getGroupByMapped($groupBy);
        $firstColumn = $groupByConfig['jsonKey'];

        $reportData = $this->getReportQuery($organizationId, $filter, $groupByConfig, $orderBy)->pluck('report_json');

        if (0 === count($reportData)) {
            throw new Exception('No data', 404);
        }

        $fileName = uniqid('products_report_', true) . '.' . $type;

        $reportTotalData = $this->getReportTotalsQuery($organizationId, $filter, $groupByConfig)->first()->toArray();
        $reportTotalData['total_' . $firstColumn] = trans('reportProduct.Total');

        if (!count($columns)) {
            $columns = array_column(config('reports.MACHINE_PRODUCTS_COLUMNS'), 'name');
        }

        $firstColumn = array_splice($columns, array_search($firstColumn, $columns), 1);
        $columns = array_merge($firstColumn, $columns);
        $organization = Organization::findOrFail($organizationId);
        $serviceProvider = $organization->serviceProvider;
        $serviceProviderLogo = $serviceProvider->logo;

        $exporter = ExportFactory::make($type);
        $exporter->export($reportData, $columns, $fileName, $reportTotalData, 'reportProduct', 'report_events', $serviceProviderLogo);

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return $fileName;
    }

    private function getReportQuery(int $organizationId, array $filter, array $groupByConfig, array $orderBy = []): EloquentBuilder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $applicationRatePerHa = Product::APPLICATION_RATE_PER_HA;
        $applicationRatePerDka = Product::APPLICATION_RATE_PER_DKA;

        $groupColumn = $groupByConfig['groupColumn'];
        $groupContent = $groupByConfig['groupContent'];
        $groupUiColumnName = $groupByConfig['uiColumnName'];
        $groupFieldNullable = $groupByConfig['nullable'];

        $query = MachineTask::getReportQuery($organizationId, $filter)->groupBy($groupContent);

        if ($groupFieldNullable) {
            // This type of where in nullable db fields
            $query->whereNotNull($groupColumn);
        }

        $mainFields = $this->getMainFields($groupUiColumnName);

        $qtySql = "''";
        if ('product' === $groupUiColumnName) {
            $qtySql = "sum(round((case 
                        when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                            then (su_machine_tasks.covered_area * {$areaCoef})::numeric * smtp.rate
                        else smtp.rate
                    end)::numeric, 3))";
        }
        $query->select(
            DB::raw("sum(round((case 
                        when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                            then (su_machine_tasks.covered_area * {$areaCoef})::numeric * smtp.rate * smtp.value
                        else smtp.rate * smtp.value
                    end)::numeric, 2)) as cost"),
            DB::raw("{$qtySql} as qty"),
            DB::raw(
                "json_build_object(
                'key', ROW_NUMBER () OVER ( ORDER BY {$groupColumn})::TEXT,
                {$mainFields},
                'cost', sum(round((case 
                            when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                                then (su_machine_tasks.covered_area   * {$areaCoef})::numeric * smtp.rate * smtp.value
                            else smtp.rate * smtp.value
                            end)::numeric, 2)),
                'qty', {$qtySql},
                'children',
                    json_agg(json_build_object(
                    'key', concat(smtp.product_id, '-', su_machine_tasks.id),
                    'farm_name', coalesce(f.name, ''),
                    'plot_name', coalesce(sp.name, ''),
                    'crop_name', coalesce(scc.crop_name, ''),
                    'state', su_machine_tasks.state,
                    'task', (SELECT jsonb_agg(name) FROM su_work_operations WHERE id = ANY (su_machine_tasks.work_operation_ids)),
                    'machine_name', mu.name,
                    'date', case when su_machine_tasks.start_date::date = su_machine_tasks.end_date::date	
                                then (to_char(su_machine_tasks.start_date::date, 'dd-mm-YYYY'))::text 
                                else concat(to_char(su_machine_tasks.start_date::date, 'dd-mm-YYYY'), '-', to_char(su_machine_tasks.end_date::date, 'dd-mm-YYYY'))
                            end,
                    'driver', su_machine_tasks.driver,
                    'implement', coalesce(mi.name, ''),
                    'product_name', p.name,
                    'rate', smtp.rate,
                    'applied_area', smtp.applied_area,
                    'unit', json_build_object(
                            'id', suom.id,
                            'full_name', suom.full_name,
                            'short_name', suom.short_name,
                            'application_rate', p.application_rate
                            ), 
                    'cost', round((case 
                                when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                                    then (su_machine_tasks.covered_area * {$areaCoef})::numeric * smtp.rate * smtp.value
                                else smtp.rate * smtp.value
                            end)::numeric, 2),
                    'qty', round((case 
                            when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                                then (su_machine_tasks.covered_area * {$areaCoef})::numeric * smtp.rate
                            else smtp.rate
                        end)::numeric, 3),
                    'duration', TO_CHAR(su_machine_tasks.completion_date - su_machine_tasks.start_date, 'HH24:MI')
                    ) order by su_machine_tasks.start_date desc)
                ) as report_json"
            )
        );

        foreach ($orderBy as $column => $order) {
            $query->orderBy($column, $order);
        }

        return $query;
    }

    /**
     * @param string $groupBy
     */
    private function getReportTotalsQuery(int $organizationId, array $filter, array $groupByConfig): EloquentBuilder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $applicationRatePerHa = Product::APPLICATION_RATE_PER_HA;
        $applicationRatePerDka = Product::APPLICATION_RATE_PER_DKA;
        $groupColumn = $groupByConfig['groupColumn'];
        $groupUiColumnName = $groupByConfig['uiColumnName'];
        $groupFieldNullable = $groupByConfig['nullable'];

        $query = MachineTask::getReportQuery($organizationId, $filter);

        if ($groupFieldNullable) {
            // This type of where in nullable db fields
            $query->whereNotNull($groupColumn);
        }

        $qtySql = "''";
        if ('product' === $groupUiColumnName) {
            $qtySql = "sum(round((case 
                            when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                                then (su_machine_tasks.covered_area * {$areaCoef})::numeric * smtp.rate
                            else smtp.rate
				            end)::numeric, 3))";
        }
        $query->select(
            DB::raw("sum(round((case 
                            when p.application_rate = '{$applicationRatePerHa}' or  p.application_rate = '{$applicationRatePerDka}'
                                then (su_machine_tasks.covered_area * {$areaCoef})::numeric * smtp.rate * smtp.value
                            else smtp.rate * smtp.value
                            end)::numeric, 2)) as total_cost"),
            DB::raw("{$qtySql} as total_qty")
        );

        return $query;
    }

    private function getGroupByMapped(string $GroupByFromUI): array
    {
        $machineGroupByConfig = ['groupColumn' => 'su_machine_events.machine_id', 'groupContent' => ['su_machine_events.machine_id'], 'nullable' => false, 'jsonKey' => 'machine_name', 'uiColumnName' => 'machine'];
        $farmGroupByConfig = ['groupColumn' => 'f.id', 'groupContent' => ['f.id'], 'nullable' => true, 'jsonKey' => 'farm_name', 'uiColumnName' => 'farm'];
        $plotGroupByConfig = ['groupColumn' => 'sp.gid', 'groupContent' => ['sp.gid'], 'nullable' => true, 'jsonKey' => 'plot_name', 'uiColumnName' => 'plot'];
        $cropGroupByConfig = ['groupColumn' => 'spc.crop_id', 'groupContent' => ['spc.crop_id'], 'nullable' => true, 'jsonKey' => 'crop_name', 'uiColumnName' => 'crop'];
        $productGroupByConfig = ['groupColumn' => 'p.id', 'groupContent' => ['p.id'], 'nullable' => true, 'jsonKey' => 'product_name', 'uiColumnName' => 'product'];

        $groupByConfig = [
            'machine' => $machineGroupByConfig,
            'farm' => $farmGroupByConfig,
            'plot' => $plotGroupByConfig,
            'crop' => $cropGroupByConfig,
            'product' => $productGroupByConfig,
        ];

        return $groupByConfig[$GroupByFromUI] ?? $plotGroupByConfig;
    }

    private function getMainFields(string $groupBy): string
    {
        $mainFieldsTemplate = "
            'product_name', '',
            'rate', '',
            'unit', '',
            'machine_name', '',
            'task', '',
            'date', '',
            'driver', '',
            'implement', '',
            'farm_name', '',
            'crop_name', '',
            'plot_name', ''
        ";

        $mainFields = str_replace("'plot_name', ''", "'plot_name', max(sp.name)", $mainFieldsTemplate);

        if ('driver' === $groupBy) {
            $mainFields = str_replace("'driver', ''", "'driver', max(su_machine_events.driver)", $mainFieldsTemplate);
        }
        if ('implement' === $groupBy) {
            $mainFields = str_replace("'implement', ''", "'implement', max(mi.name)", $mainFieldsTemplate);
        }
        if ('farm' === $groupBy) {
            $mainFields = str_replace("'farm_name', ''", "'farm_name', max(f.name)", $mainFieldsTemplate);
        }
        if ('product' === $groupBy) {
            $mainFields = str_replace("'product_name', ''", "'product_name', max(p.name)", $mainFieldsTemplate);
        }
        if ('crop' === $groupBy) {
            $mainFields = str_replace("'crop_name', ''", "'crop_name', max(scc.crop_name)", $mainFieldsTemplate);
        }
        if ('machine' === $groupBy) {
            $mainFields = str_replace("'machine_name', ''", "'machine_name', max(mu.name)", $mainFieldsTemplate);
        }

        return $mainFields;
    }
}
