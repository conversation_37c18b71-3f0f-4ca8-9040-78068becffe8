<?php

namespace App\Services\Machine;

use App\Jobs\IntegrationReportJob;
use App\Models\Country;
use App\Models\Integration;
use App\Models\IntegrationReportsTypes;
use App\Models\MachineEvent;
use App\Models\Organization;
use App\Services\Exports\ExportFactory;
use App\Services\Wialon\ReportService;
use Exception;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class MachineReportService
{
    private $reportService;
    private $machineEventService;

    public function __construct(ReportService $reportService, MachineEventService $machineEventService)
    {
        $this->reportService = $reportService;
        $this->machineEventService = $machineEventService;
    }

    public function scheduleMachineEventsReport(int $organizationId, ?int $wialonUnitId, int $toDate, int $fromDate)
    {
        $country = Country::find(Auth::user()->globalUser()->country);

        $report = IntegrationReportsTypes::getIntegrationReportsQuery(
            Integration::ACTIVE,
            IntegrationReportsTypes::SCHEDULED,
            $organizationId,
            IntegrationReportsTypes::MACHINE_EVENTS
        )->firstOrFail();

        $params = json_decode($report->params, true); // can be moved in cast

        if ($wialonUnitId) {
            $params['exec_params']['reportObjectId'] = $wialonUnitId;
        }

        $params['exec_params']['interval']['flags'] = IntegrationReportsTypes::REPORT_FLAG_BY_DATE;
        $params['exec_params']['interval']['to'] = $toDate;
        $params['exec_params']['interval']['from'] = $fromDate;

        $date = Carbon::createFromTimestamp($fromDate)->format('Y-m-d');

        dispatch(
            new IntegrationReportJob(
                $country->iso_alpha_2_code,
                $tmpTableName = uniqid('tmp_report_'),
                $report->id,
                $report->name,
                $params,
                $report->url,
                $report->token,
                $report->organization_id,
                $date,
                $wialonUnitId
            )
        );
    }

    public function getReport(int $organizationId, array $filter, string $groupBy, array $orderBy): array
    {
        $query = $this->getReportQuery($organizationId, $filter, $groupBy, $orderBy);
        $report = $query->paginate($filter['limit']);
        $report->getCollection()->transform(function ($value) {
            return $value->report_json;
        });

        $firstColumnKey = $this->getGroupByMapped($groupBy)['jsonKey'];
        $columns = Config('reports.MACHINE_COLUMNS');
        $firstColumn = array_splice($columns, array_search($firstColumnKey, array_column($columns, 'name')), 1);

        return [
            'total' => $report->total(),
            'rows' => $report->items(),
            'header' => array_merge($firstColumn, $columns),
            'footer' => $this->getReportTotalsQuery($organizationId, $filter)->get()->first()->toArray(),
        ];
    }

    /**
     * @param int $organizationId OrganizationId
     * @param array $filter = array(
     *                      from        => int,
     *                      to          => int,
     *                      farmIds     => int[],
     *                      plotIds     => int[],
     *                      cropIds     => int[],
     *                      machineIds  => int[],
     *                      implements  => int[],
     *                      types       => string[],
     *                      stages      => string[],
     *                      )
     * @param string $type The report file type ('pdf' or 'xls')
     * @param string $groupBy The name of the column to group by
     * @param array $orderBy Associative array where key is the column to be sorted and value is the sort order ('asc' or 'desc')
     *
     * @throws Exception
     *
     * @return string $tmpFilePath The path of the created report file
     */
    public function generateReport(string $type, int $organizationId, array $filter, string $groupBy, array $orderBy = [], array $columns = []): string
    {
        $reportData = $this->getReportQuery($organizationId, $filter, $groupBy, $orderBy)->pluck('report_json');

        if (0 === count($reportData)) {
            throw new Exception('No data', 404);
        }
        $firstColumn = $this->getGroupByMapped($groupBy)['jsonKey'];
        $fileName = uniqid('machines_report_', true) . '.' . $type;

        $reportTotalData = $this->getReportTotalsQuery($organizationId, $filter)->first()->toArray();
        $reportTotalData['total_' . $firstColumn] = trans('reportMachine.Total');

        if (!count($columns)) {
            $columns = array_column(config('reports.MACHINE_COLUMNS'), 'name');
        }

        $firstColumn = array_splice($columns, array_search($firstColumn, $columns), 1);
        $columns = array_merge($firstColumn, $columns);
        $organization = Organization::findOrFail($organizationId);
        $serviceProvider = $organization->serviceProvider;
        $serviceProviderLogo = $serviceProvider->logo;

        $exporter = ExportFactory::make($type);

        $exporter->export($reportData, $columns, $fileName, $reportTotalData, 'reportMachine', 'report_events', $serviceProviderLogo);
        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return $fileName;
    }

    private function getReportQuery(int $organizationId, array $filter, string $groupBy, array $orderBy = []): EloquentBuilder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $groupByConfig = $this->getGroupByMapped($groupBy);
        $groupColumn = $groupByConfig['groupColumn'];
        $groupContent = $groupByConfig['groupContent'];
        $groupFieldNullable = $groupByConfig['nullable'];

        $query = MachineEvent::getListQuery($organizationId, $filter)->groupBy($groupContent);

        if ($groupFieldNullable) {
            // This type of where in nullable db fields
            $query->whereNotNull($groupColumn);
        }

        $mainFields = $this->getMainFields($groupBy);

        $query->select(
            DB::raw("coalesce(round(sum(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric), 3), 0) as covered_area"),
            DB::raw('round(sum(su_machine_events.length_track)/1000::decimal, 1) as traveled_distance'),
            DB::raw('sum(su_machine_events.fuel_consumed_driving)::int as fuel_consumed_driving'),
            DB::raw('sum(su_machine_events.fuel_consumed_stay)::int as fuel_consumed_stay'),
            DB::raw('sum(su_machine_events.fuel_consumed_stay)::int + sum(su_machine_events.fuel_consumed_driving)::int as fuel_consumed_total'),
            DB::raw("round(coalesce(sum(su_machine_events.fuel_consumed_driving)::numeric / nullif(round(sum(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric), 3), 0), 0), 1) as fuel_consumed_ha"),
            DB::raw('round(sum(su_machine_events.fuel_consumed_driving)::numeric/ nullif(sum(su_machine_events.length_track)::numeric/ 1000, 0) * 100, 1) as fuel_consumed_100_km'),
            DB::raw(
                "json_build_object(
                'key', ROW_NUMBER () OVER ( ORDER BY {$groupColumn})::TEXT,
                {$mainFields},
                'covered_area', coalesce(sum(round(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric, 3)), 0),
                'traveled_distance', round(sum(su_machine_events.length_track)/1000::decimal, 1),
                'fuel_consumed_driving', sum(su_machine_events.fuel_consumed_driving)::int,
                'fuel_consumed_stay', sum(su_machine_events.fuel_consumed_stay)::int,
                'fuel_consumed_total', sum(su_machine_events.fuel_consumed_driving)::int + sum(su_machine_events.fuel_consumed_stay)::int,
                'fuel_consumed_ha', round(coalesce(sum(su_machine_events.fuel_consumed_driving)::numeric / nullif(round(sum(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric), 3), 0), 0), 1),
                'fuel_consumed_100_km', round(sum(su_machine_events.fuel_consumed_driving)::numeric/ nullif(sum(su_machine_events.length_track)::numeric/ 1000, 0) * 100, 1),
                'children',
                    json_agg(json_build_object(
                    'key', concat(su_machine_events.machine_id, '-', su_machine_events.id),
                    'event_id', su_machine_events.id,
                    'machine_name', mu.name,
                    'task', ewo.work_operations_names,
                    'date', to_char(su_machine_events.date, 'dd-mm-YYYY'),
                    'driver', su_machine_events.driver,
                    'implement', coalesce(mi.name, ''),
                    'start_time', su_machine_events.start_date::time,
                    'end_time', su_machine_events.end_date::time,
                    'duration', su_machine_events.end_date::time - su_machine_events.start_date::time,
                    'movement_duration', su_machine_events.duration,
                    'standing_duration', su_machine_events.duration_stay,
                    'farm_name', coalesce(f.name, ''),
                    'plot_name', coalesce(sp.name, ''),
                    'crop_name', coalesce(scc.crop_name, ''),
                    'covered_area', coalesce(round(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric, 3), 0),
                    'plot_area', coalesce(round((sp.area*{$areaCoef})::numeric, 3), 0),
                    'traveled_distance', round(su_machine_events.length_track/1000::decimal, 1),
                    'fuel_consumed_stay', su_machine_events.fuel_consumed_stay::int,
                    'fuel_consumed_driving', su_machine_events.fuel_consumed_driving::int,
                    'fuel_consumed_total', su_machine_events.fuel_consumed_stay::int + su_machine_events.fuel_consumed_driving::int,
                    'fuel_consumed_ha', round(coalesce(su_machine_events.fuel_consumed_driving::numeric / nullif(round(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric, 3), 0), 0), 1),
                    'fuel_consumed_100_km', round(su_machine_events.fuel_consumed_driving::numeric/ nullif(su_machine_events.length_track::numeric/ 1000, 0) * 100, 1)
                    ) order by su_machine_events.date desc, su_machine_events.start_date desc)
                ) as report_json"
            )
        );

        foreach ($orderBy as $column => $order) {
            $query->orderBy($column, $order);
        }

        return $query;
    }

    private function getReportTotalsQuery(int $organizationId, array $filter): EloquentBuilder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = MachineEvent::getListQuery($organizationId, $filter);

        $query->select(
            DB::raw("coalesce(round(sum(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric), 3), 0) as total_covered_area"),
            DB::raw('round(sum(su_machine_events.length_track)/1000::decimal,1) as total_traveled_distance'),
            DB::raw('sum(su_machine_events.fuel_consumed_driving)::int as total_fuel_consumed_driving'),
            DB::raw('sum(su_machine_events.fuel_consumed_stay)::int as total_fuel_consumed_stay'),
            DB::raw('sum(su_machine_events.fuel_consumed_stay)::int + sum(su_machine_events.fuel_consumed_driving)::int as total_fuel_consumed_total'),
            DB::raw("round(coalesce(sum(su_machine_events.fuel_consumed_driving)::numeric / nullif(round(sum(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric), 3), 0), 0), 1) as total_fuel_consumed_ha"),
            DB::raw('round(sum(su_machine_events.fuel_consumed_driving)::numeric/nullif((sum(su_machine_events.length_track)/1000)::numeric, 0) * 100, 1) as total_fuel_consumed_100_km')
        );

        return $query;
    }

    private function getGroupByMapped(string $GroupByFromUI): array
    {
        $machineGroupByConfig = ['groupColumn' => 'su_machine_events.machine_id', 'groupContent' => ['su_machine_events.machine_id'], 'nullable' => false, 'jsonKey' => 'machine_name'];
        $driverGroupByConfig = ['groupColumn' => 'su_machine_events.driver', 'groupContent' => ['su_machine_events.driver'], 'nullable' => true, 'jsonKey' => 'driver'];
        $implementGroupByConfig = ['groupColumn' => 'su_machine_events.machine_implement_id', 'groupContent' => ['su_machine_events.machine_implement_id'], 'nullable' => true, 'jsonKey' => 'implement'];
        $farmGroupByConfig = ['groupColumn' => 'f.id', 'groupContent' => ['f.id'], 'nullable' => true, 'jsonKey' => 'farm_name'];
        $plotGroupByConfig = ['groupColumn' => 'sp.gid', 'groupContent' => ['sp.gid'], 'nullable' => true, 'jsonKey' => 'plot_name'];
        $cropGroupByConfig = ['groupColumn' => 'spc.crop_id', 'groupContent' => ['spc.crop_id'], 'nullable' => true, 'jsonKey' => 'crop_name'];

        $groupByConfig = [
            'machine' => $machineGroupByConfig,
            'driver' => $driverGroupByConfig,
            'implement' => $implementGroupByConfig,
            'farm' => $farmGroupByConfig,
            'plot' => $plotGroupByConfig,
            'crop' => $cropGroupByConfig,
        ];

        return $groupByConfig[$GroupByFromUI] ?? $machineGroupByConfig;
    }

    private function getMainFields(string $groupBy): string
    {
        $mainFieldsTemplate = "
            'machine_name', '',
            'task', '',
            'date', '',
            'driver', '',
            'implement', '',
            'start_time', '',
            'end_time', '',
            'duration', '',
            'movement_duration', '',
            'standing_duration', '',
            'farm_name', '',
            'crop_name', '',
            'plot_name', ''
        ";

        $mainFields = str_replace("'machine_name', ''", "'machine_name', max(mu.name)", $mainFieldsTemplate);

        if ('driver' === $groupBy) {
            $mainFields = str_replace("'driver', ''", "'driver', max(su_machine_events.driver)", $mainFieldsTemplate);
        }
        if ('implement' === $groupBy) {
            $mainFields = str_replace("'implement', ''", "'implement', max(mi.name)", $mainFieldsTemplate);
        }
        if ('farm' === $groupBy) {
            $mainFields = str_replace("'farm_name', ''", "'farm_name', max(f.name)", $mainFieldsTemplate);
        }
        if ('plot' === $groupBy) {
            $mainFields = str_replace("'plot_name', ''", "'plot_name', max(sp.name)", $mainFieldsTemplate);
        }
        if ('crop' === $groupBy) {
            $mainFields = str_replace("'crop_name', ''", "'crop_name', max(scc.crop_name)", $mainFieldsTemplate);
        }

        return $mainFields;
    }
}
