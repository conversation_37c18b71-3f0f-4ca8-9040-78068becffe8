<?php

namespace App\Services\Machine;

use App\Classes\Echarts\Machine\EChartMachineFormatter;
use App\Models\CurrentMachineData;
use App\Models\MachineEvent;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class MachineEChartService
{
    public function __construct() {}

    public function getMobileEchartMachinesByState(int $organizationId, array $filters): array
    {
        $machineStateData = CurrentMachineData::getEchartMachinesByState($organizationId, $filters);
        $chartOptions = Config::get('echarts.mobile.machine-state.options');
        $chartColors = Config::get('echarts.mobile.machine-state.state_colors');

        return $this->createEchart($chartOptions, $machineStateData, $chartColors);
    }

    public function getMachinesCountByWorkOperationsEchart(int $organizationId, array $filters): array
    {
        $chartOptions = Config::get('echarts.mobile.machine-count-by-work-operation.options');
        $machinesCountByWorkOperations = CurrentMachineData::getMachinesCountByWorkOperations($organizationId, $filters);

        return $this->createWorkOperationsEchart($chartOptions, $machinesCountByWorkOperations);
    }

    public function getMobileEchartMachineEventsByType(int $organizationId, array $filters): array
    {
        $chartOptions = Config::get('echarts.mobile.machine-events-by-type.options');
        $machineEventsByTypeQuery = MachineEvent::getDataByTypeQuery($organizationId, $filters);

        $seriesData = $machineEventsByTypeQuery->select(
            'su_machine_events.type AS name',
            DB::raw('count(su_machine_events.type) AS value')
        )
            ->get()->toArray();

        return $this->createEchart($chartOptions, $seriesData);
    }

    public function getMobileEchartEventsByWorkOperation(int $organizationId, array $filters): array
    {
        $chartOptions = Config::get('echarts.mobile.machine-events-by-work-operation.options');
        $machineEventsByWorkOperationQuery = MachineEvent::getEventsCountByWorkOperationsQuery($organizationId, $filters);

        $eventsCountByWorkOperations = MachineEvent::from('events_by_work_operation')
            ->withExpression('events_by_work_operation', $machineEventsByWorkOperationQuery)
            ->select(
                'work_operations',
                'value',
            )
            ->get()->toArray();

        return $this->createWorkOperationsEchart($chartOptions, $eventsCountByWorkOperations);
    }

    private function createWorkOperationsEchart(array $chartOptions, array $data)
    {
        $colors = [];
        $series = [];
        foreach ($data as $item) {
            $name = implode(', ', array_column($item['work_operations'], 'name'));
            $value = $item['value'];

            $totalColorStops = count($item['work_operations']) < 2 ? 2 : count($item['work_operations']);
            $increase = 1 / $totalColorStops;

            $colorStops = [];
            $offsetStart = 0;
            $offsetEnd = $increase;
            while ($offsetEnd < 1.0) {
                foreach ($item['work_operations'] as $workOperation) {
                    if ($offsetEnd > 1.0) {
                        break;
                    }

                    array_push(
                        $colorStops,
                        [
                            'color' => $workOperation['color'],
                            'offset' => $offsetStart,
                        ],
                        [
                            'color' => $workOperation['color'],
                            'offset' => $offsetEnd,
                        ]
                    );

                    $offsetStart = $offsetEnd;
                    $offsetEnd += $increase;
                }
            }

            $color = [
                'type' => 'linear',
                'x' => 0,
                'y' => 0,
                'x2' => 0,
                'y2' => 1,
                'colorStops' => $colorStops,
            ];

            $series[] = [
                'name' => $name,
                'value' => [
                    'value' => $value,
                    'workOperations' => $item['work_operations'],
                ],
            ];

            $colors[$name] = $color;
        }

        return $this->createEchart($chartOptions, $series, $colors);
    }

    private function createEchart(array $chartOptions, array $seriesData, ?array $colors = []): array
    {
        $chartData = new EChartMachineFormatter();
        $chartData->setXAxis($chartOptions['xAxis']);
        $chartData->setYAxis($chartOptions['yAxis']);
        $chartData->setGrid($chartOptions['grid']);
        $chartData->setLegend($chartOptions['legend']);
        $chartData->createSeries($seriesData, $colors);

        return $chartData->jsonSerialize();
    }
}
