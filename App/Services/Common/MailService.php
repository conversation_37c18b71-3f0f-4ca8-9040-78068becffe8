<?php

namespace App\Services\Common;

use App\Classes\CMS\ContractService;
use App\Classes\CMS\ResponsibleUsersService;
use App\Helpers\Helper;
use App\Models\Country;
use App\Models\GlobalUser;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\User;
use App\Services\Exports\Pdf;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;

class MailService
{
    private $template;
    private $title;
    private $content;
    private $mailProperties;
    private $orderPlotRel;
    private $contractService;
    private $responsibleUsersService;

    public function __construct(OrderPlotRel $orderPlotRel)
    {
        $this->contractService = new ContractService(request());
        $this->responsibleUsersService = new ResponsibleUsersService(request());
        $this->orderPlotRel = $orderPlotRel;
    }

    /**
     * Sends an email with the result of the soil sampling to the client.
     *
     * @param string $syncDate the sync date of the plots in the order
     *
     * @throws Exception
     */
    public function sendSoilSamplesEmailReport(int $orderId, string $syncDate)
    {
        $order = Order::findOrFail($orderId);
        $contactPerson = $order->getContactPerson();

        if (!$contactPerson) {
            return;
        }

        $plotsData = $this->orderPlotRel->getPlotsForMailReport($orderId, $syncDate)->get();
        $sampler = User::findOrFail($plotsData->first()->sampler_id);
        $globalUser = $sampler->globalUser();
        $serviceProvider = $sampler->globalUser()->serviceProvider;
        $country = Country::find($globalUser->country);
        $countryCode = $country->iso_alpha_2_code;
        App::setlocale(strtolower($countryCode));

        if ($plotsData->isEmpty()) {
            throw new Exception('No data for report!');
        }

        $uniquePlots = $this->getUniquePlots($plotsData);
        $plotImages = $uniquePlots->map(function ($item) {
            return $this->mapPlotImages($item);
        });

        $bccMails = $this->bccMails();

        $responsibleUsers = $this->responsibleUsersService->findByOrderUuid($order->uuid);

        if (filled($responsibleUsers)) {
            $responsibleUserWithServiceRole = $responsibleUsers->firstWhere('role', 'SERVICE');
            $bccMails[] = GlobalUser::findOrFail($responsibleUserWithServiceRole['userId'])->email;
        }

        $serviceProviderLogo = null;
        if ($serviceProvider->logo) {
            $tmpServiceProviderLogoPath = sys_get_temp_dir() . '/' . uniqid('service_provider_logo', true) . '.png';
            $serviceProviderLogo = Helper::svgStringToPNGFile($serviceProvider->logo, $tmpServiceProviderLogoPath, [100, 100]);
        }

        $this->template = 'emails.send';
        $this->title = trans('emailReport.samplingReport');
        $this->content = $this->getContentSoilSamples($plotsData, $syncDate, $uniquePlots, $contactPerson, $sampler, $order->organization->name, $plotImages, $serviceProviderLogo);

        $filePath = $this->saveAttachmentSoilSamples($this->content, $orderId, $syncDate, $sampler);

        $this->mailProperties = $this->getMailPropertiesSoilSamples($sampler, $contactPerson, $bccMails, $filePath);

        $this->mailSend();
        File::delete($filePath);
        File::delete($serviceProviderLogo);
    }

    /**
     * @param string $email Email address of the receiver
     * @param string $title Email title
     * @param string $subject Subject of the email
     * @param string $message Message content of the email
     * @param string $filePath Full path to the file
     * @param string $customFileName Custom file name with extension for report file
     */
    public function sendFileToEmail(string $email, string $title, string $subject, string $message, string $filePath, string $customFileName): void
    {
        $this->title = $title;
        $this->mailProperties = [
            'fromMail' => Config::get('mail.from.address'),
            'fromName' => Config::get('mail.from.name'),
            'toMail' => $email,
            'toName' => '',
            'subject' => $subject,
            'message' => $message,
        ];
        $this->template = [];

        if ($filePath && is_file($filePath)) {
            $this->mailProperties['filePath'] = $filePath;
            $this->mailProperties['customFileName'] = $customFileName;
        }

        $this->mailSend();
    }

    private function saveAttachmentSoilSamples($content, $orderId, $syncDate, $sampler): string
    {
        $serviceProvider = $sampler->globalUser()->serviceProvider;
        $companyInfo = json_decode($serviceProvider->company_info, true);

        if ($companyInfo) {
            $protocolTitleTrans = trans('soilSamplesProtocol.protocol_title');
            $companyInfo['name'] = Helper::isCyrillic($protocolTitleTrans) && isset($companyInfo['name_cyrillic'])
                ? $companyInfo['name_cyrillic']
                : $companyInfo['name_latin'];
        }

        $serviceProviderHtmlData = [
            'companyInfo' => $companyInfo,
            'serviceProviderSlug' => $serviceProvider->slug,
            'serviceProviderLogo' => $serviceProvider->logo ?? '',
            'includeIsoCert' => $content['includeIsoCert'],
            'includeLogo' => $content['includeLogo'],
            'includeAddress' => $content['includeAddress'],
            'headerTitle' => $content['headerTitle'],
        ];

        $mainHtml = view('templates.soil_samples_protocol.soil_samples_protocol_main', $content)->render();
        $headerHtml = view('templates.soil_samples_protocol.soil_samples_protocol_header', $serviceProviderHtmlData)->render();
        $footerHtml = view('templates.soil_samples_protocol.soil_samples_protocol_footer', $serviceProviderHtmlData)->render();

        $fileName = $orderId . '_' . $syncDate . '_iso.pdf';
        $filePath = config('reports.REPORT_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . $sampler->id . DIRECTORY_SEPARATOR . $fileName;
        @mkdir(dirname($filePath), 0777, true);

        (new Pdf())
            ->loadHtml($mainHtml)
            ->headerHtml($headerHtml)
            ->footerHtml($footerHtml)
            ->showBrowserHeaderAndFooter()
            ->margins(5, 1, 3, 1, 'cm')
            ->format('a4')
            ->scale(0.8)
            ->browsershot()
            ->save($filePath);

        return $filePath;
    }

    private function getMailPropertiesSoilSamples($sampler, $contactPerson, $bccMails, $filePath): array
    {
        $mailProperties = [
            'subject' => trans('emailReport.samplingReport'),
            'fromMail' => Config::get('mail.from.address'),
            'fromName' => Config::get('mail.from.name'),
            'toMail' => $contactPerson->email,
            'toName' => $contactPerson->name,
            'ccMail' => $sampler->email,
            'ccName' => $sampler->name,
            'bccMails' => (!empty($bccMails)) ? $bccMails : null,
        ];

        if ($filePath && is_file($filePath)) {
            $mailProperties['filePath'] = $filePath;
        }

        return $mailProperties;
    }

    private function mailSend(): void
    {
        $mailProperties = $this->mailProperties;

        Mail::send(
            $this->template,
            ['title' => $this->title, 'content' => $this->content],
            function ($message) use ($mailProperties) {
                if (isset($mailProperties['fromMail'], $mailProperties['fromName'])) {
                    $message->from($mailProperties['fromMail'], $mailProperties['fromName']);
                }
                if (isset($mailProperties['subject'])) {
                    $message->subject($mailProperties['subject']);
                }
                if (isset($mailProperties['toMail'], $mailProperties['toName'])) {
                    $message->to($mailProperties['toMail'], $mailProperties['toName']);
                }
                if (isset($mailProperties['ccMail'], $mailProperties['ccName'])) {
                    $message->cc($mailProperties['ccMail'], $mailProperties['ccName']);
                }
                if (isset($mailProperties['bccMails']) && !empty($mailProperties['bccMails'])) {
                    $message->bcc($mailProperties['bccMails']);
                }
                if (isset($mailProperties['message']) && strlen($mailProperties['message']) > 0) {
                    $message->setBody($mailProperties['message']);
                }
                if (isset($mailProperties['filePath']) && is_file($mailProperties['filePath'])) {
                    $fileProps = [];

                    if (isset($mailProperties['customFileName']) && strlen($mailProperties['customFileName']) > 0) {
                        $fileProps = [
                            'as' => $mailProperties['customFileName'],
                        ];
                    }

                    $message->attach($mailProperties['filePath'], $fileProps);
                }
            }
        );
    }

    private function getContentSoilSamples(Collection $plotsData, $syncDate, Collection $uniquePlots, $contactPerson, $sampler, $organizationName, $plotImages, $serviceProviderLogoPath): array
    {
        $locale = Config::get('app.locale') . '_' . strtoupper(Config::get('app.locale')) . '.UTF-8';
        setlocale(LC_TIME, $locale);
        $dt = Carbon::parse($syncDate);
        $dt->setLocale(Config::get('app.locale'));

        $totalArea = $uniquePlots->sum('plot_area');
        $samplerImg = $this->samplerImg($sampler->id, $sampler->profile_image);
        $serviceProvider = $sampler->globalUser()->serviceProvider;

        $companyInfo = json_decode($serviceProvider['company_info'], true);
        $showGsBranding = $companyInfo['show_gs_branding'] ?? true;
        $includeIsoCert = $companyInfo['include_iso_certificates'] ?? true;
        $includeLogo = $companyInfo['include_logo'] ?? true;
        $includeAddress = $companyInfo['include_address'] ?? true;
        $headerTitle = $companyInfo['soil_samples_header_title'];

        return [
            'contactPerson' => $contactPerson,
            'plotsData' => $plotsData->toArray(),
            'totalArea' => $totalArea,
            'samplingDate' => $dt->translatedFormat('d F Y'),
            'samplerName' => $sampler->name,
            'organizationName' => $organizationName,
            'plotImages' => $plotImages,
            'samplerImg' => $samplerImg,
            'showGsBranding' => $showGsBranding,
            'serviceProviderSlug' => $serviceProvider->slug,
            'serviceProviderLogo' => $serviceProviderLogoPath,
            'includeIsoCert' => $includeIsoCert,
            'includeLogo' => $includeLogo,
            'includeAddress' => $includeAddress,
            'headerTitle' => $headerTitle,
        ];
    }

    private function bccMails(): array
    {
        $bccMailsValue = Config::get('globals.SAMPLING_REPORT_DEFAULT_EMAILS');

        $bccMailsValue = str_replace(';', ',', $bccMailsValue);

        $samplerAdminEmails = explode(',', str_replace(' ', '', $bccMailsValue));
        $bccMails = [];
        foreach ($samplerAdminEmails as $emailKey => $adminEmail) {
            if (!empty($adminEmail)) {
                $bccMails[] = str_replace(' ', '', $adminEmail);
            }
        }

        return $bccMails;
    }

    private function samplerImg(int $samplerId, $samplerProfileImage)
    {
        $samplerImg = Config::get('globals.PROFILE_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . $samplerId . DIRECTORY_SEPARATOR . $samplerProfileImage;
        if (!file_exists($samplerImg) || !is_file($samplerImg)) {
            $samplerImg = public_path() . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'no-profile-image.jpg';
        }

        return $samplerImg;
    }

    private function getUniquePlots(Collection $data): Collection
    {
        return $data->map(function ($item) {
            return (object) [
                'order_id' => $item->order_id,
                'plot_gid' => $item->plot_gid,
                'plot_name' => $item->plot_name,
                'plot_area' => $item->plot_area,
                'extent' => $item->extent,
            ];
        })->unique();
    }

    private function mapPlotImages($item): array
    {
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $dbName = $currentDatabase['database'];
        $dbHost = $currentDatabase['host'];
        $dbUser = $currentDatabase['username'];
        $dbPass = $currentDatabase['password'];
        $dbPort = $currentDatabase['port'];

        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $pythonPath = Config::get('globals.PYTHON_PATH');

        // Create image in storage_path('email_reports' . DIRECTORY_SEPARATOR . $item->order_id . DIRECTORY_SEPARATOR . $item->plot_gid . '_plot_report.png'
        $outImage = storage_path('email_reports' . DIRECTORY_SEPARATOR . $item->order_id . DIRECTORY_SEPARATOR . $item->plot_gid . '_plot_report.png');

        $command = "{$pythonPath} {$scriptsPath}static_map.py --order_id " . $item->order_id . ' --plot_id ' . $item->plot_gid . ' --base_path ' . base_path() . ' --dir_email_reports ' . storage_path('email_reports') . ' --out_image ' . $outImage . ' -d ' . $dbName . ' -H ' . $dbHost . ' -P ' . $dbPass . ' -p ' . $dbPort . ' -u ' . $dbUser;
        exec($command . ' 2>&1', $output);

        if (count($output) > 0) {
            throw new Exception("Error generating static map image:\n" . implode($output, "\n"));
        }

        return [
            'plotName' => $item->plot_name,
            'imagePath' => $outImage,
        ];
    }
}
