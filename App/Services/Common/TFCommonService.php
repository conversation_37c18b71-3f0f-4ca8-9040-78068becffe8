<?php

namespace App\Services\Common;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;

class TFCommonService
{
    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('globals.TF_COMMON_SERVICE_API_BASE_URL')]);
    }

    public function exportForTrimble($params)
    {
        try {
            $response = $this->client->request('POST', '/guidance-lines/export/trimble', [
                'json' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return $response->getBody()->getContents();
    }
}
