<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 2/16/2021
 * Time: 8:27 AM.
 */

namespace App\Services\FarmTrackReportsLog;

use App\Models\FarmTrackReportsLog;

class FarmTrackReportsLogService
{
    public function __construct() {}

    public function getFarmTrackReportsByOrganization(int $organizationId, ?string $packageSlugShort, ?string $state, ?array $reportNames)
    {
        return FarmTrackReportsLog::getFarmTrackReportsByOrganization($organizationId, $packageSlugShort, $state, $reportNames);
    }
}
