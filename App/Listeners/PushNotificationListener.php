<?php

namespace App\Listeners;

use App\Classes\APN\APN;
use App\Classes\GCM\GCM;
use App\Events\PushNotificationEvent;
use App\Models\API\Notifications;
use DB;

class PushNotificationListener
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(PushNotificationEvent $event)
    {
        $notyId = $event->notyId;

        $notyData = Notifications::where('id', '=', $notyId);
        $notyData->where('is_pushed', '=', false);
        $notification = $notyData->first();

        if (empty($notification)) {
            return;
        }

        $notification->is_pushed = true;
        $notification->save();

        $this->sendPushNotifications($notification);
    }

    /**
     * Send push notifications to users with new images.
     *
     * @param array $groupIds users group ids
     *
     * @return string response from GCM API
     */
    protected function sendPushNotifications($notification)
    {
        $title = $notification->title;
        $message = $notification->message;
        $data = $notification->data;
        $groupId = $notification->getGroupId();
        $data->notId = time();
        $data->collapse_key = 'vra_orders';

        // send notifications to android devices
        $gcm_device_keys = DB::table('su_users_device_keys')
            ->select('device_key')
            ->where('device_platform', '=', 'android')
            ->where('group_id', '=', $groupId)
            ->get();

        if (!empty($gcm_device_keys)) {
            $gcm_devices = array_map(function ($el) {
                return $el->device_key;
            }, $gcm_device_keys);

            $gcm_push = new GCM();
            $gcm_push->setDevices($gcm_devices);
            $gcm_push->send($title, $message, $data);
        }

        // send notifications to ios devices
        $apn_device_keys = DB::table('su_users_device_keys')
            ->select('device_key')
            ->where('device_platform', '=', 'ios')
            ->where('group_id', '=', $groupId)
            ->get();

        if (!empty($apn_device_keys)) {
            $apn_devices = array_map(function ($el) {
                return $el->device_key;
            }, $apn_device_keys);

            $apn_push = new APN();
            $apn_push->setDevices($apn_devices);
            $apn_push->send($title, $message);
        }
    }
}
