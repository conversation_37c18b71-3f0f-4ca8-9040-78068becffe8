<?php

namespace App\Listeners;

use App\Providers\KeycloakMachineToMachineSocialiteProvider;
use App\Providers\KeycloakPasswordGrantProvider;
use SocialiteProviders\Manager\SocialiteWasCalled;

class KeycloakSocialiteListener
{
    /**
     * Execute the provider.
     */
    public function handle(SocialiteWasCalled $socialiteWasCalled)
    {
        $socialiteWasCalled->extendSocialite('keycloak-machine-to-machine', KeycloakMachineToMachineSocialiteProvider::class);
        $socialiteWasCalled->extendSocialite('keycloak-password-grant', KeycloakPasswordGrantProvider::class);
    }
}
