<?php

namespace App\Listeners;

use App\Events\FarmTrackReportEvent;
use App\Models\FarmTrackReportsLog;
use App\Models\IntegrationsReports;
use DateTime;

class FarmTrackReportLogListener
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(FarmTrackReportEvent $event)
    {
        $integrationReportId = $event->integrationReportId;
        if (!$integrationReportId) {
            return;
        }

        $relData = IntegrationsReports::find($integrationReportId);

        if (FarmTrackReportEvent::CREATE_FARM_TRACK_REPORT === $event->farmTrackReportEvent) {
            $reportLog = new FarmTrackReportsLog();
            $reportLog->integration_id = $relData->integration_id;
            $reportLog->start_time = new DateTime();
            $reportLog->integration_reports_types_id = $relData->integration_reports_types_id;
            $reportLog->request_parameters = $event->params;
            $reportLog->state = $event->reportState;
            $reportLog->tmp_table_name = $event->tmpTableName;
            $reportLog->error = $event->error;
            $reportLog->save();

            return;
        }

        $queryParams = [
            'integration_id' => $relData->integration_id,
            'integration_reports_types_id' => $relData->integration_reports_types_id,
            'state' => FarmTrackReportsLog::PROCESSING,
        ];

        if ($event->tmpTableName) {
            $queryParams['tmp_table_name'] = $event->tmpTableName;
        }

        $reportLog = FarmTrackReportsLog::where($queryParams)->first();

        if (FarmTrackReportsLog::SUCCESS === $event->reportState) {
            $reportLog->end_time = new DateTime();
        }

        $reportLog->error = $event->error;
        $reportLog->state = $event->reportState;
        $reportLog->save();
    }
}
