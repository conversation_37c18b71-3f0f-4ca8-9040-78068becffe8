<?php

namespace App\Listeners;

use App\Mail\SendScheduledReportMail;
use App\Models\ScheduledReport;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\File;

class ProcessSentMailMessageListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(MessageSent $event)
    {
        if (property_exists($event->message, 'mailableClass')) {
            if (SendScheduledReportMail::class === $event->message->mailableClass) {
                /** @var ScheduledReport $scheduledReport */
                $scheduledReport = $event->data['scheduledReport'];

                $scheduledReport->update([
                    'last_sent_at' => now(),
                ]);

                if (File::exists($event->data['reportFile'])) {
                    File::delete($event->data['reportFile']);
                }
            }
        }
    }
}
