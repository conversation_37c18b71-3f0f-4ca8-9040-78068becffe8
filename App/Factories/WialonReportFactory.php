<?php

namespace App\Factories;

use App\Jobs\AbstractCountryAwareJob;
use App\Jobs\CurrentMachineReportJob;
use App\Jobs\IrrigationPerDayReport;
use App\Jobs\IrrigationUnitsReportJob;
use App\Jobs\MachineEventsReport;
use InvalidArgumentException;

class WialonReportFactory
{
    public static function createJob(string $serverName, string $tmpTableName, string $reportName, int $organizationId, ?string $date = null, $wialonUnitId = null): AbstractCountryAwareJob
    {
        switch ($reportName) {
            case 'irrigation_per_day':
                return new IrrigationPerDayReport(
                    $serverName,
                    $tmpTableName,
                    $organizationId,
                    $date,
                    $wialonUnitId
                );

            case 'irrigation_units':
                return new IrrigationUnitsReportJob(
                    $serverName,
                    $tmpTableName,
                    $organizationId
                );

            case 'machines_current':
                return new CurrentMachineReportJob(
                    $serverName,
                    $tmpTableName,
                    $organizationId
                );

            case 'machine_events':
                return new MachineEventsReport(
                    $serverName,
                    $tmpTableName,
                    $organizationId,
                    $date,
                    $wialonUnitId
                );

            default:
                throw new InvalidArgumentException("Unknown report type: {$reportName}");
        }
    }
}
