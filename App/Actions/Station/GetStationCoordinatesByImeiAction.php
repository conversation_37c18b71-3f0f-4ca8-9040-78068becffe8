<?php

namespace App\Actions\Station;

use App\Exceptions\NotFoundException;
use App\Models\UserStation;
use Exception;

class GetStationCoordinatesByImeiAction
{
    private $stationType;
    private $stationImei;

    public function withAttributes(array $attributes): GetStationCoordinatesByImeiAction
    {
        $this->stationType = $attributes['stationType'];
        $this->stationImei = $attributes['stationImei'];

        return $this;
    }

    public function coordinates(): array
    {
        $stationModel = new UserStation();
        $stationModel->type = $this->stationType;
        $stationModel->name = $this->stationImei; // TODO: [GPS-2102] Change this to set the station imei instead of name

        try {
            $stationApi = $stationModel->getStationApi();
            $stationData = $stationApi->getStationData(false);

            $longitude = $stationData['position']['geo']['coordinates'][0] ?? null;
            $latitude = $stationData['position']['geo']['coordinates'][1] ?? null;

            if (!$latitude || !$longitude) {
                throw new Exception();
            }
        } catch (Exception $e) {
            throw new NotFoundException('Station coordinates not found. Please check station type and IMEI.');
        }

        return [
            'longitude' => $longitude,
            'latitude' => $latitude,
        ];
    }
}
