<?php

namespace App\Actions\Integration;

use App\Models\Integration;

class DeactivateIntegrationAction
{
    /**
     * Deactivate all active integrations for a specific organization and package.
     */
    public function withAttributes(array $attributes): void
    {
        Integration::where('organization_id', $attributes['organization_id'])
            ->where('package_id', $attributes['package_id'])
            ->where('status', Integration::ACTIVE)
            ->update(['status' => Integration::INACTIVE]);
    }
}
