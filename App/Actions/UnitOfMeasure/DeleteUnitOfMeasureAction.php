<?php

namespace App\Actions\UnitOfMeasure;

use App\Models\UnitOfMeasure\UnitOfMeasure;
use App\Traits\UnitOfMeasure\UnitOfMeasureValidationTrait;
use Throwable;

class DeleteUnitOfMeasureAction
{
    use UnitOfMeasureValidationTrait;

    /**
     * @var null|UnitOfMeasure
     */
    private $unitOfMeasure;

    /**
     * @throws Throwable
     */
    public function forUnitOfMeasure(UnitOfMeasure $unitsOfMeasure): DeleteUnitOfMeasureAction
    {
        $this->unitOfMeasure = $unitsOfMeasure;

        $this->guardCanDeleteUnit();

        $this->unitOfMeasure->deleteOrFail();

        return $this;
    }
}
