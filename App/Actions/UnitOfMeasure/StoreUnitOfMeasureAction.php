<?php

namespace App\Actions\UnitOfMeasure;

use App\Models\Organization;
use App\Models\UnitOfMeasure\UnitOfMeasure;
use App\Models\UnitOfMeasure\UnitOfMeasureCategory;
use App\Traits\UnitOfMeasure\UnitOfMeasureValidationTrait;
use Auth;
use Exception;

class StoreUnitOfMeasureAction
{
    use UnitOfMeasureValidationTrait;

    /**
     * @throws Exception
     */
    public function withAttributes(array $attributes): UnitOfMeasure
    {
        if (UnitOfMeasureCategory::isCompoundCategory($attributes['category_id'])) {
            $this->guardForCompoundUnitData($attributes);
            $this->guardForExistingCompoundUnit($attributes);
        } else {
            $this->guardForNonBaseUnitData($attributes);
            $this->guardForExistingNonCompoundUnit($attributes);
        }

        $unitOfMeasure = new UnitOfMeasure();
        $attributes['service_provider_id'] = Auth::user()->globalUser()->serviceProvider->id;
        $unitOfMeasure->fill($attributes);
        $unitOfMeasure->organization()->associate(Organization::find($attributes['organization_id']));

        if (isset($attributes['base_unit_id'])) {
            $unitOfMeasure->baseUnit()->associate($this->getUnitOfMeasureById($attributes['base_unit_id']));
        }

        if (isset($attributes['numerator_unit_id'], $attributes['denominator_unit_id'])) {
            $unitOfMeasure->coefficient = 1;
            $unitOfMeasure->numeratorUnit()->associate($this->getUnitOfMeasureById($attributes['numerator_unit_id']));
            $unitOfMeasure->denominatorUnit()->associate($this->getUnitOfMeasureById($attributes['denominator_unit_id']));
        }

        $unitOfMeasure->save();

        return $unitOfMeasure;
    }

    private function getUnitOfMeasureById($id)
    {
        return UnitOfMeasure::findOrFail($id);
    }
}
