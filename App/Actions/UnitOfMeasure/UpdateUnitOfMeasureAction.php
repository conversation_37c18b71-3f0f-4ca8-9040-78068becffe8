<?php

namespace App\Actions\UnitOfMeasure;

use App\Models\UnitOfMeasure\UnitOfMeasure;
use App\Models\UnitOfMeasure\UnitOfMeasureCategory;
use App\Traits\UnitOfMeasure\UnitOfMeasureValidationTrait;
use Exception;
use InvalidArgumentException;

class UpdateUnitOfMeasureAction
{
    use UnitOfMeasureValidationTrait;

    /**
     * @var null|UnitOfMeasure
     */
    private $unitOfMeasure;

    /**
     * @throws Exception
     */
    public function withAttributes(array $attributes)
    {
        $this->guardAgainstInvalidArguments();

        if (UnitOfMeasureCategory::isCompoundCategory($attributes['category_id'])) {
            $this->guardForCompoundUnitData($attributes);
            $this->guardForExistingCompoundUnit($attributes, $this->unitOfMeasure);
        } else {
            $this->guardForNonBaseUnitData($attributes);
            $this->guardForExistingNonCompoundUnit($attributes, $this->unitOfMeasure);
        }

        $this->unitOfMeasure->fill($attributes)->save();

        return $this->unitOfMeasure;
    }

    public function forUnitOfMeasure(UnitOfMeasure $unitOfMeasure): UpdateUnitOfMeasureAction
    {
        $this->unitOfMeasure = $unitOfMeasure;

        return $this;
    }

    private function guardAgainstInvalidArguments()
    {
        if (blank($this->unitOfMeasure)) {
            throw new InvalidArgumentException('You need to provide the unit of measure using the forUnitOfMeasure() method');
        }
    }
}
