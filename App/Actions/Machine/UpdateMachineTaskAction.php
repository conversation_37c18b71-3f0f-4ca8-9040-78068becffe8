<?php

namespace App\Actions\Machine;

use App\Models\MachineEvent;
use App\Models\MachineImplement;
use App\Models\MachineTask;
use App\Traits\MachineTask\MachineTasksTrait;
use DB;
use Exception;
use Illuminate\Support\Facades\Config;

class UpdateMachineTaskAction
{
    use MachineTasksTrait;

    /**
     * @var null|MachineTask
     */
    private $machineTask;

    public function withAttributes(array $attributes): MachineTask
    {
        $oldEventId = $this->machineTask->machine_event_id;
        DB::beginTransaction();

        try {
            $attributesToFill = $this->prepareAttributesToFill($attributes);
            $this->machineTask->fill($attributesToFill);
            $this->machineTask->save();

            if ($oldEventId && $this->machineTask->machine_event_id && $this->machineTask->machine_event_id !== $oldEventId) {
                $organizationId = $this->machineTask->organization->id;
                $this->updateDetachedEvent($oldEventId, $organizationId);
            }

            if ($this->machineTask->machine_event_id) {
                $this->updateEventAfterAttachedToTask($attributes);
            }

            if ($this->machineTask->taskProducts()->count()) {
                $this->machineTask->taskProducts()->delete();
            }

            if (isset($attributes['task_products'])) {
                $this->machineTask->taskProducts()->createMany($attributes['task_products']);
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        return $this->machineTask;
    }

    public function forMachineTask(MachineTask $machineTask): UpdateMachineTaskAction
    {
        $this->machineTask = $machineTask;

        return $this;
    }

    private function updateDetachedEvent(int $eventId, int $organizationId)
    {
        $machineEvent = MachineEvent::find($eventId);
        $machineEvent->stage = MachineEvent::PROPOSED;

        if ($machineEvent->implement_width) {
            $epsgProj = Config::get('globals.DEFAULT_DB_CRS');

            $implement = MachineImplement::find($machineEvent->machine_implement_id);
            $calculateCultivatedGeom = MachineEvent::getCultivatedGeomCustom($organizationId, $eventId, $implement->width);

            $machineEvent->implement_width = null;
            $machineEvent->geom_cultivated = DB::raw("ST_Force2D(ST_SetSRID(ST_GeomFromGeoJSON('{$calculateCultivatedGeom['geojson_cultivated']}'), {$epsgProj}))");
        }

        $machineEvent->save();
    }
}
