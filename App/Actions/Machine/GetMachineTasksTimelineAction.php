<?php

namespace App\Actions\Machine;

use App\Models\MachineTask;
use App\Models\Plot;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GetMachineTasksTimelineAction
{
    private string $lang = 'en';
    private array $filters = [];

    public function forOrganization(int $organizationId)
    {
        $this->filters['organization_id'] = $organizationId;

        return $this;
    }

    public function withFilters(array $filters): GetMachineTasksTimelineAction
    {
        $filters['plot_ids'] = json_decode(Arr::get($filters, 'plot_ids', '[]'));
        $filters['task_states'] = json_decode(Arr::get($filters, 'task_states', '[]'));
        $filters['work_operation_ids'] = json_decode(Arr::get($filters, 'work_operation_ids', '[]'));
        $filters['farm_ids'] = json_decode(Arr::get($filters, 'farm_ids', '[]'));
        $filters['crop_ids'] = json_decode(Arr::get($filters, 'crop_ids', '[]'));
        $filters['farm_year'] = Arr::get($filters, 'farm_year');
        $filters['start_date'] = Arr::get($filters, 'start_date');
        $filters['end_date'] = Arr::get($filters, 'end_date');

        $this->filters = $filters;

        return $this;
    }

    public function forLang(string $lang): GetMachineTasksTimelineAction
    {
        $this->lang = $lang;

        return $this;
    }

    public function get()
    {
        $this->filters['organization_id'] ??= Auth::user()->lastChosenOrganization->id;

        $datesQuery = MachineTask::fromSub(function ($query) {
            $query->selectRaw("GENERATE_SERIES(TO_TIMESTAMP(?), TO_TIMESTAMP(?), '1 day'::INTERVAL)::DATE AS date", [
                $this->filters['start_date'],
                $this->filters['end_date'],
            ]);
        }, 'dates')
            ->selectRaw('dates.date::DATE')
            ->orderBy('dates.date', 'asc');

        $filteredMachineTasksQuery = MachineTask::getFilteredMachineTaskForPlots($this->filters)
            ->join('dates', function ($join) {
                $join->whereBetween('dates.date', [DB::raw('su_machine_tasks.start_date'), DB::raw('su_machine_tasks.end_date')]);
            })
            ->addSelect('dates.date')
            ->addSelect(DB::raw('ROW_NUMBER() OVER (PARTITION BY dates.date, su_machine_tasks.plot_id ORDER BY su_machine_tasks.id) AS row_index'))
            ->addSelect(DB::raw('(su_machine_tasks.end_date::date - su_machine_tasks.start_date::date) + 1 AS days'))
            ->groupBy('dates.date');

        $datesWithTasksQuery = MachineTask::from('filtered_machine_tasks', 'fmt')
            ->select('date')
            ->distinct();

        // row_index represents the task's position for each date (one task must have the same index for all dates)
        $rowsIndexesQuery = MachineTask::select(
            'id AS task_id',
            DB::raw('MAX(row_index) AS idx')
        )
            ->from('filtered_machine_tasks')
            ->groupBy('id');

        $filteredPlotsQuery = Plot::getFilteredPlotsForMachineTasks($this->filters, $this->lang)
            ->join('filtered_machine_tasks as fmt', 'fmt.plot_id', '=', 'su_satellite_plots.gid');

        $tasksByDateQuery = MachineTask::select(
            'fp.gid',
            'fp.uuid',
            'fp.name',
            'fp.area',
            DB::raw('fp.thumbnail::TEXT'),
            'fp.geom_json',
            'fp.farm_name',
            'fp.crop_name',
            'dwt.date',
            DB::raw("
                COALESCE( 
                    JSON_AGG(
                            JSON_BUILD_OBJECT(
                                'id',fmt.id,
                                'state', fmt.state,
                                'start_date', fmt.start_date,
                                'end_date', fmt.end_date,
                                'work_operations', fmt.work_operations,
                                'days', fmt.days,
                                'row_index', ri.idx
                            ) ORDER BY fmt.start_date
                    ) FILTER (WHERE dwt.date = fmt.date AND (fp.gid = fmt.plot_id OR fmt.plot_id ISNULL)),
                    '[]'::json
               ) as tasks"),
            DB::raw('
                COALESCE(
                    MAX(ri.idx) FILTER (WHERE dwt.date = fmt.date AND (fp.gid = fmt.plot_id OR fmt.plot_id ISNULL)),
                    0
                ) AS total_index_rows')
        )
            ->from('filtered_machine_tasks', 'fmt')
            ->join('rows_indexes AS ri', 'ri.task_id', '=', 'fmt.id')
            ->leftJoin('filtered_plots AS fp', 'fp.gid', '=', 'fmt.plot_id')
            ->crossJoin('dates_with_tasks AS dwt')
            ->groupBy(
                'fp.gid',
                'dwt.date',
                'fp.uuid',
                'fp.name',
                'fp.area',
                DB::raw('fp.thumbnail::TEXT'),
                'fp.geom_json',
                'fp.farm_name',
                'fp.crop_name'
            );

        $onlyTasksWithPlot = isset($this->filters['plot_ids']) && count($this->filters['plot_ids'])
            || isset($this->filters['farm_ids']) && count($this->filters['farm_ids'])
            || isset($this->filters['crop_ids']) && count($this->filters['crop_ids']);

        if ($onlyTasksWithPlot) {
            $filteredMachineTasksQuery->whereNotNull('su_machine_tasks.plot_id');
            $tasksByDateQuery->whereNotNull('fp.gid');
            $datesWithTasksQuery->join('filtered_plots as fp', 'fp.gid', '=', 'fmt.plot_id');
        }

        $tasksQuery = MachineTask::withExpression('dates', $datesQuery)
            ->withExpression('filtered_machine_tasks', $filteredMachineTasksQuery)
            ->withExpression('rows_indexes', $rowsIndexesQuery)
            ->withExpression('filtered_plots', $filteredPlotsQuery)
            ->withExpression('dates_with_tasks', $datesWithTasksQuery)
            ->withExpression('tasks_by_date', $tasksByDateQuery)
            ->select(
                'gid',
                'uuid',
                'name',
                'area',
                'thumbnail',
                'geom_json',
                'farm_name',
                'crop_name',
                DB::raw('MAX(total_index_rows) AS total_index_rows'),
                DB::raw("
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'date',date,
                                'tasks',tasks
                            ) ORDER BY date
                        ) AS dates
                    ")
            )
            ->from(DB::raw('tasks_by_date'))
            ->groupBy(
                'gid',
                'uuid',
                'name',
                'area',
                'thumbnail',
                'geom_json',
                'farm_name',
                'crop_name'
            )
            ->orderByRaw('gid ASC NULLS first');

        $header = MachineTask::withExpression('dates', $datesQuery)
            ->withExpression('filtered_machine_tasks', $filteredMachineTasksQuery)
            ->withExpression('filtered_plots', $filteredPlotsQuery)
            ->withExpression('dates_with_tasks', $datesWithTasksQuery)
            ->from('dates_with_tasks')->orderBy('date', 'asc')->pluck('date')->toArray();

        return [
            'header' => $header,
            'rows' => $tasksQuery->get(),
        ];
    }
}
