<?php

namespace App\Actions\Machine;

use App\Models\MachineTask;
use App\Traits\MachineTask\MachineTasksTrait;
use DB;
use Exception;

class StoreMachineTaskAction
{
    use MachineTasksTrait;

    public function withAttributes(array $attributes): MachineTask
    {
        try {
            $attributesToFill = $this->prepareAttributesToFill($attributes);
            $machineTask = new MachineTask();
            $machineTask->fill($attributesToFill);
            $machineTask->save();

            if ($machineTask->machine_event_id) {
                $this->updateEventAfterAttachedToTask($attributes);
            }

            if (isset($attributes['task_products'])) {
                $machineTask->taskProducts()->createMany($attributes['task_products']);
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        return $machineTask;
    }
}
