<?php

namespace App\Actions\Machine;

use App\Models\MachineTask;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MachineTaskWorkOperationChartAction
{
    private $filters;

    public function withFilters(array $filters): MachineTaskWorkOperationChartAction
    {
        $filters['plot_ids'] = json_decode(Arr::get($filters, 'plot_ids', '[]'));
        $filters['states'] = json_decode(Arr::get($filters, 'states', '[]'));
        $filters['work_operation_ids'] = json_decode(Arr::get($filters, 'work_operation_ids', '[]'));
        $filters['farm_ids'] = json_decode(Arr::get($filters, 'farm_ids', '[]'));
        $filters['crop_ids'] = json_decode(Arr::get($filters, 'crop_ids', '[]'));
        $filters['organization_id'] = Arr::get($filters, 'organization_id', Auth::user()->lastChosenOrganization->id);
        $filters['farm_year'] = Arr::get($filters, 'farm_year');

        $this->filters = $filters;

        return $this;
    }

    public function getResult()
    {
        $taskDataForChart = MachineTask::selectRaw('swo.id as work_operation_id, swo.name, su_machine_tasks.state, count(su_machine_tasks.state)')
            ->join('su_work_operations as swo', DB::raw('swo.id'), '=', DB::raw('any(su_machine_tasks.work_operation_ids)'))
            ->where('su_machine_tasks.organization_id', '=', $this->filters['organization_id'])
            ->where('su_machine_tasks.farm_year', $this->filters['farm_year'])
            ->whereRaw(
                "tsrange(su_machine_tasks.start_date, su_machine_tasks.end_date, '[]') && tsrange(to_timestamp(?)::timestamp, to_timestamp(?)::timestamp, '[]')",
                [$this->filters['start_date'], $this->filters['end_date']]
            )
            ->groupBy('swo.id', 'su_machine_tasks.state');

        if (count($this->filters['states'])) {
            $taskDataForChart->whereIn('su_machine_tasks.state', $this->filters['states']);
        }

        if (count($this->filters['plot_ids'])) {
            $taskDataForChart->whereIn('su_machine_tasks.plot_id', $this->filters['plot_ids']);
        }

        self::applyFarmFilters($taskDataForChart);

        if (count($this->filters['crop_ids'])) {
            $taskDataForChart->leftJoin('su_satellite_plots_crops as sspc', function ($q) {
                $q->on('sspc.plot_id', '=', 'p.gid')
                    ->where('sspc.is_primary', true)
                    ->where('sspc.year', $this->filters['farm_year']);
            });

            $taskDataForChart->whereNotNull('p.gid');
            $taskDataForChart->where(function ($q) {
                $cropIdsNullValueIdx = array_search(null, $this->filters['crop_ids']);

                // Check if there is null value in $filters['crop_ids']
                if (false !== $cropIdsNullValueIdx) {
                    unset($this->filters['crop_ids'][$cropIdsNullValueIdx]); // remove null value from array

                    $q->whereIn('sspc.crop_id', $this->filters['crop_ids'])
                        ->orWhere('sspc.crop_id', null);

                    return;
                }

                $q->whereIn('sspc.crop_id', $this->filters['crop_ids']);
            });
        }

        if (count($this->filters['work_operation_ids'])) {
            $wo = json_encode($this->filters['work_operation_ids']);
            $taskDataForChart->where(
                'su_machine_tasks.work_operation_ids',
                '&&',
                DB::raw("array{$wo}")
            );
        }

        $groupData = DB::table('task_data_for_chart as tdfc')
            ->selectRaw("
                tdfc.name as work_operation_name, 
                coalesce(sum(tdfc.count), 0) as total, 
                case when count(tdfc.state) > 0
                    then jsonb_agg(
                            jsonb_build_object(
                                'state', tdfc.state,
                                'value', tdfc.count
                            )
                        )
                    else '[]'::JSONB
                end as task_states
	        ")
            ->groupBy('tdfc.name')
            ->orderBy('tdfc.name');

        $result = DB::table('group_data as gd')
            ->withExpression('task_data_for_chart', $taskDataForChart)
            ->withExpression('group_data', $groupData)
            ->selectRaw("
                json_object_agg(gd.work_operation_name, jsonb_build_object(
                    'task_states', gd.task_states,
                    'total', gd.total
                )) as chart_data
            ")->pluck('chart_data')->first();

        return json_decode($result);
    }

    /**
     * Apply farm filters to the query.
     *
     * @param \Illuminate\Database\Query\Builder $query
     */
    protected function applyFarmFilters($query)
    {
        if (count($this->filters['farm_ids'])) {
            if (count($this->filters['plot_ids']) || count($this->filters['crop_ids'])) {
                $query->join('su_satellite_plots as p', 'p.gid', '=', 'su_machine_tasks.plot_id')
                    ->whereIn('p.farm_id', $this->filters['farm_ids']);
            } else {
                $query->leftJoin('su_satellite_plots as p', 'p.gid', '=', 'su_machine_tasks.plot_id')
                    ->where(function ($query) {
                        $query->whereNotNull('su_machine_tasks.plot_id')
                            ->whereIn('p.farm_id', $this->filters['farm_ids'])
                            ->orWhereNull('su_machine_tasks.plot_id');
                    });
            }
        }
    }
}
