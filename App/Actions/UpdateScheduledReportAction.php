<?php

namespace App\Actions;

use App\Models\ScheduledReport;
use InvalidArgumentException;

class UpdateScheduledReportAction
{
    /**
     * @var null|ScheduledReport
     */
    private $scheduledReport;

    public function withAttributes(array $attributes): ScheduledReport
    {
        $this->guardAgainstInvalidArguments();

        $this->scheduledReport->fill($attributes)->save();

        return $this->scheduledReport->fresh();
    }

    public function forScheduledReport(ScheduledReport $scheduledReport): UpdateScheduledReportAction
    {
        $this->scheduledReport = $scheduledReport;

        return $this;
    }

    private function guardAgainstInvalidArguments()
    {
        if (blank($this->scheduledReport)) {
            throw new InvalidArgumentException('You need to provide the scheduled report using the forScheduledReport() method');
        }
    }
}
