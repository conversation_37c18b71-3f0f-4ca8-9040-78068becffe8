<?php

namespace App\Actions\Farms;

use App\Models\Organization;

class StoreFarmAction
{
    private Organization $organization;
    private array $attributes = [];

    public function withAttributes(array $attributes): StoreFarmAction
    {
        $this->organization = Organization::findOrFail($attributes['organization_id']);
        $this->attributes = $attributes;

        return $this;
    }

    public function save()
    {
        return $this->organization->farms()->create($this->attributes);
    }
}
