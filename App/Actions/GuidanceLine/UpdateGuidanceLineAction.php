<?php

namespace App\Actions\GuidanceLine;

use App\Models\GuidanceLine;
use Config;
use DB;
use InvalidArgumentException;

class UpdateGuidanceLineAction
{
    /**
     * @var null|GuidanceLine
     */
    private $guidanceLine;

    public function withAttributes(array $attributes): bool
    {
        $this->guardAgainstInvalidArguments();

        $epsgProj = Config::get('globals.DEFAULT_DB_CRS');
        $guidanceLineGeom = json_encode($attributes['geom_json']['geometry']);
        unset($attributes['geom_json']);
        $attributes['geom'] = DB::raw("ST_Transform(ST_SetSRID(ST_GeomFromGeoJSON('{$guidanceLineGeom}'), 4326), {$epsgProj})");

        $this->guidanceLine->fill($attributes)->save();

        $this->guidanceLine->fresh();

        return true;
    }

    public function forGuidanceLine(GuidanceLine $guidanceLine): UpdateGuidanceLineAction
    {
        $this->guidanceLine = $guidanceLine;

        return $this;
    }

    private function guardAgainstInvalidArguments()
    {
        if (blank($this->guidanceLine)) {
            throw new InvalidArgumentException('You need to provide the guidance line using the forGuidanceLine() method');
        }
    }
}
