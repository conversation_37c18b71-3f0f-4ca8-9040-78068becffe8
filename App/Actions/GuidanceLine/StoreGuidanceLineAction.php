<?php

namespace App\Actions\GuidanceLine;

use App\Models\GuidanceLine;
use Config;
use DB;

class StoreGuidanceLineAction
{
    public function withAttributes(array $attributes): bool
    {
        $epsgProj = Config::get('globals.DEFAULT_DB_CRS');

        $attributes['guidance_lines'] = array_map(function ($guidanceLine) use ($epsgProj) {
            $guidanceLineGeom = json_encode($guidanceLine['geom_json']['geometry']);
            unset($guidanceLine['geom_json']);
            $guidanceLine['geom'] = DB::raw("ST_Transform(ST_SetSRID(ST_GeomFromGeoJSON('{$guidanceLineGeom}'), 4326), {$epsgProj})");

            return $guidanceLine;
        }, $attributes['guidance_lines']);

        GuidanceLine::insert($attributes['guidance_lines']);

        return true;
    }
}
