<?php

namespace App\Actions\Product;

use App\Models\Products\Product;
use DB;
use Exception;

class StoreProductAction
{
    public function withAttributes(array $attributes): Product
    {
        $attributes['status'] = Product::STATUS_ACTIVE;

        DB::beginTransaction();

        try {
            $product = new Product();
            $product->fill($attributes);
            $product->save();

            if (isset($attributes['active_ingredients']) && count($attributes['active_ingredients'])) {
                foreach ($attributes['active_ingredients'] as $activeIngredient) {
                    $product->activeIngredients()->attach(
                        $activeIngredient['id'],
                        [
                            'quantity' => $activeIngredient['quantity'],
                            'unit_id' => $activeIngredient['unit_id'],
                        ]
                    );
                }
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        return Product::listProductsCustom()->forProduct($product->id)->first();
    }
}
