<?php

namespace App\Actions\Product;

use App\Models\ProductActiveIngredient;
use App\Models\Products\Product;
use App\Models\Products\ProductType;
use DB;
use Exception;

class UpdateProductAction
{
    private Product $product;

    public function withAttributes(array $attributes): Product
    {
        DB::beginTransaction();

        try {
            $this->product->rate = null;
            $this->product->application_rate = null;
            $this->product->fill($attributes);

            if ($this->product->type_id !== ProductType::getProductTypeIdByName(ProductType::PPP)) {
                $this->product->quarantine_period = null;
                $this->product->information = null;
            }

            $this->product->save();

            if (count($this->product->activeIngredients)) {
                ProductActiveIngredient::where('product_id', $this->product->id)->delete();
            }

            if (isset($attributes['active_ingredients'])) {
                foreach ($attributes['active_ingredients'] as $activeIngredient) {
                    $this->product->activeIngredients()->attach(
                        $activeIngredient['id'],
                        [
                            'quantity' => $activeIngredient['quantity'],
                            'unit_id' => $activeIngredient['unit_id'],
                        ]
                    );
                }
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        return Product::listProductsCustom()->forProduct($this->product->id)->first();
    }

    public function forProduct(Product $product): UpdateProductAction
    {
        $this->product = $product;

        return $this;
    }
}
