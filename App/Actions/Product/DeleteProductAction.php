<?php

namespace App\Actions\Product;

use App\Models\ProductActiveIngredient;
use App\Models\Products\Product;
use DB;
use Exception;

class DeleteProductAction
{
    public function forProduct(Product $product): void
    {
        DB::beginTransaction();

        try {
            ProductActiveIngredient::where('product_id', $product->id)->delete();
            $product->delete();

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }
    }
}
