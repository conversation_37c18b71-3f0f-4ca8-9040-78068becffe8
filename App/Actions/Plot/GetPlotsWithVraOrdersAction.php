<?php

namespace App\Actions\Plot;

use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GetPlotsWithVraOrdersAction
{
    public function withAttributes(array $attributes): array
    {
        $limit = $attributes['limit'] ?? 10;
        $filters['element'] = $attributes['filters']['element'] ?? null;
        $filters['farm_year'] = $attributes['filters']['farm_year'];
        $filters['plot_ids'] = json_decode($attributes['filters']['plot_ids']);
        $filters['farm_ids'] = json_decode($attributes['filters']['farm_ids']);
        $filters['crop_ids'] = json_decode($attributes['filters']['crop_ids']);
        $filters['organization_id'] = $attributes['organization_id'] ?? Auth::user()->lastChosenOrganization->id;

        $soilVraOrdersQuery = OrderSoilVra::getFilteredOrdersQuery($filters)
            ->addSelect(
                'p.gid AS plot_gid',
                'p.uuid AS plot_uuid',
                'p.thumbnail AS plot_thumbnail',
                DB::raw('ST_AsGeoJSON(ST_Transform(p.geom, 3857)) AS geom_json')
            );

        $satelliteVraOrdersQuery = OrderSatelliteVra::getFilteredOrdersQuery($filters)
            ->addSelect(
                'p.gid AS plot_gid',
                'p.uuid AS plot_uuid',
                'p.thumbnail AS plot_thumbnail',
                DB::raw('ST_AsGeoJSON(ST_Transform(p.geom, 3857)) AS geom_json')
            );

        $allVraOrdersQuery = $soilVraOrdersQuery->unionAll($satelliteVraOrdersQuery);

        $vraOrdersByPlotQuery = OrderSoilVra::from('all_vra_orders')
            ->withExpression('all_vra_orders', $allVraOrdersQuery)
            ->select(
                'plot_gid AS gid',
                'plot_name AS name',
                'area',
                'crop',
                'farm',
                DB::raw('plot_uuid AS uuid'),
                DB::raw('plot_thumbnail::text AS thumbnail'),
                'geom_json',
                DB::raw("JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'id', id,
                        'order_id', order_id,
                        'plot_id', plot_gid,
                        'layer_id', layer_id,
                        'created', created,
                        'class_number', class_number,
                        'flat_rate', flat_rate,
                        'data', data,
                        'flat_rate_total', flat_rate_total,
                        'variable_rate_total', variable_rate_total,
                        'difference', difference,
                        'difference_percent', difference_percent,
                        'vector_data_json', vector_data_json::json,
                        'product_percent', product_percent,
                        'product_text', product_text,
                        'tiff_path', tiff_path,
                        'name', name,
                        'status', status,
                        'date', date,
                        'order_type', order_type,
                        'year', year,
                        'organization', organization,
                        'farm', farm,
                        'soil_element', soil_element,
                        'area', area,
                        'plot_name', plot_name,
                        'order_uuid', order_uuid
                    )
                )::json as vra_orders")
            )
            ->groupBy(
                'plot_gid',
                'plot_uuid',
                'plot_name',
                DB::raw('plot_thumbnail::text'),
                'geom_json',
                'area',
                'crop',
                'farm'
            );

        $vraOrdersByPlot = $vraOrdersByPlotQuery
            ->paginate($limit, ['*']);

        $rows = array_map(function ($item) {
            $item->vra_orders = json_decode($item->vra_orders);

            return $item;
        }, $vraOrdersByPlot->items());

        return [
            'rows' => $rows,
            'total' => $vraOrdersByPlot->total(),
        ];
    }
}
