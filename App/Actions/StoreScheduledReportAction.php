<?php

namespace App\Actions;

use App\Models\ScheduledReport;
use App\Models\User;
use InvalidArgumentException;

class StoreScheduledReportAction
{
    /**
     * @var null|User
     */
    private $user;

    public function withAttributes(array $attributes): ScheduledReport
    {
        $this->guardAgainstInvalidArguments();

        $scheduledReport = new ScheduledReport();
        $scheduledReport->fill($attributes);
        $scheduledReport->owner()->associate($this->user);
        $scheduledReport->save();

        return $scheduledReport;
    }

    public function forUser(User $user): StoreScheduledReportAction
    {
        $this->user = $user;

        return $this;
    }

    private function guardAgainstInvalidArguments()
    {
        if (blank($this->user)) {
            throw new InvalidArgumentException('You need to provide the user using the forUser() method');
        }
    }
}
