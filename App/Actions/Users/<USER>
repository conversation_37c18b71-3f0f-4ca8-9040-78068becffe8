<?php

namespace App\Actions\Users;

use App\Models\Farm;
use App\Models\User;

class ManageUserFarmsAccessAction
{
    private User $user;

    public function forUser(User $user): ManageUserFarmsAccessAction
    {
        $this->user = $user;

        return $this;
    }

    public function manageFarmsAccess(array $farmsData)
    {
        if (!$this->user || !count($farmsData)) {
            return;
        }

        $farmsToDetachUuids = array_column($farmsData, 'uuid');
        $farmsToDetachIds = Farm::getByUuids($farmsToDetachUuids)->pluck('id')->toArray();

        $farmsToAttachUuids = array_column(
            array_filter($farmsData, function ($farm) {
                return $farm['isAttached'];
            }),
            'uuid'
        );
        $farmsToAttachIds = Farm::getByUuids($farmsToAttachUuids)->pluck('id')->toArray();

        $this->user->farms()->detach($farmsToDetachIds);
        if (count($farmsToAttachIds)) {
            $this->user->farms()->attach($farmsToAttachIds);
        }

        return $this->user->farms();
    }
}
