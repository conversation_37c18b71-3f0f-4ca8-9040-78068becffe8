<?php

namespace App\Http\Middleware;

use App;
use Closure;
use Config;

class SetLang
{
    public function handle($request, Closure $next)
    {
        if ($request->lang) {
            App::setLocale(strtolower($request->lang));

            $format = Config::get('globals.' . strtoupper(Config::get('app.locale')) . '_DATE_FORMAT');
            if ($format) {
                Config::set('globals.DATE_FORMAT', $format);
            }

            $format_sql = Config::get('globals.' . strtoupper(Config::get('app.locale')) . '_DATE_FORMAT_SQL');
            if ($format_sql) {
                Config::set('globals.DATE_FORMAT_SQL', $format_sql);
            }

            $crop_name_lang = Config::get('globals.CROP_NAME_' . strtoupper(Config::get('app.locale')));
            if ($crop_name_lang) {
                Config::set('globals.CROP_NAME', $crop_name_lang);
            }
        }

        return $next($request);
    }
}
