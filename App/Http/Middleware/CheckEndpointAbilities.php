<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Middleware;

use App\Exceptions\ForbiddenException;
use App\Models\ApiEndpoint;
use Closure;
use Route;

class CheckEndpointAbilities
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function handle($request, Closure $next)
    {
        if ($request->user()->globalUser()->isAn('SUPER_ADMIN')) {
            return $next($request);
        }

        /** @var \Illuminate\Routing\Route $route */
        $route = Route::getRoutes()->match($request);

        $action = $route->getActionName();
        $controller = str_before($action, '@');
        $method = str_after($action, '@');

        $apiEndpoint = ApiEndpoint::where('class', $controller)->where('method', $method)->with('abilities')->first();
        if (!$apiEndpoint) {
            return $next($request);
        }

        $abilities = $apiEndpoint->abilities;
        if (empty($abilities)) {
            return $next($request);
        }

        foreach ($abilities as $ability) {
            if ($request->user()->globalUser()->cannot($ability->name)) {
                throw new ForbiddenException();
            }
        }

        return $next($request);
    }
}
