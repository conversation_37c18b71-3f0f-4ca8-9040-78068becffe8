<?php

namespace App\Http\Middleware;

use App\Exceptions\ForbiddenException;
use Auth;
use Closure;

class ReduceGDDEntriesLeft
{
    public function handle($request, Closure $next)
    {
        if (!Auth::user()->gdd_entries_left > 0) {
            throw new ForbiddenException('no_entries_left');
        }
        if ('2017-10-01' == $request->get('sowing_date') && '-' == $request->get('crop')) {
            Auth::user()->gdd_entries_left--;
            Auth::user()->save();
        }

        return $next($request);
    }
}
