<?php

namespace App\Http\Middleware;

use App\Models\Country;
use App\Models\User;
use Auth;
use Closure;
use Illuminate\Database\Eloquent\Model;

class SetUserData
{
    public function handle($request, Closure $next)
    {
        $user = Auth::user();
        if ($user) {
            $countryId = $user->country;
            /** @var Country $country */
            $country = Country::find($countryId);

            Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

            $localUser = User::find($user->old_id);
            Auth::setUser($localUser);
        }

        return $next($request);
    }
}
