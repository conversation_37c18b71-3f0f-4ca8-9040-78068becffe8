<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Middleware;

use App\Exceptions\ForbiddenException;
use Closure;
use Illuminate\Contracts\Auth\Guard;

class Ability
{
    protected $auth;

    /**
     * Creates a new instance of the middleware.
     */
    public function __construct(Guard $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function handle($request, Closure $next, $abilities)
    {
        if ($this->auth->guest()) {
            throw new ForbiddenException();
        }

        $abilitiesArray = explode('|', $abilities);
        foreach ($abilitiesArray as $ability) {
            if ($request->user()->globalUser()->cannot($ability)) {
                throw new ForbiddenException();
            }
        }

        return $next($request);
    }
}
