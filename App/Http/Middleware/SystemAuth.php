<?php

namespace App\Http\Middleware;

use App\Exceptions\MissingScopeException;
use App\Models\ConfigParamValue;
use App\Models\Country;
use App\Services\Auth\KeycloakResourceServer;
use App\Services\Auth\TokenValidator;
use Closure;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

class SystemAuth
{
    /**
     * Handle an incoming request.
     *
     * @param array $scopes
     */
    public function handle(Request $request, Closure $next, ...$scopes)
    {
        try {
            $resourceServer = new KeycloakResourceServer(new TokenValidator());
            $token = $resourceServer->getTokenValidator()->verifyToken($request);

            foreach ($scopes as $scope) {
                if (!$token->can($scope)) {
                    throw new MissingScopeException($scope);
                }
            }
        } catch (Exception $ex) {
            throw $ex;
        }

        if (!$request->get('country')) {
            return response('Country code is required.', 400);
        }

        $country = Country::where('iso_alpha_2_code', strtoupper($request->get('country')))->get()->first();

        // @var ConfigParamValue[] $configParams
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain . '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));
        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        return $next($request);
    }
}
