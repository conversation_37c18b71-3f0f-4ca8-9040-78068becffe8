<?php

namespace App\Http\Middleware;

use App\Models\ConfigParamValue;
use App\Models\Country;
use App\Models\User;
use Auth;
use Closure;
use Config;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class SetAdminUserData
{
    public function handle($request, Closure $next)
    {
        /** @var User $user */
        $user = Auth::user();

        /** @var Country $country */
        $country = $user->globalUser()->country()->get()->first();

        // @var ConfigParamValue[] $configParams
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain . '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));

        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        return $next($request);
    }
}
