<?php

namespace App\Http\Middleware;

use App\Traits\Mail\ServiceProviderConfigurableMail;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SetServiceProviderOutgoingEmail
{
    use ServiceProviderConfigurableMail;

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $serviceProvider = Auth::user()->globalUser()->serviceProvider;
        $this->configureServiceProviderMail($serviceProvider);

        return $next($request);
    }
}
