<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Middleware;

use App\Models\RequestLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RequestLogger
{
    private $start;
    private $end;

    public function handle(Request $request, Closure $next)
    {
        $this->start = microtime(true);

        return $next($request);
    }

    public function terminate(Request $request, $response)
    {
        $this->end = microtime(true);

        $this->log($request, $response);
    }

    private function log(Request $request, $response)
    {
        $log = new RequestLog();
        $log->ip = $request->getClientIp();
        $log->url = $request->fullUrl();
        $log->method = $request->getMethod();
        $log->status = $response->getStatusCode();
        $log->duration = number_format($this->end - $this->start, 3);
        $log->user_id = Auth::user() ? Auth::user()->globalUser()->id : null;
        $log->request = json_encode($request->all());

        $log->save();
    }
}
