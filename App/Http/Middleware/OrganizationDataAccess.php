<?php

namespace App\Http\Middleware;

use App\Exceptions\ForbiddenException;
use Auth;
use Closure;

class OrganizationDataAccess
{
    public function handle($request, Closure $next)
    {
        $organizationId = $request->get('organization_id');

        if (!$organizationId) {
            return $next($request);
        }

        $user = Auth::user();
        $userOrganizations = $user->organizations->pluck('id');

        if (!$userOrganizations->contains($organizationId)) {
            throw new ForbiddenException('organization_no_data_access');
        }

        return $next($request);
    }
}
