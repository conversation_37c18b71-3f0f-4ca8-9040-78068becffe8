<?php

namespace App\Http\Requests\Orders;

use Illuminate\Foundation\Http\FormRequest;

class PostSoilPlotsDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'plotsData' => ['required', 'array'],
            'plotsData.*.orderUuid' => ['required', 'string'],
            'plotsData.*.plotUuid' => ['required', 'string'],
            'plotsData.*.sampler_id' => ['required', 'integer'],
            'plotsData.*.sampling_types' => ['required', 'array'],
            'plotsData.*.package_type' => ['required', 'string'],
            'plotsData.*.cells' => ['sometimes', 'array'],
            'plotsData.*.demo_sampling' => ['sometimes', 'boolean'],
        ];
    }
}
