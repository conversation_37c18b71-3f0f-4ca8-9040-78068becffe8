<?php

namespace App\Http\Requests\Orders;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSamplingPlotsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'plotsData' => [
                'required',
                'array',
            ],
            'plotsData.*.order_uuid' => [
                'required',
                'string',
            ],
            'plotsData.*.plot_uuid' => [
                'required',
                'string',
            ],
            'plotsData.*.sampler_id' => [
                'sometimes',
                'integer',
            ],
            'plotsData.*.package_id' => [
                'required',
                'integer',
            ],
            'plotsData.*.cells' => [
                'required',
                'array',
            ],
            'plotsData.*.cells.sampling' => [
                'required_without:plotsData.*.cells.for_sampling',
                'array',
            ],
            'plotsData.*.cells.sampling.*' => [
                'integer',
            ],
            'plotsData.*.cells.for_sampling' => [
                'required_without:plotsData.*.cells.sampling',
                'array',
            ],
            'plotsData.*.cells.for_sampling.*' => [
                'integer',
            ],
            'plotsData.*.cells.not_sampled' => [
                'sometimes',
                'array',
            ],
            'plotsData.*.cells.not_sampled.*' => [
                'integer',
            ],
            'plotsData.*.demo_sampling' => [
                'sometimes',
                'boolean',
            ],
        ];
    }
}
