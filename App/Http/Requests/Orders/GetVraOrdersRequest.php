<?php

namespace App\Http\Requests\Orders;

use Illuminate\Foundation\Http\FormRequest;

class GetVraOrdersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'page' => [
                'sometimes',
                'integer',
            ],
            'limit' => [
                'sometimes',
                'integer',
            ],
            'filters' => [
                'array',
            ],
            'filters.element' => [
                'string',
            ],
            'filters.farm_year' => [
                'integer',
            ],
            'filters.plot_ids' => [
                'json',
            ],
            'filters.farm_ids' => [
                'json',
            ],
            'filters.crop_ids' => [
                'json',
            ],
            'filters.organization_id' => [
                'integer',
            ],
            'withPagination' => [
                'sometimes',
                'boolean',
            ],
        ];
    }
}
