<?php

namespace App\Http\Requests\GuidanceLine;

use Illuminate\Foundation\Http\FormRequest;

class IndexGuidanceLineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'limit' => [
                'sometimes',
                'integer',
                'min:1',
                'max:50',
            ],
            'plot_id' => [
                'sometimes',
                'integer',
            ],
        ];
    }
}
