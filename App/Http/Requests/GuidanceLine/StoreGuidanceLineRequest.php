<?php

namespace App\Http\Requests\GuidanceLine;

use App\Models\GuidanceLine;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreGuidanceLineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'guidance_lines' => [
                'required',
                'array',
                'min:1',
            ],
            'guidance_lines.*.name' => [
                'required',
                'string',
                'max:255',
            ],
            'guidance_lines.*.type' => [
                'required',
                'string',
                Rule::in(GuidanceLine::getAllGuidanceLineTypes()),
            ],
            'guidance_lines.*.plot_id' => [
                'required',
                'integer',
            ],
            'guidance_lines.*.shift' => [
                'sometimes',
                'numeric',
            ],
            'guidance_lines.*.offset' => [
                'sometimes',
                'numeric',
            ],
            'guidance_lines.*.geom_json' => [
                'required',
                'array',
            ],
        ];
    }
}
