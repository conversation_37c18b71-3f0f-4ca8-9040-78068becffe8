<?php

namespace App\Http\Requests\GuidanceLine;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory as ValidationFactory;

class ExportGuidanceLinesRequest extends FormRequest
{
    public function __construct(ValidationFactory $validationFactory)
    {
        $validationFactory->extend(
            'json_int_array',
            function ($attribute, $value, $parameters) {
                $arrayValue = json_decode($value);

                return is_array($arrayValue)
                        && count($arrayValue) > 0
                        && is_numeric($arrayValue[0]);
            },
            'The json value must be an array of integers and contain at least one item.'
        );
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'organization_id' => [
                'sometimes',
                'required',
                'integer',
            ],
            'ids' => [
                'sometimes',
                'required',
                'json_int_array',
            ],
        ];
    }
}
