<?php

namespace App\Http\Requests\GuidanceLine;

use App\Models\GuidanceLine;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateGuidanceLineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'type' => [
                'required',
                'string',
                Rule::in(GuidanceLine::getAllGuidanceLineTypes()),
            ],
            'plot_id' => [
                'required',
                'integer',
            ],
            'shift' => [
                'required',
                'numeric',
            ],
            'offset' => [
                'required',
                'numeric',
            ],
            'geom_json' => [
                'required',
                'array',
            ],
        ];
    }
}
