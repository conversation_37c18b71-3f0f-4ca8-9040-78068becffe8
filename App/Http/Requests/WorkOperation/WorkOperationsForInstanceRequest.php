<?php

namespace App\Http\Requests\WorkOperation;

use App\Models\WorkOperation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkOperationsForInstanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'for_instance' => [
                'required',
                'string',
                Rule::in(WorkOperation::forInstance()),
            ],
            'filters' => [
                'sometimes',
                'required',
                'array',
                'min:1',
            ],
            'filters.start_date' => [
                'sometimes',
                'date_format:Y-m-d',
            ],
            'filters.end_date' => [
                'sometimes',
                'date_format:Y-m-d',
            ],
            'filters.farm_ids' => [
                'sometimes',
                'required',
                'json',
            ],
            'filters.plot_ids' => [
                'sometimes',
                'required',
                'json',
            ],
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'for_instance' => $this->route('for_instance'),
        ]);
    }
}
