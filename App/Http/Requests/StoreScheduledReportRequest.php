<?php

namespace App\Http\Requests;

use App\Models\ScheduledReport;
use App\Rules\AllowedOrderByColumnsForReportType;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Fluent;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class StoreScheduledReportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        // TODO:: Add crops filter !
        return [
            'recipient_emails' => [
                'required',
                'array',
                'min:1',
            ],
            'recipient_emails.*' => [
                'string',
                'email',
            ],
            'subject' => [
                'required',
                'string',
                'max:255',
            ],
            'body' => [
                'nullable',
                'string',
                'max:10000',
            ],
            'locale' => [
                'sometimes',
                'nullable',
                'min:2',
                'max:2',
            ],
            'file_type' => [
                'required',
                Rule::in(ScheduledReport::getAllFileTypes()),
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'report_parameters' => [
                'required',
                'array',
                'min:1',
            ],
            'report_parameters.group_by' => [
                'required',
                'string',
            ],
            'report_parameters.order_by' => [
                'bail',
                'sometimes',
                'array',
                'min:1',
                new AllowedOrderByColumnsForReportType($this->input('type')),
            ],
            'report_parameters.farm_ids' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'report_parameters.farm_ids.*' => [
                'sometimes',
                'integer',
            ],
            'report_parameters.plot_ids' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'report_parameters.plot_ids.*' => [
                'sometimes',
                'integer',
            ],
            'report_parameters.crop_ids' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'report_parameters.crop_ids.*' => [
                'sometimes',
                'integer',
            ],
            'report_date_range' => [
                'required',
                'string',
                Rule::in(ScheduledReport::getAllReportDateRanges()),
            ],
            'delivery_frequency' => [
                'required',
                Rule::in(ScheduledReport::getAllDeliveryFrequencies()),
            ],
            'delivery_time' => [
                'required',
                Rule::in(ScheduledReport::getAllTimeSlots()),
            ],
            'delivery_days' => [
                'required',
                'array',
                'min:1',
            ],
            'delivery_days.*' => [
                'required',
            ],
            'type' => [
                'required',
                Rule::in(ScheduledReport::getAllReportTypes()),
            ],
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->sometimes(
            'report_parameters.group_by',
            [
                Rule::in(ScheduledReport::getAllowedGroupByValuesFor(ScheduledReport::REPORT_TYPE_MACHINES_TASKS)),
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.group_by',
            [
                Rule::in(ScheduledReport::getAllowedGroupByValuesFor(ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS)),
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.group_by',
            [
                Rule::in(ScheduledReport::getAllowedGroupByValuesFor(ScheduledReport::REPORT_TYPE_IRRIGATION_TASKS)),
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_IRRIGATION_TASKS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.platform_ids',
            [
                'sometimes',
                'array',
                'min:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_IRRIGATION_TASKS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.platform_ids.*',
            [
                'sometimes',
                'integer',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_IRRIGATION_TASKS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.product_ids',
            [
                'sometimes',
                'array',
                'min:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.product_ids.*',
            [
                'sometimes',
                'integer',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.machine_ids',
            [
                'sometimes',
                'array',
                'min:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'report_parameters.machine_ids.*',
            [
                'sometimes',
                'integer',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'report_parameters.implements',
            [
                'sometimes',
                'array',
                'min:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'report_parameters.implements.*',
            [
                'sometimes',
                'string',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'report_parameters.stages',
            [
                'sometimes',
                'array',
                'min:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'report_parameters.stages.*',
            [
                'sometimes',
                'string',
                Rule::in(['Proposed', 'Approved']),
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'report_parameters.types',
            [
                'sometimes',
                'array',
                'min:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS
                    || ScheduledReport::REPORT_TYPE_IRRIGATION_TASKS;
            }
        );

        $validator->sometimes(
            'report_parameters.types.*',
            [
                'sometimes',
                'string',
                Rule::in(['irrigation', 'alert', 'no data', 'movement', 'transportation']),
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_IRRIGATION_TASKS === $input->get('type');
            }
        );

        $validator->sometimes(
            'report_parameters.types.*',
            [
                'sometimes',
                'string',
                Rule::in(['Work', 'Transportation']),
            ],
            function (Fluent $input) {
                return ScheduledReport::REPORT_TYPE_MACHINES_TASKS === $input->get('type')
                    || ScheduledReport::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS;
            }
        );

        $validator->sometimes(
            'delivery_days',
            [
                'max:7',
            ],
            function (Fluent $input) {
                return ScheduledReport::DELIVERY_FREQUENCY_DAILY === $input->get('delivery_frequency');
            }
        );

        $validator->sometimes(
            'delivery_days',
            [
                'max:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::DELIVERY_FREQUENCY_WEEKLY === $input->get('delivery_frequency');
            }
        );

        $validator->sometimes(
            'delivery_days',
            [
                'max:1',
            ],
            function (Fluent $input) {
                return ScheduledReport::DELIVERY_FREQUENCY_MONTHLY === $input->get('delivery_frequency');
            }
        );

        $validator->sometimes(
            'delivery_days.*',
            [
                Rule::in(Carbon::getDays()),
            ],
            function (Fluent $input) {
                return ScheduledReport::DELIVERY_FREQUENCY_DAILY === $input->get('delivery_frequency')
                    || ScheduledReport::DELIVERY_FREQUENCY_WEEKLY === $input->get('delivery_frequency');
            }
        );

        $validator->sometimes(
            'delivery_days.*',
            [
                Rule::in(ScheduledReport::getAllDeliveryDaysForMonthlyFrequency()),
            ],
            function (Fluent $input) {
                return ScheduledReport::DELIVERY_FREQUENCY_MONTHLY === $input->get('delivery_frequency');
            }
        );
    }
}
