<?php

namespace App\Http\Requests\MachineTaskProducts;

use Illuminate\Foundation\Http\FormRequest;

class GetReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'driver' => 'sometimes|string',
            'farm_ids' => 'sometimes|json',
            'plot_ids' => 'sometimes|json',
            'machine_ids' => 'sometimes|json',
            'implements' => 'sometimes|json',
            'stages' => 'sometimes|json',
            'crop_ids' => 'sometimes|json',
            'work_operations' => 'sometimes|json',
            'task_states' => 'sometimes|json',
            'product_ids' => 'sometimes|json',
        ];
    }
}
