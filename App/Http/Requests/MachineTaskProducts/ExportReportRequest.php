<?php

namespace App\Http\Requests\MachineTaskProducts;

use Illuminate\Foundation\Http\FormRequest;

class ExportReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'lang' => 'required|string|min:2|max:2',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|array',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'driver' => 'string',
            'farm_ids' => 'sometimes|array',
            'farm_ids.*' => 'required|integer',
            'plot_ids' => 'sometimes|array',
            'plot_ids.*' => 'required|integer',
            'machine_ids' => 'sometimes|array',
            'machine_ids.*' => 'required|integer',
            'implements' => 'sometimes|array',
            'implements.*' => 'required|string',
            'stages' => 'sometimes|array',
            'stages.*' => 'required|string',
            'crop_ids' => 'sometimes|array',
            'crop_ids.*' => 'required|integer',
            'work_operations' => 'sometimes|array',
            'work_operations.*' => 'required|integer',
            'task_states' => 'sometimes|array',
            'task_states.*' => 'required|string',
            'products_ids' => 'sometimes|array',
            'products_ids.*' => 'required|array',
            'selected_columns' => 'sometimes|array',
            'selected_columns.*' => 'required|string',
            'type' => 'required|string|in:pdf,xls',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'type' => $this->route('type'),
        ]);
    }
}
