<?php

namespace App\Http\Requests\Users;

use Illuminate\Foundation\Http\FormRequest;

class ManageUserFarmsAccessRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'farms' => [
                'array',
                'required',
            ],
            'farms.*.uuid' => [
                'required',
                'uuid',
            ],
            'farms.*.isVisible' => [
                'required',
                'boolean',
            ],
            'farms.*.isAttached' => [
                'required',
                'boolean',
            ],
        ];
    }
}
