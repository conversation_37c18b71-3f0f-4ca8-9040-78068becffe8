<?php

namespace App\Http\Requests\Users;

use Illuminate\Foundation\Http\FormRequest;

class FindUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'username' => [
                'requiredWithout:email',
                'string',
            ],
            'email' => [
                'requiredWithout:username',
                'email',
            ],
        ];
    }
}
