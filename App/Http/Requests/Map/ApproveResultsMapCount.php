<?php

namespace App\Http\Requests\Map;

use Illuminate\Foundation\Http\FormRequest;

class ApproveResultsMapCount extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'filter' => [
                'array',
                'required',
            ],
            'filter.element_group_state' => [
                'string',
                'required',
            ],
            'filter.plot_uuids' => [
                'json',
                'sometimes',
                'required',
            ],
            'filter.package_id' => [
                'integer',
                'sometimes',
                'required',
            ],
            'filter.element_state_diff' => [
                'string',
                'sometimes',
                'required',
            ],
        ];
    }
}
