<?php

namespace App\Http\Requests\MachineUnits;

use App\Models\MachineUnit;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetMachineUnitTrackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get data to be validated from the request.
     */
    public function validationData(): array
    {
        if (method_exists($this->route(), 'parameters')) {
            // Add route parameters to the request.
            $this->request->add($this->route()->parameters());

            return array_merge($this->all(), $this->route()->parameters());
        }

        return $this->all();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'from' => ['required_with:to', 'string'],
            'to' => ['required_with:from', 'string'],
            'format' => ['string', Rule::in([
                MachineUnit::TRACK_FORMAT_GEOJSON,
                MachineUnit::TRACK_FORMAT_GPX,
            ])],
        ];
    }
}
