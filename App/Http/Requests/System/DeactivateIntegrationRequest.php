<?php

namespace App\Http\Requests\System;

use Illuminate\Foundation\Http\FormRequest;

class DeactivateIntegrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'package_id' => 'required|integer',
            'organization_id' => 'required|integer',
            'country' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'package_id.required' => 'Package ID is required.',
            'package_id.integer' => 'Package ID must be an integer.',
            'organization_id.required' => 'Organization ID is required.',
            'organization_id.integer' => 'Organization ID must be an integer.',
            'country.required' => 'Country code is required.',
            'country.string' => 'Country code must be a string.',
        ];
    }
}
