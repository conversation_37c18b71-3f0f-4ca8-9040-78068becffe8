<?php

namespace App\Http\Requests\Farms;

use App\Models\Farm;
use App\Models\Organization;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class StoreFarmRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'uuid' => [
                'required',
                'uuid',
            ],
            'name' => [
                'required',
                'string',
            ],
            'organization_id' => [
                'required',
                'integer',
            ],
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $organization = Organization::findOrFail($this->get('organization_id'));

            if (Farm::getByName($organization, $this->get('name'))) {
                $validator->errors()->add('duplicate_value', 'Farm already exists');
            }
        });
    }
}
