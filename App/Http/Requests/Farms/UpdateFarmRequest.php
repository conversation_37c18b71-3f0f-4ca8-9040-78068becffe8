<?php

namespace App\Http\Requests\Farms;

use App\Models\Farm;
use App\Models\Organization;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class UpdateFarmRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $farm = $this->route('farm');

        return $this->user()->can('accessFarm', $farm);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'organization_id' => [
                'required',
                'integer',
            ],
            'name' => [
                'string',
            ],
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $farm = $this->route('farm');
            $organization = Organization::find($this->get('organization_id'));

            if (!$organization) {
                if ($this->get('organization_id')) {
                    $validator->errors()->add('organization_id', 'Organization not found');
                }

                return;
            }

            $existingFarm = Farm::getByName($organization, $this->get('name'));

            if ($existingFarm && $existingFarm->uuid !== $farm->uuid) {
                $validator->errors()->add('duplicate_value', 'Farm already exists');
            }
        });
    }
}
