<?php

namespace App\Http\Requests\Integration;

use Illuminate\Foundation\Http\FormRequest;

class UpdateIntegrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'token' => 'string',
            'integration_address_id' => 'integer',
            'status' => 'string|in:Active,Inactive',
            'machine_units' => 'array',
            'machine_units.*.id' => 'present|integer|nullable',
            'machine_units.*.name' => 'required|string',
            'machine_units.*.type' => 'required|string',
            'machine_units.*.last_communication' => 'present|string|nullable',
            'machine_units.*.last_position_geojson' => 'present|nullable|array',
            'machine_units.*.wialon_unit_imei' => 'required|integer',
            'machine_units.*.wialon_unit_id' => 'present|integer|nullable',
            'machine_units.*.mode' => 'required|string|in:create,update,delete',
            'machine_implements' => 'array',
            'machine_implements.*.id' => 'present|integer|nullable',
            'machine_implements.*.name' => 'required|string',
            'machine_implements.*.width' => 'required|numeric',
            'machine_implements.*.work_operation_ids' => 'array',
            'machine_implements.*.work_operation_ids.*' => 'int',
            'machine_implements.*.status' => 'required|string',
            'machine_implements.*.wialon_unit_id' => 'present|integer|nullable',
            'machine_implements.*.mode' => 'required|string|in:create,update,delete',
        ];
    }
}
