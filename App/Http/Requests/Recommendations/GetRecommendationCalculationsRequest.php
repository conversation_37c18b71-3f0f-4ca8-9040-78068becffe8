<?php

namespace App\Http\Requests\Recommendations;

use Illuminate\Foundation\Http\FormRequest;

class GetRecommendationCalculationsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get data to be validated from the request.
     */
    public function validationData(): array
    {
        $subscriptionPackageFieldId = $this->route('subscriptionPackageFieldId');

        return array_merge($this->all(), ['subscription_package_field_id' => $subscriptionPackageFieldId]);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'subscription_package_field_id' => 'required|integer',
            'crop_id' => 'required|integer',
            'model_id' => 'required|integer',
            'humus' => 'required|numeric',
            'yield' => 'required|numeric',
            'valid_from' => 'required|date_format:Y-m-d',
            'sampling_type_ids' => 'json',
        ];
    }
}
