<?php

namespace App\Http\Requests\Recommendations;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRecommendationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer',
            'crop_id' => 'sometimes|required|integer',
            'model_id' => 'sometimes|required|integer',
            'humus' => 'sometimes|required|numeric',
            'valid_from' => 'sometimes|required|date_format:Y-m-d',
            'yield' => 'sometimes|required|numeric',
            'results' => 'sometimes|required|array',
            'results.*.result_element' => 'required|string',
            'results.*.result_element_value' => 'required|numeric',
            'comments' => 'sometimes|required|array',
            'comments.*.result_element' => 'string|nullable',
            'comments.*.comment_text' => 'required|string',
            'sampling_type_ids' => 'required|array',
            'susces' => 'sometimes|required|array',
            'susces.*.value' => 'boolean',
            'susces.*.element' => 'string',
            'orders' => 'sometimes|required|array',
            'sampling_type_ids' => 'sometimes|required|array',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $subscriptionPackageFieldId = $this->route('id');

        $this->merge([
            'id' => $subscriptionPackageFieldId,
        ]);
    }
}
