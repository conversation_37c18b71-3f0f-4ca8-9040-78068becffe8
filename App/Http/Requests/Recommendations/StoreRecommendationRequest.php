<?php

namespace App\Http\Requests\Recommendations;

use Illuminate\Foundation\Http\FormRequest;

class StoreRecommendationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'subscription_package_field_id' => 'required|integer',
            'plot_name' => 'required|string',
            'crop_id' => 'required|integer',
            'model_id' => 'required|integer',
            'humus' => 'required|numeric',
            'valid_from' => 'required|date_format:Y-m-d',
            'yield' => 'required|numeric',
            'results' => 'required|array',
            'results.*.result_element' => 'required|string',
            'results.*.result_element_value' => 'required|numeric',
            'comments' => 'required|array',
            'comments.*.result_element' => 'string|nullable',
            'comments.*.comment_text' => 'required|string',
            'sampling_type_ids' => 'required|array',
            'orders' => 'array',
            'susces' => 'array',
            'susces.*.value' => 'boolean',
            'susces.*.element' => 'string',
        ];
    }
}
