<?php

namespace App\Http\Requests\SamplingType;

use Illuminate\Foundation\Http\FormRequest;

class SamplingTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'filter' => [
                'sometimes',
                'array',
            ],
            'filter.plot_uuid' => [
                'sometimes',
                'string',
            ],
            'filter.package_id' => [
                'sometimes',
                'integer',
            ],
            'filter.start_date' => [
                'required_with:end_date',
                'string',
            ],
            'filter.end_date' => [
                'required_with:start_date',
                'string',
            ],
        ];
    }
}
