<?php

namespace App\Http\Requests\Crop;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CropRotationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'farm_ids' => [
                'required',
                'json',
            ],
            'farm_ids.*' => [
                'sometimes',
                'integer',
            ],
            'plot_ids' => [
                'required',
                'json',
            ],
            'plot_ids.*' => [
                'sometimes',
                'integer',
            ],
            'farm_years' => [
                'required',
                'json',
            ],
            'farm_years.*' => [
                'sometimes',
                'integer',
            ],
            'lang' => [
                'required',
                'string',
                'min:2',
                'max:2',
            ],
            'order_by' => [
                'sometimes',
                'required',
                'json',
            ],
            'sort.*' => [
                'sometimes',
                'string',
                Rule::in(['asc', 'desc']),
            ],
            'limit' => [
                'required',
                'string',
            ],
            'page' => [
                'required',
                'string',
                'min:1',
            ],
            'crops_filter' => [
                'sometimes',
                'required',
                'json',
            ],
            'crops_filter.farm_year' => [
                'sometimes',
                'required',
                'integer',
            ],
            'crops_filter.crops' => [
                'sometimes',
                'required',
                'array',
            ],
            'crops_filter.crops.planned_crops' => [
                'sometimes',
                'required',
                'array',
            ],
            'crops_filter.crops.planned_crops.*' => [
                'sometimes',
                'required',
                'integer',
            ],
            'crops_filter.crops.alternative_crops' => [
                'sometimes',
                'required',
                'array',
            ],
            'crops_filter.crops.alternative_crops.*' => [
                'sometimes',
                'required',
                'integer',
            ],
        ];
    }
}
