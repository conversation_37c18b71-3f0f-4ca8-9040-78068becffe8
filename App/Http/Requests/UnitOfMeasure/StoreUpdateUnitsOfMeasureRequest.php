<?php

namespace App\Http\Requests\UnitOfMeasure;

use Illuminate\Foundation\Http\FormRequest;

class StoreUpdateUnitsOfMeasureRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'full_name' => [
                'required',
                'string',
            ],
            'short_name' => [
                'required',
                'string',
            ],
            'category_id' => [
                'required',
                'integer',
            ],
            'organization_id' => [
                'required',
                'integer',
            ],
            'base_unit_id' => [
                'required_with:coefficient',
                'required_without:numerator_unit_id,denominator_unit_id',
                'integer',
            ],
            'coefficient' => [
                'required_with:base_unit_id',
                'required_without:numerator_unit_id,denominator_unit_id',
                'numeric',
            ],
            'numerator_unit_id' => [
                'required_with:denominator_unit_id',
                'required_without:base_unit_id,coefficient',
                'integer',
            ],
            'denominator_unit_id' => [
                'required_with:numerator_unit_id',
                'required_without:base_unit_id,coefficient',
                'integer',
            ],
        ];
    }
}
