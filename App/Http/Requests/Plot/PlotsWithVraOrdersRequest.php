<?php

namespace App\Http\Requests\Plot;

use Illuminate\Foundation\Http\FormRequest;

class PlotsWithVraOrdersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'lang' => [
                'required',
                'string',
                'min:2',
                'max:2',
            ],
            'page' => [
                'sometimes',
                'required',
                'integer',
            ],
            'limit' => [
                'sometimes',
                'required',
                'integer',
            ],
            'organization_id' => [
                'sometimes',
                'required',
                'integer',
            ],
            'filters.element' => [
                'string',
                'sometimes',
                'required',
            ],
            'filters' => [
                'array',
                'required',
            ],
            'filters.farm_year' => [
                'integer',
                'required',
            ],
            'filters.plot_ids' => [
                'json',
                'required',
            ],
            'filters.farm_ids' => [
                'json',
                'required',
            ],
            'filters.crop_ids' => [
                'json',
                'required',
            ],
        ];
    }
}
