<?php

namespace App\Http\Requests\Plot;

use Illuminate\Foundation\Http\FormRequest;

class PlotWithGuidanceLineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'lang' => [
                'required',
                'string',
                'min:2',
                'max:2',
            ],
            'page' => [
                'required',
                'integer',
            ],
            'limit' => [
                'required',
                'integer',
            ],
            'filters' => [
                'required',
                'array',
            ],
            'filters.plot_ids' => [
                'required',
                'json',
            ],
            'filters.plot_name' => [
                'sometimes',
                'string',
            ],
            'filters.farm_ids' => [
                'required',
                'json',
            ],
            'filters.farm_year' => [
                'required',
                'integer',
            ],
            'filters.crop_ids' => [
                'required',
                'json',
            ],
        ];
    }
}
