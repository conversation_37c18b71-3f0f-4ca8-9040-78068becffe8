<?php

namespace App\Http\Requests\Plot;

use Illuminate\Foundation\Http\FormRequest;

class PlotsMachineTasksRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'organization_id' => [
                'sometimes',
                'integer',
            ],
            'page' => [
                'required',
                'integer',
            ],
            'limit' => [
                'required',
                'integer',
            ],
            'lang' => [
                'sometimes',
                'required',
                'string',
                'min:2',
                'max:2',
            ],
            'filters' => [
                'required',
                'array',
            ],
            'filters.plot_ids' => [
                'required',
                'json',
            ],
            'filters.farm_ids' => [
                'required',
                'json',
            ],
            'filters.farm_year' => [
                'required',
                'integer',
            ],
            'filters.crop_ids' => [
                'required',
                'json',
            ],
            'filters.work_operation_ids' => [
                'required',
                'json',
            ],
            'filters.task_states' => [
                'required',
                'json',
            ],
            'filters.start_date' => [
                'required',
                'integer',
            ],
            'filters.end_date' => [
                'required',
                'integer',
            ],
        ];
    }
}
