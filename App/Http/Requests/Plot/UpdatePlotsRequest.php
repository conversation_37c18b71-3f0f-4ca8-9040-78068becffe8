<?php

namespace App\Http\Requests\Plot;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePlotsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'plots' => [
                'required',
                'array',
                'min:1',
            ],
            'guidance_lines.*.name' => [
                'required',
                'string',
                'max:255',
            ],
            'plots.*.name' => [
                'required',
                'string',
            ],
            'plots.*.irrigated' => [
                'boolean',
            ],
            'plots.*.crops' => [
                'required',
                'array',
            ],
            'plots.*.crops.*.id' => [
                'required',
                'integer',
            ],
            'plots.*.crops.*.sowing_date' => [
                'required',
                'date_format:Y-m-d',
            ],
            'plots.*.crops.*.harvest_date' => [
                'required',
                'date_format:Y-m-d',
            ],
            'plots.*.crops.*.plot_crop_rel_id' => [
                'sometimes',
                'required',
                'integer',
            ],
            'plots.*.crops.*.is_primary' => [
                'boolean',
            ],
            'plots.*.crops.*.category_id' => [
                'sometimes',
                'integer',
            ],
            'plots.*.crops.*.hybrid_id' => [
                'sometimes',
                'integer',
            ],
            'plots.*.crops.*.should_be_deleted' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'plots.*.farm_id' => [
                'required',
                'integer',
            ],
            'plots.*.farm_year' => [
                'sometimes',
                'required',
                'integer',
            ],
        ];
    }
}
