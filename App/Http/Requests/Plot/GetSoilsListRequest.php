<?php

namespace App\Http\Requests\Plot;

use App\Rules\ArrayStringKeys;
use Illuminate\Foundation\Http\FormRequest;

class GetSoilsListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'lang' => ['required', 'string', 'min:2', 'max:2'],
            'page' => ['required', 'integer'],
            'limit' => ['required', 'integer'],
            'filters' => ['array'],
            'filters.plot_ids' => ['json'],
            'filters.farm_ids' => ['json'],
            'filters.status' => ['json'],
            'filters.is_sampling' => ['boolean'],
            'filters.is_vra' => ['boolean'],
            'filters.farm_year' => ['integer'],
            'filters.crop_ids' => ['json'],
            'sort' => ['array', new ArrayStringKeys()],
            'sort.*' => ['sometimes', 'required', 'string', 'in:asc,desc'],
        ];
    }
}
