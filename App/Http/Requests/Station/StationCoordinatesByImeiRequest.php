<?php

namespace App\Http\Requests\Station;

use App\Models\UserStation;
use Illuminate\Foundation\Http\FormRequest;

class StationCoordinatesByImeiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $allowedStationTypes = UserStation::getStationTypes([UserStation::TYPE_VIRTUAL]);
        $allowedStationTypesStr = implode(',', $allowedStationTypes);

        return [
            'stationType' => "required|string|in:{$allowedStationTypesStr}",
            'stationImei' => 'required|string|max:63',
        ];
    }

    /**
     * Get data to be validated from the request.
     *
     * @return array
     */
    public function validationData()
    {
        if (method_exists($this->route(), 'parameters')) {
            // Add route parameters to the request. (Used for stationImei param)
            $this->request->add($this->route()->parameters());

            return array_merge($this->all(), $this->route()->parameters());
        }

        return $this->all();
    }
}
