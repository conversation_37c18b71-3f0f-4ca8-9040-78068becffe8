<?php

namespace App\Http\Requests\Machines;

use App\Exceptions\ValidationException;
use App\Models\MachineEvent;
use Illuminate\Foundation\Http\FormRequest;

class UpdateMachineEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $machineEvent = $this->route('machineEvent');

        if (MachineEvent::APPROVED !== $machineEvent->stage) {
            throw new ValidationException('Only approved events can be updated!');
        }

        return [
            'driver' => 'required|string',
            'implement_id' => 'sometimes|required|integer',
            'implement_width' => 'sometimes|required|numeric|min:0',
            'work_operation_ids' => 'sometimes|required|array',
            'work_operation_ids.*' => 'int',
            'geom_cultivated' => 'sometimes|required|json',
            'products' => 'sometimes|required|array',
            'products.*.rate' => 'required|numeric',
            'products.*.value' => 'required|numeric',
            'products.*.pest_name' => 'sometimes|nullable|string',
            'products.*.pest_application' => 'sometimes|nullable|string',
            'products.*.pest_quarantine' => 'sometimes|nullable|int',
            'products.*.product' => 'sometimes|nullable|array',
            'products.*.product.id' => 'required|int',
            'products.*.product.name' => 'required|string',
            'products.*.product.type' => 'required|string',
            'products.*.product.unit' => 'required|string',
        ];
    }
}
