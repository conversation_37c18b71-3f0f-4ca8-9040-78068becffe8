<?php

namespace App\Http\Requests\Machines;

use Illuminate\Foundation\Http\FormRequest;

class GetMachineTasksTimelineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'organization_id' => [
                'sometimes',
                'integer',
            ],
            'lang' => [
                'sometimes',
                'required',
                'string',
                'min:2',
                'max:2',
            ],

            'filters' => [
                'required',
                'array',
            ],
            'filters.farm_year' => [
                'required',
                'integer',
            ],
            'filters.start_date' => [
                'required',
                'integer',
            ],
            'filters.end_date' => [
                'required',
                'integer',
            ],
            'filters.plot_ids' => [
                'json',
            ],
            'filters.farm_ids' => [
                'json',
            ],
            'filters.crop_ids' => [
                'json',
            ],
            'filters.work_operation_ids' => [
                'json',
            ],
            'filters.task_states' => [
                'json',
            ],
        ];
    }
}
