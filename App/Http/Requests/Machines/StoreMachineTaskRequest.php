<?php

namespace App\Http\Requests\Machines;

use App\Models\MachineTask;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class StoreMachineTaskRequest extends AbstractMachineTaskRequest
{
    public function rules(): array
    {
        return [
            'organization_id' => [
                'sometimes',
                'integer',
            ],
            'farm_year' => [
                'required',
                'integer',
            ],
            'start_date' => [
                'required',
                'integer',
            ],
            'end_date' => [
                'required',
                'integer',
            ],
            'completion_date' => [
                'sometimes',
                'integer',
            ],
            'plot_id' => [
                'sometimes',
                'integer',
            ],
            'covered_area' => [
                'numeric',
                'required_with:plot_id',
            ],
            'state' => [
                'required',
                'string',
                Rule::in(MachineTask::getAvailableStateForStore()),
            ],
            'machine_event_id' => [
                'sometimes',
                'integer',
            ],
            'machine_unit_id' => [
                'integer',
                'required_with:machine_event_id',
            ],
            'driver' => [
                'sometimes',
                'string',
            ],
            'machine_implement_id' => [
                'integer',
                'required_with:implement_width',
            ],
            'work_operation_ids' => [
                'required',
                'array',
                'min:1',
            ],
            'work_operation_ids.*' => [
                'required',
                'integer',
            ],
            'guidance_line_ids' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'guidance_line_ids.*' => [
                'required',
                'integer',
            ],
            'vra_order_ids' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'vra_order_ids.*' => [
                'required',
                'integer',
            ],
            'task_products' => [
                'sometimes',
                'required',
                'array',
                'min:1',
            ],
            'task_products.*.product_id' => [
                'required',
                'int',
            ],
            'task_products.*.rate' => [
                'required',
                'numeric',
            ],
            'task_products.*.value' => [
                'required',
                'numeric',
            ],
            'task_products.*.applied_area' => [
                'required',
                'numeric',
            ],
            'task_products.*.has_event_data' => [
                'required',
                'boolean',
            ],
            'task_products.*.pest_name' => [
                'sometimes',
                'required',
                'string',
                'required_with:task_products.*.pest_application,task_products.*.pest_quarantine',
            ],
            'task_products.*.pest_application' => [
                'sometimes',
                'required',
                'string',
                'required_with:task_products.*.pest_name,task_products.*.pest_quarantine',
            ],
            'task_products.*.pest_quarantine' => [
                'sometimes',
                'required',
                'int',
                'required_with:task_products.*.pest_name,task_products.*.pest_application',
            ],
            'implement_width' => [
                'sometimes',
                'numeric',
                'min:1',
            ],
            'geom_cultivated' => [
                'sometimes',
                'json',
            ],
        ];
    }

    public function withValidator(Validator $validator)
    {
        parent::withValidator($validator);
    }
}
