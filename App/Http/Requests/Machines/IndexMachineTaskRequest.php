<?php

namespace App\Http\Requests\Machines;

use Illuminate\Validation\Validator;

class IndexMachineTaskRequest extends AbstractMachineTaskRequest
{
    public function rules(): array
    {
        return [
            'organization_id' => [
                'sometimes',
                'integer',
            ],
            'start_date' => [
                'sometimes',
                'integer',
            ],
            'end_date' => [
                'sometimes',
                'integer',
            ],
            'plot_ids' => [
                'sometimes',
                'json',
            ],
            'work_operation_ids' => [
                'sometimes',
                'json',
            ],
            'states' => [
                'sometimes',
                'json',
            ],
        ];
    }

    public function withValidator(Validator $validator) {}
}
