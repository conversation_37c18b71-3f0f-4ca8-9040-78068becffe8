<?php

namespace App\Http\Requests\Machines;

use Illuminate\Foundation\Http\FormRequest;

class WorkOperationsWithTasksStateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'farm_year' => [
                'required',
                'integer',
            ],
            'organization_id' => [
                'sometimes',
                'integer',
            ],
            'start_date' => [
                'required',
                'integer',
            ],
            'end_date' => [
                'required',
                'integer',
            ],
            'plot_ids' => [
                'sometimes',
                'json',
            ],
            'work_operation_ids' => [
                'sometimes',
                'json',
            ],
            'states' => [
                'sometimes',
                'json',
            ],
            'farm_ids' => [
                'sometimes',
                'json',
            ],
            'crop_ids' => [
                'sometimes',
                'json',
            ],
        ];
    }
}
