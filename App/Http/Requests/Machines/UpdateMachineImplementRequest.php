<?php

namespace App\Http\Requests\Machines;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMachineImplementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'width' => 'sometimes|required|numeric',
            'name' => 'sometimes|required|string',
            'status' => 'sometimes|required|string',
            'work_operation_ids' => 'sometimes|required|array',
            'work_operations.*' => 'integer',
        ];
    }
}
