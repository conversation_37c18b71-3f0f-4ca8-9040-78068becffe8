<?php

namespace App\Http\Requests\Machines;

use App\Models\GuidanceLine;
use App\Models\MachineImplementWorkOperation;
use App\Models\MachineTask;
use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use App\Models\WorkOperation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

abstract class AbstractMachineTaskRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    abstract public function rules(): array;

    public function withValidator(Validator $validator)
    {
        $validator->after(function (Validator $validator) {
            $data = $this->input();
            $workOperationIds = (array)$this->request->get('work_operation_ids', []);
            $workOperations = WorkOperation::getNamesByIds($workOperationIds);

            if (isset($data['machine_event_id']) && MachineTask::STATE_DONE_APPROVED !== $data['state']) {
                $validator->errors()->add('state', 'When the task has an event state must be ' . MachineTask::STATE_DONE_APPROVED);
            }

            if (isset($data['vra_order_ids'], $data['plot_id'])) {
                $orderSoilVra = OrderSoilVra::whereIn('order_id', $data['vra_order_ids'])
                    ->where(['plot_id' => $data['plot_id']])
                    ->get();

                $orderSatelliteVra = OrderSatelliteVra::whereIn('order_id', $data['vra_order_ids'])
                    ->where(['plot_id' => $data['plot_id']])
                    ->get();

                $vraOrders = $orderSoilVra->merge($orderSatelliteVra);

                if ($vraOrders->count() !== count($data['vra_order_ids'])) {
                    $validator->errors()->add('vra_order_ids', "Any of of the following VRA order ids does not belong to a plot {$data['plot_id']}");
                }
            }

            if (isset($data['guidance_line_ids'], $data['plot_id'])) {
                $guidanceLines = GuidanceLine::whereIn('gid', $data['guidance_line_ids'])
                    ->where(['plot_id' => $data['plot_id']])
                    ->get();

                if ($guidanceLines->count() !== count($data['guidance_line_ids'])) {
                    $validator->errors()->add('guidance_line_ids', "Any of of the following guidance line ids does not belong to a plot {$data['plot_id']}");
                }
            }

            $transportWorkOperationId = WorkOperation::where('name', WorkOperation::WORK_OPERATION_TRANSPORT)->first();

            if (
                isset($data['machine_implement_id'], $data['work_operation_ids'])
                && !$transportWorkOperationId
            ) {
                $machineImplementWorkOperation = MachineImplementWorkOperation::whereIn('work_operation_id', $data['work_operation_ids'])
                    ->where(['implement_id' => $data['machine_implement_id']])
                    ->get();

                if ($machineImplementWorkOperation->count() !== count($data['work_operation_ids'])) {
                    $validator->errors()->add('work_operation_ids', "Any of of the following work operation ids does not belong to a machine implement {$data['machine_implement_id']}");
                }
            }

            if (isset($data['covered_area']) && !isset($data['plot_id'])) {
                $validator->errors()->add('covered_area', 'The covered_area field must be null if plot_id is null.');
            }

            if (
                isset($data['machine_event_id'])
                && !isset($data['plot_id'])
                && !$workOperations->contains(WorkOperation::WORK_OPERATION_TRANSPORT)
            ) {
                $validator->errors()->add(
                    'plot_id',
                    "The plot id is required when there is an event attached to the task and there is no 'Transport' in task's work operatinos"
                );
            }
        });
    }
}
