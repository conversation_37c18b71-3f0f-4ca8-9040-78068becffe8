<?php

namespace App\Http\Requests\Product;

use App\Models\Products\Product;
use Illuminate\Validation\Rule;

class StoreProductRequest extends AbstractProductRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
            ],
            'type_id' => [
                'required',
                'integer',
            ],
            'unit_id' => [
                'required',
                'integer',
            ],
            'default_price' => [
                'required',
                'numeric',
            ],
            'rate' => [
                'sometimes',
                'required',
                'numeric',
            ],
            'application_rate' => [
                'sometimes',
                'string',
                Rule::in(Product::getApplicationRates()),
            ],
            'organization_id' => [
                'required',
                'integer',
            ],
            'quarantine_period' => [
                'sometimes',
                'required_with:information',
                'numeric',
            ],
            'information' => [
                'sometimes',
                'required_with:quarantine_period',
                'string',
            ],
            'active_ingredients' => [
                'sometimes',
                'array',
            ],
            'active_ingredients.*.id' => [
                'required',
                'integer',
            ],
            'active_ingredients.*.quantity' => [
                'required',
                'numeric',
            ],
            'active_ingredients.*.unit_id' => [
                'required',
                'integer',
            ],
        ];
    }
}
