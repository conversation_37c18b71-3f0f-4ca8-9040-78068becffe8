<?php

namespace App\Http\Requests\Product;

use App\Models\Products\ProductType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

abstract class AbstractProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    abstract public function rules(): array;

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $data = $this->input(); // Вземи всички входни данни от заявката
            $typeId = $data['type_id'];

            if ($typeId === ProductType::getProductTypeIdByName(ProductType::PPP)) {
                if (empty($data['quarantine_period'])) {
                    $validator->errors()->add('quarantine_period', 'The field is required when product type is PPP.');
                }

                if (empty($data['information'])) {
                    $validator->errors()->add('information', 'The field is required when product type is PPP.');
                }
            } else {
                if (isset($data['quarantine_period'])) {
                    $validator->errors()->add('quarantine_period', 'The field shall only be submitted when product type is PPP.');
                }

                if (isset($data['information'])) {
                    $validator->errors()->add('information', 'The field shall only be submitted when product type is PPP.');
                }
            }

            if (!ProductType::getProductTypeIdsByNames([ProductType::PPP, ProductType::FERTILISER])->contains($typeId)) {
                if (isset($data['active_ingredients'])) {
                    $validator->errors()->add('active_ingredients', 'The field shall only be submitted when product type is PPP or Fertiliser.');
                }
            }
        });
    }
}
