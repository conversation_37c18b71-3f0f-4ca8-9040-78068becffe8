<?php

namespace App\Http;

use App\Http\Middleware\CheckEndpointAbilities;
use App\Http\Middleware\SystemAuth;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * @var array
     */
    protected $middleware = [
        \App\Http\Middleware\AddHeaderAccessToken::class,
        \Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance::class,
        'log-request',
    ];

    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\SentryUserContext::class,
        ],
        'api' => [
            'throttle:120,1',
            'check-endpoint-abilities',
            'bindings',
            'sentry',
        ],
    ];

    /**
     * The application's route middleware.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'csrf' => \App\Http\Middleware\VerifyCsrfToken::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'set-user-data' => \App\Http\Middleware\SetUserData::class,
        'set-admin-user-data' => \App\Http\Middleware\SetAdminUserData::class,
        'set-organization-data' => \App\Http\Middleware\SetOrganizationData::class,
        'verify-admin' => \App\Http\Middleware\VerifyAdmin::class,
        'verify-write-rights' => \App\Http\Middleware\VerifyWriteRights::class,
        'role' => \App\Http\Middleware\Role::class,
        'ability' => \App\Http\Middleware\Ability::class,
        'set-lang' => \App\Http\Middleware\SetLang::class,
        'reduce-gdd-entries-left' => \App\Http\Middleware\ReduceGDDEntriesLeft::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'check-endpoint-abilities' => CheckEndpointAbilities::class,
        'system-auth' => SystemAuth::class,
        'organization-data-access' => \App\Http\Middleware\OrganizationDataAccess::class,
        'sentry' => \App\Http\Middleware\SentryUserContext::class,
        'set-service-provider-outgoing-email' => \App\Http\Middleware\SetServiceProviderOutgoingEmail::class,
    ];
}
