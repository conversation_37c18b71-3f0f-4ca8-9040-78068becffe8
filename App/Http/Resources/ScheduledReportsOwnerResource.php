<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

class ScheduledReportsOwnerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var User $owner */
        $owner = $this;

        return [
            'id' => $owner->getKey(),
            'name' => $owner->name,
        ];
    }
}
