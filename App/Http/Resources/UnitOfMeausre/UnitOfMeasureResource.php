<?php

namespace App\Http\Resources\UnitOfMeausre;

use App\Models\UnitOfMeasure\UnitOfMeasure;
use Illuminate\Http\Resources\Json\JsonResource;

class UnitOfMeasureResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var UnitOfMeasure $unitOfMeasure */
        $unitOfMeasure = $this;

        return [
            'id' => $unitOfMeasure->getKey(),
            'full_name' => $unitOfMeasure->full_name,
            'short_name' => $unitOfMeasure->short_name,
            'coefficient' => (float)$unitOfMeasure->coefficient,
            'organization' => $unitOfMeasure->organization,
            'category' => $unitOfMeasure->category,
            'products' => $unitOfMeasure->products,
            'base_unit' => $unitOfMeasure->baseUnit,
            'numerator_unit' => $unitOfMeasure->numeratorUnit,
            'denominator_unit' => $unitOfMeasure->denominatorUnit,
            'created_at' => $unitOfMeasure->created_at,
            'updated_at' => $unitOfMeasure->updated_at,
        ];
    }
}
