<?php

namespace App\Http\Resources\Plot;

use App\Models\Plot;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Config;

class PlotResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var Plot $plot */
        $plot = $this;

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        return [
            'gid' => $plot->getKey(),
            'uuid' => $plot->uuid,
            'area' => round($plot->area * $areaCoef, 3),
            'name' => $plot->name,
            'geom_json' => $plot->geom,
            'thumbnail' => $plot->thumbnail,
            'is_editable' => $plot->is_editable,
        ];
    }
}
