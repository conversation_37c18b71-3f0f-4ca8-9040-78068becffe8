<?php

namespace App\Http\Resources\ActiveIngredient;

use App\Models\ActiveIngredient;
use Illuminate\Http\Resources\Json\JsonResource;

class ActiveIngredientResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string
     */
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var ActiveIngredient $activeIngredient */
        $activeIngredient = $this;

        return [
            'id' => $activeIngredient->id,
            'name' => $activeIngredient->name,
        ];
    }
}
