<?php

namespace App\Http\Resources\Machine;

use App\Http\Resources\GuidanceLine\GuidanceLineCollection;
use App\Http\Resources\Plot\PlotResource;
use App\Models\MachineTask;
use Illuminate\Http\Resources\Json\JsonResource;

class MachineTaskResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var MachineTask $machineTask */
        $machineTask = $this;
        $guidanceLine = new GuidanceLineCollection($machineTask->guidanceLines());

        return [
            'id' => $machineTask->getKey(),
            'start_date' => $machineTask->start_date,
            'end_date' => $machineTask->end_date,
            'farm_year' => $machineTask->farm_year,
            'completion_date' => $machineTask->completion_date,
            'state' => $machineTask->state,
            'organization' => $machineTask->organization,
            'plot' => new PlotResource($machineTask->plot),
            'machine_unit' => $machineTask->machineUnit,
            'driver' => $machineTask->driver,
            'machine_event' => new MachineEventResource($machineTask->machineEvent),
            'machine_implement' => $machineTask->machineImplement,
            'guidance_lines' => $guidanceLine->collection,
            'work_operations' => $machineTask->workOperations(),
            'vra_orders' => new MachineTaskVraOrderCollection($machineTask->vraOrders()),
            'task_products' => new MachineTaskProductCollection($machineTask->taskProducts()->get()),
            'covered_area' => isset($machineTask->covered_area) ? (float)$machineTask->covered_area : null,
            'created_at' => $machineTask->created_at,
            'updated_at' => $machineTask->updated_at,
        ];
    }
}
