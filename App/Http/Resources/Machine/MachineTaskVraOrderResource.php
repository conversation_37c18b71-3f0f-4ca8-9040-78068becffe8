<?php

namespace App\Http\Resources\Machine;

use App\Models\OrderSoilVra;
use Illuminate\Http\Resources\Json\JsonResource;

class MachineTaskVraOrderResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var OrderSoilVra $vraOrder */
        $vraOrder = $this;

        return [
            'id' => $vraOrder->id,
            'order_id' => $vraOrder->order_id,
            'order_type' => $vraOrder->order()->first()->type,
            'plot_id' => $vraOrder->plot_id,
            'layer_id' => $vraOrder->layer_id,
            'created' => $vraOrder->created,
            'class_number' => $vraOrder->class_number,
            'flat_rate' => $vraOrder->flat_rate,
            'data' => $vraOrder->data,
            'flat_rate_total' => $vraOrder->flat_rate_total,
            'variable_rate_total' => $vraOrder->variable_rate_total,
            'difference' => $vraOrder->difference,
            'difference_percent' => $vraOrder->difference_percent,
            'vector_data_json' => json_decode($vraOrder->vector_data),
            'product_percent' => $vraOrder->product_percent,
            'product_text' => $vraOrder->product_text,
            'tiff_path' => $vraOrder->tiff_path,
            'name' => $vraOrder->name,
        ];
    }
}
