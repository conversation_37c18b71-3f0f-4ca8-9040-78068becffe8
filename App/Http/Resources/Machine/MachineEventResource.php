<?php

namespace App\Http\Resources\Machine;

use App\Http\Resources\Plot\PlotResource;
use App\Models\MachineEvent;
use Illuminate\Http\Resources\Json\JsonResource;

class MachineEventResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var MachineEvent $machineEvent */
        $machineEvent = $this;

        return [
            'id' => $machineEvent->getKey(),
            'plot' => new PlotResource($machineEvent->plot()->first()),
            'machine_unit' => $machineEvent->machineUnit()->first(),
            'machine_implement' => $machineEvent->machineImplement()->first(),
            'date' => $machineEvent->date,
            'start_date' => $machineEvent->start_date,
            'end_date' => $machineEvent->end_date,
            'max_speed' => $machineEvent->max_speed,
            'avg_speed' => $machineEvent->avg_speed,
            'length_track' => round($machineEvent->length_track / 1000, 1),
            'fuel_consumed_driving' => $machineEvent->fuel_consumed_driving,
            'fuel_consumed_stay' => $machineEvent->fuel_consumed_stay,
            'type' => $machineEvent->type,
            'duration' => $machineEvent->duration,
            'geojson_cultivated' => $machineEvent->geom_cultivated,
            'geojson_track' => $machineEvent->geom_track,
            'duration_stay' => $machineEvent->duration_stay,
            'stage' => $machineEvent->stage,
            'driver' => $machineEvent->driver,
            'implement_width' => $machineEvent->implement_width,
            'work_operations' => $machineEvent->workOperations(),
        ];
    }
}
