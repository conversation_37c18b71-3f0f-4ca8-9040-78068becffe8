<?php

namespace App\Http\Resources\Machine;

use App\Http\Resources\Product\ProductResource;
use App\Models\MachineTaskProduct;
use Illuminate\Http\Resources\Json\JsonResource;

class MachineTaskProductResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var MachineTaskProduct $machineTaskProduct */
        $machineTaskProduct = $this;

        return [
            'id' => $machineTaskProduct->getKey(),
            'product' => new ProductResource($machineTaskProduct->product()->first()),
            'rate' => (float)$machineTaskProduct->rate,
            'value' => (float)$machineTaskProduct->value,
            'applied_area' => (float)$machineTaskProduct->applied_area,
            'has_event_data' => $machineTaskProduct->has_event_data,
            'pest_name' => $machineTaskProduct->pest_name,
            'pest_application' => $machineTaskProduct->pest_application,
            'pest_quarantine' => $machineTaskProduct->pest_quarantine,
            'created_at' => $machineTaskProduct->created_at,
            'updated_at' => $machineTaskProduct->updated_at,
        ];
    }
}
