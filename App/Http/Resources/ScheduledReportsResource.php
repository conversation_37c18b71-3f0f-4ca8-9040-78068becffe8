<?php

namespace App\Http\Resources;

use App\Models\ScheduledReport;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class ScheduledReportsResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string
     */
    public static $wrap = 'scheduledReport';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var ScheduledReport $scheduledReport */
        $scheduledReport = $this;

        return [
            'id' => $scheduledReport->getKey(),
            'name' => $scheduledReport->name,
            'type' => $scheduledReport->type,
            'subject' => $scheduledReport->subject,
            'body' => $scheduledReport->body,
            'locale' => $scheduledReport->getLocale(),
            'is_using_default_service_provider_locale' => $scheduledReport->is_using_default_service_provider_locale,
            'file_type' => $scheduledReport->file_type,
            'recipient_emails' => $scheduledReport->recipient_emails,
            'report_parameters' => $scheduledReport->getParameters(),
            'report_date_range' => $scheduledReport->report_date_range,
            'delivery_frequency' => $scheduledReport->delivery_frequency,
            'delivery_time' => Carbon::parse($scheduledReport->delivery_time)->format('H:i'),
            'delivery_days' => $scheduledReport->delivery_days,
            'scheduled_to_send_at' => optional(
                $scheduledReport->scheduled_to_send_at
            )->toIso8601String(),
            'last_sent_at' => optional($scheduledReport->last_sent_at)->toIso8601String(),
            'created_at' => $scheduledReport->created_at->toIso8601String(),
            'updated_at' => $scheduledReport->updated_at->toIso8601String(),
            'owner' => ScheduledReportsOwnerResource::make(
                $this->whenLoaded('owner')
            ),
        ];
    }
}
