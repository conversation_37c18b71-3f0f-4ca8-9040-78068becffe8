<?php

namespace App\Http\Resources\GuidanceLine;

use App\Models\GuidanceLine;
use Illuminate\Http\Resources\Json\JsonResource;

class GuidanceLineResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     */
    public static $wrap = 'guidanceLine';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        /** @var GuidanceLine $guidanceLine */
        $guidanceLine = $this;

        return [
            'id' => $guidanceLine->getKey(),
            'name' => $guidanceLine->name,
            'type' => $guidanceLine->type,
            'plot_id' => $guidanceLine->plot_id,
            'shift' => $guidanceLine->shift,
            'offset' => $guidanceLine->offset,
            'geom_json' => $guidanceLine->geom_json ?? $guidanceLine->geom,
        ];
    }
}
