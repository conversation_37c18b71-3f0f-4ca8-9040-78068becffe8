<?php

namespace App\Http\Resources\GuidanceLine;

use Illuminate\Http\Resources\Json\ResourceCollection;
use JsonSerializable;

class GuidanceLineCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = GuidanceLineResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        $count = $this->collection->count();

        return [
            'total' => $count,
            'rows' => $this->collection,
        ];
    }
}
