<?php

namespace App\Http\Resources\Product;

use App\Models\Products\Product;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string
     */
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var Product $product */
        $product = $this;

        return [
            'id' => $product->id,
            'name' => $product->name,
            'type' => $product->type ?? $product->type()->first(),
            'rate' => (float)$product->rate,
            'application_rate' => $product->application_rate,
            'default_price' => (float)$product->default_price,
            'status' => $product->status,
            'unit' => $product->unit ?? $product->unit()->first(),
            'organization' => $product->organization ?? $product->organization()->first(),
            'active_ingredients' => $product->active_ingredients ?? $product->activeIngredients()->get(),
            'quarantine_period' => $product->quarantine_period,
            'information' => $product->information,
            'created_at' => $product->created_at,
            'updated_at' => $product->updated_at,
        ];
    }
}
