<?php

namespace App\Http\Resources\Product;

use App\Models\Products\ProductType;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductTypeResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string
     */
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function toArray($request): array
    {
        /** @var ProductType $productType */
        $productType = $this;

        return [
            'id' => $productType->id,
            'name' => $productType->name,
            'type' => $productType->icon,
        ];
    }
}
