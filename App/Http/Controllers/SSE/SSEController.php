<?php

namespace App\Http\Controllers\SSE;

use App\Events\SSEvent;
use App\Http\Controllers\BaseController;

use function Illuminate\Events\queueable;

use Illuminate\Queue\Listener;
use Illuminate\Queue\ListenerOptions;
use Illuminate\Support\Facades\Event;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SSEController extends BaseController
{
    public function trigger()
    {
        Event::dispatch(new SSEvent());

        return response()->json(['message' => 'Event dispatched']);
    }

    public function stream(): StreamedResponse
    {
        return response()->stream(
            function () {
                $break = false;
                $o = new ListenerOptions(
                    'default',
                    'dev',
                );

                $listener = app('queue.listener');
                Event::listen(queueable(function (SSEvent $event) {
                    echo 'data: ' . json_encode(['time' => date('Y-m-d H:i:s')]) . PHP_EOL;
                    ob_flush();
                    flush();
                    exit();
                })->onConnection('redis')->onQueue('default'));
                $listener->setOutputHandler(function ($type, $line) {
                    echo $line;
                    ob_flush();
                    flush();
                    exit();
                });
                $listener->listen('redis', 'default', $o);
                // while (true) {
                //     if (connection_aborted()) {
                //         break;
                //     }
                //     Listener::create()->listen('Illuminate\Queue\Events\JobProcessed', function () use (&$break) {
                //         $break = true;
                //     });
                //     Event::listen(queueable(function (SSEvent $event) use ($break) {
                //         echo 'data: ' . json_encode(['time' => date('Y-m-d H:i:s')]) . PHP_EOL;
                //         ob_flush();
                //         flush();
                //         $break = true;
                //     })->onConnection('redis'));
                //     if ($break) {
                //         break;
                //     }
                //     sleep(5);
                // }
            },
            200,
            [
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
            ]
        );
    }
}
