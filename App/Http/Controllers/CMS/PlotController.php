<?php
/**
 * Created by PhpStorm.
 * User: l.nonchev
 * Date: 10/10/2019
 * Time: 3:43 PM.
 */

namespace App\Http\Controllers\CMS;

use App\Classes\CMS\AnalysisService;
use App\Classes\CMS\ContractService;
use App\Classes\CMS\PlotService as CmsPlotService;
use App\Exceptions\ValidationException;
use App\Http\Requests\Map\ApproveResultsMapCount;
use App\Http\Requests\Plot\GetSoilsListRequest;
use App\Models\Plot;
use App\Services\Plot\PlotService;
use Auth;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PlotController
{
    private $contractService;
    private $analysisService;
    private $plotService;
    private $cmsPlotService;

    public function __construct(ContractService $contractService, AnalysisService $analysisService, PlotService $plotService, CmsPlotService $cmsPlotService)
    {
        $this->contractService = $contractService;
        $this->analysisService = $analysisService;
        $this->plotService = $plotService;
        $this->plotService = $plotService;
        $this->cmsPlotService = $cmsPlotService;
    }

    public function listByUserIdAndOrganization()
    {
        return Plot::listByUserIdAndOrganization();
    }

    /**
     * @throws ValidationException
     */
    public function getPlotsByContractId(Request $request, string $contractId = null)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->request->all();
        $plots = $this->contractService->getAllPlotsUuidWithStateByContractId($contractId, $queryParams);
        $orderUuids = $this->contractService->getAllOrdersIdByContractId($contractId, $queryParams);
        $response = $this->plotService->getPlotsByOrderUuidsAndPlotUuids($orderUuids, $plots);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @return JsonResponse
     */
    public function getPlotDataWithOrganizationAndPackages(string $plotUuId)
    {
        $data = $this->plotService->getPlotWithOrganizationAndPackages($plotUuId);

        return new JsonResponse($data, 200, ['Content-Type: application/json']);
    }

    /**
     * @return JsonResponse
     */
    public function getPackagesByPlot(Request $request, int $plotId)
    {
        $headerParams = $request->all();
        $plotUuid = Plot::findOrFail($plotId)->uuid;
        $data = $this->cmsPlotService->getPackagesByPlot($plotUuid, $headerParams);

        return new JsonResponse($data, 200, ['Content-Type: application/json']);
    }

    public function getMapCountsForApprove(ApproveResultsMapCount $approveResultsMapCount)
    {
        $filters = $approveResultsMapCount->validated();
        $plotsCms = $this->analysisService->getPlotsCount($filters);

        $result = $this->plotService->getMapCountsForApprove($plotsCms);

        return new JsonResponse($result, 200);
    }

    public function getHeaderCounts()
    {
        $filters['element_group_state'] = AnalysisService::STATE_FOR_APPROVED;
        $plotsCms = $this->analysisService->getPlotsCount($filters);

        $result = $this->plotService->getHeaderCountsForApprove($plotsCms);

        return new JsonResponse($result, 200);
    }

    public function getPlotsForApproveExtent(Request $request)
    {
        $requestData = $request->all();
        $validator = Validator::make($requestData, [
            'proj' => 'numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userId = Auth::user()->id;
        $proj = $requestData['proj'];

        return $this->plotService->plotsForApproveExtent($userId, $proj);
    }

    public function getSoilsList(GetSoilsListRequest $request)
    {
        $lang = $request->get('lang');
        $page = $request->get('page');
        $limit = $request->get('limit');
        $filters = $request->get('filters', []);
        $sort = $request->get('sort', []);

        $filters['plot_ids'] = json_decode($request->input('filters.plot_ids', '[]'));
        $filters['farm_ids'] = json_decode($request->input('filters.farm_ids', '[]'));
        $filters['crop_ids'] = json_decode($request->input('filters.crop_ids', '[]'));
        $filters['status'] = json_decode($request->input('filters.status', '[]'));
        $filters['package_type'] = json_decode($request->input('filters.package_type', '[]'));

        $result = $this->plotService->getFilteredPlotsSoilsList($lang, $filters, $sort, $limit, $page);

        $response = [
            'rows' => $result['rows'],
            'total' => $result['total'],
        ];

        return new JsonResponse($response, 200, []);
    }

    /**
     * @OA\POST(
     *     path="/cms/plots/ab-overview",
     *     summary="Plots Ab overview",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     *
     * @return JsonResponse
     */
    public function getFieldsABOverview(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
            'filter.start_date' => 'required|string',
            'filter.end_date' => 'required|string',
            'filter.customer_identification' => 'required|array',
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $filter = $request->get('filter', []);

        $result = $this->cmsPlotService->getPlotABOverviewData($filter, $page, $limit);

        return new JsonResponse($result, 200);
    }

    public function getCellsForSamplingWithElements(Request $request)
    {
        $query = $request->all();
        $validator = Validator::make($query, [
            'filter' => 'required|array',
            'filter.contract_id' => 'required|integer',
            'filter.subscription_package_id' => 'required|integer',
            'filter.plot_uuids' => 'required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->cmsPlotService->getCellsForSamplingWithElements($query);

        return new JsonResponse($response, 200, []);
    }
}
