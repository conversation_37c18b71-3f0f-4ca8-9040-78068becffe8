<?php

namespace App\Http\Controllers\CMS;

use App\Classes\CMS\AnalysisReportService;
use App\Classes\CMS\AnalysisService;
use App\Exceptions\ValidationException;
use App\Http\Requests\SamplingType\SamplingTypeRequest;
use App\Services\Common\MailService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class AnalysisController
{
    private $analysisService;
    private $analysisReportService;
    private $mailService;

    public function __construct(
        AnalysisService $analysisService,
        MailService $mailService,
        AnalysisReportService $analysisReportService
    ) {
        $this->analysisService = $analysisService;
        $this->mailService = $mailService;
        $this->analysisReportService = $analysisReportService;
    }

    /**
     * @param null|string $contractId
     *
     * @throws ValidationException
     */
    public function updateLabElementsResultStates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'approved' => 'array',
            'approved.*' => 'integer',
            'for_reanalysis' => 'array',
            'for_reanalysis.*' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $this->analysisService->updateLabElementsResultStates($data);

        return new JsonResponse(null, 200);
    }

    /**
     * @OA\Get(
     *     path="/cms/analysis/lab-elements/results/for-approve",
     *     summary="Get elements results for approve",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getLabElementsResultsForApprove(Request $request)
    {
        $query = $request->all();
        $validator = Validator::make($query, [
            'filter' => 'required|array',
            'filter.contract_id' => 'required|integer',
            'filter.subscription_package_id' => 'required|integer',
            'filter.plot_uuids' => 'required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $result = $this->analysisService->getLabElementsResultsForApprove($query);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/cms/analysis/lab-elements/soil-map",
     *     summary="Plots samples",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getSoilMapElements(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
            'filter.meta_groups' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->all();
        $result = $this->analysisService->getSoilMapElements($headerParams);

        return new JsonResponse($result, 200);
    }

    public function getMetaElementsGroups(): JsonResponse
    {
        $metaElementsGroupsList = $this->analysisService->getMetaElementsGroupsList();

        return new JsonResponse($metaElementsGroupsList, 200);
    }

    public function getLabElementsResults(Request $request): JsonResponse
    {
        $params = $request->all();
        $labElementsResults = $this->analysisService->getLabElementsResults($params);

        return new JsonResponse($labElementsResults, 200);
    }

    /**
     * @OA\GET(
     *     path="/cms/barcodes",
     *     summary="Get barcodes",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getBarcodes(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'barcode_number' => 'sometimes|required|string',
            'customer_identification' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();

        if (!isset($queryParams['customer_identification'])) {
            $queryParams['customer_identification']
                = Auth::user()->lastChosenOrganization->identity_number;
        }

        $result = $this->analysisService->getBarcodes($queryParams);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\GET(
     *     path="/cms/lab_numbers",
     *     summary="Get lab numbers",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getLabNumbers(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'lab_number' => 'sometimes|required|string',
            'customer_identification' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();

        if (!isset($queryParams['customer_identification'])) {
            $queryParams['customer_identification']
                = Auth::user()->lastChosenOrganization->identity_number;
        }

        $result = $this->analysisService->getLabNumbers($queryParams);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\GET(
     *     path="/cms/analysis/report",
     *     summary="Get analyses report by given subscription package",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'filter' => 'required|array',
            'filter.subscription_packages_id' => 'required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.barcode' => 'sometimes|required|string',
            'filter.lab_number' => 'sometimes|required|string',
            'filter.sampling_type_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();
        $result = $this->analysisReportService->getAnalysesReport($queryParams);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     * path="/cms/analysis/report/export
     *     summary="Export recommendations report in .xls format.",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function exportReport(Request $request): BinaryFileResponse
    {
        $requestData = $request->all();
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'filter' => 'required|array',
            'filter.subscription_packages_id' => 'required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.barcode' => 'sometimes|required|string',
            'filter.lab_number' => 'sometimes|required|string',
            'filter.sampling_type_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $reportFile = $this->analysisReportService->exportXLSAnalysesReport($headerParams);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        $customFileName = uniqid('analyses_report_', true) . '.xls';

        return response()
            ->download(
                $reportFileFullPath,
                $customFileName
            )
            ->deleteFileAfterSend();
    }

    /**
     * @OA\Get(
     * path="/cms/analysis/report/send
     *     summary="Send recommendations report in .xls format to specific email",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function sendReport(Request $request): JsonResponse
    {
        $requestData = $request->all();
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'filter' => 'required|array',
            'filter.subscription_packages_id' => 'required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.barcode' => 'sometimes|required|string',
            'filter.lab_number' => 'sometimes|required|string',
            'filter.sampling_type_ids' => 'sometimes|required|json',
            'email' => 'required|email',
            'subject' => 'sometimes|string',
            'message' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $email = $request->get('email');
        $subject = $request->get('subject', 'Agrimi analyses report');
        $message = $request->get('message', '');
        $title = trans('emailReport.analysesReport');

        $reportFile = $this->analysisReportService->exportXLSAnalysesReport($headerParams);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        $customFileName = uniqid('analyses_report_', true) . '.xls';

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFileFullPath, $customFileName);

        if (Storage::disk('qnap_storage')->exists($reportFile)) {
            Storage::disk('qnap_storage')->delete($reportFile);
        }

        return new JsonResponse('Success', 200);
    }

    public function updatePlotPointsStateBySelectedCellsForSampling(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'for_sampling' => 'sometimes|array',
            'for_sampling.package_id' => 'sometimes|required|integer',
            'for_sampling.plot_uuid' => 'sometimes|required|string',
            'for_sampling.sample_ids' => 'sometimes|required|array',
            'for_sampling.sample_ids.*.' => 'sometimes|required|integer',
            'not_sampled' => 'sometimes|array',
            'not_sampled.package_id' => 'sometimes|required|integer',
            'not_sampled.plot_uuid' => 'sometimes|required|string',
            'not_sampled.sample_ids' => 'sometimes|required|array',
            'not_sampled.sample_ids.*' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $this->analysisService->updatePlotPointsStateBySelectedCellsForSampling($data);

        return new JsonResponse(null, 200);
    }

    public function getSamplingTypes(SamplingTypeRequest $typeRequest): JsonResponse
    {
        $queryParams = $typeRequest->validated();
        $samplingTypes = $this->analysisService->getSamplingTypes($queryParams);

        return new JsonResponse($samplingTypes, 200);
    }
}
