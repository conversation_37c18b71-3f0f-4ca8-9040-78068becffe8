<?php

namespace App\Http\Controllers\CMS;

use App;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Recommendations\GetRecommendationCalculationsRequest;
use App\Http\Requests\Recommendations\StoreRecommendationRequest;
use App\Http\Requests\Recommendations\UpdateRecommendationRequest;
use App\Http\Requests\Recommendations\UpdateRecommendationStatusRequest;
use App\Services\Common\MailService;
use App\Services\Recommendations\RecommendationEChartsService;
use App\Services\Recommendations\RecommendationPrintService;
use App\Services\Recommendations\RecommendationService;
use Auth;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class RecommendationController extends BaseController
{
    private $recommendationService;
    private $recommendationEChartsService;
    private $recommendationPrintService;
    private $mailService;

    public function __construct(
        RecommendationService $recommendationService,
        RecommendationEChartsService $recommendationEChartsService,
        RecommendationPrintService $recommendationPrintService,
        MailService $mailService
    ) {
        $this->recommendationService = $recommendationService;
        $this->recommendationEChartsService = $recommendationEChartsService;
        $this->recommendationPrintService = $recommendationPrintService;
        $this->mailService = $mailService;
    }

    /**
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function list(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'sort' => 'array',
            'filter' => 'sometimes|required|array',
            'array_result' => 'sometimes|required|bool',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrayResult = $request->get('array_result', true);
        $headerParams = $request->request->all();

        $params = $this->recommendationService->list($headerParams, $arrayResult);

        return new JsonResponse($params, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendation/plot/{uuid}",
     *     summary="List recommendations by plot and farming year",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function listByPlot(string $plotUuid, Request $request)
    {
        $validator = Validator::make(array_merge_recursive($request->all(), ['plot_uuid' => $plotUuid]), [
            'plot_uuid' => 'required|string',
            'farm_year' => 'required|integer',
            'status' => ['sometimes', 'max:255', 'in:For approve,Declined,Delivered'],
            'lang' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->recommendationService->listByPlot($plotUuid, $headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     * path="/cms/recommendations/report
     *     summary="Get recommendations report.",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|json',
            'filter' => 'sometimes|required|array',
            'filter.start_date' => 'sometimes|required_with:start_date|integer',
            'filter.end_date' => 'sometimes|required_with:end_date|integer',
            'filter.crop_ids' => 'sometimes|required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.sampling_type_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $headerParams = $request->request->all();

        $response = $this->recommendationService->getReportData($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Post(
     * path="/cms/recommendations/report/{type}
     *     summary="Export recommendations report in .pdf or .xls format.",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function exportReport(Request $request, string $type): BinaryFileResponse
    {
        $requestData = $request->all();
        $requestData['type'] = $type;
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:xls,pdf',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|array',
            'filter' => 'required|array',
            'filter.start_date' => 'sometimes|required_with:start_date|integer',
            'filter.end_date' => 'sometimes|required_with:end_date|integer',
            'filter.crop_ids' => 'sometimes|array',
            'filter.plot_ids' => 'sometimes|array',
            'filter.sampling_type_ids' => 'sometimes|array',
            'selected_columns' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = [
            'group_by' => $request->get('group_by'),
            'order_by' => $request->get('order_by', '[]'),
            'filter' => [
                'start_date' => $requestData['filter']['start_date'],
                'end_date' => $requestData['filter']['end_date'],
                'crop_ids' => json_encode($requestData['filter']['crop_ids']),
                'plot_ids' => json_encode($requestData['filter']['plot_ids']),
            ],
        ];

        if (isset($requestData['filter']['sampling_type_ids'])) {
            $headerParams['filter']['sampling_type_ids'] = json_encode($requestData['filter']['sampling_type_ids']);
        }

        $filter = $request->get('filter');
        $selectedColumns = $request->get('selected_columns', []);
        $reportFile = $this->recommendationService->getExportReportData($type, $headerParams, $selectedColumns);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        $customFileName = 'recommendation_report_' . $filter['start_date'] . '-' . $filter['end_date'] . '.' . $type;

        return response()->download($reportFileFullPath, $customFileName)->deleteFileAfterSend(true);
    }

    /**
     * @OA\Post(
     * path="/cms/recommendations/report/{type}/send
     *     summary="Send recommendations report in .pdf or .xls format. to specific email",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function sendReport(Request $request, string $type): JsonResponse
    {
        $requestData = $request->all();
        $requestData['type'] = $type;
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:xls,pdf',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|array',
            'filter' => 'required|array',
            'filter.start_date' => 'sometimes|required_with:start_date|integer',
            'filter.end_date' => 'sometimes|required_with:end_date|integer',
            'filter.plot_ids' => 'sometimes|array',
            'filter.crop_ids' => 'sometimes|array',
            'filter.sampling_type_ids' => 'sometimes|array',
            'email' => 'required|email',
            'subject' => 'sometimes|string',
            'message' => 'sometimes|string',
            'selected_columns' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = [
            'group_by' => $request->get('group_by'),
            'order_by' => $request->get('order_by', '[]'),
            'filter' => [
                'start_date' => $requestData['filter']['start_date'],
                'end_date' => $requestData['filter']['end_date'],
                'crop_ids' => json_encode($requestData['filter']['crop_ids']),
                'plot_ids' => json_encode($requestData['filter']['plot_ids']),
            ],
        ];

        if (isset($requestData['filter']['sampling_type_ids'])) {
            $headerParams['filter']['sampling_type_ids'] = json_encode($requestData['filter']['sampling_type_ids']);
        }

        $filter = $request->get('filter');
        $email = $request->get('email');
        $subject = $request->get('subject', 'Agrimi recommendation report');
        $message = $request->get('message', '');
        $selectedColumns = $request->get('selected_columns', []);
        $title = trans('emailReport.recommendationReport');

        $reportFile = $this->recommendationService->getExportReportData($type, $headerParams, $selectedColumns);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        $customFileName = 'recommendation_report_' . $filter['start_date'] . '-' . $filter['end_date'] . '.' . $type;

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFileFullPath, $customFileName);

        if (Storage::disk('qnap_storage')->exists($reportFile)) {
            Storage::disk('qnap_storage')->delete($reportFile);
        }

        return new JsonResponse('Success', 200);
    }

    /**
     * @return JsonResponse
     */
    public function getRecommendationLabResults(int $subscriptionPackageFieldId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sampling_type_ids' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $samplingTypeIds = json_decode($request->get('sampling_type_ids', '[]'));

        $response = $this->recommendationService->getRecommendationLabResults($subscriptionPackageFieldId, $samplingTypeIds);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @return JsonResponse
     */
    public function getModels(Request $request)
    {
        $response = $this->recommendationService->getRecommendationModels();

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function listByLastChosenOrganization(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'sort' => 'array',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();

        $response = $this->recommendationService->listByLastChosenOrganization($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/client/soil-analysis/echarts",
     *     summary="Plots samples",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     *
     * @return JsonResponse
     */
    public function getSoilAnalysisECharts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plot_ids' => 'json',
            'plot_name' => 'string',
            'farm_ids' => 'json',
            'date' => 'date_format:Y-m-d',
            'farm_year' => 'required|integer',
            'crop_ids' => 'json',
            'status' => 'json',
            'package_type' => 'json',
            'organization_id' => 'sometimes|integer',
            'sampling_type_id' => 'sometimes|required|integer',
            'filter' => 'sometimes|required|array',
            'filter.meta_groups' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filters['plot_ids'] = json_decode($request->get('plot_ids', '[]'));
        $filters['farm_ids'] = json_decode($request->get('farm_ids', '[]'));
        $filters['crop_ids'] = json_decode($request->get('crop_ids', '[]'));
        $filters['status'] = json_decode($request->get('status', '[]'));
        $filters['package_type'] = json_decode($request->get('package_type', '[]'));
        $filters['farm_year'] = $request->get('farm_year');
        $filters['date'] = $request->get('date');
        $filters['organization_id'] = $request->get('organization_id');
        $filters['sampling_type_id'] = $request->get('sampling_type_id');

        $queryParams['filter'] = $request->get('filter', []);

        try {
            $result = $this->recommendationEChartsService->renderSoilAnalysisECharts($filters, $queryParams);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse($result);
    }

    /**
     * @throws GuzzleException
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getRecommendationById(int $id, Request $request)
    {
        $validator = Validator::make([
            'id' => $id,
        ], [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->recommendationService->getRecommendationById($id);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getRecommendationVra(int $id, Request $request)
    {
        $validator = Validator::make(
            ['id' => $id],
            ['id' => 'required|numeric'],
            ['organization_id' => 'required|numeric']
        );

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->recommendationService->getRecommendationVra($id, $headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getCalculations(int $subscriptionPackageFieldId, GetRecommendationCalculationsRequest $request)
    {
        $params = $request->validated();
        $params['sampling_type_ids'] = json_decode($request->get('sampling_type_ids', '[]'));

        $response = $this->recommendationService->getCalculations($subscriptionPackageFieldId, $params);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function store(StoreRecommendationRequest $request)
    {
        $requestData = $request->validated();
        $response = $this->recommendationService->storeRecommendation($requestData);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function update(int $id, UpdateRecommendationRequest $request)
    {
        try {
            $params = $request->validated();
            $response = $this->recommendationService->update($id, $params);
        } catch (Exception $e) {
            return new JsonResponse($e->getMessage(), 200, ['Content-Type: application/json']);
        }

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function updateStatus(int $id, UpdateRecommendationStatusRequest $request): JsonResponse
    {
        $request->validated();

        try {
            $status = $request->get('status');
            $declineReason = $request->get('decline_reason');

            $response = $this->recommendationService->updateStatus($id, $status, $declineReason);
        } catch (Exception $e) {
            return new JsonResponse($e->getMessage(), 200, ['Content-Type: application/json']);
        }

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param int $id The recommendation id
     *
     * @throws ValidationException
     * @throws GuzzleException
     *
     * @return JsonResponse
     */
    public function exportRecommendationToPdf(Request $request, int $id)
    {
        $validator = Validator::make([
            'id' => $id,
        ], [
            'id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $serviceProvider = Auth::user()->globalUser()->serviceProvider;
        $locale = $serviceProvider->getLocaleByCountryCode();
        App::setLocale($locale);

        $recommendationPDF = $this->recommendationPrintService->printRecommendation($id);

        return response()->download($recommendationPDF['path'], $recommendationPDF['customFileName'])->deleteFileAfterSend(true);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/plots/search",
     *     summary="Search plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array of plot uuids"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function searchPlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'sometimes|required|integer',
            'name' => 'required|string',
            'year' => 'sometimes|required|integer',
            'crop_id' => 'sometimes|required|integer',
            'all_organizations' => 'sometimes|required|boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $year = $request->get('year');
        $name = $request->get('name');
        $cropId = $request->get('crop_id');
        $allOrganizations = filter_var($request->get('all_organizations', false), FILTER_VALIDATE_BOOLEAN);

        $organizationId = null;
        if (!$allOrganizations) {
            $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);
        }

        $plots = $this->recommendationService->searchPlots($organizationId, $name, $year, $cropId);

        return new JsonResponse($plots, 200, ['Content-Type: application/json']);
    }
}
