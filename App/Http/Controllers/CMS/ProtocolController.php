<?php

namespace App\Http\Controllers\CMS;

use App\Classes\CMS\ProtocolService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;

class ProtocolController extends Controller
{
    private $protocolService;

    public function __construct(ProtocolService $protocolService)
    {
        $this->protocolService = $protocolService;
    }

    public function index(Request $request, int $protocolId)
    {
        $requestData = $request->all();
        $requestData['protocol_id'] = $protocolId;

        $validator = Validator::make($requestData, [
            'protocol_id' => 'integer|min:1',
            'organization_identity_number' => 'string|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        return $this->protocolService->listProtocols($requestData, $protocolId);
    }

    public function list(Request $request)
    {
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'page' => 'integer|min:0',
            'limit' => 'integer|min:1',
            'organization_identity_number' => 'string|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        return $this->protocolService->listProtocols($requestData);
    }
}
