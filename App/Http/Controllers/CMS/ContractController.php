<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\CMS;

use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageFieldService;
use App\Classes\CMS\PackageService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Contract\CardDataRequest;
use App\Models\Ability;
use App\Models\Plot;
use App\Models\UserStation;
use App\Services\Plot\PlotService;
use Auth;
use Config;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;

class ContractController extends BaseController
{
    private $contractService;
    private $plotService;
    private $packageService;
    private $packageFieldService;

    public function __construct(
        ContractService $contractService,
        PlotService $plotService,
        PackageService $packageService,
        PackageFieldService $packageFieldService
    ) {
        $this->contractService = $contractService;
        $this->plotService = $plotService;
        $this->packageService = $packageService;
        $this->packageFieldService = $packageFieldService;
    }

    public function list(Request $request)
    {
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'organization_identity_number' => 'required|string',
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'sort' => 'array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        return $this->contractService->list($requestData);
    }

    /**
     * @throws GuzzleException
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function listContractsShortDataForChosenOrg(Request $request)
    {
        $identityNumber = null;

        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->contractService->getContractsShortDataByIdentityNumber($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function listContractsShortDataByOrganizationsAndPackages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'sometimes|required|integer',
            'offset' => 'sometimes|required|integer',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $data = $this->contractService->getContractsShortDataByOrganizations($headerParams);

        $response = [
            'items' => $data['items'],
            'total' => $data['total'],
        ];

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function manageFields(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plot_ids' => 'required|array',
            'plot_ids.*' => 'integer',
            'packages' => 'required|array',
            'packages.*.id' => 'required|integer',
            'packages.*.type' => 'required|in:service,subscription',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotIds = $request->get('plot_ids');
        $plots = Plot::whereIn('gid', $plotIds)->get();
        $errors = [];

        $packages = $request->get('packages');

        foreach ($packages as $package) {
            try {
                if ('subscription' == $package['type']) {
                    $this->contractService->addFieldsToSubscription($plots, $package['id']);
                }

                if ('service' == $package['type']) {
                    $this->contractService->addFieldsToService($plots, $package['id']);
                }
            } catch (Exception $e) {
                $errors[] = $package['id'];
            }
        }

        if (!empty($errors)) {
            return response()->json($errors, 400);
        }

        return response()->json('success');
    }

    // TODO:: GPS-1540 change endpoint to be contracts/{contractId}/packages//{type?}/
    public function listPackagesByContract(Request $request, string $contractId, string $type = null): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $headerParams = $request->all();

        if ($type) {
            $response = $this->contractService->getCmsDataForPackages($type, $headerParams, $contractId);

            return new JsonResponse($response, 200, ['Content-Type: application/json']);
        }

        $response = $this->contractService->getAllServiceOrSubscriptionPackages($headerParams, $contractId);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function listPackagesForCard(Request $request, string $orgIdentityNumber)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->contractService->listPackagesForCard($request->all(), $orgIdentityNumber);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getAllOrdersIdByContractId($contractId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();

        return $this->contractService->getAllOrdersIdByContractId($contractId, $queryParams);
    }

    /**
     * @throws GuzzleException
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function countContractsAmount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->contractService->getCountAmountsForContracts($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getCardData(CardDataRequest $request)
    {
        $requestData = $request->validated();
        $cards = [];
        $organizationIds = [];

        if (Auth::user()->globalUser()->canAny([Ability::MANAGE_FARM_TRACK_INTEGRATIONS, Ability::MANAGE_IRRIGATION_MONITORING_INTEGRATIONS])) {
            $organizationIds = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get()->pluck('id');
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_FIELDS)) {
            $cards['manage-fields'] = [
                'cardTitle' => 'Manage fields',
                'cardRout' => 'manage-fields',
                'staticTitle' => 'Manage fields',
                'availableItems' => null,
                'allItems' => '',
                'templateName' => '',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_FIELDS_SUBSCRIPTIONS)) {
            $cards['manage-fields-subscriptions'] = [
                'cardTitle' => 'Manage fields subscriptions',
                'cardRout' => 'manage-fields-subscriptions',
                'staticTitle' => 'Contracts',
                'availableItems' => null,
                'allItems' => '',
                'templateName' => '',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::SELECT_FOR_SAMPLING)) {
            $cards['select-for-sampling'] = [
                'cardTitle' => 'Manage sampling',
                'cardRout' => 'select-for-sampling',
                'staticTitle' => 'Plot status',
                'plotsByState' => null,
                'templateName' => 'SelectPlotForSamplingTemplate',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_WEATHER_STATIONS)) {
            $stationContractsId = Arr::get($requestData, 'station_contracts_id', []);

            $cards['manage-stations'] = [
                'cardTitle' => 'Manage weather stations',
                'cardRout' => 'manage-stations',
                'staticTitle' => 'Stations',
                'availableItems' => count($stationContractsId) ? UserStation::getByOrganizationsAndContracts($stationContractsId)->count() : 0,
                'allItems' => null,
                'templateName' => '',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_FARM_TRACK_INTEGRATIONS)) {
            $cards['integrations/farm-track'] = [
                'cardTitle' => 'Manage machines and products',
                'cardRout' => 'integrations/farm-track',
                'staticTitle' => 'Integrations',
                'availableItems' => null,
                'allItems' => null,
                'templateName' => '',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_IRRIGATION_MONITORING_INTEGRATIONS)) {
            $cards['integrations/irrigation-mngt'] = [
                'cardTitle' => 'Manage irrigation monitoring',
                'cardRout' => 'integrations/irrigation-mngt',
                'staticTitle' => 'Integrations',
                'availableItems' => null,
                'allItems' => null,
                'templateName' => '',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::APPROVE_RESULTS)) {
            $cards['approve-results'] = [
                'cardTitle' => 'Approve results',
                'cardRout' => 'approve-results',
                'staticTitle' => 'Results for approve',
                'availableItems' => null,
                'allItems' => null,
                'templateName' => '',
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_AGRONOMIC_TASKS)) {
            $cards['agronomic-tasks'] = [
                'cardTitle' => 'Agronomic tasks',
                'cardRout' => 'agronomic-tasks',
                'staticTitle' => 'Agronomic tasks',
                'availableItems' => null,
                'allItems' => null,
                'templateName' => '',
            ];
        }

        return new JsonResponse($cards, 200, ['Content-Type: application/json']);
    }

    public function getContractsStationsAmountByOrganizations(Request $request)
    {
        $withPagination = $request->get('withPagination', true);
        $offset = $request->get('offset', 0);
        $limit = $request->get('limit', 6);
        $customerIdentification = $request->get('customer_identification', null);

        $organizations = Auth::user()->organizations()->with('stations')->where('active', true)->whereNotNull('identity_number');
        if ($customerIdentification) {
            $organizations->where('identity_number', $customerIdentification);
        }

        $organizations = $organizations->distinct()->get();
        $customerIdentifications = array_column($organizations->toArray(), 'identity_number');
        $stationsAmounts = $this->contractService->getStationsAmountByOrganizations($customerIdentifications, $withPagination, $offset, $limit);

        $stationsAmounts['items'] = array_map(function ($item) use ($organizations) {
            $organizationIndex = array_search($item['customerIdentification'], array_column($organizations->toArray(), 'identity_number'));
            $organization = $organizations[$organizationIndex];
            $stations = array_filter($organization['stations']->toArray(), function ($station) use ($item) {
                return $station['contract_id'] == $item['contractId'];
            });

            $item['organizationName'] = $organization['name'];
            $item['organizationId'] = $organization['id'];
            $item['availableStations'] = count($stations);

            return $item;
        }, $stationsAmounts['items']);

        return new JsonResponse($stationsAmounts, 200, ['Content-Type: application/json']);
    }

    public function getPlotsOverlapByContract(Request $request, int $contractId)
    {
        $validator = Validator::make($request->all(), [
            'plot_uuids' => 'json',
            'organization_id' => 'integer',
            'lang' => 'string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotUuids = json_decode($request->get('plot_uuids', '[]'));
        $lang = $request->get('lang') ? $request->get('lang') : Config::get('app.locale');
        $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);

        $plots = $this->contractService->getPlotsOverlapBySubscriptionContract($plotUuids, $contractId, $organizationId, $lang);

        return new JsonResponse($plots, 200, ['Content-Type: application/json']);
    }

    public function getContractFieldsByPackage(Request $request, int $contractId, string $contractType)
    {
        $packageId = intval($request->get('package_id'));
        $requestData = [
            'contract_id' => $contractId,
            'contract_type' => $contractType,
            'package_id' => $packageId,
        ];

        $validator = Validator::make($requestData, [
            'contract_id' => 'required|integer',
            'contract_type' => 'required|string',
            'package_id' => 'required|int',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contractFields = $this->plotService->getContractFieldsByPackage($contractId, $contractType, $packageId);

        return new JsonResponse($contractFields, 200, ['Content-Type: application/json']);
    }

    public function removeFieldsFromContract(Request $request, int $contractId)
    {
        $validator = Validator::make(array_merge($request->all(), [
            'contract_id' => 'required|integer',
        ]), [
            'plot_uuids' => 'required|json',
            'order_uuids' => 'required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotUuids = json_decode($request->get('plot_uuids', '[]'), true);
        $orderUuids = json_decode($request->get('order_uuids', '[]'), true);

        $removedFieldsCount = $this->plotService->removeFieldsFromContract($contractId, $orderUuids, $plotUuids);

        if (0 === $removedFieldsCount) {
            return new JsonResponse('Fields not found.', 404);
        }

        return new JsonResponse('Success', 204);
    }
}
