<?php

namespace App\Http\Controllers\CMS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Organization;
use App\Services\Organization\OrganizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OrganizationController extends BaseController
{
    private $organizationService;

    public function __construct(OrganizationService $organizationService)
    {
        $this->organizationService = $organizationService;
    }

    /**
     * @OA\Get(
     *     path="/cms/organizations/list",
     *     summary="List for Contracts API",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return \Illuminate\Database\Eloquent\Collection|Organization[]
     */
    public function list(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'identityNumber' => 'string',
            'organizationManagerId' => 'integer',
            'serviceManagerId' => 'integer',
            'page' => 'integer',
            'limit' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $identityNumber = $request->get('identityNumber');
        $organizationManagerId = $request->get('organizationManagerId');
        $serviceManagerId = $request->get('serviceManagerId');
        $page = $request->get('page');
        $limit = $request->get('limit');

        return $this->organizationService->getListForCms($identityNumber, $organizationManagerId, $serviceManagerId, $page, $limit);
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        $limit = $request->get('limit', 10);

        return Organization::search($query, $limit);
    }

    public function getOrganization(int $id, Request $request)
    {
        return Organization::with('contactsPersons')->where('id', $id)->get();
    }
}
