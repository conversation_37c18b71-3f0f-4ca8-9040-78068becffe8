<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App\Entities\Local\CalculatedRisk;
use App\Entities\Local\CalculatedRiskRepository;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PestsDiseasesController extends BaseController
{
    public function risks(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date_format:Y-m-d',
            'end_date' => 'required|date_format:Y-m-d',
            'plot_id' => 'sometimes|integer',
            'gdd_collection' => 'sometimes|string',
            'phenophase' => 'sometimes|string',
            'pest_disease' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $options = [
            'start_date' => $request->get('start_date'),
            'end_date' => $request->get('end_date'),
            'plot_id' => $request->get('plot_id'),
            'gdd_collection' => $request->get('gdd_collection'),
            'phenophase' => $request->get('phenophase'),
            'pest_disease' => $request->get('pest_disease'),
        ];

        $em = $this->getEntityManager();
        /** @var CalculatedRiskRepository $repo */
        $repo = $em->getRepository(CalculatedRisk::class);

        return $repo->getCalculations($options);
    }
}
