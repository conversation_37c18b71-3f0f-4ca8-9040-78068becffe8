<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App\Actions\Farms\StoreFarmAction;
use App\Actions\Farms\UpdateFarmAction;
use App\Exceptions\NotFoundException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Farms\StoreFarmRequest;
use App\Http\Requests\Farms\UpdateFarmRequest;
use App\Models\Farm;
use Exception;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

/**
 * Class FarmController.
 */
class FarmController extends BaseController
{
    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * @OA\Get(
     *     path="/apigs/farms",
     *     summary="Get all farms",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return Farm[]|\Illuminate\Database\Eloquent\Collection
     */
    public function index()
    {
        throw new NotFoundException();
    }

    /**
     * @OA\Post(
     *     path="/apigs/farms",
     *     summary="Create farm",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function store(StoreFarmRequest $request, StoreFarmAction $action)
    {
        $farm = $action->withAttributes($request->validated())->save();

        return new JsonResponse($farm, 201);
    }

    /**
     * @OA\Get(
     *     path="/apigs/farms/:uuid",
     *     summary="Get farm",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return Farm
     */
    public function show(Farm $farm)
    {
        $this->authorize('accessFarm', $farm);

        return $farm;
    }

    /**
     * @OA\Put(
     *     path="/apigs/farms/:uuid",
     *     summary="Update farm",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws Exception
     *
     * @return Farm
     */
    public function update(UpdateFarmRequest $request, UpdateFarmAction $action, Farm $farm)
    {
        $farm = $action->forFarm($farm)->update($request->validated());

        return new JsonResponse($farm, 200);
    }

    /**
     * @OA\Delete(
     *     path="/apigs/farms/:uuid",
     *     summary="Delete farm",
     *
     *     @OA\Response(
     *         response="204"
     *     )
     * )
     *
     * @throws Exception
     *
     * @return JsonResponse
     */
    public function destroy(Farm $farm)
    {
        $this->authorize('accessFarm', $farm);

        $farm->delete();

        return new JsonResponse(null, 204);
    }
}
