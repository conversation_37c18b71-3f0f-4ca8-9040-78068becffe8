<?php

namespace App\Http\Controllers\APIGS;

use App\Actions\GuidanceLine\StoreGuidanceLineAction;
use App\Actions\GuidanceLine\UpdateGuidanceLineAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\GuidanceLine\ExportGuidanceLinesRequest;
use App\Http\Requests\GuidanceLine\IndexGuidanceLineRequest;
use App\Http\Requests\GuidanceLine\StoreGuidanceLineRequest;
use App\Http\Requests\GuidanceLine\UpdateGuidanceLineRequest;
use App\Http\Resources\GuidanceLine\GuidanceLineCollection;
use App\Http\Resources\GuidanceLine\GuidanceLineResource;
use App\Models\GuidanceLine;
use App\Services\GuidanceLines\GuidanceLineService;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class GuidanceLineController extends Controller
{
    public function index(IndexGuidanceLineRequest $request): GuidanceLineCollection
    {
        $organizationId = Auth::user()->lastChosenOrganization->id;
        $plotId = $request->get('plot_id', null);

        $guidanceLineQuery = GuidanceLine::selectWithGeomJSON();
        if ($plotId) {
            $guidanceLineQuery->filterByPlotId($plotId);
        }
        $guidanceLine = $guidanceLineQuery->forOrganization($organizationId)
            ->paginate($request->get('limit', 20));

        return new GuidanceLineCollection($guidanceLine);
    }

    public function show(int $gid): GuidanceLineResource
    {
        $guidanceLine = GuidanceLine::selectWithGeomJSON()->findOrFail($gid);

        return new GuidanceLineResource($guidanceLine);
    }

    public function store(
        StoreGuidanceLineRequest $storeGuidanceLineRequest,
        StoreGuidanceLineAction $storeGuidanceLineAction
    ): Response {
        $storeGuidanceLineAction->withAttributes($storeGuidanceLineRequest->validated());

        return response()->noContent(Response::HTTP_CREATED);
    }

    public function update(
        UpdateGuidanceLineRequest $updateGuidanceLineRequest,
        GuidanceLine $guidanceLine,
        UpdateGuidanceLineAction $updateGuidanceLineAction
    ): Response {
        $updateGuidanceLineAction
            ->forGuidanceLine($guidanceLine)
            ->withAttributes($updateGuidanceLineRequest->validated());

        return response()->noContent(Response::HTTP_OK);
    }

    /**
     * @throws \Illuminate\Validation\ValidationException
     */
    public function destroy(GuidanceLine $guidanceLine): Response
    {
        try {
            $guidanceLine->delete();
        } catch (Exception $exception) {
            throw ValidationException::withMessages(['guidance_line' => __('There was a problem deleting the Guidance line')]);
        }

        return response()->noContent(Response::HTTP_ACCEPTED);
    }

    public function export(
        ExportGuidanceLinesRequest $exportGuidanceLinesRequest,
        GuidanceLineService $guidanceLineService
    ): BinaryFileResponse {
        $requestData = $exportGuidanceLinesRequest->validated();
        $ids = json_decode($requestData['ids'] ?? '[]');
        $organizationId = $requestData['organization_id'] ?? Auth::user()->lastChosenOrganization->id;

        $file = $guidanceLineService->exportGuidanceLines($organizationId, $ids);

        return response()->download($file)->deleteFileAfterSend(true);
    }
}
