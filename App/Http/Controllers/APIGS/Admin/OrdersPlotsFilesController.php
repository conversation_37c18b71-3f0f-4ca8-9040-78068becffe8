<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Jobs\LoadSoilGridSHP;
use App\Models\File as FileModel;
use App\Models\Layer;
use App\Rules\ValidateGridUploadedFile;
use Auth;
use Illuminate\Http\JsonResponse;
use Plupload;
use Request;
use Validator;

class OrdersPlotsFilesController extends BaseController
{
    /**
     * @throws ValidationException
     */
    public function uploadSoilGridSHP()
    {
        $validator = Validator::make(Request::all(), [
            'file' => [new ValidateGridUploadedFile(request()->file('file'))],
            'type' => ['required', 'string', 'in:shp'],
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userId = Auth::user()->id;

        $response = Plupload::receive('file', function ($uploadedFile) use ($userId) {
            $file = new FileModel();
            $file->name = basename(
                $uploadedFile->getClientOriginalName(),
                '.' . $uploadedFile->getClientOriginalExtension()
            );
            $file->user_id = $userId;
            $file->filename = $userId . '_' . $uploadedFile->getClientOriginalName();
            $file->crs = (int)Request::input('srs', config('globals.DEFAULT_DB_CRS'));
            $file->shape_type = Layer::LAYER_TYPE_SOIL_GRID;
            $file->device_type = 0;
            $file->group_id = $userId;
            $file->path = config('globals.LAYERS_QUEUE_PATH') . $userId . '_' . $uploadedFile->getClientOriginalName();

            $uploadedFile->move(config('globals.LAYERS_QUEUE_PATH'), $file->filename);
            $file->save();

            return $this->dispatchNow(new LoadSoilGridSHP(FileModel::find($file->id)));
        });

        return new JsonResponse(json_decode($response['result']), 200);
    }
}
