<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Classes\AgroLab\AgroLabService;
use App\Classes\CouchDBClient;
use App\Exceptions\NotFoundException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Analyzes;
use App\Models\SoilPoints;
use App\Models\SoilSampleNumber;
use App\Services\Exports\ExportFactory;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class SoilsController extends BaseController
{
    private $agroLabService;

    public function __construct(CouchDBClient $couchDB, AgroLabService $agroLabService)
    {
        $this->couchDBClient = $couchDB->client;
        $this->agroLabService = $agroLabService;
    }

    /**
     * @deprecated Used in a1(admin) project
     *
     * @throws ValidationException
     * @throws Exception
     */
    public function getProbeContent(): array
    {
        $validator = Validator::make(Request::all(), [
            'soprId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $soprId = Request::get('soprId');

        $model = new SoilPoints();

        try {
            $data = $model->loadProbeContent((int) $soprId);
        } catch (Exception $e) {
            throw $e;
        }

        return [
            'total' => count($data),
            'rows' => $data,
            'elements' => [],
        ];
    }

    /**
     * @throws ValidationException
     */
    public function getAnalyzes(): array
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'sort' => 'string',
            'comment' => 'string',
            'username' => 'string',
            'original_name' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userId = Auth::user()->id;
        $sort = Request::get('sort');
        $comment = Request::get('comment');
        $username = Request::get('username');
        $originalName = Request::get('original_name');

        $model = new Analyzes();

        try {
            $resultQuery = $model->loadAnalyzes($userId, $originalName, $username, $comment, $sort);
            $result = $resultQuery->paginate(Request::get('limit'));
        } catch (Exception $e) {
            throw $e;
        }

        return [
            'total' => $result->total(),
            'rows' => $result->items(),
        ];
    }

    /**
     * @throws ValidationException
     * @throws Exception
     */
    public function searchForTemplate(): array
    {
        $validator = Validator::make(Request::all(), [
            'batch' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');

        $docData = $this->couchDBClient->findDocument('p_' . $batch);

        if (isset($docData->body['error'])) {
            return ['total' => 0, 'rows' => []];
        }

        try {
            $data = $this->dataForTemplate($docData);
        } catch (Exception $e) {
            throw $e;
        }

        return [
            'total' => count($data),
            'rows' => $data,
        ];
    }

    /**
     * @throws ValidationException
     * @throws Exception
     */
    public function searchForTemplateExcell(): BinaryFileResponse
    {
        $validator = Validator::make(Request::all(), [
            'batch' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');

        $docData = $this->couchDBClient->findDocument('p_' . $batch);

        if (isset($docData->body['error'])) {
            throw new NotFoundException("Cannot find document with batch '{$batch}'!");
        }

        try {
            $rows = $this->dataForTemplate($docData);
        } catch (Exception $e) {
            throw $e;
        }

        $printData = [];
        $headers = [
            0 => trans('general.lab_number'),
            1 => trans('general.block_name'),
            2 => trans('general.cell_number'),
        ];

        foreach ($rows as $subKey => $subValue) {
            $printData[] = [
                $headers[0] => $subValue['lab_number'],
                $headers[1] => $subValue['name'],
                $headers[2] => $subValue['sample_id'],
            ];
        }

        $fileName = 'ForTemplate.xls';

        Config::set('filesystems.disks.qnap_storage.root', Config::get('globals.STORAGE_PATH'));

        $exporter = ExportFactory::make('xls');
        $exporter->withoutColumnsMapping();
        $exporter
            ->export(
                collect($printData),
                $headers,
                $fileName,
                [],
                null,
                null
            );

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return response()
            ->download(Storage::disk('qnap_storage')->path($fileName))
            ->deleteFileAfterSend();
    }

    /**
     * @throws ValidationException
     */
    public function recievedSamples()
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'required|string',
            'limit' => 'required|integer',
            'page' => 'required|integer',
            'batch' => 'string',
            'from_date' => 'date_format:Y-m-d',
            'to_date' => 'date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');
        $limit = intval(Request::get('limit'));
        $page = intval(Request::get('page'));
        $sort = Request::get('sort');

        $recievedSamples = $this->agroLabService->getRecievedSamples($batch, $fromDate, $toDate, $limit, $page, $sort);

        if ('error' == $recievedSamples) {
            return Response::json(['error' => 'No result'], 400);
        }

        $result = SoilSampleNumber::joinBarcodesWith($recievedSamples['jsonDocs'], $sort);

        return [
            'rows' => $result,
            'total' => $recievedSamples['totalCount'],
        ];
    }

    /**
     * @throws ValidationException
     *
     * @return mixed
     */
    public function checkedBarcodes(): array
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'required|string',
            'limit' => 'required|integer',
            'page' => 'required|integer',
            'batch' => 'string',
            'barcode_lab' => 'string',
            'barcode_sampling' => 'string',
            'order_id' => 'integer',
            'plot_name' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');
        $barcodeLab = Request::get('barcode_lab');
        $barcodeSampling = Request::get('barcode_sampling');
        $orderId = Request::get('order_id');
        $plotName = Request::get('plot_name');
        $sort = Request::get('sort');

        $filter = [
            'batch' => $batch,
            'barcodeLab' => $barcodeLab,
            'barcodeSampling' => $barcodeSampling,
            'orderId' => $orderId,
            'plotName' => $plotName,
        ];

        $isFilterEmpty = $this->agroLabService->isFilterCheckedBarcodesEmpty($filter);

        if ($isFilterEmpty) {
            return [
                'rows' => [],
            ];
        }

        $couchDbBarcodes = $this->agroLabService->getCouchDbBarcodes($filter, $sort);

        if ('error' == $couchDbBarcodes) {
            return Response::json(['error' => 'Error occurred'], 400);
        }

        $result = $this->agroLabService->checkBarcodes($couchDbBarcodes['jsonDocs'], $filter, $sort);

        return [
            'rows' => $result,
        ];
    }

    /**
     * @throws ValidationException
     */
    public function updateBarcode(): JsonResponse
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'sample_number' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');
        $barcode = Request::get('sample_number');

        if (SoilSampleNumber::barcodeExists($barcode)) {
            return new JsonResponse('The entered value already exists', 400);
        }

        SoilSampleNumber::updateBarcode($gid, $barcode);

        return new JsonResponse(['result' => 'Changed successfully.'], 200);
    }

    private function dataForTemplate($docData): array
    {
        if (!$docData) {
            return [];
        }

        $arrCouch = $docData->body['samples'];

        $arrBarcodesCouch = array_map(function ($value) {
            return $value['barcode'];
        }, $arrCouch);

        $arrGeoScan = SoilSampleNumber::loadForTemplate($arrBarcodesCouch);

        $arrMerge = array_map(function ($value) use ($arrCouch) {
            $arrFound = array_filter($arrCouch, function ($elem) use ($value) {
                return $elem['barcode'] == $value['barcode'];
            });

            $arrFoundCouch = reset($arrFound);

            if (isset($arrFoundCouch['labnumber'])) {
                $value['lab_number'] = $arrFoundCouch['labnumber'];
            }

            $value['cell'] = $arrFoundCouch['cell'];

            return $value;
        }, $arrGeoScan);

        // Sort the array by cell
        usort($arrMerge, function ($a, $b) {
            return intval($a['cell']) > intval($b['cell']);
        });

        return $arrMerge;
    }
}
