<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Classes\PlotShape;
use App\Exceptions\ValidationException;
use Exception;
use Illuminate\Http\Request;
use Validator;

class PlotsController
{
    /**
     * @var PlotShape
     */
    private $plotShape;

    public function __construct(PlotShape $plotShape)
    {
        $this->plotShape = $plotShape;
    }

    public function generateShpZip(Request $request, $plotId)
    {
        $requestData = array_merge($request->all(), ['plotId' => $plotId]);

        $validator = Validator::make($requestData, [
            'plotId' => 'integer',
            'orderId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $shpFilesPath = $this->plotShape->generateShpFiles($requestData['plotId'], $requestData['orderId']);

        if (!is_dir($shpFilesPath)) {
            throw new Exception('Cannot find shp files!');
        }

        $plotsDir = dirname($shpFilesPath);
        exec("cd {$plotsDir};zip -j {$plotId}.zip {$plotId}/*.*");

        $zipFilePath = $plotsDir . "/{$plotId}.zip";

        if (!is_file($zipFilePath)) {
            throw new Exception('Zip file is missing!');
        }

        $this->removeDirectoryWithFiles($shpFilesPath);

        return response()->download($zipFilePath)->deleteFileAfterSend(true);
    }

    private function removeDirectoryWithFiles($path)
    {
        $files = array_diff(scandir($path), ['.', '..']);

        foreach ($files as $file) {
            if (is_file($path . $file)) {
                unlink($path . $file);
            }
        }

        rmdir($path);
    }
}
