<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\PlotRecommendation;
use Request;
use Response;
use Validator;

class PlotsRecommendationsController extends BaseController
{
    public function postPlotRecommendation()
    {
        $plotId = Request::get('plot_id');
        $orderPlotRelId = Request::get('order_plot_rel_id');
        $title = Request::get('title');
        $description = Request::get('description');

        $validator = Validator::make([
            'plot_id' => $plotId,
            'order_plot_rel_id' => $orderPlotRelId,
            'title' => $title,
            'description' => $description,
        ], [
            'plot_id' => 'required|integer',
            'order_plot_rel_id' => 'required|integer',
            'title' => 'required|string',
            'description' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotRecom = PlotRecommendation::firstOrNew(['plot_id' => $plotId, 'order_plot_rel_id' => $orderPlotRelId]);
        $plotRecom->title = $title;
        $plotRecom->description = $description;
        $plotRecom->save();

        return Response::json(['Response' => 'Success']);
    }
}
