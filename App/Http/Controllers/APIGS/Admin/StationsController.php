<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Actions\Station\GetStationCoordinatesByImeiAction;
use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageService;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Station\StationCoordinatesByImeiRequest;
use App\Models\UserStation;
use App\Services\RequestLog\RequestLogService;
use App\Services\Station\StationService;
use Config;
use DB;
use Exception;
use Illuminate\Http\JsonResponse;
use Request;
use Response;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Validator;

class StationsController extends BaseController
{
    private $stationService;
    private $requestLogService;
    private $packageService;
    private $contractService;

    public function __construct(StationService $stationService, RequestLogService $requestLogService, PackageService $packageService, ContractService $contractService)
    {
        $this->stationService = $stationService;
        $this->requestLogService = $requestLogService;
        $this->packageService = $packageService;
        $this->contractService = $contractService;
    }

    /**
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function postCreateStation()
    {
        // TODO:: https://technofarm.atlassian.net/browse/GPS-2102
        $validator = Validator::make(Request::all(), [
            'name' => 'required|string|max:63',
            'custom_name' => 'sometimes|required|string|max:63',
            'organization_id' => 'required|integer',
            'contract_id' => 'required|integer',
            'radius' => 'required|numeric',
            'type' => 'required|string',
            'install_date' => 'required|date',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationName = Request::get('name');
        $stationCustomName = Request::get('custom_name');
        $stationType = Request::get('type');
        $stationInstallDate = Request::get('install_date');
        $radius = Request::get('radius');
        $contractId = Request::get('contract_id');
        $organizationId = Request::get('organization_id');

        $hasActivePackage = $this->contractService->hasPackage($contractId, 'Active', 'WS');
        if (!$hasActivePackage) {
            throw new ForbiddenException('No active Weather stations package.');
        }

        if (!$this->stationService->canAddStation($contractId)) {
            throw new ConflictHttpException('No more stations allowed to create for this contract.');
        }

        $stationExistsForContract = $this->stationService->stationExistsForContract($stationName, $contractId);
        if ($stationExistsForContract) {
            throw new ConflictHttpException('Station already exists for this contract.');
        }

        $station = new UserStation();
        $station->organization_id = $organizationId;
        $station->contract_id = $contractId;
        $station->name = $stationName;
        $station->radius = $radius;
        $station->active = true;
        $station->type = $stationType;
        $station->install_date = $stationInstallDate;

        $data = $station->getStationApi()->getStationData();
        if (!is_array($data) || !isset($data['dates']) || !isset($data['position']) || (is_array($data) && isset($data['error']))) {
            throw new NotFoundException('Station not found. Please check station type and IMEI.');
        }

        if (isset($data['params'])) {
            $station->params = json_encode($data['params']);
        }

        $lastCommunication = $data['dates']['last_communication'];
        $longitude = $data['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];
        $latitude = $data['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];

        if (empty($stationCustomName) && isset($data['name']['custom'])) {
            $stationCustomName = $data['name']['custom'];
        }

        $station->latitude = $latitude;
        $station->longitude = $longitude;
        $station->last_communication = $lastCommunication;
        $station->geom = DB::raw('ST_Transform(ST_SetSRID(ST_MakePoint(' . $longitude . ', ' . $latitude . '), 4326), ' . Config::get('globals.DEFAULT_DB_CRS') . ')');
        $station->custom_name = $stationCustomName ? $stationCustomName : $stationName;

        $result = $station->save();

        if (!$result) {
            throw new Exception('Error creating weather station.');
        }

        $this->packageService->stationAdded($contractId);

        return new JsonResponse(['Response' => 'Success', 'station' => $station], JsonResponse::HTTP_CREATED);
    }

    /**
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function findStation()
    {
        $validator = Validator::make(Request::all(), [
            'name' => 'required|string|max:63',
            'organization_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationName = Request::get('name');
        $organizationId = Request::get('organization_id');

        $station = UserStation::where('name', $stationName)->first();
        $data = $station->getStationApi()->getStationData();

        if (!is_array($data) || !isset($data['dates']) || !isset($data['position']) || (is_array($data) && isset($data['error']))) {
            throw new NotFoundException('Station not found. Please check station type and IMEI.');
        }

        $hasOverlapContracts = $this->stationService->hasOverlapContracts($stationName, $station->contract_id, 'Active', true);
        if ($hasOverlapContracts) {
            throw new ConflictHttpException('Station already exists.');
        }

        $stationExists = $this->stationService->stationExistsForOrganization($stationName, $organizationId);
        if ($stationExists) {
            throw new ConflictHttpException('Station already exists for the organization.');
        }

        return [
            'longitude' => $data['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX],
            'latitude' => $data['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX],
        ];
    }

    /**
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function postUpdateStation()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'organization_id' => 'required|integer',
            'name' => 'string',
            'custom_name' => 'string',
            'radius' => 'numeric',
            'install_date' => 'date',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $station = UserStation::select('*')
            ->where('id', Request::get('id'))
            ->where('organization_id', Request::get('organization_id'))->first();

        if (!$station) {
            return;
        }

        if (null !== Request::get('set_active')) {
            $setActive = Request::get('set_active');

            return $this->stationService->activateDeactivate($station, $setActive);
        }

        $stationName = Request::get('name');

        $object = UserStation::where('name', $stationName)->first()->getStationApi();
        $data = $object->getStationData();

        if (!is_array($data) || !isset($data['dates']) || !isset($data['position']) || (is_array($data) && isset($data['error']))) {
            throw new NotFoundException('Station not found. Please check station type and IMEI.');
        }

        $lastCommunication = $data['dates']['last_communication'];
        $longitude = $data['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];
        $latitude = $data['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];

        $station->name = Request::get('name');
        $station->custom_name = Request::get('custom_name') ? Request::get('custom_name') : $stationName;
        $station->radius = intval(Request::get('radius'));
        $station->latitude = $latitude;
        $station->longitude = $longitude;
        $station->last_communication = $lastCommunication;
        $station->install_date = Request::get('install_date');
        $station->geom = DB::raw('ST_Transform(ST_SetSRID(ST_MakePoint(' . $longitude . ', ' . $latitude . '), 4326), ' . Config::get('globals.DEFAULT_DB_CRS') . ')');
        $station->save();

        return new JsonResponse($station);
    }

    /**
     * @throws ValidationException
     */
    public function postDeleteStation()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'organization_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $station = UserStation::select('*')
            ->where('id', Request::get('id'))
            ->where('organization_id', Request::get('organization_id'))->first();

        if (!$station) {
            return;
        }

        $station->delete();
    }

    public function getStationHistory(Request $request, $stationId)
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $log = $this->requestLogService->getStationHistory($stationId);

        return new JsonResponse(['rows' => $log], 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/admin/stations/coordinates-by-imei/{stationImei}",
     *     summary="Get station coordinates by imei",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getStationCoordinatesByImei(
        StationCoordinatesByImeiRequest $stationCoordinatesByImeiRequest,
        GetStationCoordinatesByImeiAction $getStationCoordinatesByImeiAction
    ): JsonResponse {
        $stationCoordinates = $getStationCoordinatesByImeiAction
            ->withAttributes($stationCoordinatesByImeiRequest->validated())
            ->coordinates();

        return response()->json($stationCoordinates);
    }
}
