<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Role;
use Bouncer;
use Illuminate\Http\Request;
use Validator;

class RolesController extends BaseController
{
    public function getRoles()
    {
        return Role::where('name', '!=', 'SUPER_ADMIN')->get();
    }

    public function getAbilitiesByRole(Role $role, Request $request)
    {
        $abilitiesAll = $role->getAbilities();
        $abilities = $abilitiesAll->forPage($request->get('page'), $request->get('limit'));

        return [
            'total' => count($abilitiesAll),
            'rows' => array_values($abilities->toArray()),
        ];
    }

    public function manageAbilities(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'role_name' => 'required|string',
            'ability_name' => 'required|string',
            'action' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $action = $request->get('action');
        $roleName = $request->get('role_name');
        $abilityName = $request->get('ability_name');

        // Adding & removing abilities for roles
        if ('allow' == $action) {
            Bouncer::allow($roleName)->to($abilityName);

            return;
        }

        Bouncer::disallow($roleName)->to($abilityName);
    }

    public function index()
    {
        $roles = Role::orderBy('id', 'desc')->get();

        return [
            'total' => count($roles),
            'rows' => $roles,
        ];
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'title' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lowName = strtolower($request->get('name'));
        $role = Bouncer::role()->updateOrCreate([
            'name' => strtoupper(snake_case($lowName)),
        ]);

        $role->title = $request->get('title');
        $role->save();
    }

    public function update(Role $role, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'title' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lowName = strtolower($request->get('name'));
        $role->name = strtoupper(snake_case($lowName));
        $role->title = $request->get('title');
        $role->save();
    }
}
