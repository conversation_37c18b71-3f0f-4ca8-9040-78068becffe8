<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Actions\Users\ManageUserFarmsAccessAction;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Users\ManageUserFarmsAccessRequest;
use App\Models\Country;
use App\Models\Farm;
use App\Models\GlobalUser;
use App\Models\Organization;
use App\Models\Role;
use App\Models\User;
use App\Services\RequestLog\RequestLogService;
use App\Services\User\UserService;
use Auth;
use Config;
use DB;
use Exception;
use GuzzleHttp\Psr7\Response;
use Hash;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;

class UsersController extends BaseController
{
    private $requestLogService;

    public function __construct(RequestLogService $requestLogService)
    {
        $this->requestLogService = $requestLogService;
    }

    public function index(): JsonResponse
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'sometimes|string',
            'limit' => 'sometimes|integer',
            'user' => 'sometimes|json',
            'name' => 'sometimes|string',
            'address' => 'sometimes|string',
            'active' => 'sometimes|boolean',
            'role' => 'sometimes|string',
            'orderId' => 'sometimes|integer',
            'email' => 'sometimes|string',
            'parent' => 'sometimes|json',
            'organizations' => 'sometimes|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $sort = Request::get('sort', 'name');
        $limit = Request::get('limit', '10');

        $filters['user'] = json_decode(Request::get('user'), true);
        $filters['name'] = Request::get('name', null);
        $filters['address'] = Request::get('address', null);
        $filters['active'] = Request::get('active', null);
        $filters['parent'] = Request::get('parent', null);
        $filters['role'] = Request::get('role', null);
        $filters['email'] = Request::get('email', null);
        $filters['organizations'] = json_decode(Request::get('organizations'), true);

        $users = UserService::listUsers($sort, $limit, $filters);

        return new JsonResponse($users, 200, ['Content-Type: application/json']);
    }

    public function getUsersRaw()
    {
        $sort = Request::get('sort', 'name');
        $limit = Request::get('limit', '10');

        $filters['user'] = json_decode(Request::get('user'), true);
        $filters['name'] = Request::get('name', null);
        $filters['address'] = Request::get('address', null);
        $filters['active'] = Request::get('active', null);
        $filters['parent'] = Request::get('parent', null);
        $filters['role'] = Request::get('role', null);
        $filters['email'] = Request::get('email', null);
        $filters['organizations'] = json_decode(Request::get('organizations'), true);

        $users = UserService::listUsers($sort, $limit, $filters);
        $systemUsers = DB::table('su_system_users')->get()->toArray();

        try {
            foreach ($users['rows'] as $user) {
                $foundKey = array_search($user['id'], array_column($systemUsers, 'user_id'));
                $user->password_raw = $systemUsers[$foundKey]->password;
                $user->system_id = $systemUsers[$foundKey]->user_id;
                unset($user->abilities, $user->organizations);
            }
        } catch (Exception $ex) {
            throw $ex;
        }

        return new JsonResponse($users, 200, ['Content-Type: application/json']);
    }

    public function search()
    {
        $validator = Validator::make(Request::all(), [
            'value' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $value = Request::get('value');

        return User::search($value);
    }

    public function show($username)
    {
        return User::where('username', $username)->first();
    }

    /**
     * @throws ValidationException
     *
     * @return User
     */
    public function store()
    {
        $arrValidator = [
            'username' => ['required', 'regex:/^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/'],
            'password' => 'required|string',
            'email' => 'required|email',
            'name' => 'required|string',
            'is_trial' => 'boolean',
            'start_date' => 'date',
            'due_date' => 'date',
            'use_meteo' => 'sometimes|required|boolean',
            'use_vra' => 'sometimes|required|boolean',
            'use_sentinel' => 'sometimes|required|boolean',
            'email_bcc' => 'sometimes|string',
            'organization' => 'array',
            'organizations' => 'array',
        ];

        $userRole = 'STAFF';

        if (Auth::user()->globalUser()->isAn('SUPER_ADMIN') || Auth::user()->globalUser()->isAn('SAMPLER_ADMIN') || Auth::user()->globalUser()->isAn('SERVICE_ADMIN')) {
            $arrValidator['role'] = 'required|array';
            $userRole = Request::get('role')['name'];
        }

        $validator = Validator::make(Request::all(), $arrValidator);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if (Request::get('organization') && Request::get('organizations')) {
            throw new ValidationException('The organization and organizastions fields cannot be present at the same time!');
        }

        if (User::where('username', Request::get('username'))->first()) {
            throw new ValidationException('The username field value already exists.');
        }

        $connection = DB::connection();

        $connection->beginTransaction();

        try {
            $currentUser = Auth::user();
            $currentGlobalUser = $currentUser->globalUser();

            $newUser = new User();
            $newUser->username = Request::get('username');
            $newUser->password = Hash::make(Request::get('password'));
            $newUser->email = Request::get('email');
            $newUser->email_bcc = Request::get('email_bcc');
            $newUser->name = Request::get('name');
            $newUser->phone = Request::get('phone');
            $newUser->parent()->associate($currentUser);
            $newUser->createdBy()->associate($currentUser);
            $newUser->level = User::LEVEL_CLIENT;

            $is_trial = Request::get('is_trial');
            $start_date = Request::get('start_date');
            $due_date = Request::get('due_date');

            // If Seller Admin - add 7 days trial period for the user
            if (Auth::user()->globalUser()->isAn('SELLER_ADMIN')) {
                $is_trial = true;
                $start_date = date('Y-m-d');
                $due_date = date('Y-m-d', strtotime('+7 days'));
            }

            if ($is_trial) {
                $newUser->is_trial = $is_trial;
                $newUser->start_date = $start_date;
                $newUser->due_date = $due_date;
            }

            $newUser->keycloak_uid = Request::get('keycloak_uid');
            $newUser->save();

            $newUser->group_id = $newUser->id;
            $newUser->save();

            $newGlobalUser = new GlobalUser();
            $newGlobalUser->username = Request::get('username');
            $newGlobalUser->password = Hash::make(Request::get('password'));
            $newGlobalUser->email = Request::get('email');
            $newGlobalUser->name = Request::get('name');
            $newGlobalUser->country = $currentGlobalUser->country;
            $newGlobalUser->old_id = $newUser->id;
            $newGlobalUser->old_group_id = $newUser->group_id;
            $newGlobalUser->parent()->associate($currentUser->globalUser()->id);
            $newGlobalUser->serviceProvider()->associate($currentUser->globalUser()->serviceProvider);
            $newGlobalUser->keycloak_uid = $newUser->keycloak_uid;

            $newGlobalUser->save();

            $newGlobalUser->group_id = $newGlobalUser->id;
            $newGlobalUser->save();

            // assign Role
            $newGlobalUser->assign($userRole);

            // Manage abilties
            $newGlobalUser->forbid('use_meteo');
            $newGlobalUser->forbid('use_vra');
            if (Request::get('use_meteo')) {
                $newGlobalUser->unforbid('use_meteo');
            }
            if (Request::get('use_vra')) {
                $newGlobalUser->unforbid('use_vra');
            }

            GlobalUser::fixTree();

            DB::table('su_system_users')->insert(
                ['user_id' => $newUser->id, 'username' => $newUser->username, 'password' => Request::get('password')]
            );

            $organizations = [];
            if (Request::get('organizations')) {
                $organizations = Request::get('organizations');
            } elseif (Request::get('organization')) {
                $organizationData = Request::get('organization');
                $country = Country::find(Auth::user()->globalUser()->country);

                $organization = DB::transaction(function () use ($organizationData, $country) {
                    $organization = new Organization();
                    $organization->name = $organizationData['name'];
                    $organization->iso_alpha_2_code = $country->iso_alpha_2_code;
                    $organization->email = $organizationData['email'];
                    $organization->service_provider_id = Auth::user()->globalUser()->serviceProvider->id;

                    if (array_key_exists('phone', $organizationData)) {
                        $organization->phone = $organizationData['phone'];
                    }
                    if (array_key_exists('address', $organizationData)) {
                        $organization->address = $organizationData['address'];
                    }
                    $organization->save();

                    $defaultFarm['name'] = 'Main Farm';
                    $defaultFarm['organization_id'] = $organization->id;
                    $farm = Farm::create($defaultFarm);

                    return $organization;
                });

                array_push($organizations, $organization);
            }

            if ($organizations) {
                $organizations = Organization::whereIn('id', array_column($organizations, 'id'))->get();

                foreach ($organizations as $organization) {
                    $farms = $organization->farms;
                    $organization->users()->attach($newUser->id);

                    foreach ($farms as $farm) {
                        $farm->users()->attach($newUser->id);
                    }
                }
            }

            $connection->commit();
        } catch (Exception $e) {
            $connection->rollBack();

            throw $e;
        }

        return $newUser;
    }

    /**
     * @throws ValidationException
     */
    public function update($userId)
    {
        $user = User::find((int)$userId);
        $globalUser = $user->globalUser();

        if (null !== Request::get('active') && $user->active !== (bool)Request::get('active')) {
            // Update Local user
            $user->active = (bool)Request::get('active');
            $user->save();
            // Update Global user
            $globalUser->active = (bool)Request::get('active');
            $globalUser->save();

            return $user;
        }

        $arrValidator = [
            'password' => 'sometimes|required|string',
            'active' => 'sometimes|required|boolean',
            'use_meteo' => 'sometimes|required|boolean',
            'use_vra' => 'sometimes|required|boolean',
            'use_sentinel' => 'sometimes|required|boolean',
            'is_trial' => 'boolean',
            'start_date' => 'date',
            'due_date' => 'date',
            'email_bcc' => 'sometimes',
        ];

        if (Auth::user()->globalUser()->isAn('SUPER_ADMIN') || Auth::user()->globalUser()->isAn('SERVICE_ADMIN')) {
            $arrValidator['role'] = 'required|array';
            $userRole = Request::get('role')['name'];
        }

        $validator = Validator::make(Request::all(), $arrValidator);

        $validator->sometimes(['email'], 'required|email', function ($input) {
            return null === $input->active;
        });
        $validator->sometimes(['name'], 'required|string', function ($input) {
            return null === $input->active;
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if (Request::get('password')) {
            $password = Hash::make(Request::get('password'));
            $user->password = $password;
            $globalUser->password = $password;

            DB::table('su_system_users')
                ->where('user_id', (int)$userId)
                ->update(['password' => Request::get('password')]);
        }

        $user->email = Request::get('email');
        $user->email_bcc = Request::get('email_bcc');
        $user->name = Request::get('name');
        $user->phone = Request::get('phone');
        $user->address = Request::get('address');
        $user->comment = Request::get('comment');
        $user->is_trial = Request::get('is_trial');

        $globalUser->email = Request::get('email');
        $globalUser->name = Request::get('name');

        if (true == $user->is_trial) {
            $user->start_date = Request::get('start_date');
            $user->due_date = Request::get('due_date');
        }

        $user->keycloak_uid = Request::get('keycloak_uid');
        $user->save();

        $globalUser->keycloak_uid = Request::get('keycloak_uid');
        $globalUser->save();

        if ((Auth::user()->globalUser()->isAn('SUPER_ADMIN') || Auth::user()->globalUser()->isAn('SERVICE_ADMIN')) && Auth::user()->id != $userId && $userRole) {
            $roles = $user->globalUser()->getRoles();
            foreach ($roles as $role) {
                $globalUser->retract($role);
            }

            $globalUser->assign($userRole);
        }

        $globalUser->forbid('use_meteo');
        $globalUser->forbid('use_vra');
        if (Request::get('use_meteo')) {
            $globalUser->unforbid('use_meteo');
        }
        if (Request::get('use_vra')) {
            $globalUser->unforbid('use_vra');
        }

        // Atach or Detach User's Roles
        /*$this->manageRole('meteo_user', 'use_meteo', $user);
        $this->manageRole('vra_user', 'use_vra', $user);
        $this->manageRole('rapideye_user', 'use_rapideye', $user);
        $this->manageRole('sentinel_user', 'use_sentinel', $user);
        $this->manageRole('landsat_user', 'use_landsat', $user);*/

        return $user;
    }

    public function destroy($userId)
    {
        $user = User::find((int)$userId);

        $user->delete();

        $this->revokeSessions($userId);
    }

    /**
     * @return array
     */
    public function getAllowedUsers()
    {
        $users = User::select('id', 'name', 'email', 'username')->where('parent_id', Auth::user()->id)->get();

        return [
            'rows' => $users,
            'total' => count($users),
        ];
    }

    /**
     * @return array
     */
    public function getOwnSamplers()
    {
        // For Super admin
        $globalUsers = GlobalUser::whereIs('SAMPLER')
            ->where('country', Auth::user()->globalUser()->country)
            ->where('active', true)
            ->where('service_provider_id', Auth::user()->globalUser()->serviceProvider->id)
            ->get();

        $globalUsersIds = $globalUsers->map(function ($user) {
            return $user->only(['old_id']);
        });
        $users = User::whereIn('id', $globalUsersIds)->get();

        return [
            'rows' => $users,
            'total' => count($users),
        ];
    }

    public function getOrganizations()
    {
        $validator = Validator::make(Request::all(), [
            'userId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userOrganizations = User::findOrFail(Request::get('userId'))->organizations()->get();

        return [
            'rows' => $userOrganizations,
            'total' => count($userOrganizations),
        ];
    }

    public function getUserAbilities()
    {
        $validator = Validator::make(Request::all(), [
            'userId' => 'required|integer',
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $page = Request::get('page');
        $limit = Request::get('limit');

        $abilities = User::findOrFail(Request::get('userId'))->globalUser()->getAbilities()->pluck('name');
        $forbiddenAbilities = User::findOrFail(Request::get('userId'))->globalUser()->getForbiddenAbilities()->pluck('name');

        $allowedAbilitiesAll = $abilities->diff($forbiddenAbilities)->values();

        $arrAllowedAbilities = $allowedAbilitiesAll->forPage($page, $limit)->all();

        return [
            'rows' => array_values($arrAllowedAbilities),
            'total' => count($allowedAbilitiesAll),
        ];
    }

    public function managePermission()
    {
        $validator = Validator::make(Request::all(), [
            'userId' => 'required|integer',
            'abilityName' => 'required|string|max:255',
            'action' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $user = User::findOrFail(Request::get('userId'))->globalUser();
        $abilityName = Request::get('abilityName');
        $action = Request::get('action');

        // Adding & removing abilities for user
        if ('allow' === $action) {
            $user->allow($abilityName);
            $user->unforbid($abilityName);

            return;
        }

        $user->forbid($abilityName);
        $user->disallow($abilityName);
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/users/:userId/farms/visibility",
     *     summary="Toggle visibility of farms by user",
     *
     *     @OA\Response(
     *         response="200",
     *         description="String"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function farmsVisibility(User $user, \Illuminate\Http\Request $request)
    {
        $validator = Validator::make($request->all(), [
            'farms' => 'array|required',
            'farms.*.id' => 'required|integer',
            'farms.*.isVisible' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $farms = $request->get('farms');
        foreach ($farms as $farm) {
            $user->farms()->updateExistingPivot($farm['id'], ['is_visible' => $farm['isVisible']]);
        }

        return new JsonResponse('Success');
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/users/:userId/farms/manage-access",
     *     summary="Detach farms of user",
     *
     *     @OA\Response(
     *         response="200",
     *         description="String"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function manageFarmsAccess(User $user, ManageUserFarmsAccessRequest $request, ManageUserFarmsAccessAction $action)
    {
        $requestData = $request->validated();
        $action->forUser($user)->manageFarmsAccess($requestData['farms']);

        return new JsonResponse('Success');
    }

    public function getProfileImageDirPath()
    {
        $serverProfileImagePath = Config::get('globals.SERVER_PROFILE_IMG_PATH');
        $machine = Config::get('globals.MACHINE');

        $url = $serverProfileImagePath . $machine;

        return new Response(200, ['Content-Type: text/html; charset=utf-8'], $url);
    }

    /**
     * @throws ValidationException
     *
     * @return Response
     */
    public function getUserHistory($userId)
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $logData = $this->requestLogService->getUserHistory($userId);

        return new JsonResponse(['rows' => $logData], 200);
    }

    private function manageRole($roleName, $requestName, $user)
    {
        if (Request::get($requestName) && !$user->isAn($roleName)) {
            $role = Role::where('name', $roleName)->first();
            $user->attachRole($role);
        }
        if (!Request::get($requestName) && $user->isAn($roleName)) {
            $role = Role::where('name', $roleName)->first();
            $user->detachRole($role);
        }
    }
}
