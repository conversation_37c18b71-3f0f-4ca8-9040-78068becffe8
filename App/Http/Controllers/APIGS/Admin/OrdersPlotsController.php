<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\Plot;
use App\Models\SoilGridParams;
use App\Services\Exports\ExportFactory;
use App\Services\Order\OrderService;
use Exception;
use GuzzleHttp\Psr7\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class OrdersPlotsController extends BaseController
{
    private $orderService;

    public function __construct(OrderPlotRel $orderPlotRel, OrderService $orderService)
    {
        $this->orderPlotRel = $orderPlotRel;
        $this->orderService = $orderService;
    }

    /**
     * @return array
     *
     * @deprecated Used in angular 1 project (admin)
     */
    public function getOrderPlots($orderId)
    {
        $sort = Helper::getSortRequest('-gid');

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $order = Order::find($orderId);

        $orderPlots = $this->orderService->getPlots($order, $sort, $areaCoef);

        return [
            'rows' => $orderPlots,
            'total' => count($orderPlots),
        ];
    }

    /**
     * @deprecated Used in angular 1 project (admin)
     *
     * @throws ValidationException
     */
    public function postSoilPlotsData(Request $request, $orderId)
    {
        $arrValidator = [
            'orderId' => 'required|integer',
            'plotData' => 'required|array',
            'plotsData.*.sampling_types' => 'required|array',
            'plotsData.*.cells' => 'array',
            'plotsData.*.leaf_sample_cells' => 'array',
            'plotsData.*.sampler_id' => 'integer',
        ];

        $validator = Validator::make($request->all(), $arrValidator);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $plotData = $request->get('plotData');

        DB::beginTransaction();

        try {
            foreach ($plotData as $plot) {
                $orderPlotRel = OrderPlotRel::find((int)$plot['orders_plots_rel_id']);
                $orderPlotRel->note = $plot['note'];

                if (null != $plot['sampler_id']) {
                    if ($orderPlotRel->sampler_id != $plot['sampler_id']) {
                        $orderPlotRel->date_assigned = Carbon::now();
                    }
                    $orderPlotRel->sampler_id = $plot['sampler_id'];
                }

                if (isset($plot['cells'])) {
                    $orderPlotRel->note = 'cells:' . implode(',', $plot['cells']);
                }

                if (isset($plot['sampling_types']) && count($plot['sampling_types']) > 0) {
                    $existingSamplingTypes = $orderPlotRel->samplingTypes()->get()->toArray();
                    if (count($existingSamplingTypes) > 0) {
                        throw new Exception('The sampling types for this plot are already set.');
                    }

                    $samplingTypes = array_map(function ($samplingType) use ($orderPlotRel) {
                        return [
                            'sopr_id' => $orderPlotRel->id,
                            'sampling_type_id' => $samplingType['id'],
                            'sampling_type_name' => $samplingType['type'],
                        ];
                    }, $plot['sampling_types']);

                    $orderPlotRel->samplingTypes()->createMany($samplingTypes);
                }

                $orderPlotRel->ekatte_code = strlen($plot['ekatte_code']) ? $plot['ekatte_code'] : '';
                $orderPlotRel->ekatte_name = strlen($plot['ekatte_name']) ? $plot['ekatte_name'] : '';

                if (is_array($plot['leaf_sample_cells'])) {
                    $orderPlotRel->leaf_sample_cells = empty($plot['leaf_sample_cells']) ? '' : implode(',', $plot['leaf_sample_cells']);
                }

                $orderPlotRel->save();
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return new Response(409, ['Content-Type: application/json']);
        }

        return new Response(201, ['Content-Type: application/json']);
    }

    /**
     * @throws ValidationException
     */
    public function deletePlot($orderId, $plotId)
    {
        $validator = Validator::make([
            'order_id' => $orderId,
            'plot_id' => $plotId,
        ], [
            'order_id' => 'required|numeric',
            'plot_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order || 'new' != $order->status) {
            return;
        }

        $orderPlot = Plot::from('su_satellite_plots as sp')->select(
            'sp.area',
            'sopr.id as orders_plots_rel_id',
            'sopr.price'
        )
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'sp.gid')
            ->where('sopr.order_id', $orderId)
            ->where('sopr.plot_id', $plotId)
            ->first();

        $orderPlotPrice = $orderPlot->price;
        $orderPlotArea = $orderPlot->area;
        $orderPlotRelId = $orderPlot->orders_plots_rel_id;

        // decrease area and price from the order cause deleting plot from it
        $order->price -= $orderPlotPrice;
        $order->area -= $orderPlotArea;
        $order->save();

        // delete relation between order and plot
        OrderPlotRel::destroy($orderPlotRelId);
    }

    public function updatePlot(Request $request, $orderId, $gid)
    {
        $orderPlotRel = OrderPlotRel::where('order_id', $orderId)
            ->where('plot_id', $gid)
            ->first();

        if (!$orderPlotRel) {
            return;
        }

        $orderPlotRel->note = $request->get('note');
        $orderPlotRel->save();

        return $orderPlotRel;
    }

    /**
     * @throws ValidationException
     */
    public function samplesPeriodReport(Request $request): array
    {
        $validator = Validator::make($request->all(), [
            'sort' => 'required|string',
            'limit' => 'required|string',
            'page' => 'required|integer',

            'from_date' => 'string',
            'to_date' => 'string',
            'sampler' => 'integer',
            'company' => 'string',
            'order' => 'integer',
            'plot' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrSamples = $this->orderPlotRel->getOrdersPeriodReport();
        $limit = $request->get('limit');
        $result = $arrSamples->paginate($limit);

        $rows = $result->each(function ($order) {
            $order->date = date('d.m.Y H:i', strtotime($order->date));
        });

        return [
            'rows' => $rows,
            'total' => $result->total(),
        ];
    }

    /**
     * @throws Exception
     */
    public function getSamplesPeriodReportExcell(): BinaryFileResponse
    {
        $arrSamples = $this->orderPlotRel->getOrdersPeriodReport();
        $result = $arrSamples->get();

        $rows = collect($result)->each(function ($order) {
            $order->date = date('d.m.Y H:i', strtotime($order->date));
        });

        $printData = [];
        $headers = [
            0 => trans('general.order'),
            1 => trans('general.area') . '(' . Config::get('globals.AREA_UNIT_LABEL') . ')',
            2 => trans('general.date'),
            3 => trans('general.sampler'),
            4 => trans('general.client'),
            5 => trans('general.plot'),
            6 => trans('general.samples_with_data'),
            7 => trans('general.total_samples'),
        ];

        foreach ($rows as $subKey => $subValue) {
            $printData[] = [
                $headers[0] => $subValue->order_id,
                $headers[1] => $subValue->area,
                $headers[2] => $subValue->sync_date,
                $headers[3] => $subValue->sampler,
                $headers[4] => $subValue->company_name,
                $headers[5] => $subValue->plot_name,
                $headers[6] => $subValue->samples_with_data,
                $headers[7] => $subValue->total_samples,
            ];
        }

        $fileName = 'SamplesPeriodReport.xls';
        Config::set('filesystems.disks.qnap_storage.root', Config::get('globals.STORAGE_PATH'));

        $exporter = ExportFactory::make('xls');
        $exporter->withoutColumnsMapping();
        $exporter
            ->export(
                collect($printData),
                $headers,
                $fileName,
                [],
                null,
                null
            );

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return response()
            ->download(Storage::disk('qnap_storage')->path($fileName))
            ->deleteFileAfterSend();
    }

    /**
     * @deprecated
     *
     * @OA\Post(
     *     path="/apigs/admin/orders/:order/plots/grid",
     *     summary="Generate grid for order's plots.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return object
     */
    public function generateGrid(Request $request, Order $order)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:2ha,5ha,iso,vra,custom',
        ]);

        $validator->sometimes('area', 'required|numeric', function ($params) {
            return 'custom' == $params->type;
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $type = $request->get('type');
        $area = $request->get('area');

        if ('custom' != $type) {
            $area = null;
        }

        $orderPlotsRel = $order->orderPlots()->with('plot')->get();

        $soilGridParams = SoilGridParams::getByOrderId($order->id);
        $soilGridParamsRow = $soilGridParams->first();

        if ($soilGridParamsRow) {
            if ('processing' == $soilGridParamsRow->status) {
                return response()->json(['message' => 'Grid is currently processing. Please wait.'], 400);
            }

            $soilGridParams->delete();
        }

        $soilGridParams = new SoilGridParams();

        $soilGridParams->type = $type;
        $soilGridParams->area = $area;
        $soilGridParams->order_id = $order->id;
        $soilGridParams->user_id = Auth::user()->id;
        $soilGridParams->datetime = date('Y-m-d H:i:s');
        $soilGridParams->save();

        DB::beginTransaction();

        try {
            foreach ($orderPlotsRel as $sopr) {
                $sopr->plot->createGrid($sopr->id, $type, $area);
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $soilGridParams->status = 'error';
            $soilGridParams->save();

            throw $e;
        }

        $soilGridParams->status = 'processed';
        $soilGridParams->save();

        return $soilGridParams;
    }

    /**
     * @OA\gET(
     *     path="/apigs/admin/orders/:order/grid/params",
     *     summary="Get last grid params by order id.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return object
     */
    public function lastGridParams(Order $order)
    {
        $params = SoilGridParams::getByOrderId($order->id)->first();

        if (!$params) {
            return;
        }

        return response()->json(['type' => $params->type, 'area' => $params->area, 'status' => $params->status]);
    }
}
