<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Events\PushNotificationEvent;
use App\Events\SoilOrderPushNotificationEvent;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException;
use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\API\Notifications;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\OrderVra;
use App\Models\StaticModels\FarmingYear;
use App\Models\User;
use App\Services\Exports\ExportFactory;
use App\Services\Exports\Pdf;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class OrdersController extends BaseController
{
    protected $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * @return array
     */
    public function index()
    {
        $limit = Helper::getLimitRequest();

        $orders = $this->order->getOrders();

        $result = $orders->paginate($limit);

        $rows = $result->each(function ($order) {
            $order->status_txt = Order::getOrderStatuses()->get($order->status);
            $order->date = date('d.m.Y H:i', strtotime($order->date));
            $order->salesman = User::select('name')->where('id', $order->salesman)->first()['name'] ?? '';
        });

        return [
            'rows' => $rows,
            'total' => $result->total(),
        ];
    }

    /**
     * @throws ValidationException
     */
    public function update($orderId)
    {
        $input = Request::all();

        $arrArguments = [
            'orderId' => $orderId,
        ];
        $request = array_merge($arrArguments, $input);

        $arrEnums = $this->order->getOrderStatusEnum();
        $arrStatusEnums = [];
        foreach ($arrEnums as $key => $value) {
            $arrStatusEnums[] = $value->enumlabel;
        }
        $sStatusEnums = implode(',', $arrStatusEnums);

        $validator = Validator::make($request, [
            'orderId' => 'required|integer',
            'status' => 'required|in:' . $sStatusEnums,
        ]);

        $validator->sometimes(['end_price'], 'required|numeric', function ($input) {
            return 'waiting_payment' == $input->status;
        });
        $validator->sometimes(['salesman'], 'required|integer', function ($input) {
            return 'waiting_payment' == $input->status;
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order || !$this->allowModify($order)) {
            return;
        }
        if ('paid' == Request::get('status')) {
            if ('waiting_payment' != $order->status) {
                throw new ValidationException('The status field value not allowed');
            }

            $order->status = Request::get('status');

            if ('vra' == $order->type) {
                $plot = $order->vraPlots()->first();
                $noty = [];
                $noty['group_id'] = $order->user_id;
                $noty['data'] = ['plots' => []];
                $noty['data']['plots'][] = $plot['gid'];
                $noty['data']['year'] = $order->year;
                $noty['data']['type'] = $order->type;
                $noty['plot_name'] = $plot['name'];

                $notyData = new Notifications();
                $notyData->group_id = $noty['group_id'];
                $notyData->title = trans('notifications.vra_orders', ['plotName' => $noty['plot_name']]);
                $notyData->message = trans('notifications.new_vra_orders_details', ['plotName' => $noty['plot_name']]);
                $notyData->data = json_encode($noty['data']);
                $notyData->type = 'processed_order';
                $notyData->is_pushed = false;
                $notyData->save();

                $order->status = 'processed';
                $order->save();

                event(new PushNotificationEvent($notyData->getId()));
            } else {
                $order->save();
            }

            return $order;
        }

        if ('soil' == $order->type) {
            // SEND SOIL-ORDERS PUSH NOTIFICATIONS
            $arrSamplers = OrderPlotRel::selectRaw('sampler_id')
                ->where('order_id', $orderId)
                ->get()->toArray();

            if ('waiting_payment' == Request::get('status') && count($arrSamplers)) {
                for ($i = 0; $i < count($arrSamplers); $i++) {
                    if ($arrSamplers[$i]['sampler_id']) {
                        event(new SoilOrderPushNotificationEvent($arrSamplers[$i]['sampler_id']));

                        /*$exitCode = Artisan::call('soil-orders:send-push-notification', [
                            'groupId' => $arrSamplers[$i]['sampler_id']
                        ]);*/
                    }
                }
            }
        }

        $previousStatus = $order->status;

        if (null !== Request::get('end_price')) {
            $order->end_price = Request::get('end_price');
        }
        if (null !== Request::get('salesman')) {
            $order->salesman = Request::get('salesman');
        }
        if (null !== Request::get('status')) {
            $order->status = Request::get('status');
        }
        if ('waiting_payment' == Request::get('status')) {
            $order->approvedBy()->associate(Auth::user());
        }

        $order->save();
    }

    public function paymentOrderPDF()
    {
        $orderId = Request::get('orderId');
        if (!$orderId) {
            return;
        }

        $order = $this->order->find($orderId);

        if (!$order || !$this->allowModify($order)) {
            return;
        }

        $orderPlots = $order->plots;

        $fullYearName = FarmingYear::getAll()->where('id', $order->year)->first()['title'];
        $totalArea = $orderPlots->sum('area');

        $printData = [
            'orderId' => $orderId,
            'farm' => '',
            'year' => $fullYearName,
            'totalArea' => $totalArea,
            'plots' => $orderPlots->toArray(),
            'endPrice' => $order->end_price,
        ];

        $pdf = (new Pdf())
            ->loadView('templates.satellite_payment_order', $printData)
            ->margins(2.5, 2.5, 2.5, 2.5, 'cm')
            ->format('a4')
            ->scale(0.8)
            ->browsershot()
            ->pdf("paymentOrder{$orderId}.pdf");

        return response($pdf, 200, ['Content-Type' => 'application/pdf']);
    }

    /**
     * @throws Exception
     */
    public function orderReportExcell(): BinaryFileResponse
    {
        $orderId = Request::get('orderId');
        $order = $this->order->findOrFail($orderId);
        $fileName = "Order_{$orderId}.xls";

        $companyName = $order->company_name;
        $orderPlots = $order->plots;

        $areaCoef = config('globals.DKA_' . config('globals.AREA_UNIT'));

        $arrSamples = OrderPlotRel::from('su_satellite_orders_plots_rel as sopr')
            ->select(
                'sopr.ekatte_name',
                'sopr.ekatte_code',
                'sopr.sync_date',
                'p.gid',
                'p.name AS plot_name',
                DB::raw("round((p.area*{$areaCoef})::numeric, 3) AS area"),
                'u.name',
                'sp.sample_id',
                'ssn.sample_number',
                'sopst.sampling_type_id',
                'sopst.sampling_type_name',
                DB::raw('ST_X(ST_Transform(sp.geom, 4326))'),
                DB::raw('ST_Y(ST_Transform(sp.geom, 4326))')
            )
            ->join('su_satellite_plots as p', 'p.gid', '=', 'sopr.plot_id')
            ->leftJoin('su_satellite_soil_points as sp', 'sp.sopr_id', '=', 'sopr.id')
            ->leftJoin('su_satellite_soil_sample_numbers as ssn', 'sp.gid', '=', 'ssn.gid')
            ->leftJoin('su_satellite_orders_plots_sampling_types as sopst', 'sopst.id', '=', 'ssn.sopst_id')
            ->leftJoin('su_users as u', 'u.id', '=', 'sopr.sampler_id')
            ->where('order_id', $orderId)
            ->orderBy('plot_name', 'ASC')
            ->orderBy('sample_id', 'ASC')
            ->get()->toArray();

        $SamplerCollection = collect($arrSamples);
        $grouped = $SamplerCollection->groupBy('gid');
        $grouped->toArray();

        $printData[] = [trans('general.client') . ':' . $companyName];
        $boldColumns[] = 1;

        $headers = [
            0 => trans('general.ekatteCode'),
            1 => trans('general.ekatteName'),
            2 => trans('general.plot'),
            3 => trans('general.cellNumber'),
            4 => trans('general.sampleType'),
            5 => trans('general.barcode'),
            6 => trans('general.longitude'),
            7 => trans('general.latitude'),
        ];

        foreach ($grouped as $key => $value) {
            $printData[] = [trans('general.sampler') . ':' . $value[0]['name']];
            $boldColumns[] = count($printData);
            $printData[] = [trans('general.area') . ':' . $value[0]['area'] . '(' . config('globals.AREA_UNIT_LABEL') . ')'];
            $boldColumns[] = count($printData);

            $formattedDate = '';
            if (null != $value[0]['sync_date']) {
                $formattedDate = Carbon::createFromFormat('Y-m-d H:i:s', $value[0]['sync_date'])->format('d.m.Y H:i');
            }
            $printData[] = [trans('general.sync_date') . ':' . $formattedDate];
            $boldColumns[] = count($printData);

            foreach ($value as $subKey => $subValue) {
                $samplingType = '';

                if (isset($subvalue['sampling_type_name'])) {
                    $samplingType = 'Leaf' === $subValue['sampling_type_name']
                        ? trans('leaf_samples')
                        : trans('general.sampling_type', ['sampling_type_name' => $subValue['sampling_type_name']]);
                }

                $printData[] = [
                    $headers[0] => $subValue['ekatte_code'],
                    $headers[1] => $subValue['ekatte_name'],
                    $headers[2] => $subValue['plot_name'],
                    $headers[3] => $subValue['sample_id'],
                    $headers[4] => $samplingType,
                    $headers[5] => $subValue['sample_number'],
                    $headers[6] => $subValue['st_x'],
                    $headers[7] => $subValue['st_y'],
                ];
            }
        }

        Config::set('filesystems.disks.qnap_storage.root', Config::get('globals.STORAGE_PATH'));

        $exporter = ExportFactory::make('xls');
        $exporter->withoutColumnsMapping();
        $exporter->export(
            collect($printData),
            $headers,
            $fileName,
            [],
            null,
            null
        );

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return response()
            ->download(Storage::disk('qnap_storage')->path($fileName))
            ->deleteFileAfterSend();
    }

    /**
     * @throws ForbiddenException
     * @throws NotFoundException
     * @throws ValidationException
     */
    public function updateFromDate($orderId)
    {
        $arrArguments = [
            'orderId' => $orderId,
        ];
        $requestData = array_merge($arrArguments, Request::all());

        $validator = Validator::make($requestData, [
            'orderId' => 'required|integer',
            'from_date' => 'required|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if (!$this->allowModify($order)) {
            throw new ForbiddenException('Operation is not allowed');
        }

        if ('new' != $order->status) {
            throw new ValidationException('The status should be new');
        }

        $farmYearData = FarmingYear::getAll()->where('id', $order->year)->first();

        if (Request::get('from_date') < $farmYearData['from_date']) {
            throw new ValidationException('The from date should not be lower than ' . $farmYearData['from_date']);
        }

        $order->from_date = Request::get('from_date');
        $order->save();

        return $order;
    }

    /**
     * @throws NotFoundException
     * @throws ValidationException
     */
    public function updateToDate($orderId)
    {
        $arrArguments = [
            'orderId' => $orderId,
        ];
        $requestData = array_merge($arrArguments, Request::all());

        $validator = Validator::make($requestData, [
            'orderId' => 'required|integer',
            'to_date' => 'required|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if (!$this->allowModify($order)) {
            throw new ValidationException('Operation is not allowed');
        }

        if ('new' != $order->status) {
            throw new ValidationException('The status should be new');
        }

        $farmYearData = FarmingYear::getAll()->where('id', $order->year)->first();

        if (Request::get('to_date') > $farmYearData['to_date']) {
            throw new ValidationException('The to date should not be greater than ' . $farmYearData['to_date']);
        }

        $order->to_date = Request::get('to_date');
        $order->save();

        return $order;
    }

    /**
     * @throws Exception
     */
    public function exportOrdersListExcell(): BinaryFileResponse
    {
        $orderTypes = [
            'soil' => 'Soil Samples',
            'soil_vra' => 'Soil VRA',
            'vra' => 'VRA',
            'index' => 'Satellite Imaging',
            'meteo' => 'Meteo Data',
        ];
        $orders = $this->order->getOrders();

        $result = $orders->get();

        $rows = collect($result)->each(function ($order) use ($orderTypes) {
            $order->status_txt = trans('general.' . (Order::getOrderStatuses()->get($order->status)));
            $order->date = date('d.m.Y H:i', strtotime($order->date));
            $order->salesman = User::select('name')->where('id', $order->salesman)->first()['name'] ?? '';
            $order->type = trans('general.' . $orderTypes[$order->type]);
        });

        $printData = [];
        $headers = [
            0 => trans('general.number'),
            1 => trans('general.orderType'),
            2 => trans('general.organization_name'),
            3 => trans('general.dealer'),
            4 => trans('general.farmingYear'),
            5 => trans('general.date'),
            6 => trans('general.status'),
            7 => trans('general.plotsCount'),
            8 => trans('general.area'),
            9 => trans('general.price'),
            10 => trans('general.endPrice'),
        ];

        foreach ($rows as $subKey => $subValue) {
            $printData[] = [
                $headers[0] => $subValue->order_id,
                $headers[1] => $subValue->type,
                $headers[2] => $subValue->organization_name,
                $headers[3] => $subValue->salesman,
                $headers[4] => $subValue->year,
                $headers[5] => $subValue->date,
                $headers[6] => $subValue->status_txt,
                $headers[7] => $subValue->plots_num,
                $headers[8] => $subValue->area,
                $headers[9] => $subValue->price,
                $headers[10] => $subValue->end_price,
            ];
        }

        $fileName = 'OrdersList.xls';
        Config::set('filesystems.disks.qnap_storage.root', Config::get('globals.STORAGE_PATH'));

        $exporter = ExportFactory::make('xls');
        $exporter->withoutColumnsMapping();
        $exporter
            ->export(
                collect($printData),
                $headers,
                $fileName,
                [],
                null,
                null
            );

        if (!Storage::disk('qnap_storage')->exists($fileName)) {
            throw new Exception('File \'' . $fileName . '\' does not exist!');
        }

        return response()
            ->download(Storage::disk('qnap_storage')->path($fileName))
            ->deleteFileAfterSend();
    }

    /**
     * @throws ValidationException
     * @throws Exception
     */
    public function vraOrder(): array
    {
        $validator = Validator::make(Request::all(), [
            'orderId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = Request::get('orderId');

        $model = new OrderVra();

        try {
            $data = $model->loadVraByOrderId((int) $orderId);
        } catch (Exception $e) {
            throw $e;
        }

        return [
            'vra' => $data,
        ];
    }

    private function allowModify($order): bool
    {
        return (bool) (
            Auth::user()->globalUser()->isAn('SUPER_ADMIN')
            || Auth::user()->globalUser()->isAn('SAMPLER_ADMIN')
            || Auth::user()->globalUser()->isAn('SERVICE_ADMIN')
        );
    }
}
