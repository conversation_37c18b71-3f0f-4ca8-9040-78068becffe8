<?php
/**
 * Created by PhpStorm.
 * User: l.nonchev
 * Date: 2/19/2020
 * Time: 12:18 PM.
 */

namespace App\Http\Controllers\APIGS\Admin\v2;

use App\Classes\CMS\ContractService;
use App\Http\Controllers\BaseController;
use App\Models\UserStation;
use Response;

class StationsController extends BaseController
{
    private $contractService;

    public function __construct(ContractService $contractService)
    {
        $this->contractService = $contractService;
    }

    public function index($organizationId)
    {
        $stations = UserStation::where('organization_id', $organizationId)->get();
        $contractIds = $stations->map(function ($station) {
            return $station['contract_id'];
        });

        if (count($contractIds) > 0) {
            $headerParams['filter'] = ['contracts_id' => json_encode($contractIds)];
            $contracts = $this->contractService->getContractShortData($headerParams);

            foreach ($stations as $stationKey => $station) {
                foreach ($contracts['items'] as $contract) {
                    if ($contract['id'] === $station['contract_id']) {
                        $stations[$stationKey]['contract_period'] = substr($contract['start_date'], 0, 10) . ' - ' . substr($contract['end_date'], 0, 10);
                    }
                }
            }
        }

        return Response::json($stations, 200);
    }

    public function getAvailableNumberStation() {}
}
