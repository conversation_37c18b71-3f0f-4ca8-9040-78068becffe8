<?php

namespace App\Http\Controllers\APIGS\Admin\v2;

use App\Classes\CMS\AnalysisService;
use App\Classes\CMS\ContractService;
use App\Classes\PlotShape;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Orders\PostSoilPlotsDataRequest;
use App\Http\Requests\Orders\UpdateSamplingPlotsRequest;
use App\Models\OrderPlotRel;
use App\Models\SoilPoints;
use Carbon\Carbon;
use DB;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;

class OrdersPlotsController extends BaseController
{
    private $plotShape;
    private $contractService;
    private $analysisService;

    public function __construct(PlotShape $plotShape, ContractService $contractService, AnalysisService $analysisService)
    {
        $this->plotShape = $plotShape;
        $this->contractService = $contractService;
        $this->analysisService = $analysisService;
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/plots/:plotId/grid",
     *     summary="Generate grid for plots.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return object
     */
    public function generateGrid($plotId)
    {
        $validator = Validator::make(Request::all(), [
            'type' => 'required|in:2ha,5ha,iso,vra,custom',
        ]);

        $validator->sometimes('customGridCellArea', 'required|numeric', function ($params) {
            return 'custom' == $params->type;
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $type = Request::get('type');
        $customCellArea = Request::get('customGridCellArea');

        if ('custom' != $type) {
            $customCellArea = null;
        }

        $response = $this->plotShape->generateGridByPlotId($plotId, $type, $customCellArea);

        return new Response(200, ['Content-Type: application/json'], $response);
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/plots/:plotId/grid",
     *     summary="Generate grid for plots.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @param $plotId
     *
     * @throws ValidationException
     *
     * @return object
     */
    public function generateMultipleGrids()
    {
        $validator = Validator::make(Request::all(), [
            'gridIds' => 'required',
            'type' => 'required|in:2ha,5ha,iso,vra,custom',
        ]);

        $validator->sometimes('customGridCellArea', 'required|numeric', function ($params) {
            return 'custom' == $params->type;
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gridIds = urldecode(Request::get('gridIds'));
        $gridIds = json_decode($gridIds, true);
        $type = Request::get('type');
        $customCellArea = Request::get('customGridCellArea');

        if ('custom' != $type) {
            $customCellArea = null;
        }

        $grids = [];
        foreach ($gridIds as $gridId) {
            $grids[] = json_decode($this->plotShape->generateGridByPlotId((int)$gridId, $type, $customCellArea));
        }

        return new Response(200, ['Content-Type: application/json'], json_encode($grids));
    }

    /**
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function postSoilPlotsData(PostSoilPlotsDataRequest $postSoilPlotsDataRequest): Response
    {
        $requestData = $postSoilPlotsDataRequest->validated();
        $arrDataToCMS = [];

        DB::beginTransaction();

        try {
            foreach ($requestData['plotsData'] as $plot) {
                $orderPlotRel = OrderPlotRel::where([['plot_uuid', $plot['plotUuid']], ['order_uuid', $plot['orderUuid']]])->first();
                $orderPlotRel->note = $plot['package_type'];
                $orderPlotRel->sampler_id = $plot['sampler_id'];
                $orderPlotRel->date_assigned = Carbon::now();
                $orderPlotRel->demo_sampling = $plot['demo_sampling'] ?? false;

                if (isset($plot['sampling_types']) && count($plot['sampling_types'])) {
                    $existingSamplingTypes = $orderPlotRel->samplingTypes()->get()->toArray();
                    if (count($existingSamplingTypes)) {
                        throw new Exception('The sampling types for this plot are already set.');
                    }

                    $samplingTypes = array_map(function ($samplingType) use ($orderPlotRel) {
                        return [
                            'sopr_id' => $orderPlotRel->id,
                            'sampling_type_id' => $samplingType['id'],
                            'sampling_type_name' => $samplingType['type'],
                        ];
                    }, $plot['sampling_types']);

                    $orderPlotRel->samplingTypes()->createMany($samplingTypes);
                }

                if (isset($plot['cells']) && count($plot['cells'])) {
                    SoilPoints::where(['sopr_id' => $orderPlotRel->id])
                        ->whereIntegerInRaw('sample_id', $plot['cells'])
                        ->update(['for_sampling' => true]);
                } else {
                    SoilPoints::where(['sopr_id' => $orderPlotRel->id])
                        ->update(['for_sampling' => true]);
                }

                $orderPlotRel->save();

                $arrDataToCMS['plotUuids'][] = $plot['plotUuid'];
                $arrDataToCMS['orderUuids'][] = $plot['orderUuid'];
            }

            try {
                $this->contractService->updateSubscriptionPackageFieldStateWhenSelectedSampler('subscription', $arrDataToCMS);
            } catch (ClientException $client_exception) {
                return new Response(400, ['Content-Type: application/json'], 'Update field state is unavailable!');
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return new Response(409, ['Content-Type: application/json']);
        }

        return new Response(201, ['Content-Type: application/json']);
    }

    public function updatePlotsSamplingPoints(UpdateSamplingPlotsRequest $updateSamplingPlotsRequest): Response
    {
        $request = $updateSamplingPlotsRequest->validated();
        $arrDataToCMS = [];

        DB::beginTransaction();

        try {
            foreach ($request['plotsData'] as $plot) {
                $orderPlotRel = OrderPlotRel::where([['plot_uuid', $plot['plot_uuid']], ['order_uuid', $plot['order_uuid']]])->first();

                if (null != $plot['sampler_id']) {
                    if ($orderPlotRel->sampler_id != $plot['sampler_id']) {
                        $orderPlotRel->date_assigned = Carbon::now();
                    }
                    $orderPlotRel->sampler_id = $plot['sampler_id'];
                    $orderPlotRel->demo_sampling = $plot['demo_sampling'] ?? false;
                }

                $cellsForSampling = array_merge($plot['cells']['sampling'], $plot['cells']['for_sampling']);
                SoilPoints::where(['sopr_id' => $orderPlotRel->id])
                    ->whereIntegerInRaw('sample_id', $cellsForSampling)
                    ->update(['for_sampling' => true]);

                SoilPoints::where(['sopr_id' => $orderPlotRel->id])
                    ->whereIntegerInRaw('sample_id', $plot['cells']['not_sampled'])
                    ->update(['for_sampling' => false]);

                if ($plot['cells']['sampling']) {
                    $arrDataToCMS['sampling'][] = [
                        'package_id' => $plot['package_id'],
                        'plot_uuid' => $plot['plot_uuid'],
                        'sample_ids' => $plot['cells']['sampling'],
                    ];
                }

                if ($plot['cells']['for_sampling']) {
                    $arrDataToCMS['for_sampling'][] = [
                        'package_id' => $plot['package_id'],
                        'plot_uuid' => $plot['plot_uuid'],
                        'sample_ids' => $plot['cells']['for_sampling'],
                    ];
                }

                if ($plot['cells']['not_sampled']) {
                    $arrDataToCMS['not_sampled'][] = [
                        'package_id' => $plot['package_id'],
                        'plot_uuid' => $plot['plot_uuid'],
                        'sample_ids' => $plot['cells']['not_sampled'],
                    ];
                }

                $orderPlotRel->save();
            }

            try {
                $this->analysisService->updatePlotsSamplingPoints($arrDataToCMS);
            } catch (ClientException $client_exception) {
                DB::rollBack();

                return new Response(400, ['Content-Type: application/json'], 'Update field state is unavailable!');
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return new Response(409, ['Content-Type: application/json']);
        }

        return new Response(201, ['Content-Type: application/json']);
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/order/exist",
     *     summary="Get existing orders by plot ids.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @return Response
     */
    public function getOrdersContainingPlots()
    {
        $plotsIds = urldecode(Request::get('plotsIds'));
        $plotsIds = json_decode($plotsIds, true);
        $ordersData = OrderPlotRel::findExistingOrdersByPlots($plotsIds);

        return new JsonResponse($ordersData, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/plots/grids/exist",
     *     summary="Get existsing grid by plots ids and order id.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @return Response
     */
    public function getExistingGridsData()
    {
        $plotsIds = urldecode(Request::get('plotsIds'));
        $orderId = urldecode(Request::get('orderId'));
        $plotsIds = json_decode($plotsIds, true);
        $orderId = json_decode($orderId, true);

        $existingGrids = $this->plotShape->getExistingGridsAsGeoJSON($plotsIds, $orderId);

        return new Response(200, ['Content-Type: application/json'], $existingGrids);
    }
}
