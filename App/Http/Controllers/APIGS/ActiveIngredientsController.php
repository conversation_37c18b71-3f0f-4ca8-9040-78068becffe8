<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\NotImplementedException;
use App\Http\Controllers\Controller;
use App\Http\Resources\ActiveIngredient\ActiveIngredientCollection;
use App\Models\ActiveIngredient;

class ActiveIngredientsController extends Controller
{
    public function index(): ActiveIngredientCollection
    {
        $activeIngredients = ActiveIngredient::get();

        return new ActiveIngredientCollection($activeIngredients);
    }

    public function show(ActiveIngredient $activeIngredient)
    {
        throw new NotImplementedException();
    }

    public function store()
    {
        throw new NotImplementedException();
    }

    public function edit(ActiveIngredient $activeIngredient)
    {
        throw new NotImplementedException();
    }

    public function update()
    {
        throw new NotImplementedException();
    }

    public function destroy(ActiveIngredient $activeIngredient)
    {
        throw new NotImplementedException();
    }
}
