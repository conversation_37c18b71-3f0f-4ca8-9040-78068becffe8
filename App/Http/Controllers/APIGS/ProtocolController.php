<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App\Classes\CMS\ProtocolService;
use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\Organization;
use App\Models\Plot;
use App\Services\Exports\Pdf;
use Auth;
use Config;
use DateTime;
use Illuminate\Http\Request;
use Validator;

class ProtocolController extends BaseController
{
    private $protocolService;

    public function __construct(ProtocolService $protocolService)
    {
        $this->protocolService = $protocolService;
    }

    public function generateProtocolByOrder(Order $order)
    {
        $date = new DateTime();
        $plots = OrderPlotRel::getPlotsForProtocol($order);
        $totalArea = 0;
        foreach ($plots as $each) {
            $totalArea += $each->area;
        }

        $data = [
            'period' => (new DateTime($order->from_date))->format('d-m-Y') . ' - ' . (new DateTime($order->to_date))->format('d-m-Y'),
            'customerName' => $order->company_name,
            'plots' => $plots,
            'totalArea' => $totalArea,
            'date' => $date->format('d-m-Y'),
        ];

        $pdf = (new Pdf())
            ->loadView('templates.contract_protocol_by_order', $data)
            ->margins(2.5, 2.5, 2.5, 2.5, 'cm')
            ->format('a4')
            ->scale(0.8)
            ->browsershot()->pdf('protocol_' . $date->format('Y-m-d') . '.pdf');

        return response($pdf, 200, ['Content-Type' => 'application/pdf']);
    }

    public function generatePackagesProtocol(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'packages' => 'required|json',
            'plotIds' => 'required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $date = new DateTime();
        $plotIds = json_decode($request->get('plotIds'));
        $packages = json_decode($request->get('packages'));

        $plots = Plot::getForProtocol($plotIds);

        $totalArea = 0;
        foreach ($plots as $each) {
            $totalArea += $each->area;
        }

        $data = [
            'period' => (new DateTime())->format('d-m-Y') . ' - ' . (new DateTime())->format('d-m-Y'),
            'customerName' => 'CHANGE ME',
            'plots' => $plots,
            'totalArea' => $totalArea,
            'date' => $date->format('d-m-Y'),
        ];

        $pdf = (new Pdf())
            ->loadView('templates.packages_protocol', $data)
            ->margins(2.5, 2.5, 2.5, 2.5, 'cm')
            ->format('a4')
            ->scale(0.8)
            ->browsershot()->pdf();

        return response($pdf, 200, ['Content-Type' => 'application/pdf']);
    }

    public function generateProtocolById(Request $request, int $protocolId)
    {
        $requestData = $request->all();
        $requestData['protocol_id'] = $protocolId;

        $validator = Validator::make($requestData, [
            'protocol_id' => 'integer|min:1',
            'organization_identity_number' => 'string|required',
            'lang' => 'string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organization = Organization::where('identity_number', $requestData['organization_identity_number'])->first();
        $serviceProvider = Auth::user()->globalUser()->serviceProvider;

        $protocolData = $this->protocolService->listProtocols($requestData, $protocolId);
        $plots = OrderPlotRel::getPlotsProtocolData($protocolData['orderUuids'], $protocolData['plotUuids'], $organization->identity_number);
        $protocolDate = (new DateTime($protocolData['protocolDate']))->format('d-m-Y');

        $totalArea = 0;
        $totalGridCells = 0;
        foreach ($plots as $each) {
            $totalArea += $each->area;
            $totalGridCells += $each->grid_cells_num;
        }

        $companyInfo = json_decode($serviceProvider->company_info, true);

        if ($companyInfo) {
            $contractTrans = trans('contractProtocol.contract');
            $companyInfo['name'] = Helper::isCyrillic($contractTrans) && isset($companyInfo['name_cyrillic'])
                ? $companyInfo['name_cyrillic']
                : $companyInfo['name_latin'];
        }

        $mainHtmldata = [
            'customerName' => $organization->name,
            'serviceManager' => $protocolData['responsibleUser'],
            'contractId' => $protocolData['contractId'],
            'contractStartDate' => (new DateTime($protocolData['contractStartDate']))->format('d-m-Y'),
            'contractEndDate' => (new DateTime($protocolData['contractEndDate']))->format('d-m-Y'),
            'contractArea' => $protocolData['contractArea'],
            'protocolId' => $protocolData['protocolId'],
            'plots' => $plots,
            'totalArea' => $totalArea,
            'totalGridCells' => $totalGridCells,
            'date' => $protocolDate,
            'areaUnit' => trans('general.' . Config::get('globals.AREA_UNIT_LABEL')),
            'serviceProvider' => $serviceProvider,
            'companyInfo' => $companyInfo,
        ];

        $headerHtmlData = [
            'companyInfo' => $companyInfo,
            'serviceProviderLogo' => $serviceProvider->logo ?? '',
        ];

        $mainHtml = view('templates.contract_protocol_by_id', $mainHtmldata)->render();
        $headerHtml = view('templates.helpers.contract_protocol_header', $headerHtmlData)->render();
        $footerHtml = view('templates.helpers.contract_protocol_footer')->render();

        $pdf = (new Pdf())
            ->loadHtml($mainHtml)
            ->headerHtml($headerHtml)
            ->footerHtml($footerHtml)
            ->showBrowserHeaderAndFooter()
            ->margins(2.5, 2.5, 1.3, 2.5, 'cm')
            ->format('a4')
            ->scale(0.8)
            ->browsershot()->pdf();

        return response($pdf, 200, ['Content-Type' => 'application/pdf']);
    }
}
