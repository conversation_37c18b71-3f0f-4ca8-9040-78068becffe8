<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2020 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\CurrentIrrigationPlatform;
use App\Models\IrrigationPlatform;
use App\Services\Crop\CropService;
use App\Services\Irrigation\IrrigationPlatformService;
use Auth;
use Config;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class IrrigationPlatformController extends BaseController
{
    private $cropService;

    public function __construct(CropService $cropService)
    {
        $this->cropService = $cropService;
    }

    /**
     * @OA\Get(
     * path="/apigs/irrigation-platform
     *     summary="Get all irrigation platforms data",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @return JsonResponse
     */
    public function get(Request $request)
    {
        $name = $request->get('name');
        $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);
        $query = IrrigationPlatform::getIrrigations($organizationId, $name);
        $irrigationPlatforms = $query->orderBy('id', 'desc')->get();

        return new JsonResponse($irrigationPlatforms, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-platform/:irrigationPlatformId",
     *     summary="Get irrigations platform data",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function getById($irrigationPlatformId)
    {
        $irrigationPlatform = IrrigationPlatform::getIrrigationBuId($irrigationPlatformId)->first();

        return new JsonResponse($irrigationPlatform, 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/irrigation-platform",
     *     summary="Create irrigation platform",
     *
     *     @OA\Response(
     *         response="201",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contract_id' => 'required|integer',
            'name' => 'required|string',
            'farm_id' => 'required|integer',
            'status' => 'required|boolean',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $integration = new IrrigationPlatform();
        $defaultDBCrs = Config::get('globals.DEFAULT_DB_CRS');
        $centreSql = 'ST_SetSRID(ST_MakePoint(' . $data['longitude'] . ', ' . $data['latitude'] . "), {$defaultDBCrs})";

        $data['centre'] = DB::raw($centreSql);
        $data['centre_buff'] = DB::raw('ST_Buffer(' . $centreSql . ', 15)');
        $integration->fill($data);
        $integration->save();

        // Set irrigated field in su_satellite_plots_crops
        $param['farm_ids'] = [$data['farm_id']];
        $this->cropService->setIrrigatedFromPlatform($param);

        return new JsonResponse($integration, 201);
    }

    /**
     * @OA\PUT(
     *     path="/apigs/irrigation-platform/:irrigationPlatformId",
     *     summary="Update irrigation platform data",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function update($irrigationPlatformId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contract_id' => 'required|integer',
            'name' => 'required|string',
            'farm_id' => 'required|integer',
            'status' => 'required|boolean',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $irrigationPlatform = IrrigationPlatform::getIrrigationBuId($irrigationPlatformId)->first();
        $data = $request->all();
        $defaultDBCrs = Config::get('globals.DEFAULT_DB_CRS');
        $centreSql = 'ST_SetSRID(ST_MakePoint(' . $data['longitude'] . ', ' . $data['latitude'] . "), {$defaultDBCrs})";

        $data['centre'] = DB::raw($centreSql);
        $data['centre_buff'] = DB::raw('ST_Buffer(' . $centreSql . ', 15)');
        $irrigationPlatform->update($data);

        // Set irrigated field in su_satellite_plots_crops
        $param['farm_ids'] = [$data['farm_id']];
        $this->cropService->setIrrigatedFromPlatform($param);

        return new JsonResponse($irrigationPlatform, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/map",
     *     summary="Get irrigation platform data",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function getPlatformsGeoJSON(Request $request)
    {
        $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);
        $response = CurrentIrrigationPlatform::getPlatformsDataGeoJsonByOrganizationId($organizationId);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/{irrigationPlatformId}/pivot-position",
     *     summary="Get pivot current position geometry",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getPivotPositionGeoJSON($irrigationPlatformId)
    {
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $response = CurrentIrrigationPlatform::getCurrentPivotPosition($organizationId, $irrigationPlatformId);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/pivot/state/echart",
     *     summary="Get eChart data for pivots state",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getPivotStateDataEChart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'farm_ids' => 'sometimes|required|json',
            'plot_ids' => 'sometimes|required|json',
            'states' => 'sometimes|required|json',
            'platform_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $platformIds = $request->get('platform_ids') ? json_decode($request->get('platform_ids'), true) : [];
        $states = $request->get('states') ? json_decode($request->get('states'), true) : [];
        $farmIds = $request->get('farm_ids') ? json_decode($request->get('farm_ids'), true) : [];
        $plotIds = $request->get('plot_ids') ? json_decode($request->get('plot_ids'), true) : [];

        $response = CurrentIrrigationPlatform::getPivotCurrentDataStateEchart($organizationId, $platformIds, $states, $farmIds, $plotIds);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/state/echart",
     *     summary="Get eChart data for irrigation platforms state",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getIrrigationPlatformStateDataEChart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'farm_ids' => 'sometimes|required|json',
            'plot_ids' => 'sometimes|required|json',
            'states' => 'sometimes|required|json',
            'platform_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $platformIds = json_decode($request->get('platform_ids', '[]'), true);
        $states = json_decode($request->get('states', '[]'), true);
        $farmIds = json_decode($request->get('farm_ids', '[]'), true);
        $plotIds = json_decode($request->get('plot_ids', '[]'), true);

        $response = CurrentIrrigationPlatform::getPlatformCurrentDataState($organizationId, $platformIds, $states, $farmIds, $plotIds);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/report",
     *     summary="Update irrigation reprot for platfroms",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getIrrigationReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $limit = $request->get('limit', 30);
        $organizationId = Auth::user()->lastChosenOrganization->id;
        $query = IrrigationPlatform::irrigationReport($organizationId);

        $data = $query->paginate($limit, ['*']);

        $irrigationReport = [
            'total' => $data->total(),
            'rows' => $data->items(),
        ];

        return new JsonResponse($irrigationReport, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/download-report-list",
     *     summary="Download irrigation platform report to CSV",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function downloadReportList()
    {
        return IrrigationPlatformService::downloadCsvFile();
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/tasks",
     *     summary="Get irrigation tasks list by filter",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function tasks(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'types' => 'json',
            'plot_ids' => 'json',
            'farm_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json',
            'event_plot_ids' => 'json',
            'from' => 'required_without:event_plot_ids|required_with:to|integer',
            'to' => 'required_without:event_plot_ids|required_with:from|integer',
            'limit' => 'required_without:event_plot_ids|integer|min:1',
            'page' => 'required_without:event_plot_ids|integer|min:1',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'types' => json_decode($request->get('types', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'eventPlotIds' => json_decode($request->get('event_plot_ids', '[]')),
            'from' => $request->get('from'),
            'to' => $request->get('to'),
            'limit' => $request->get('limit'),
            'page' => $request->get('page'),
        ];

        $tasks = IrrigationPlatform::getIrrigationTasksList($organizationId, $filter);

        return new JsonResponse($tasks, 200);
    }

    /**
     * @OA\GET(
     *     path="/apigs/irrigation-platform/tasks/{id}/raw",
     *     summary="Get the raw data for irrigation task",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @param int $id The task id (The id from table su_irrigation_events_plots)
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getTaskRawData(Request $request, int $id)
    {
        $organizationId = Auth::user()->lastChosenOrganization->id;
        $taskRawData = IrrigationPlatform::getIrrigationTaskRawData($id, $organizationId);

        return new JsonResponse($taskRawData, 200);
    }
}
