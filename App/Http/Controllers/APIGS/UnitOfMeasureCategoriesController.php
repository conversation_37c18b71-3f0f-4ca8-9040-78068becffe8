<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\NotImplementedException;
use App\Http\Controllers\Controller;
use App\Models\UnitOfMeasure\UnitOfMeasureCategory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UnitOfMeasureCategoriesController extends Controller
{
    public function index(): JsonResponse
    {
        $categories = UnitOfMeasureCategory::get();

        return new JsonResponse($categories, Response::HTTP_OK);
    }

    public function store(Request $request)
    {
        throw new NotImplementedException();
    }

    public function show($id)
    {
        throw new NotImplementedException();
    }

    public function update(Request $request, $id)
    {
        throw new NotImplementedException();
    }

    public function destroy($id)
    {
        throw new NotImplementedException();
    }
}
