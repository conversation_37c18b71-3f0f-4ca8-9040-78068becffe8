<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Plot;
use App\Models\PlotCrop;
use App\Services\Crop\CropService;
use DB;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class PlotsCropsController extends BaseController
{
    private $cropService;

    public function __construct(CropService $cropService)
    {
        $this->middleware('verify-write-rights', ['except' => ['getCultures']]);
        $this->cropService = $cropService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/crops",
     *     summary="Crops for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getCrops($plotId)
    {
        $validator = Validator::make([
            'plotId' => $plotId,
            'year' => Request::get('year'),
        ], [
            'plotId' => 'required|integer',
            'year' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $year = Request::get('year');
        $sort = Request::get('sort');
        $limit = Request::get('limit');

        return Plot::getPlotCrops($plotId, $year, $sort, $limit);
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots/:plotId/crops/save-gdd-settings",
     *     summary="Save GDD settings",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postSaveGddSettings($gid)
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'crop_id' => 'required|integer',
            'year' => 'required|integer',
            'emergence_date' => 'sometimes|date_format:Y-m-d',
            'phenophase_date' => 'sometimes|date_format:Y-m-d',
            'gdd_phenophase_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        DB::table('su_satellite_plots_crops')
            ->where('plot_id', Request::get('gid'))
            ->where('crop_id', Request::get('crop_id'))
            ->where('year', Request::get('year'))
            ->update([
                'emergence_date' => Request::get('emergence_date'),
                'phenophase_date' => Request::get('phenophase_date'),
                'gdd_phenophase_id' => Request::get('gdd_phenophase_id'),
            ]);

        return Response::json(['Response' => 'Success']);
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots/:plotId/crops/replace",
     *     summary="Replace crops for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postReplace($gid)
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'year' => 'required|integer',
            'crops' => 'array',
            'crops.*.crop_id' => 'required|integer',
            'crops.*.year' => 'required|integer',
            'crops.*.from_date' => 'required|date_format:Y-m-d',
            'crops.*.to_date' => 'required|date_format:Y-m-d',
            'crops.*.is_primary' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotCrops = Request::get('crops');
        $year = Request::get('year');

        // If only 1 crop is filled
        if (1 === count($plotCrops)) {
            // Update or Create crop for plot
            PlotCrop::updateOrCreate(['plot_id' => $gid, 'year' => $year], $plotCrops[0]);
        } // If there are more than 1 crop for update or add
        else {
            //  Delete all crops for plot
            PlotCrop::where('plot_id', $gid)
                ->where('year', $year)
                ->delete();

            // Add new crops for plot
            PlotCrop::insert($plotCrops);
        }

        // Set irrigated field in su_satellite_plots_crops
        $param['gid'] = $gid;
        $this->cropService->setIrrigatedFromPlatform($param);

        return Response::json(['Response' => 'Success']);
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots/:plotId/crops",
     *     summary="Store crop for plot",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postStore($gid)
    {
        $input = array_merge(Request::all(), [
            'gid' => $gid,
        ]);
        $validator = Validator::make($input, [
            'gid' => 'required|integer',
            'crop_id' => 'required|integer',
            'year' => 'required|integer',
            'from_date' => 'required|date_format:Y-m-d',
            'to_date' => 'required|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        Plot::find($gid)
            ->crops()
            ->attach(Request::get('crop_id'), [
                'year' => Request::get('year'),
                'from_date' => Request::get('from_date'),
                'to_date' => Request::get('to_date'),
            ]);
    }

    /**
     * @OA\Put(
     *     path="/apigs/plots/:plotId/crops/:relId",
     *     summary="Update crop for plot",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function putUpdate($gid, $relId)
    {
        $input = array_merge(Request::all(), [
            'gid' => $gid,
            'relId' => $relId,
        ]);
        $validator = Validator::make($input, [
            'gid' => 'required|integer',
            'relId' => 'required|integer',
            'crop_id' => 'required|integer',
            'year' => 'required|integer',
            'from_date' => 'required|date_format:Y-m-d',
            'to_date' => 'required|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        DB::table('su_satellite_plots_crops')
            ->where('id', $relId)
            ->where('plot_id', $gid)
            ->update([
                'crop_id' => Request::get('crop_id'),
                'from_date' => Request::get('from_date'),
                'to_date' => Request::get('to_date'),
            ]);
    }

    /**
     * @OA\Delete(
     *     path="/apigs/plots/:plotId/crops/:relId",
     *     summary="Delete crop for plot",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function destroy($gid, $relId)
    {
        $input = array_merge(Request::all(), [
            'gid' => $gid,
            'relId' => $relId,
        ]);
        $validator = Validator::make($input, [
            'gid' => 'required|integer',
            'relId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        DB::table('su_satellite_plots_crops')
            ->where('id', $relId)
            ->where('plot_id', $gid)
            ->delete();
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots/crops/set-irrigated",
     *     summary="Set irrigated crop",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @param id
     * @param irrigated
     *
     * @throws ValidationException
     */
    public function setIrrigated()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'irrigated' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrParam['id'] = Request::get('id');
        $irrigated = Request::get('irrigated');

        PlotCrop::setIrrigatedBy($arrParam, $irrigated);
    }
}
