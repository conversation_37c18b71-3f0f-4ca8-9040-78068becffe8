<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\Controller;
use App\Http\Requests\MachineTaskProducts\ExportReportRequest;
use App\Http\Requests\MachineTaskProducts\GetReportRequest;
use App\Http\Requests\MachineTaskProducts\SendReportRequest;
use App\Services\Common\MailService;
use App\Services\Machine\MachineProductsReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class MachineTaskProductsController extends Controller
{
    private MachineProductsReportService $machineProductsReportService;
    private MailService $mailService;

    public function __construct(
        MachineProductsReportService $machineProductsReportService,
        MailService $mailService
    ) {
        $this->machineProductsReportService = $machineProductsReportService;
        $this->mailService = $mailService;
    }

    public function getReport(GetReportRequest $request): JsonResponse
    {
        $requestData = $request->validated();
        $filter = [
            'page' => Arr::get($requestData, 'page'),
            'limit' => Arr::get($requestData, 'limit'),
            'from' => Arr::get($requestData, 'from', strtotime('- 20 days')),
            'to' => Arr::get($requestData, 'to', time()),
            'farmIds' => json_decode(Arr::get($requestData, 'farm_ids', '[]')),
            'plotIds' => json_decode(Arr::get($requestData, 'plot_ids', '[]')),
            'machineIds' => json_decode(Arr::get($requestData, 'machine_ids', '[]')),
            'implements' => json_decode(Arr::get($requestData, 'implements', '[]')),
            'stages' => json_decode(Arr::get($requestData, 'stages', '[]')),
            'cropIds' => json_decode(Arr::get($requestData, 'crop_ids', '[]')),
            'workOperations' => json_decode(Arr::get($requestData, 'work_operations', '[]')),
            'taskStates' => json_decode(Arr::get($requestData, 'task_states', '[]')),
            'driver' => Arr::get($requestData, 'driver'),
            'productIds' => json_decode(Arr::get($requestData, 'product_ids', '[]')),
        ];
        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $result = $this->machineProductsReportService->getReport($organizationId, $filter, $groupBy, $orderBy);

        return new JsonResponse($result, 200);
    }

    public function exportReport(ExportReportRequest $request, string $type): BinaryFileResponse
    {
        $requestData = $request->validated();
        $filter = [
            'from' => Arr::get($requestData, 'from', strtotime('- 20 days')),
            'to' => Arr::get($requestData, 'to', time()),
            'farmIds' => Arr::get($requestData, 'farm_ids', []),
            'plotIds' => Arr::get($requestData, 'plot_ids', []),
            'machineIds' => Arr::get($requestData, 'machine_ids', []),
            'implements' => Arr::get($requestData, 'implements', []),
            'stages' => Arr::get($requestData, 'stages', []),
            'cropIds' => Arr::get($requestData, 'crop_ids', []),
            'workOperations' => Arr::get($requestData, 'work_operations', []),
            'taskStates' => Arr::get($requestData, 'task_states', []),
            'productIds' => Arr::get($requestData, 'products_ids', []),
            'driver' => Arr::get($requestData, 'driver'),
        ];
        $selectedColumns = Arr::get($requestData, 'selected_columns', []);
        $groupBy = Arr::get($requestData, 'group_by');
        $orderBy = Arr::get($requestData, 'order_by', []);
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $reportFile = $this->machineProductsReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy, $selectedColumns);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        $customFileName = 'machines_products_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        return response()
            ->download(
                $reportFileFullPath,
                $customFileName
            )
            ->deleteFileAfterSend();
    }

    public function sendReport(SendReportRequest $request, string $type): JsonResponse
    {
        $requestData = $request->validated();
        $filter = [
            'from' => Arr::get($requestData, 'from', strtotime('- 20 days')),
            'to' => Arr::get($requestData, 'to', time()),
            'farmIds' => Arr::get($requestData, 'farm_ids', []),
            'plotIds' => Arr::get($requestData, 'plot_ids', []),
            'machineIds' => Arr::get($requestData, 'machine_ids', []),
            'implements' => Arr::get($requestData, 'implements', []),
            'stages' => Arr::get($requestData, 'stages', []),
            'cropIds' => Arr::get($requestData, 'crop_ids', []),
            'workOperations' => Arr::get($requestData, 'work_operations', []),
            'taskStates' => Arr::get($requestData, 'task_states', []),
            'productIds' => Arr::get($requestData, 'product_ids', []),
        ];
        $selectedColumns = Arr::get($requestData, 'selected_columns', []);
        $groupBy = Arr::get($requestData, 'group_by');
        $orderBy = Arr::get($requestData, 'order_by', []);
        $email = Arr::get($requestData, 'email');
        $subject = Arr::get($requestData, 'subject', 'Agrimi Products report');
        $message = Arr::get($requestData, 'message', '');
        $title = trans('emailReport.machinesProductsReport');
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $reportFile = $this->machineProductsReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy, $selectedColumns);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);
        $customFileName = 'machines_products_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFileFullPath, $customFileName);

        if (Storage::disk('qnap_storage')->exists($reportFile)) {
            Storage::disk('qnap_storage')->delete($reportFile);
        }

        return new JsonResponse('Success', 200);
    }
}
