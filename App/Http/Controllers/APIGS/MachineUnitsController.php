<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\MachineUnits\GetMachineUnitTrackRequest;
use App\Models\Integration;
use App\Models\MachineUnit;
use App\Services\Machine\MachineUnitService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class MachineUnitsController extends BaseController
{
    private $machineUnitService;

    public function __construct(MachineUnitService $machineUnitService)
    {
        $this->machineUnitService = $machineUnitService;
    }

    /**
     * @OA\Get(
     * path="/apigs/machine-units/sync
     *     summary="Sync machine units from wialon for the specified integration.",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function sync(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'integration_id' => 'integer|required_without:integration_token,integration_url_address',
            'integration_token' => 'string|required_without:integration_id|required_with:integration_url_address',
            'integration_url_address' => 'string|required_without:integration_id|required_with:integration_token',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $integrationId = $request->get('integration_id');
        $integrationToken = $request->get('integration_token');
        $integrationUrlAddress = $request->get('integration_url_address');

        if (!$integrationId) {
            // Get all units from wialon by url and token
            $machineUnits = $this->machineUnitService->getUnitsByIntegrationUrlAndToken($integrationUrlAddress, $integrationToken);

            return new JsonResponse($machineUnits, 200);
        }

        $integration = Integration::findOrFail($integrationId);
        $this->authorize('manageIntegration', $integration);

        // Get existing units from db by integration_id and all units from wialon
        $machineUnits = $this->machineUnitService->syncUnitsByIntegration($integration);

        return new JsonResponse($machineUnits, 200);
    }

    /**
     * @OA\Get(
     * path="/apigs/machine-units
     *     summary="Get units by organization",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer|sometimes|required',
            'name' => 'string|sometimes|required',
            'option_all' => 'boolean|sometimes|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);

        $unitsQuery = MachineUnit::getUnits()->where('organization_id', $organizationId);

        if ($request->get('name')) {
            $unitName = preg_replace('/\s+/i', ' ', $request->get('name'));
            $unitsQuery->where(DB::raw("REGEXP_REPLACE(name, '\s+', ' ', 'g')"), 'ILIKE', "%{$unitName}%");
        }

        $units = $unitsQuery->orderBy('id', 'desc')->get()->toArray();

        $addOptionAll = $request->get('option_all', false);
        if ($addOptionAll) {
            array_push($units, [
                'id' => 0,
                'organization_id' => $organizationId,
                'name' => 'All',
                'wialon_unit_imei' => 0,
                'type' => '',
                'last_communication' => '',
                'last_position' => '',
                'wialon_unit_id' => null,
                'integration_id' => null,
            ]);
        }

        return new JsonResponse($units, 200);
    }

    /**
     * @OA\Get(
     * path="/apigs/machine-units/types
     *     summary="Get machine unit types(e.g. Tractor, Harvester, Sprayer)",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getTypes(Request $request)
    {
        $units = MachineUnit::getUnitTypes();

        return new JsonResponse($units, 200);
    }

    /**
     * @OA\Put(
     * path="/apigs/machine-units/{unit}
     *     summary="Update unit",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function update(MachineUnit $unit, Request $request)
    {
        $unitTypes = MachineUnit::getUnitTypes();
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'name' => 'sometimes|required|string',
            'type' => 'sometimes|required|string|in:' . implode(',', $unitTypes),
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $unit->update($requestData);

        return new JsonResponse($unit, 200);
    }

    /**
     * @OA\Delete(
     * path="/apigs/machine-units/{unit}
     *     summary="Delete unit and all related events",
     *
     *     @OA\Response(
     *          response="200",
     *          description="String"")
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function delete(MachineUnit $unit)
    {
        $success = $this->machineUnitService->deleteUnitWithEvents($unit);

        if (!$success) {
            return new JsonResponse('Error deleting unit!', 409);
        }

        return new JsonResponse('Success', 200);
    }

    /**
     * @OA\Get(
     * path="/apigs/machine-units/{unit}/track/{format?}
     *     summary="Get unit track geojson for specified time range",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @param Request $request
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getUnitTrack(
        GetMachineUnitTrackRequest $machineUnitTrackRequest,
        MachineUnit $unit,
        string $format = MachineUnit::TRACK_FORMAT_GEOJSON
    ) {
        $organizationId = Auth::user()->lastChosenOrganization->id;
        $from = $machineUnitTrackRequest->get('from', strtotime('-5 minutes'));
        $to = $machineUnitTrackRequest->get('to', time());

        $machineUnitTrack = $this->machineUnitService->getMachineUnitTrack($unit, $organizationId, $from, $to, $format);

        return new JsonResponse($machineUnitTrack, 200);
    }
}
