<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\TFConnect;
use App\Exceptions\ForbiddenException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderVra;
use App\Services\Order\OrderService;
use App\Services\Order\OrderVraService;
use Auth;
use Exception;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class OrderVraController extends BaseController
{
    private $tfc;
    private $orderVraService;
    private $orderService;

    public function __construct(TFConnect $tfc, OrderVraService $orderVraService, OrderService $orderService)
    {
        $this->tfc = $tfc;
        $this->orderVraService = $orderVraService;
        $this->orderService = $orderService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/order-vra/orders",
     *     summary="Get VRA orders",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getOrders()
    {
        $validator = Validator::make(Request::all(), [
            'plotId' => 'required|integer',
            'farmYear' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new OrderVra();
        $plotId = Request::get('plotId');
        $farmYear = Request::get('farmYear');

        try {
            $order = $model->getVraOrdersByPlots((int) $plotId, (int) $farmYear);
        } catch (Exception $e) {
            throw $e;
        }

        return isset($order[0]) ? $order : [];
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/export",
     *     summary="Export VRA orders",
     *
     *     @OA\Response(
     *         response="200",
     *         description="File"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function getExport()
    {
        $vraOrdersType = [OrderVraService::VRA_SATELLITE_ORDER_TYPE, OrderVraService::VRA_SOIL_ORDER_TYPE];
        $vraOrdersTypeStr = implode(',', $vraOrdersType);

        $validator = Validator::make(Request::all(), [
            'vraOrders' => 'required|array',
            'vraOrders.*.id' => 'required|integer',
            'vraOrders.*.type' => "required|string|in:{$vraOrdersTypeStr}",
            'format' => ['required', 'max:255', 'in:john_deere,shp,trimble'],
            'forTfc' => 'boolean',
            'deviceSerial' => 'alpha_num|min:16|max:16',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $forTfc = Request::input('forTfc');
        $deviceSerial = Request::input('deviceSerial');
        $vraOrders = Request::input('vraOrders');
        $format = Request::input('format');

        try {
            $outputFilePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid("vra_export_{$format}_") . '.zip';

            $outputFilePath = $this->orderVraService->exportVraMaps(
                $vraOrders,
                $format,
                $outputFilePath
            );

            if ($forTfc) {
                $info = $this->tfc->uploadFile($deviceSerial, $outputFilePath);
                $this->tfc->setStatus($deviceSerial, $info['file_id'], 'sending');

                if (is_file($outputFilePath)) {
                    unlink($outputFilePath);
                }

                return Response::json(['result' => 'exported_tfc', 'info' => $info], 200);
            }
        } catch (Exception $e) {
            throw $e;
        }

        return response()->download($outputFilePath)->deleteFileAfterSend(true);
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/create-order",
     *     summary="Create VRA order",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postCreateOrder()
    {
        if (!Auth::user()->globalUser()->can('create_vra_satellite_maps') && !Auth::user()->globalUser()->can('create_vra_soil_maps')) {
            throw new ForbiddenException();
        }

        $validator = Validator::make(Request::all(), [
            'plot_id' => 'integer|required',
            'order' => 'array|required',
            'class_number' => 'integer|required',
            'layer_id' => 'integer|required',
            'flat_rate' => 'numeric|required',
            'flat_rate_total' => 'numeric|required',
            'variable_rate_total' => 'numeric|required',
            'difference' => 'numeric|required',
            'difference_percent' => 'numeric|required',
            'data' => 'array|required',
            'vector_data' => 'array|required',
            'product_percent' => 'numeric|required',
            'tiff_path' => 'required',
            'name' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new Order();

        try {
            $id = $model->createVraOrder(Request::all());
        } catch (Exception $e) {
            throw $e;
        }

        return Response::json(['result' => 'order created', 'id' => $id], 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/cance-order",
     *     summary="Cancel VRA order",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postCancelOrder()
    {
        $validator = Validator::make(Request::all(), [
            'order_id' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->orderService->cancelOrder(Request::get('order_id'));

        return Response::json(['result' => 'order canceled'], 200);
    }
}
