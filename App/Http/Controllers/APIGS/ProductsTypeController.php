<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\NotImplementedException;
use App\Http\Controllers\BaseController;
use App\Http\Resources\Product\ProductTypeCollection;
use App\Models\Products\ProductType;
use Illuminate\Http\Request;

class ProductsTypeController extends BaseController
{
    public function index(): ProductTypeCollection
    {
        $productsType = ProductType::get();

        return new ProductTypeCollection($productsType);
    }

    public function store(Request $request)
    {
        throw new NotImplementedException();
    }

    public function show($id)
    {
        throw new NotImplementedException();
    }

    public function update(Request $request, $id)
    {
        throw new NotImplementedException();
    }

    public function destroy($id)
    {
        throw new NotImplementedException();
    }
}
