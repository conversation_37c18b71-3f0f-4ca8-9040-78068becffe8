<?php

namespace App\Http\Controllers\APIGS;

use App\Actions\Product\DeleteProductAction;
use App\Actions\Product\StoreProductAction;
use App\Actions\Product\UpdateProductAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Product\IndexProductRequest;
use App\Http\Requests\Product\StoreProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Http\Resources\Product\ProductResource;
use App\Http\Resources\Product\ProductsCollection;
use App\Models\Products\Product;
use Illuminate\Http\Response as HttpResponse;
use Symfony\Component\HttpFoundation\Response;

class ProductsController extends Controller
{
    public function index(IndexProductRequest $request): ProductsCollection
    {
        $requestData = $request->validated();
        $user = $request->user();
        $organizationId = $requestData['organization_id'] ?? $user->lastChosenOrganization->id;

        $results = Product::listProductsCustom()->forOrganization($organizationId)->get();

        return new ProductsCollection($results);
    }

    public function show(int $productId): ProductResource
    {
        $product = Product::listProductsCustom()->forProduct($productId)->first();

        return new ProductResource($product);
    }

    public function store(StoreProductRequest $request, StoreProductAction $storeProductAction): ProductResource
    {
        $requestData = $request->validated();
        $product = $storeProductAction->withAttributes($requestData);

        return new ProductResource($product);
    }

    public function update(UpdateProductRequest $request, UpdateProductAction $updateProductAction, Product $product): ProductResource
    {
        $requestData = $request->validated();
        $product = $updateProductAction
            ->forProduct($product)
            ->withAttributes($requestData);

        return new ProductResource($product);
    }

    public function destroy(Product $product, DeleteProductAction $deleteProductAction): HttpResponse
    {
        $deleteProductAction->forProduct($product);

        return response()->noContent(Response::HTTP_ACCEPTED);
    }
}
