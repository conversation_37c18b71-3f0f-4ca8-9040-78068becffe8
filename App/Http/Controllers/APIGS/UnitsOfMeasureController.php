<?php

namespace App\Http\Controllers\APIGS;

use App\Actions\UnitOfMeasure\DeleteUnitOfMeasureAction;
use App\Actions\UnitOfMeasure\StoreUnitOfMeasureAction;
use App\Actions\UnitOfMeasure\UpdateUnitOfMeasureAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\UnitOfMeasure\IndexShowUnitsOfMeasureRequest;
use App\Http\Requests\UnitOfMeasure\StoreUpdateUnitsOfMeasureRequest;
use App\Http\Resources\UnitOfMeausre\UnitOfMeasureCollection;
use App\Http\Resources\UnitOfMeausre\UnitOfMeasureResource;
use App\Models\UnitOfMeasure\UnitOfMeasure;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class UnitsOfMeasureController extends Controller
{
    public function index(IndexShowUnitsOfMeasureRequest $request): UnitOfMeasureCollection
    {
        $user = $request->user();
        $requestData = $request->validated();
        $organizationId = $requestData['organization_id'] ?? $user->lastChosenOrganization->id;
        $units = UnitOfMeasure::forServiceProvider($user->globalUser()->serviceProvider->id)
            ->forOrganization($organizationId)
            ->productsByOrganization($organizationId)
            ->get();

        return new UnitOfMeasureCollection($units);
    }

    public function store(StoreUpdateUnitsOfMeasureRequest $request, StoreUnitOfMeasureAction $storeUnitOfMeasureAction): UnitOfMeasureResource
    {
        $unitOfMeasure = $storeUnitOfMeasureAction->withAttributes($request->validated());

        return new UnitOfMeasureResource($unitOfMeasure);
    }

    public function show(IndexShowUnitsOfMeasureRequest $request, $unitOfMeasureId): UnitOfMeasureResource
    {
        $user = $request->user();
        $organizationId = $requestData['organization_id'] ?? $user->lastChosenOrganization->id;
        $unitOfMeasure = UnitOfMeasure::productsByOrganization($organizationId)->find($unitOfMeasureId);

        return new UnitOfMeasureResource($unitOfMeasure);
    }

    public function update(StoreUpdateUnitsOfMeasureRequest $request, UpdateUnitOfMeasureAction $updateUnitOfMeasureAction, UnitOfMeasure $unitOfMeasure): UnitOfMeasureResource
    {
        $attributes = $request->validated();
        $updateUnitOfMeasureAction
            ->forUnitOfMeasure($unitOfMeasure)
            ->withAttributes($attributes);

        $unitOfMeasure = UnitOfMeasure::productsByOrganization($attributes['organization_id'])->find($unitOfMeasure->id);

        return new UnitOfMeasureResource($unitOfMeasure);
    }

    public function destroy(DeleteUnitOfMeasureAction $deleteUnitOfMeasureAction, UnitOfMeasure $unitOfMeasure): JsonResponse
    {
        $deleteUnitOfMeasureAction->forUnitOfMeasure($unitOfMeasure);

        return new JsonResponse(null, Response::HTTP_ACCEPTED);
    }
}
