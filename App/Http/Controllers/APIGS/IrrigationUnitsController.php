<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\IrrigationUnit;
use App\Services\Irrigation\IrrigationUnitService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class IrrigationUnitsController extends BaseController
{
    private $irrigationUnitService;

    public function __construct(IrrigationUnitService $irrigationUnitService)
    {
        $this->irrigationUnitService = $irrigationUnitService;
    }

    /**
     * @OA\Get(
     * path="/apigs/irrigation-units/sync
     *     summary="Sync wialon units based on organization's integrations",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function sync(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        $execParams = [
            'spec' => [
                'itemsType' => 'avl_unit',
                'propName' => 'rel_hw_type_id',
                'propValueMask' => '',
                'sortType' => 'sys_id',
            ],
            'force' => 1,
            'flags' => 4353, // Units flags: 1 (base flag) + 256 (advanced properties) + 4096 (sensors) 4353
            'from' => 0,
            'to' => 0,
        ];
        $jsonParams = json_encode($execParams);
        $svcType = 'core/search_items';

        $this->irrigationUnitService->syncUnits($organizationId, $svcType, $jsonParams);
        $units = IrrigationUnit::getUnits()->where('organization_id', $organizationId)->orderBy('id', 'desc')->get();

        return new JsonResponse($units, 200);
    }

    /**
     * @OA\Get(
     * path="/apigs/irrigation-units
     *     summary="Get units by organization",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer|required',
            'option_all' => 'boolean|sometimes|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        $units = IrrigationUnit::getUnits()->where('organization_id', $organizationId)->orderBy('id', 'desc')->get()->toArray();

        $addOptionAll = $request->get('option_all', false);
        if ($addOptionAll) {
            array_push($units, [
                'id' => 0,
                'organization_id' => $organizationId,
                'name' => 'All',
                'wialon_unit_imei' => 0,
                'type' => '',
                'length' => 0,
                'wialon_unit_id' => null,
            ]);
        }

        return new JsonResponse($units, 200);
    }

    /**
     * @OA\PUT(
     *     path="/apigs/irrigation-units/:unitId",
     *     summary="Update unit data",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function update($unitId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer|required',
            'name' => 'required|string',
            'type' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $name = $request->get('name');
        $type = $request->get('type');
        $unit = IrrigationUnit::findOrFail($unitId);
        $unit->update(['name' => $name, 'type' => $type]);

        return new JsonResponse($unit, 200);
    }
}
