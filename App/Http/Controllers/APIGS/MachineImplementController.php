<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Machines\UpdateMachineImplementRequest;
use App\Models\Integration;
use App\Models\MachineImplement;
use App\Services\Implement\MachineImplementService;
use Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MachineImplementController extends BaseController
{
    private $machineImplementService;

    public function __construct(MachineImplementService $machineImplementService)
    {
        $this->machineImplementService = $machineImplementService;
    }

    /**
     * @OA\Get(
     * path="/apigs/machine-implements/sync
     *     summary="Sync machine implements from wialon for the specified integration.",
     *
     *     @OA\Response(
     *     response="200",
     *     description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function sync(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'integration_id' => 'integer|required_without:integration_token,integration_url_address',
            'integration_token' => 'string|required_without:integration_id|required_with:integration_url_address',
            'integration_url_address' => 'string|required_without:integration_id|required_with:integration_token',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $integrationId = $request->get('integration_id');
        $integrationToken = $request->get('integration_token');
        $integrationUrlAddress = $request->get('integration_url_address');

        if (!$integrationId) {
            // Get all implements from wialon by url and token
            $machineImplements = $this->machineImplementService->getImplementsByIntegrationUrlAndToken($integrationUrlAddress, $integrationToken);

            return new JsonResponse($machineImplements, 200);
        }

        $integration = Integration::findOrFail($integrationId);
        $this->authorize('manageIntegration', $integration);

        // Get existing implements from db by integration_id and all implements from wialon
        $machineImplements = $this->machineImplementService->syncImplementsByIntegration($integration);

        return new JsonResponse($machineImplements, 200);
    }

    /**
     * @OA\Get(
     * path="/apigs/machine-implements
     *     summary="Get all machine implements data",
     *
     *     @OA\Response(
     *          response="200",
     *          description="Array"")
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'name' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filters = [
            'name' => $request->get('name', null),
            'organization_id' => $request->get('organization_id', Auth::user()->lastChosenOrganization->id),
        ];

        $machineImplementsQuery = MachineImplement::getFilteredMachineImplementsQuery($filters);
        $machineImplements = $machineImplementsQuery->orderBy('id', 'desc')->get();

        return new JsonResponse($machineImplements, 200);
    }

    /**
     * @OA\PUT(
     *     path="/apigs/machine-implements/{implement}",
     *     summary="Update machine implement",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @param $machineImplement
     * @param Request $request
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function update(MachineImplement $implement, UpdateMachineImplementRequest $request)
    {
        $updatedImplement = $this->machineImplementService->updateImplement($implement, $request->validated());

        return new JsonResponse($updatedImplement, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine-implements/statuses",
     *     summary="Get all statuses for machine implements",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function getImplementStatuses()
    {
        $machineImplementStatuses = $this->machineImplementService->getImplementStatuses();

        return new JsonResponse($machineImplementStatuses, 200);
    }
}
