<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Recommendation;
use App\Models\RecommendationFile;
use Auth;
use DB;
use File;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class RecommendationsController extends BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/recommendations/list",
     *     summary="List of recommendations",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getList()
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'title' => 'nullable|string|max:127',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $resultQuery = Recommendation::select(
            'su_users_recommendations.*',
            DB::raw('(not count(rf.recommendation_id)=0) as has_files', 'rf.file_name')
        )
            ->leftJoin('su_recommendations_files AS rf', 'rf.recommendation_id', '=', 'su_users_recommendations.id')
            ->where('su_users_recommendations.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_users_recommendations.active', true)
            ->groupBy('su_users_recommendations.id')
            ->restOrderBy(Request::get('sort'));

        // Filter
        if (Request::get('title')) {
            $resultQuery->where('title', 'ILIKE', trim('%' . Request::get('title') . '%'));
        }
        if (Request::get('file_name')) {
            $resultQuery->where('rf.file_name', 'ILIKE', trim('%' . Request::get('file_name') . '%'));
        }

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items(),
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/recommendations/files-list",
     *     summary="List of recommendation files",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getFilesList()
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'recommendation_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $resultQuery = RecommendationFile::selectRaw('su_recommendations_files.*')
            ->join('su_users_recommendations AS ur', 'ur.id', '=', 'su_recommendations_files.recommendation_id')
            ->join('su_organizations AS o', 'ur.organization_id', '=', 'o.id')
            ->where('o.id', '=', Auth::user()->lastChosenOrganization->id)
            ->where('su_recommendations_files.recommendation_id', '=', Request::get('recommendation_id'))
            ->restOrderBy(Request::get('sort'));

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items(),
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/recommendations/download-file",
     *     summary="Download recommendation file",
     *
     *     @OA\Response(
     *         response="200",
     *         description="File"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getDownloadFile()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $recomFile = RecommendationFile::find((int)Request::get('id'));

        $path_parts = pathinfo($recomFile->file_name);
        $ext = $path_parts['extension'];
        $destinationPath = config('globals.RECOMMENDATIONS_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->id . '_user' . DIRECTORY_SEPARATOR . $recomFile->recommendation_id . '_rec';
        $file = $destinationPath . DIRECTORY_SEPARATOR . $recomFile->id . '.' . $ext;

        return Response::download($file, $recomFile->file_name);
    }
}
