<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Exceptions\ValidationException;
use App\Models\Ekatte;
use Request;
use Validator;

class EkatteController extends \App\Http\Controllers\BaseController
{
    /**
     * @throws ValidationException
     */
    public function getEkatte()
    {
        $arrValidator = [
            'name' => 'string',
        ];

        $validator = Validator::make(Request::all(), $arrValidator);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $resultQuery = Ekatte::select('ekatte_code as id', 'ekatte_name as name')
            ->orderBy('ekatte_name');

        // Filter
        if (Request::get('name')) {
            $resultQuery->where('ekatte_name', 'ILIKE', trim('%' . Request::get('name') . '%'));
            $resultQuery->orWhere('ekatte_code', 'ILIKE', trim('%' . Request::get('name') . '%'));
        }

        return $resultQuery->get();
    }
}
