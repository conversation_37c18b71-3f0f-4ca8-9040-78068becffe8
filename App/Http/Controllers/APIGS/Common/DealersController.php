<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Models\GlobalUser;
use App\Models\User;

class DealersController extends \App\Http\Controllers\BaseController
{
    public function index()
    {
        $globalUsers = GlobalUser::whereIs('SELLER_ADMIN')->where('active', true)->get();
        $globalUsersIds = $globalUsers->map(function ($user) {
            return $user->only(['old_id']);
        });

        return User::select('su_users.id', 'su_users.name')->whereIn('id', $globalUsersIds)->orderBy('su_users.name')->get();
    }
}
