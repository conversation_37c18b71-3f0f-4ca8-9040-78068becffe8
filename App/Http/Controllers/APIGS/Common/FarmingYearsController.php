<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Models\Organization;
use App\Models\StaticModels\FarmingYear;
use App\Services\FarmingYear\FarmingYearService;
use Auth;
use Illuminate\Support\Collection;

class FarmingYearsController extends \App\Http\Controllers\BaseController
{
    public function index(): Collection
    {
        return FarmingYear::getAll();
    }

    public function byOrganization(FarmingYearService $farmingYearService, ?Organization $organization = null): Collection
    {
        if (!$organization) {
            /** @var Organization $organization */
            $organization = Auth::user()->lastChosenOrganization;
        }

        return $farmingYearService->getFarmingYearsByOrganization($organization);
    }
}
