<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Models\LayerPlot;
use App\Models\Plot;
use Config;
use Illuminate\Http\Request;

class LayersController
{
    public function getLayerData(Request $request, $layerId)
    {
        $layerPlot = LayerPlot::findOrFail($layerId);
        $imageUrl = $layerPlot->layerPlotFiles()->where('type', 'PNG')->first();
        $plot = Plot::find($layerPlot->plot_id);

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $plotArea = $plot->area * $areaCoef;

        $lang = $request->get('lang');
        if (!$lang) {
            $lang = 'en';
        }

        $crop = $plot->getCropNameByDate(date('Y-m-d'), $lang)->first();

        $cropName = 'No Crop';

        if ($crop) {
            $cropName = $crop->crop_name;
        }

        return response()->json([
            'layerId' => $layerPlot->id,
            'plotId' => $layerPlot->plot_id,
            'plotName' => $plot->name,
            'plotArea' => $plotArea,
            'imageUrl' => $imageUrl->web_path,
            'cropName' => $cropName,
        ]);
    }

    public function getLayerPng($layerId)
    {
        $layerPlot = LayerPlot::findOrFail($layerId);

        return $layerPlot->layerPlotFiles()->where('type', 'PNG')->first();
    }
}
