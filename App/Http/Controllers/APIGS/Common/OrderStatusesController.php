<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Http\Controllers\BaseController;
use App\Models\Order;

class OrderStatusesController extends BaseController
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function index()
    {
        $response = Order::getOrderStatuses()->map(function ($item, $key) {
            return [
                'value' => $key,
                'title' => $item,
            ];
        });

        return $response->values();
    }
}
