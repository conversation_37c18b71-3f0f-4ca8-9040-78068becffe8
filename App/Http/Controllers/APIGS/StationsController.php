<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\Echarts\EChartsFormatter;
use App\Classes\Heap;
use App\Classes\Meteo\IStation;
use App\Classes\MeteoService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\UserStation;
use App\Services\Station\ReportService;
use App\Services\Station\StationService;
use Auth;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class StationsController extends BaseController
{
    private $meteoService;
    private $stationService;
    private $reportService;

    public function __construct(Heap $heap, MeteoService $meteoService, StationService $stationService, ReportService $reportService)
    {
        $this->heap = $heap;
        $this->meteoService = $meteoService;
        $this->stationService = $stationService;
        $this->reportService = $reportService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/for-map",
     *     summary="List meteo stations",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getForMap()
    {
        $validator = Validator::make(Request::all(), [
            'geojson_format' => 'boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userId = Auth::user()->id;
        $organizationId = Auth::user()->lastChosenOrganization->id;
        $useGeojsonFormat = Request::get('geojson_format', false);

        $response = $useGeojsonFormat ? UserStation::getForMapGeoJson($userId, $organizationId) : UserStation::getForMap($userId, $organizationId);

        return new JsonResponse($response);
    }

    /**
     * @deprecated
     *
     * @OA\Get(
     *     path="/apigs/stations/data-history",
     *     summary="History data from meteo station",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     *
     * @return array
     */
    public function getDataHistory()
    {
        $validator = Validator::make(Request::all(), [
            'station_id' => 'required|integer',
            'period' => ['sometimes', 'max:255', 'in:7days,14days,30days,6months,1year'],
            'feed' => ['sometimes', 'string', 'in:raw,hourly,daily'],
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationId = Request::get('station_id');
        $period = Request::get('period') ?: '1week';
        $feed = Request::get('feed') ?: 'daily';

        $userStation = UserStation::where('id', $stationId)->first();

        if (!$userStation) {
            return Response::json(['error' => 'User station not found.'], 500);
        }

        /** @var IStation $stationObject */
        $stationObject = $userStation->getStationApi();

        // update last
        $stationInfo = $stationObject->getStationData();

        if (is_array($stationInfo) && isset($stationInfo['error'])) {
            return Response::json(['error' => $stationInfo['error']], $stationInfo['http_code']);
        }

        if (!is_array($stationInfo) || !isset($stationInfo['dates']) || !isset($stationInfo['position'])) {
            return Response::json(['error' => 'Station not found.'], 500);
        }

        $lastCommunication = $stationInfo['dates']['last_communication'];

        $timeZone = config('app.timezone');
        if (isset($stationInfo['position']['timezoneCode'])) {
            $timeZone = $stationInfo['position']['timezoneCode'];
        }

        $lastCommunication = (new Carbon($lastCommunication, $timeZone))->getTimestamp() * 1000;

        $heap = $this->heap;

        $now = new DateTime();
        $periodStart = clone $now;
        $periodStart->modify('-' . $period);
        $stationInstallDate = DateTime::createFromFormat('Y-m-d', $userStation->install_date);

        if ($stationInstallDate > $periodStart) {
            $periodStart = $stationInstallDate;
        }

        $historyPeriod = mktime(
            0,
            0,
            0,
            $periodStart->format('m'),
            $periodStart->format('d'),
            $periodStart->format('Y')
        );

        $cacheMinutes = now()->addMinutes(15);
        $chart_data = $stationObject->getHistoryDataFrom(
            $cacheMinutes,
            $historyPeriod,
            $feed,
            function () {}
        );

        if (is_array($chart_data) && isset($chart_data['error'])) {
            return Response::json(['error' => $chart_data['error']], $chart_data['http_code']);
        }

        if (!is_array($chart_data)) {
            return Response::json(['error' => 'Station not found.'], 500);
        }

        return ['chart_data' => $chart_data, 'last_communication' => $lastCommunication];
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/data",
     *     summary="History data from meteo station",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     *
     * @return false|string
     */
    public function getData()
    {
        $result = $this->getStationDataHistory();

        if (is_object($result)) {
            return $result;
        }

        return $this->meteoService->formatSimple($result);
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/data-history-charts",
     *     summary="History data from meteo station formatted for ECharts",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function getDataHistoryEChartsFormatted()
    {
        try {
            $result = $this->getStationDataHistory();
        } catch (Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], $e->getCode());
        }

        if (!count($result)) {
            return new JsonResponse();
        }

        return EChartsFormatter::format($result);
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/last-data",
     *     summary="Last data from meteo station",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     *
     * @return array
     */
    public function getLastData()
    {
        $validator = Validator::make(Request::all(), [
            'station_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationId = Request::get('station_id');

        $userStation = UserStation::where('id', $stationId)->first();

        if (!$userStation) {
            return Response::json(['error' => 'User station not found.'], 500);
        }

        $stationObject = $userStation->getStationApi();

        $stationName = $userStation->name;
        $stationType = $userStation->type;

        // update last communication
        $stationInfo = $stationObject->getStationData();

        if (is_array($stationInfo) && isset($stationInfo['error'])) {
            return Response::json(['error' => $stationInfo['error']], $stationInfo['http_code']);
        }

        if (!is_array($stationInfo) || !isset($stationInfo['dates']) || !isset($stationInfo['position'])) {
            return Response::json(['error' => 'Station not found.'], 500);
        }

        $heap = $this->heap;

        $cacheMinutes = now()->addMinutes(5);

        $station_data = $stationObject->getCurrentSensorValues(
            $cacheMinutes,
            function () use ($stationName, $heap, $stationType) {
                $heap->track('Current Data - ' . $stationType, [
                    'Station' => $stationName,
                    'Type' => 'Current',
                ]);
            }
        );

        $stationDaily = $stationObject->getDailySensorValues($cacheMinutes);

        $sensors_daily_data = $stationDaily ? $stationDaily['sensors'] : [];
        $sensors_daily_start_from = $stationDaily ? $stationDaily['start_from_ts'] : null;

        $timeZone = config('app.timezone');
        if (isset($stationInfo['position']['timezoneCode'])) {
            $timeZone = $stationInfo['position']['timezoneCode'];
        }

        $lastCommunicationTs = (new Carbon($stationInfo['dates']['last_communication'], $timeZone))->getTimestamp() * 1000;

        return [
            'sensors' => $station_data,
            'sensors_daily' => $sensors_daily_data,
            'sensors_daily_start_from' => $sensors_daily_start_from,
            'last_communication' => $lastCommunicationTs,
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/hourly-data-history",
     *     summary="History data by hour from meteo station",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     *
     * @return array|mixed
     */
    public function getHourlyDataHistory()
    {
        $validator = Validator::make(Request::all(), [
            'station_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationName = Request::get('station_name');
        $userStation = UserStation::where('name', $stationName)->first();

        if (!$userStation) {
            return Response::json(['error' => 'User station not found.'], 500);
        }

        $stationObject = $userStation->getStationApi();

        $station_data = $stationObject->getStationData();

        if (is_array($station_data) && isset($station_data['error'])) {
            return Response::json(['error' => $station_data['error']], $station_data['http_code']);
        }

        if (!is_array($station_data) || !isset($station_data['dates']) || !isset($station_data['position'])) {
            return Response::json(['error' => 'Station not found.'], 500);
        }

        $lastCommunication = $station_data['dates']['last_communication'];

        $cacheMinutes = now()->addHour();

        $last_data = $stationObject->historyDataHourlyCommon($stationName, 1, $cacheMinutes, function () {});

        if (is_array($last_data) && isset($last_data['error'])) {
            return Response::json(['error' => $last_data['error']], $last_data['http_code']);
        }

        if (!is_array($last_data)) {
            return Response::json(['error' => 'Station not found.'], 500);
        }
        $last_data = array_shift($last_data);

        $last_data['last_communication'] = $lastCommunication;

        return $last_data;
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/report-list",
     *     summary="Get Stations Report List",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function getStationsReportList()
    {
        $validator = Validator::make(Request::all(), [
            'from' => 'required|string',
            'to' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $from = Request::get('from');
        $to = Request::get('to');

        return $this->stationService->getStationsReportData($from, $to);
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/download-report-list",
     *     summary="Download Report List",
     *
     *     @OA\Response(
     *         response="200",
     *         description="File"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadReportList()
    {
        $validator = Validator::make(Request::all(), [
            'from' => 'required|int',
            'to' => 'required|int',
            'format' => 'string',
            'includeRowChildren' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $from = Request::get('from');
        $to = Request::get('to');
        $format = Request::get('format');
        $includeRowChildren = 'true' === Request::get('includeRowChildren') ? true : false;
        $reportFile = $this->reportService->generateReport($format, $from, $to, $includeRowChildren);
        $reportFileFullPath = Storage::disk('qnap_storage')->path($reportFile);

        $customFileName = 'weather_stations_report_' . date('d_m_Y', $from) . '-' . date('d_m_Y', $to) . '.' . $format;

        return response()
            ->download(
                $reportFileFullPath,
                $customFileName
            )
            ->deleteFileAfterSend();
    }

    private function validateStationRequest()
    {
        $stationId = Request::get('station_id');
        $feed = Request::get('feed') ?: 'daily';
        $period = Request::get('period') ?: '1week';
        $sensors = json_decode(Request::get('sensors'), true);
        $sensors = $sensors ? $sensors : [];

        $data = [
            'station_id' => $stationId,
            'feed' => $feed,
            'period' => $period,
            'sensors' => $sensors,
        ];

        $validator = Validator::make($data, [
            'station_id' => 'required|integer',
            'sensors' => [
                'required',
                'array',
            ],
            'sensors.*' => [
                'required',
                'max:255',
                'in:all,air_temperature_min,air_temperature_max,air_temperature_avg,
                soil_temperature_min,soil_temperature_max,soil_temperature_avg,
                air_pressure_min,air_pressure_max,air_pressure_avg,
                air_humidity_min,air_humidity_max,air_humidity_avg,precipitation_sum,precipitation_cumulative_sum,wind_speed_max,wind_speed_avg,battery_last,radiation_avg,panel_voltage_last',
            ],
            'period' => ['sometimes', 'max:255', 'in:7days,14days,30days,6months,1year,custom'],
            'feed' => ['sometimes', 'string', 'in:raw,hourly,daily'],
            'from_date' => 'date_format:Y-m-d',
            'to_date' => 'date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
    }

    private function getStationDataHistory()
    {
        $this->validateStationRequest();
        $stationId = Request::get('station_id');
        $feed = Request::get('feed') ?: 'daily';
        $period = Request::get('period') ?: '1week';
        $sensors = json_decode(Request::get('sensors'), true);
        $sensors = $sensors ? $sensors : [];
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');

        $stationObj = UserStation::where('id', $stationId)->first()->getStationApi();

        return $stationObj->getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate);
    }
}
