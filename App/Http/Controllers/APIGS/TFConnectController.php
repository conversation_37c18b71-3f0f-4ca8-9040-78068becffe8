<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Models\OrganizationDevice;
use Auth;
use OpenApi\Annotations as OA;

class TFConnectController extends BaseController
{
    private $orgDevices;

    public function __construct(OrganizationDevice $orgDevices)
    {
        $this->orgDevices = $orgDevices;
    }

    /**
     * @OA\Get(
     *     path="/apigs/tfc/devices",
     *     summary="List TFC devices for last chosen organization of current logged in user",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     * Returns All the devices for specific organization.
     *
     * @return list of devices serial numbers
     */
    public function getDevices()
    {
        $user = Auth::user();
        $organization = $user->lastChosenOrganization;

        return $organization->devices()->get();
    }
}
