<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ForbiddenException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Plot;
use App\Models\PlotNote;
use App\Models\User;
use Auth;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class PlotsNotesController extends BaseController
{
    public function __construct()
    {
        $this->middleware('verify-write-rights', ['except' => ['getList']]);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots-notes/list",
     *     summary="List of plot notes",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ForbiddenException
     * @throws ValidationException
     *
     * @return array
     */
    public function getList()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'note' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->checkRights();

        $resultQuery = PlotNote::select('su_satellite_plots_notes.id', 'su_satellite_plots_notes.plot_id', 'su_satellite_plots_notes.note', 'su_satellite_plots_notes.created_at')
            ->join('su_satellite_plots', 'su_satellite_plots.gid', '=', 'su_satellite_plots_notes.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_plots_notes.plot_id', Request::get('gid'))
            ->orderBy('su_satellite_plots_notes.id', 'desc');

        // Filter
        if (Request::get('note')) {
            $resultQuery->where('note', 'ILIKE', trim('%' . Request::get('note') . '%'));
        }

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items(),
        ];
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots-notes/create-note",
     *     summary="Create plot note",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postCreateNote()
    {
        $validator = Validator::make(Request::all(), [
            'note' => 'required|string',
            'gid' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->checkRights();

        $plotNote = new PlotNote();
        $plotNote->plot_id = Request::get('gid');
        $plotNote->note = Request::get('note');
        $result = $plotNote->save();

        if (!$result) {
            return Response::json(['error' => 'Insert faild'], 500);
        }

        return Response::json(['Response' => 'Success', 'noteId' => $plotNote->id]);
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots-notes/delete-note",
     *     summary="Delete plot note",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postDeleteNote()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'gid' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->checkRights();

        $plotNote = PlotNote::find((int)Request::get('id'));

        if ($plotNote) {
            $plotNote->delete();

            return Response::json(['Response' => 'Success']);
        }

        return Response::json(['Response' => 'No note deleted.']);
    }

    /**
     * checkRights Check Rights.
     */
    private function checkRights()
    {
        /**
         * @var Plot $plot
         */
        $plot = Plot::find(Request::get('gid'));

        /**
         * @var User $user
         */
        $user = Auth::user();

        if (!$user->farms()->get()->contains($plot->farm()->get()->first())) {
            throw new ForbiddenException();
        }
    }
}
