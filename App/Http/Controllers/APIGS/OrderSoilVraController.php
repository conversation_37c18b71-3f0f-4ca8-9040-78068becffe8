<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\TFConnect;
use App\Exceptions\ForbiddenException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderSoilVra;
use App\Services\Order\OrderVraService;
use Auth;
use Exception;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class OrderSoilVraController extends BaseController
{
    private $tfc;
    private $orderVraService;

    public function __construct(TFConnect $tfc, OrderVraService $orderVraService)
    {
        $this->tfc = $tfc;
        $this->orderVraService = $orderVraService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/order-soil-vra/orders",
     *     summary="Get soil VRA orders",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getOrders()
    {
        $validator = Validator::make(Request::all(), [
            'plotId' => 'required|integer',
            'farmYear' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new OrderSoilVra();
        $plotId = Request::get('plotId');
        $farmYear = Request::get('farmYear');

        try {
            $order = $model->getByPlot((int)$plotId, (int)$farmYear);
        } catch (Exception $e) {
            throw $e;
        }

        return isset($order[0]) ? $order : [];
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-soil-vra/create-order",
     *     summary="Create soil VRA order",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postCreateOrder()
    {
        $validator = Validator::make(Request::all(), [
            'plot_id' => 'integer|required',
            'order' => 'array|required',
            'class_number' => 'integer|required',
            'layer_id' => 'integer|required',
            'flat_rate' => 'numeric|required',
            'flat_rate_total' => 'numeric|required',
            'variable_rate_total' => 'numeric|required',
            'difference' => 'numeric|required',
            'difference_percent' => 'numeric|required',
            'data' => 'array|required',
            'vector_data' => 'array|required',
            'product_percent' => 'numeric|required',
            'name' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if (!Auth::user()->globalUser()->can('create_vra_soil_maps')) {
            throw new ForbiddenException();
        }

        $model = new Order();

        try {
            $id = $model->createVraOrder(Request::all());
        } catch (Exception $e) {
            throw $e;
        }

        return Response::json(['result' => 'order created', 'id' => $id], 200);
    }
}
