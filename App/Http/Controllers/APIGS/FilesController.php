<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\LoadData\ProcessBoundariesClass;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\File;
use App\Models\Layer;
use App\Rules\ValidateBoundaryUploadedFile;
use App\Services\FarmingYear\FarmingYearService;
use App\Services\Plot\PlotsBoundariesTmpTableService;
use Auth;
use Config;
use DB;
use Exception;
use Illuminate\Http\JsonResponse;
use Plupload;
use Request;
use Response;
use Validator;

class FilesController extends BaseController
{
    public function __construct()
    {
        $this->middleware('verify-write-rights', ['only' => ['postUpload']]);
    }

    /**
     * @OA\Get(
     *     path="/apigs/files",
     *     summary="Get files list",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getIndex()
    {
        $files = Auth::user()
            ->files()
            ->where('shape_type', Layer::LAYER_TYPE_SATELLITE_WORK)
            ->restOrderBy(Request::get('sort'))
            ->paginate(Request::get('limit'));

        return Response::json([
            'rows' => $files->items(),
            'total' => $files->total(),
        ]);
    }

    /**
     * @OA\Post(
     *     path="/apigs/files/upload",
     *     summary="Upload file",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postUpload(FarmingYearService $farmingYearService)
    {
        $validator = Validator::make(Request::all(), [
            'file' => [new ValidateBoundaryUploadedFile(request()->file('file'))],
            'type' => ['required', 'string', 'in:shp,kml,kmz'],
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $user = Auth::user();
        $fileType = Request::get('type');

        return Plupload::receive('file', function ($uploadedFile) use ($user, $fileType, $farmingYearService) {
            $file = new File();
            $file->name = basename(
                $uploadedFile->getClientOriginalName(),
                '.' . $uploadedFile->getClientOriginalExtension()
            );
            $file->user_id = $user->id;
            $file->filename = $user->id . '_' . $uploadedFile->getClientOriginalName();
            $file->crs = (int)Request::input('srs', config('globals.DEFAULT_DB_CRS'));
            $file->shape_type = Layer::LAYER_TYPE_SATELLITE_WORK;
            $file->device_type = (int)Request::input('device');
            $file->group_id = $user->group_id;
            $file->path = config('globals.LAYERS_QUEUE_PATH') . $user->id . '_' . $uploadedFile->getClientOriginalName();

            $uploadedFile->move(config('globals.LAYERS_QUEUE_PATH'), $file->filename);

            $file->save();

            $processor = new ProcessBoundariesClass($file, $farmingYearService, $fileType);
            $processor->startProcessing();

            return $file->id;
        });
    }

    /**
     * @OA\Get(
     *     path="/apigs/files/definition",
     *     summary="Get file definition",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getDefinition()
    {
        // $connection = DB::connection('userdb');

        $validator = Validator::make(Request::all(), [
            'fileId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $defaultdb = Config::get('database.default');

        $resultColumns = DB::select("SELECT column_name FROM information_schema.columns 
            where table_catalog = '" . Config::get('database.connections.' . $defaultdb . '.database') . "'
            and table_schema = 'public' 
            and table_name = 'tmp_satellite_" . Request::get('fileId') . "'
            and column_name NOT IN('gid', 'geom')");

        return array_map(function ($column) {
            return ['column' => $column->column_name];
        }, $resultColumns);
    }

    /**
     * @OA\Get(
     *     path="/apigs/files/tmp/plots-boundaries",
     *     summary="Get count of plots boundarie",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getPlotsBoundariesInfo()
    {
        $validator = Validator::make(Request::all(), [
            'fileId' => 'required|integer',
            'cropDefinition' => 'sometimes|required|string',
            'nameDefinition' => 'sometimes|required|string',
            'organizationId' => 'required|int',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $fileId = Request::get('fileId');
        $cropDefinition = Request::get('cropDefinition', null);
        $nameDefinition = Request::get('nameDefinition', null);
        $organizationId = Request::get('organizationId');

        return PlotsBoundariesTmpTableService::getPlotsBoundariesInfo($fileId, $organizationId, $nameDefinition, $cropDefinition);
    }

    /**
     * @OA\Delete(
     *     path="/apigs/files/tmp/plots-boundaries",
     *     summary="Delete plots boundaries",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function deletePlotsBoundaries()
    {
        $validator = Validator::make(Request::all(), [
            'fileId' => 'required|integer',
            'plotIds' => 'required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $fileId = Request::get('fileId');
        $plotIds = Request::get('plotIds');

        try {
            PlotsBoundariesTmpTableService::deletePlotsBoundariesFromTmpTable($fileId, $plotIds);
        } catch (Exception $e) {
            return new JsonResponse('This temp table with code ' . $fileId . ' not found!', 404);
        }

        return new JsonResponse('Success!', 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/files/save-definition",
     *     summary="Save definition for file",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postSaveDefinition(FarmingYearService $farmingYearService)
    {
        $validator = Validator::make(Request::all(), [
            'fileId' => 'required|integer',
            'farmId' => 'required|integer',
            'cropDefinition' => 'string',
            'nameDefinition' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $fileModel = File::find(Request::get('fileId'));

        // Load with definition
        $arrayDefinition = [];
        if (strlen(Request::get('cropDefinition'))) {
            $arrayDefinition['crop'] = Request::get('cropDefinition');
        }

        if (strlen(Request::get('nameDefinition'))) {
            $arrayDefinition['name'] = Request::get('nameDefinition');
        }

        if (count($arrayDefinition)) {
            $fileModel->definition = serialize($arrayDefinition);
        }

        // Load without definition
        $fileModel->status = File::ERROR_WAITING_COPYING;
        $fileModel->farm_id = Request::get('farmId');
        $fileModel->save();
        $processor = new ProcessBoundariesClass($fileModel, $farmingYearService);
        $processor->startProcessing();

        return Response::json(['Response' => 'Success']);
    }
}
