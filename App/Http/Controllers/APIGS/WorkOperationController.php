<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Http\Requests\WorkOperation\WorkOperationsForInstanceRequest;
use App\Models\WorkOperation;
use App\Services\Implement\WorkOperationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class WorkOperationController extends BaseController
{
    private $workOperationService;

    public function __construct(WorkOperationService $workOperationService)
    {
        $this->workOperationService = $workOperationService;
    }

    public function index(): JsonResponse
    {
        $workOperations = WorkOperation::get();

        return new JsonResponse($workOperations, 200);
    }

    public function getWorkOperationsForInstance(WorkOperationsForInstanceRequest $request): JsonResponse
    {
        $requestData = $request->validated();
        $forInstance = Arr::get($requestData, 'for_instance');
        $filters = Arr::get($requestData, 'filters', []);

        if (0 === count($filters)) {
            return new JsonResponse(WorkOperation::get(), 200);
        }
        $workOperations = $this->workOperationService->getWorkOperationsNameFilteredData($forInstance, $filters);

        return new JsonResponse($workOperations, 200);
    }
}
