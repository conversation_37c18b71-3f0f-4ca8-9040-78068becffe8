<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 2/16/2021
 * Time: 8:41 AM.
 */

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\FarmTrackReportsLog\FarmTrackReportsLogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FarmTrackReportsLogController extends BaseController
{
    private $farmTrackReportLogService;

    public function __construct(FarmTrackReportsLogService $farmTrackReportLogService)
    {
        $this->farmTrackReportLogService = $farmTrackReportLogService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/farm-track-reports-log/organization/:organizationId",
     *     summary="Get farm track reports log by organization",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getFarmTrackReportsByOrganization(int $organizationId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_slug_short' => 'sometimes|required|string',
            'type' => 'sometimes|required|string',
            'report_names' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $packageSlugShort = $request->get('package_slug_short', null);
        $state = $request->get('state', 'processing');
        $reportNames = $request->get('report_names', ['machine_events', 'irrigation_per_day']);

        $response = $this->farmTrackReportLogService->getFarmTrackReportsByOrganization($organizationId, $packageSlugShort, $state, $reportNames);

        return new JsonResponse($response, 200);
    }
}
