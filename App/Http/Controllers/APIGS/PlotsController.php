<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\CMS\ContractService;
use App\Classes\Echarts\EChartsIndexDataFormatter;
use App\Classes\Meteo\MeteoBlue;
use App\Classes\Plot3D;
use App\Exceptions\ForbiddenException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Plot\PlotsMachineTasksRequest;
use App\Models\Farm;
use App\Models\LayerPlot;
use App\Models\Pin;
use App\Models\Plot;
use App\Models\PlotCrop;
use App\Models\PlotFile;
use App\Rules\VRAElements;
use App\Services\Crop\CropService;
use App\Services\FarmingYear\FarmingYearService;
use App\Services\Plot\PlotService;
use DateTime;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class PlotsController extends BaseController
{
    private $meteo;
    private $contractService;
    private $plotService;
    private $cropService;

    public function __construct(MeteoBlue $meteo, Plot3D $plot3d, ContractService $contractService, PlotService $plotService, CropService $cropService)
    {
        $this->middleware('verify-write-rights', ['only' => ['postUpdate']]);
        $this->meteo = $meteo;
        $this->plot3d = $plot3d;
        $this->contractService = $contractService;
        $this->plotService = $plotService;
        $this->cropService = $cropService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots",
     *     summary="List of plots",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getPlots()
    {
        $validator = Validator::make(Request::all(), [
            'gids' => 'required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $user = Auth::user();

        return Plot::getInfoByGids(json_decode(Request::get('gids')), $user)->get();
    }

    /**
     * @deprecated
     *
     * @OA\Get(
     *     path="/apigs/plots/index/:gid",
     *     summary="List of plots",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getIndex($gid)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        return Plot::select(
            'su_satellite_plots.gid',
            'su_satellite_plots.name',
            'f.name as farm',
            'su_satellite_plots.upload_date',
            'su_satellite_plots.meteo_location_id',
            DB::raw('CASE WHEN usp.custom_name IS NOT NULL THEN usp.custom_name ELSE usp.name END as station_name'),
            'su_satellite_plots.station_id',
            DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area"),
            DB::raw('ST_AsText(ST_Transform(su_satellite_plots.geom, 3857)) AS geom_as_text')
        )
            ->leftJoin('su_users_stations AS usp', 'usp.id', '=', 'su_satellite_plots.station_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('su_satellite_plots.gid', $gid)
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->first();
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/detailed/:gid?",
     *     summary="Plots detailed",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     * Used in A2 project
     *
     * @param null $gid
     *
     * @throws ValidationException
     *
     * @return array
     *
     * @deprecated
     */
    public function getDetailed(FarmingYearService $farmingYearService, $gid = null)
    {
        $arrFarms = json_decode(Request::get('farms'), true);
        $arrFarms = $arrFarms ? $arrFarms : [];
        $data = [
            'farms' => $arrFarms,
        ];

        $validator = Validator::make($data, [
            'farms' => 'array',
            'farms.*' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $farmingYears = $farmingYearService->getFarmingYearsByOrganization(Auth::user()->lastChosenOrganization);
        $currentFarmYear = $farmingYears->first(function ($item, $key) {
            return true === $item['default'];
        });

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $cropName = Config::get('globals.CROP_NAME');
        $cropId = Request::get('crop_id');

        $q = Plot::select(
            'su_satellite_plots.gid',
            'su_satellite_plots.area AS dka_area',
            DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area"),
            'su_satellite_plots.name',
            'su_satellite_plots.farm_id',
            'su_satellite_plots.upload_date',
            'su_satellite_plots.last_marker_date',
            DB::raw('MAX(slp.date) AS last_image_date'),
            DB::raw('MAX(slp.date_time) AS last_image_date_time'),
            DB::raw('(array_agg(slp.stats ORDER BY slp.date DESC) FILTER (WHERE slp.mean IS NOT NULL))[1] AS last_stats'),
            DB::raw('(array_agg(slp.mean ORDER BY slp.date DESC) FILTER (WHERE slp.mean IS NOT NULL))[1] AS last_mean'),
            DB::raw('MAX(slps.date) AS last_soil_date'),
            DB::raw('(array_agg(c.' . $cropName . ' ORDER BY spc.year DESC))[1] AS crop'),
            DB::raw('(array_agg(spc.crop_id ORDER BY spc.year DESC))[1] AS crop_id'),
            DB::raw('(not count(so.id)=0) as has_order'),
            DB::raw('MAX(so.year) AS last_data_year')
        )
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders AS so', function ($join) {
                $join->on('so.id', '=', 'sopr.order_id')
                    ->where('so.status', '<>', 'canceled');
            })
            ->leftJoin('su_satellite_layers_plots AS slp', function ($join) {
                $join->on('slp.sopr_id', '=', 'sopr.id')
                    ->where('so.status', '=', 'processed')
                    ->where('slp.type', '=', 'index');
            })
            ->leftJoin('su_satellite_layers_plots AS slps', function ($join) {
                $join->on('slps.sopr_id', '=', 'su_satellite_plots.gid')
                    ->where('sopr.soil_sample_status', '=', 'processed')
                    ->where('slps.type', '=', 'soil');
            })
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($currentFarmYear) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('spc.is_primary', '=', true);

                if ($currentFarmYear && $currentFarmYear['year']) {
                    $join->where('spc.year', '>=', $currentFarmYear['year']);
                }
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->groupBy('gid');

        if ($gid) {
            $q->where('su_satellite_plots.gid', $gid);
        }
        if (Request::get('name') && '' !== trim(Request::get('name'))) {
            $q->where('su_satellite_plots.name', 'ILIKE', '%' . trim(Request::get('name')) . '%');
        }
        if ($arrFarms) {
            $q->whereIn('su_satellite_plots.farm_id', $arrFarms);
        }
        if ($cropId) {
            $q->where('spc.crop_id', Request::get('crop_id'));
        }
        if ('0' === $cropId) {
            $q->whereNull('spc.crop_id');
        }
        if (Request::get('from_date')) {
            $q->where('so.from_date', '>=', Request::get('from_date'));
        }
        if (Request::get('to_date')) {
            $q->where('so.to_date', '<=', Request::get('to_date'));
        }
        if (Request::get('area_from')) {
            $q->where(
                DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3)"),
                '>=',
                Request::get('area_from')
            );
        }
        if (Request::get('area_to')) {
            $q->where(
                DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3)"),
                '<=',
                Request::get('area_to')
            );
        }
        if (Request::get('order_type')) {
            $q->whereIn('so.type', json_decode(Request::get('order_type')));
        }
        if (Request::get('mean_from')) {
            $q->having(DB::raw('(array_agg(slp.mean ORDER BY slp.date DESC))[1]'), '>=', Request::get('mean_from'));
        }
        if (Request::get('mean_to')) {
            $q->having(DB::raw('(array_agg(slp.mean ORDER BY slp.date DESC))[1]'), '<=', Request::get('mean_to'));
        }
        if (Request::get('from_date_loaded')) {
            $q->where(DB::raw('su_satellite_plots.upload_date::date'), '>=', Request::get('from_date_loaded'));
        }
        if (Request::get('to_date_loaded')) {
            $q->where(DB::raw('su_satellite_plots.upload_date::date'), '<=', Request::get('to_date_loaded'));
        }
        if (Request::get('sort')) {
            $q->restOrderBy(Request::get('sort'), 'last');
        }

        $opResult = $q->paginate(Request::get('limit'), ['*']);

        $opResultCollection = $opResult->getCollection();

        $opResultCollection->each(function ($plot) {
            if ($plot->last_image_date) {
                $plot->last_image_crop_id = PlotCrop::where('plot_id', $plot->gid)
                    ->where('from_date', '<=', $plot->last_image_date)
                    ->where('to_date', '>=', $plot->last_image_date)
                    ->where('is_primary', true)
                    ->value('crop_id');
            }

            $plot->web_path = PlotFile::where('plot_id', $plot->gid)->where('type', 'plot')->value('web_path');
        });

        $gids = $opResultCollection->pluck('gid');
        $cropIds = collect($opResultCollection->pluck('crop_id'))->unique()->values();

        $farmingYearDates = [[$currentFarmYear['from_date'], $currentFarmYear['to_date']]];
        $plotsMean = $this->getPlotsMean($gids, $farmingYearDates);
        $plotsAvgData = $this->plotService->getPlotsAvgData($cropIds, $farmingYearDates);

        $lastImageCropIds = collect($opResultCollection->pluck('last_image_crop_id'))->unique()->values();
        $lastImageDates = collect($opResultCollection->pluck('last_image_date'))->unique()->values();

        $lastImageFarmYearDates = [[$lastImageDates->min(), $lastImageDates->max()]];
        $lastImagePlotsAvgData = $this->plotService->getPlotsAvgData($lastImageCropIds, $lastImageFarmYearDates);

        $meteo = $this->meteo;

        $opResultCollection->each(function ($plot) use (
            $currentFarmYear,
            $lastImagePlotsAvgData,
            $plotsMean,
            $plotsAvgData,
            $meteo
        ) {
            $plot->farm_year = $currentFarmYear;
            $plotArea = $plot->dka_area;
            $plot->last_stats = collect(json_decode($plot->last_stats));
            $plot->last_stats->transform(function ($item) use ($plotArea) {
                return round($item / $plotArea, 4, PHP_ROUND_HALF_EVEN);
            });
            $plot->show_warning = false;
            $plot->last_mean_diff = null;

            if (isset($plotsMean[$plot->gid])) {
                $plotAvgDataArr = [];
                if (isset($plotsAvgData[$plot->crop_id])) {
                    $plotAvgDataArr = $plotsAvgData[$plot->crop_id];
                }
                $plot->chartData = $this->getMeanChartData($plotsMean[$plot->gid], $plotAvgDataArr);
            }

            if (isset($lastImagePlotsAvgData[$plot->last_image_crop_id], $lastImagePlotsAvgData[$plot->last_image_crop_id][$plot->last_image_date])) {
                $plotsLastAvgData = $lastImagePlotsAvgData[$plot->last_image_crop_id][$plot->last_image_date];

                if ($plotsLastAvgData) {
                    $lastStatsArr = $plot->last_stats->toArray();
                    $plot->show_warning = $this->getShowWarning($lastStatsArr, $plotsLastAvgData);

                    $plot->last_mean_diff = $plot->last_mean - $plotsLastAvgData['mean'];
                }
            }

            if ($plot->last_image_date || $plot->last_soil_date) {
                // Get Meteo Data
                if (Auth::user()->globalUser()->can('use_meteo')) {
                    $resultMeteo = $meteo->getCurrentMeteo($plot->gid);

                    if (isset($resultMeteo) && count($resultMeteo)) {
                        $temp = round($resultMeteo['temperature']);
                        if (-0 == $temp) {
                            $temp = abs($temp);
                        }

                        $plot->pictocode = $resultMeteo['pictocode'];
                        $plot->temperature = $temp;
                        $plot->isdaylight = $resultMeteo['isdaylight'] ? 'day' : 'night';
                    }
                }
            }
        });

        return [
            'total' => $opResult->total(),
            'rows' => $opResult->items(),
        ];
    }

    /**
     * @throws ValidationException
     *
     * @deprecated
     *
     * @OA\Get(
     *     path="/apigs/plots/info/:gid",
     *     summary="Plot info",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getInfo($gid)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'gid' => $gid,
        ]), [
            'gid' => 'required|integer',
            'from_date' => 'date_format:Y-m-d',
            'to_date' => 'date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $crop_name = Config::get('globals.CROP_NAME');

        $year = date('Y', strtotime(Request::get('to_date')));

        $plot = Plot::select([
            'su_satellite_plots.gid',
            'su_satellite_plots.name',
            'su_satellite_plots.meteo_location_id',
            DB::raw('(array_agg(spf.web_path))[1] AS web_path'),
            DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area"),
            DB::raw('MAX(slp.date_time) AS last_image_date'),
            DB::raw('(array_agg(slp.mean ORDER BY slp.date DESC))[1] AS last_mean'),
            DB::raw('(array_agg(slp.stats ORDER BY slp.date DESC))[1] AS last_stats'),
            DB::raw('ST_AsText(ST_Transform(geom, 3857)) AS geom'),
            DB::raw('max(DISTINCT c.' . $crop_name . ') as crop'),
            DB::raw('COALESCE(COUNT(DISTINCT slps. ID), 0) as count_soil_map'),
            DB::raw('COALESCE(COUNT(DISTINCT (slp.date::TEXT || \'_\' || slp.plot_id::TEXT) ORDER BY (slp.date::TEXT || \'_\' || slp.plot_id::TEXT)), 0) as count_index_images'),
            DB::raw('COALESCE(count(DISTINCT pin.id), 0) as count_pins'),
        ])
            ->join('su_satellite_plots_files as spf', 'spf.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders AS so', function ($join) {
                $join->on('so.id', '=', 'sopr.order_id')
                    ->where('so.status', '<>', 'canceled');
            })
            ->leftJoin('su_satellite_layers_plots AS slp', function ($join) {
                $join->on('slp.plot_id', '=', 'su_satellite_plots.gid')
                    ->on('slp.order_id', '=', 'so.id')
                    ->where('so.status', '=', 'processed')
                    ->where('slp.type', '=', 'index')
                    ->where(DB::raw('slp.date::DATE'), '>=', Request::get('from_date'))
                    ->where(DB::raw('slp.date::DATE'), '<=', Request::get('to_date'));
            })
            ->leftJoin('su_satellite_layers_plots AS slps', function ($join) {
                $join->on('slps.plot_id', '=', 'su_satellite_plots.gid')
                    ->on('slps.order_id', '=', 'so.id')
                    ->where('sopr.soil_sample_status', '=', 'processed')
                    ->where('slps.type', '=', 'soil')
                    ->where('slps.stats_type', '=', 'summarized')
                    ->where(DB::raw('slps.date::DATE'), '>=', Request::get('from_date'))
                    ->where(DB::raw('slps.date::DATE'), '<=', Request::get('to_date'));
            })
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($year) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('spc.is_primary', '=', true)
                    ->where('spc.year', '=', $year);
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->leftJoin('su_users_pins AS pin', function ($join) {
                $join->where('pin.group_id', '=', Auth::user()->group_id)
                    ->where(
                        DB::raw('ST_Contains(su_satellite_plots.geom, ST_Transform(ST_SetSRID(ST_MakePoint(pin.lon, pin.lat), 3857), ' . Config::get('globals.DEFAULT_DB_CRS') . '))'),
                        '=',
                        true
                    )
                    ->where('pin.isDeleted', '=', false)
                    ->where(DB::raw('pin.date::DATE'), '>=', Request::get('from_date'))
                    ->where(DB::raw('pin.date::DATE'), '<=', Request::get('to_date'));
            })
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('su_satellite_plots.gid', $gid)
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('spf.type', 'cover')
            ->groupBy('gid')
            ->first();

        if (!$plot) {
            return;
        }

        return $plot;
    }

    /**
     * @OA\Put(
     *     path="/apigs/plots/update/:gid",
     *     summary="Update Plot",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function putUpdate($gid)
    {
        $validator = Validator::make([
            'gid' => $gid,
            'name' => Request::get('name'),
            'farm_id' => Request::get('farm_id'),
        ], [
            'gid' => 'required|integer',
            'name' => 'nullable|string',
            'farm_id' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotModel = Plot::find($gid);
        if (!$plotModel) {
            return;
        }

        if (null !== Request::get('name')) {
            $plotModel->name = Request::get('name');
        }
        if (null !== Request::get('farm')) {
            $farm = Farm::findOrFail(Request::get('farm_id'));
            $plotModel->farm()->associate($farm);
        }
        $plotModel->save();
    }

    /**
     * @param null $pinId
     *
     * @throws ValidationException
     *
     * @return array
     *
     * @deprecated
     *
     * @OA\Get(
     *     path="/apigs/plots/:plotId/pins",
     *     summary="Get pins for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getPins($plotId, $pinId = null)
    {
        $validator = Validator::make([
            'plotId' => $plotId,
            'pinId' => $pinId,
        ], [
            'plotId' => 'required|integer',
            'pinId' => 'integer|nullable',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $pQuery = Pin::join('su_satellite_plots as sp', function ($join) use ($plotId) {
            $join->where('sp.gid', '=', $plotId)
                ->where(
                    DB::raw('ST_Contains(sp.geom, ST_Transform(ST_SetSRID(ST_MakePoint(su_users_pins.lon, su_users_pins.lat), 3857), ' . Config::get('globals.DEFAULT_DB_CRS') . '))'),
                    '=',
                    true
                );
        })
            ->where('group_id', Auth::user()->group_id)
            ->where('isDeleted', false)
            ->restOrderBy(Request::get('sort'));

        // Filter
        if ($pinId) {
            $pQuery->where('id', trim($pinId));
        }
        if (Request::get('q')) {
            $pQuery->where(
                DB::raw("title || ' ' || COALESCE(comment, '')"),
                'ILIKE',
                trim('%' . Request::get('q') . '%')
            );
        }
        if (Request::get('from_date')) {
            $pQuery->where(DB::raw('date::date'), '>=', Request::get('from_date'));
        }
        if (Request::get('to_date')) {
            $pQuery->where(DB::raw('date::date'), '<=', Request::get('to_date'));
        }

        $pins = $pQuery->paginate(Request::get('limit'), [
            'id',
            'title',
            DB::raw("COALESCE(comment, '') AS comment"),
            'date',
            'images',
            'lon',
            'lat',
            DB::raw("to_char(date, 'DD.MM.YYYY HH24:MI:SS') as date_formated"),
        ]);

        return [
            'total' => $pins->total(),
            'rows' => $pins->items(),
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/history",
     *     summary="Get history for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function getHistory($plotId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
        ]), [
            'plotId' => 'integer|required',
            'type' => 'string',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
            'sort' => 'string',
            'sort_direction' => 'string|in:asc,desc,ASC,DESC',
            'organization_id' => 'sometimes|integer',
            'element' => 'sometimes|required|string',
            'max_cloud_percentage' => 'int',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $type = Request::get('type');
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');
        $sort = Request::get('sort', 'date');
        $sortDirection = Request::get('sort_direction', 'DESC');
        $organizationId = (int)Request::get('organization_id', Auth::user()->lastChosenOrganization->id);
        $element = Request::get('element');
        $maxCloudPercentage = Request::get('max_cloud_percentage');

        return $this->plotService->getHistory($plotId, $type, $organizationId, $sort, $sortDirection, $fromDate, $toDate, $element, true, $maxCloudPercentage);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/element-default-classes",
     *     summary="Get default classes for elements",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getElementsDefaultClasses()
    {
        return Config::get('globals.ELEMENT_CLASSES');
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/element-dynamic-classes",
     *     summary="Get stats for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getElementDynamicClasses($plotId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
        ]), [
            'plotId' => 'integer|required',
            'classes' => 'required',
            'date' => 'date_format:Y-m-d|required',
            'compound' => ['required', 'max:255', new VRAElements()],
            'type' => ['required', 'max:255', 'in:satellite,soil'],
            'sampling_type_id' => 'required_if:type,soil|integer',
            'satellite_type' => 'sometimes',
            'organization_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $classes = json_decode(Request::get('classes'), true);
        $date = Request::get('date');
        $compound = Request::get('compound') ? Request::get('compound') : '';
        $type = Request::get('type');
        $satelliteType = Request::get('satellite_type');
        $organizationId = (int)Request::get('organization_id', Auth::user()->lastChosenOrganization->id);
        $samplingTypeId = Request::get('sampling_type_id');
        $model = new Plot();

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        try {
            switch ($type) {
                case 'satellite':
                    $data = $model->getIndexDate(Auth::user(), $plotId, $classes, $date, $satelliteType, $organizationId);

                    break;
                case 'soil':
                    $data = $model->getSoilDate(Auth::user(), $plotId, $classes, $date, $compound, $samplingTypeId, $organizationId);

                    break;
            }

            $parsedData = [];
            foreach ($data[1] as $key => $value) {
                if (!str_contains($key, '|')) {
                    continue;
                }
                $newKey = strstr($key, '|', true);
                $parsedData[$newKey] = $value;
            }

            $returnData = [];
            $returnData[] = $data[0];
            foreach ($classes as $key => $value) {
                $area = 0;
                if (array_key_exists($key, $parsedData)) {
                    $area = $parsedData[$key];
                }

                $area = round($area * $areaCoef, 3);

                $returnData[] = [
                    $value[0] . '-' . $value[1],
                    (float)$area,
                    $value[3],
                    (float)($area) . ' ' . $areaLabel,
                ];
            }

            $returnData[] = [$data[1]['path']];
        } catch (Exception $e) {
            throw $e;
        }

        return $returnData;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/element-dynamic-classes-vectorized",
     *     summary="Get vectorized stats for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return false|string
     */
    public function getElementDynamicClassesVectorized(Plot $plot)
    {
        $validator = Validator::make(Request::all(), [
            'classes' => 'required',
            'rates' => 'required',
            'product' => 'nullable|numeric',
            'tiff_path' => 'required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $classes = json_decode(Request::get('classes'), true);
        $rates = json_decode(Request::get('rates'), true);
        $product = Request::get('product');
        $tiffPath = Request::get('tiff_path');

        return Plot::polygonizeVraMap($tiffPath, $classes, $rates, $product);
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots/update-viewed-plot",
     *     summary="Update is viewed status",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postUpdateViewedPlot()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'is_viewed' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        DB::table('su_satellite_layers_plots')
            ->join('su_satellite_plots AS p', 'p.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_layers_plots.id', Request::get('id'))
            ->update(['is_viewed' => Request::get('is_viewed')]);

        return Response::json(['Response' => 'Success']);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/mean-history",
     *     summary="Get mean history",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|Collection
     */
    public function getMeanHistory()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'crop_id' => 'integer',
            'hybrid_id' => 'integer',
            'farm_years' => 'required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $sort = 'date';
        if (Request::get('sort')) {
            $sort = Request::get('sort');
        }

        $farmYears = json_decode(Request::get('farm_years'));

        $plotsMean = $this->getPlotsMean([Request::get('gid')], $farmYears, $sort);
        $plotsAvgData = $this->plotService->getPlotsAvgData([Request::get('crop_id')], $farmYears, $sort, Request::get('hybrid_id'));

        $return = [];
        if (isset($plotsMean[Request::get('gid')])) {
            $plotAvgDataArr = [];
            if (isset($plotsAvgData[Request::get('crop_id')])) {
                $plotAvgDataArr = $plotsAvgData[Request::get('crop_id')];
            }

            $return = $this->getMeanChartData($plotsMean[Request::get('gid')], $plotAvgDataArr);
        }

        return $return;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/search",
     *     summary="Plots detailed",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getSearch()
    {
        $validator = Validator::make(Request::all(), [
            'name' => 'nullable|string',
            'year' => 'integer',
            'organization_id' => 'sometimes|required|integer',
            'limit' => 'sometimes|required|integer',
            'farm_ids' => 'sometimes|required|json',
        ]);
        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $year = Request::get('year');
        $name = Request::get('name');
        $limit = Request::get('limit', 10);
        $allOrganizations = filter_var(Request::get('all_organizations', false), FILTER_VALIDATE_BOOLEAN);
        $farmIds = json_decode(Request::get('farm_ids', '[]'));

        $organizationId = null;
        if (!$allOrganizations) {
            $organizationId = Request::get('organization_id', Auth::user()->lastChosenOrganization->id);
        }

        $plots = $this->plotService->searchData($name, $year, $farmIds, $organizationId, $limit);

        return [
            'total' => $plots->total(),
            'rows' => $plots->items(),
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/crop-analysis/echarts",
     *     summary="Get crop analysis in eChart format",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getCropAnalysisECharts()
    {
        $validator = Validator::make(Request::all(), [
            'year' => 'date_format:Y',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        try {
            $user = Auth::user();
            $year = Request::get('year');
            $farmIds = json_decode(Request::get('farm_ids', '[]'));
            $plotIds = json_decode(Request::get('plot_ids', '[]'));

            $result = $this->cropService->getEChartCropAnalyses($user, $year, $farmIds, $plotIds);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/crops",
     *     summary="Get crops for plots by filter",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getCrops()
    {
        $validator = Validator::make(Request::all(), [
            'year' => 'date_format:Y',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        try {
            $user = Auth::user();
            $year = Request::get('year');
            $farmIds = json_decode(Request::get('farm_ids', '[]'));
            $plotIds = json_decode(Request::get('plot_ids', '[]'));

            $result = $this->cropService->getCrops($user, $year, $farmIds, $plotIds);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/images-dates",
     *     summary="Get images dates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getImagesDates()
    {
        $validator = Validator::make(Request::all(), [
            'from' => 'string',
            'to' => 'string',
            'farm_year' => 'integer|required',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'max_cloud_percentage' => 'int',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new LayerPlot();

        try {
            $farmYear = Request::get('farm_year');
            $from = Request::get('from') ?? (new DateTime('- 14 days'))->getTimestamp();
            $to = Request::get('to') ?? (new DateTime())->getTimestamp();

            $farmIds = json_decode(Request::get('farm_ids', '[]'));
            $plotIds = json_decode(Request::get('plot_ids', '[]'));
            $cropIds = json_decode(Request::get('crop_ids', '[]'));
            $maxCloudPercentage = Request::get('max_cloud_percentage', 100);

            $arrDates = $model->imagesDates($farmYear, $from, $to, $farmIds, $plotIds, $cropIds, $maxCloudPercentage)->get()->toArray();
        } catch (Exception $e) {
            throw $e;
        }

        return $arrDates;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/index-analysis/gcharts",
     *     summary="Get index analysis in gCharts formata",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|false|string
     */
    public function getIndexAnalysisGCharts()
    {
        $validator = Validator::make(Request::all(), [
            'date' => 'date_format:Y-m-d|required',
            'satellite_type' => 'string|required',
            'year' => 'integer|required',
            'crop_id' => 'integer',
            'farm_id' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new LayerPlot();

        try {
            $user = Auth::user();
            $date = Request::get('date');
            $year = Request::get('year');
            $cropIds = [Request::get('crop_id', null)];
            $satelliteType = Request::get('satellite_type');
            $farmIds = [Request::get('farm_id')];

            $avgData = $model->avgIndexGCharts($user, $date, $satelliteType, $year, 'index', [], $cropIds, $farmIds)->get()->toArray();
            $result = $model->statsInChartFormat($avgData);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/analysis/{type}/echarts",
     *     summary="Get index analysis in eChart format",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|false|string
     */
    public function getIndexAnalysisECharts($type)
    {
        $requestData = Request::all();
        $requestData['type'] = $type;

        $validator = Validator::make($requestData, [
            'date' => 'date_format:Y-m-d|required',
            'satellite_type' => 'string|required',
            'year' => 'integer|required',
            'crop_ids' => 'json',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'type' => 'string|required|in:index,index_water',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new LayerPlot();

        try {
            $user = Auth::user();
            $date = Request::get('date');
            $year = Request::get('year');
            $satelliteType = Request::get('satellite_type');
            $cropIds = json_decode(Request::get('crop_ids', '[]'));
            $farmIds = json_decode(Request::get('farm_ids', '[]'));
            $plotIds = json_decode(Request::get('plot_ids', '[]'));

            $avgData = $model->avgIndexECharts($user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)->get()->toArray();
            $chartData = new EChartsIndexDataFormatter();
            $result = $chartData->formatIndexAnalysis($avgData);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/crop-development/echarts",
     *     summary="Get iCrop development echart format",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|false|string
     */
    public function getCropDevelopmentECharts()
    {
        $validator = Validator::make(Request::all(), [
            'from' => 'string',
            'to' => 'string',
            'farm_year' => 'integer|required',
            'crop_ids' => 'json',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'max_cloud_percentage' => 'int',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $farmYear = Request::get('farm_year');
        $from = Request::get('from') ?? (new DateTime('- 14 days'))->getTimestamp();
        $to = Request::get('to') ?? (new DateTime())->getTimestamp();

        $cropIds = json_decode(Request::get('crop_ids', '[]'));
        $farmIds = json_decode(Request::get('farm_ids', '[]'));
        $plotIds = json_decode(Request::get('plot_ids', '[]'));
        $maxCloudPercentage = Request::get('max_cloud_percentage', 100);

        return PlotService::getCropDevelopmentEChartData($farmYear, $from, $to, $cropIds, $farmIds, $plotIds, false, $maxCloudPercentage);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/bin",
     *     summary="Get binary with plot's hights.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getPlotBin()
    {
        $validator = Validator::make(Request::all(), [
            'plotId' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotGeoJson = Plot::getPlotGeoJson(Request::get('plotId'), 4326);

        return json_decode($this->plot3d->downloadBinFile($plotGeoJson), true);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/details/:plotUuId",
     *     summary="Get plot details by plot uuid.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getPlotByUuid($plotUuId)
    {
        try {
            return Plot::select(['gid', 'area', 'name', 'meteo_location_id', 'station_id', 'farm_id'])->where('uuid', $plotUuId)->first();
        } catch (Exception $e) {
            return [
                'error' => [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage(),
                ],
            ];
        }
    }

    /**
     * @OA\POST(
     *     path="/apigs/plots/change-editable-status",
     *     summary="Update plots editable value",
     *
     *     @OA\Response(
     *         response="200",
     *         description="String"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function updatePlotEditableValue()
    {
        $validator = Validator::make(Request::all(), [
            'plot_uuids' => 'array|required',
            'is_editable' => 'boolean|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotsUuIds = Request::get('plot_uuids');
        $status = Request::get('is_editable');

        try {
            Plot::whereIn('uuid', $plotsUuIds)->update(['is_editable' => $status]);
        } catch (Exception $e) {
            return new JsonResponse($e->getMessage(), $e->getCode(), ['Content-Type: application/json']);
        }

        return new JsonResponse('Success', 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/soil-sampling-dates",
     *     summary="Get stats for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getSoilSamplingDates($plotId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
        ]), [
            'plotId' => 'integer|required',
            'farm_year' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        try {
            $farmYear = Request::get('farm_year');

            $dates = LayerPlot::imagesDatesByPlotAndFarmingYear($plotId, $farmYear)->get()->toArray();
        } catch (Exception $e) {
            throw $e;
        }

        return $dates;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:gid/crop-history",
     *     summary="Get the crop history by plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getCropHistoryByPlot($gid)
    {
        $requestData = Request::all();
        $requestData['gid'] = $gid;

        $validator = Validator::make($requestData, [
            'gid' => 'required|integer',
            'lang' => 'required|string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $cropsHistory = $this->cropService->getCropHistoryByPlot($requestData['gid'], $requestData['lang']);

        return new JsonResponse($cropsHistory, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/dates",
     *     summary="Get upated_ad for plots",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @param Request $request
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getPlotsDate()
    {
        $validator = Validator::make(Request::all(), [
            'organization_id' => 'sometimes|required|integer',
            'farm_year' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Request::get('organization_id', null);
        $farmYear = Request::get('farm_year', null);

        $dates = $this->plotService->getPlotsDate($organizationId, $farmYear);

        return new JsonResponse($dates, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\POST(
     *     path="/apigs/plots/for-edit",
     *     summary="Get plots for edit",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @param Request $request
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function listPlotsForEdit()
    {
        $validator = Validator::make(Request::all(), [
            'organization_id' => 'required|integer',
            'lang' => 'required|string',
            'start_date' => 'sometimes|required|string',
            'end_date' => 'sometimes|required|string',
            'farm_ids' => 'sometimes|required|array',
            'plot_ids' => 'sometimes|required|array',
            'plot_uuids' => 'sometimes|required|array',
            'sort' => 'sometimes|required|array',
            'page' => 'sometimes|required|integer',
            'limit' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Request::get('organization_id');
        $lang = Request::get('lang');
        $filter['startDate'] = Request::get('start_date', null);
        $filter['endDate'] = Request::get('end_date', null);
        $filter['farmIds'] = Request::get('farm_ids', null);
        $filter['plotIds'] = Request::get('plot_ids', null);
        $filter['plotUuIds'] = Request::get('plot_uuids', null);
        $sort = Request::get('sort', []);
        $page = Request::get('page', 1);
        $limit = Request::get('limit', 10);

        $plotsForEdit = Plot::listPlotsForEdit($organizationId, $lang, $filter, $sort, $page, $limit);

        return new JsonResponse($plotsForEdit, 200, ['Content-Type: application/json']);
    }

    public function getPlotsMachineTasks(PlotsMachineTasksRequest $machineTasksRequest): JsonResponse
    {
        $requestData = $machineTasksRequest->validated();

        $filters = [];
        $filters['plot_ids'] = json_decode($requestData['filters']['plot_ids']);
        $filters['farm_ids'] = json_decode($requestData['filters']['farm_ids']);
        $filters['crop_ids'] = json_decode($requestData['filters']['crop_ids']);
        $filters['task_states'] = json_decode($requestData['filters']['task_states']);
        $filters['work_operation_ids'] = json_decode($requestData['filters']['work_operation_ids']);
        $filters['farm_year'] = $requestData['filters']['farm_year'];
        $filters['organization_id'] = Arr::get($requestData['filters'], 'organization_id', Auth::user()->lastChosenOrganization->id);
        $filters['start_date'] = Arr::get($requestData['filters'], 'start_date');
        $filters['end_date'] = Arr::get($requestData['filters'], 'end_date');
        $lang = $machineTasksRequest->get('lang', Config::get('app.locale'));

        $plots = Plot::getFilteredPlotsMachineTasksLists($filters, $lang)
            ->paginate($requestData['limit'], ['gid'], 'page', $requestData['page']);

        $response = [
            'rows' => $plots->items(),
            'total' => $plots->total(),
        ];

        return new JsonResponse($response, 200, []);
    }

    private function getPlotsMean($gids, $farmYears = [], $sort = 'date')
    {
        $plotDataQuery = LayerPlot::select(['su_satellite_layers_plots.date', 'mean', 'plot_id'])
            ->join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('su_satellite_layers_plots.satellite_type', 'sentinel')
            ->whereIn('plot_id', $gids)
            ->whereNotNull('mean')
            ->where(function ($query) {
                $query->whereNull('clouds_percent')
                    ->orWhere(DB::raw('round(clouds_percent::numeric, 2)'), 0);
            })
            ->where('so.status', 'processed')
            ->restOrderBy($sort);

        if (count($farmYears) > 1) {
            $plotDataQuery->orWhere(function ($query) use ($farmYears) {
                foreach ($farmYears as $farmYear) {
                    $query->whereBetween('su_satellite_layers_plots.date', $farmYear);
                }
            });
        }
        if (1 === count($farmYears)) {
            $plotDataQuery->whereBetween('su_satellite_layers_plots.date', $farmYears[0]);
        }

        $plotsData = $plotDataQuery->get()->groupBy('plot_id');

        $plotsData->transform(function ($item) {
            return $item->keyBy('date')->toArray();
        });

        return $plotsData;
    }

    private function getMeanChartData(array $plotDataArr, array $plotsAvgDataArr)
    {
        $plotData = collect($plotDataArr);
        $plotsAvgData = collect($plotsAvgDataArr);

        $chartData = collect();
        $plotData->each(function ($item, $key) use ($chartData, $plotsAvgData) {
            if (!$plotsAvgData->count()) {
                $chartData[$key] = [
                    $item['date'],
                    intval($item['mean']),
                ];

                return;
            }
            $plotsAvgMean = null;
            if ($plotsAvgData->get($key)) {
                $plotsAvgMean = intval($plotsAvgData[$key]['mean']);
            }
            $chartData[$key] = [
                $item['date'],
                intval($item['mean']),
                $plotsAvgMean,
            ];
        });

        $plotsAvgData->each(function ($item, $key) use ($chartData, $plotData) {
            if ($chartData->get($key)) {
                return;
            }
            $plotMean = null;
            if ($plotData->get($key)) {
                $plotMean = intval($plotData[$key]['mean']);
            }
            $chartData[$key] = [
                $item['date'],
                $plotMean,
                intval($item['mean']),
            ];
        });

        $sortedChartData = $chartData->sortBy(function ($item, $key) {
            return $key;
        });

        return $sortedChartData->values();
    }

    private function getShowWarning(array $plotStats, array $plotsAvgStats)
    {
        foreach ($plotStats as $indexClass => $value) {
            $plotsAvgMin = $plotsAvgStats[$indexClass] - $plotsAvgStats['stddev_' . $indexClass];
            $plotsAvgMax = $plotsAvgStats[$indexClass] + $plotsAvgStats['stddev_' . $indexClass];

            if ($plotStats[$indexClass] < $plotsAvgMin || $plotStats[$indexClass] > $plotsAvgMax) {
                return true;
            }
        }

        return false;
    }
}
