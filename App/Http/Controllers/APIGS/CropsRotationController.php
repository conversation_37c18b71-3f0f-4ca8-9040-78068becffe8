<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Http\Requests\Crop\CropRotationRequest;
use App\Services\Crop\CropRotationService;
use Auth;

class CropsRotationController extends BaseController
{
    public function index(CropRotationRequest $cropRotationRequest, CropRotationService $cropRotationService)
    {
        $params = $cropRotationRequest->validated();
        $user = Auth::user();
        $filters['plot_ids'] = json_decode($params['plot_ids']);
        $filters['farm_ids'] = json_decode($params['farm_ids']);
        $filters['farm_years'] = json_decode($params['farm_years']);
        $filters['crops_filter'] = isset($params['crops_filter']) ? json_decode($params['crops_filter'], true) : [];

        $page = $params['page'];
        $limit = $params['limit'];
        $sort = isset($params['order_by']) ? json_decode($params['order_by'], true) : [];

        return $cropRotationService->getCropRotationData($user, $filters, $sort, $page, $limit);
    }
}
