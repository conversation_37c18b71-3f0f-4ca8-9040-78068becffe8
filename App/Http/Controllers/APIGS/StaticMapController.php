<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\StaticMap;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;

class StaticMapController extends Controller
{
    /**
     * @var staticMap
     */
    public $staticMap;

    public function __construct(StaticMap $staticMap)
    {
        $this->staticMap = $staticMap;
    }

    /**
     * @deprecated
     */
    public function downloadPNG(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plotId' => 'required|integer',
            'lang' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $validator->errors()->all();
        }

        $plotId = $request->get('plotId');
        $lang = $request->get('lang');

        return $this->staticMap->downloadPNG($plotId, $lang);
    }
}
