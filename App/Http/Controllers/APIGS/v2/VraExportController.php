<?php

namespace App\Http\Controllers\APIGS\v2;

use App\Models\Order;
use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use App\Models\User;
use App\Services\Exports\Pdf;
use Illuminate\Http\Request;
use Throwable;

class VraExportController
{
    /**
     * @throws Throwable
     */
    public function show(Request $request, string $type, int $vraOrderId): string
    {
        /** @var User $user */
        $user = $request->user();
        $country = $user->globalUser()->country()->first();
        $order = null;
        $price = null;

        if ($getPrice = $request->input('price')) {
            $price = $getPrice;
        }

        if (Order::TYPE_SOIL_VRA === $type) {
            $order = OrderSoilVra::getFilteredOrdersQuery([
                'organization_id' => $user->lastChosenOrganization->id,
                'soil_vra_ids' => [$vraOrderId],
            ])->first();
        }

        if (Order::TYPE_VRA === $type) {
            $order = OrderSatelliteVra::getFilteredOrdersQuery([
                'organization_id' => $user->lastChosenOrganization->id,
                'satellite_vra_ids' => [$vraOrderId],
            ])->first();
        }

        $pdf = (new Pdf())
            ->loadView('export.vra', [
                'order' => $order,
                'user' => $user,
                'country' => $country->iso_alpha_2_code,
                'price' => $price,
            ])
            ->browsershot()->pdf();

        return response($pdf, 200, ['Content-Type' => 'application/pdf']);
    }
}
