<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 4/3/2021
 * Time: 4:56 PM.
 */

namespace App\Http\Controllers\APIGS\v2;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Plot;
use App\Services\Plot\PlotSoilService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PlotsSoilController extends BaseController
{
    private $plotSoilService;

    public function __construct(PlotSoilService $plotSoilService)
    {
        $this->plotSoilService = $plotSoilService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/v2/plots/:plot/soil/samples",
     *     summary="Plots samples",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     *
     * @return JsonResponse
     */
    public function getSamples(Request $request, Plot $plot)
    {
        $validator = Validator::make($request->all(), [
            'orderId' => 'required|integer',
            'date' => 'required|string',
            'samplingTypeIds' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = $request->get('orderId');
        $date = $request->get('date');
        $samplingTypeIds = json_decode($request->get('samplingTypeIds', '[]'));

        $arrContent = $this->plotSoilService->samplesContent($plot->gid, $orderId, $date, $samplingTypeIds);
        $arrContent['plot'] = [
            'gid' => $plot->gid,
            'name' => $plot->name,
        ];

        return new JsonResponse($arrContent);
    }
}
