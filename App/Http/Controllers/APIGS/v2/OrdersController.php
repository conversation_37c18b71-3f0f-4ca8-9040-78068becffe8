<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS\v2;

use App\Exceptions\ValidationException;
use App\Http\Requests\Orders\GetVraOrdersRequest;
use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use App\Services\Order\OrderService;
use App\Services\Order\OrderVraService;
use Auth;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Request;
use Response;
use Validator;

class OrdersController
{
    private $orderService;
    private $orderVraService;

    public function __construct(OrderService $orderService, OrderVraService $orderVraService)
    {
        $this->orderService = $orderService;
        $this->orderVraService = $orderVraService;
    }

    /**
     * @OA\Post(
     *     path="/apigs/v2/orders/store",
     *     summary="Generate orders.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     * @return JsonResponse
     */
    public function postStore()
    {
        $headerParams = [];
        $protocolData = null;

        $validator = Validator::make(Request::all(), [
            'contractId' => 'required|integer',
            'organizationId' => 'required|integer',
            'contractType' => 'required|string',
            'plots' => 'required|array',
            'existingOrderId' => 'sometimes|integer|nullable',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contractId = Request::get('contractId');
        $organizationId = Request::get('organizationId');
        $contractType = Request::get('contractType');
        $plots = Request::get('plots');
        $existingOrderId = Request::get('existingOrderId');
        $headerParams['filter'] = Request::get('filter', []);

        try {
            $protocolData = $this->orderService->postStore($contractId, $contractType, $plots, $organizationId, $headerParams, $existingOrderId);
        } catch (Exception $e) {
            return new JsonResponse([$e->getMessage()], 503);
        }

        return new JsonResponse($protocolData, 201);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/vra-orders",
     *     summary="Get VRA orders",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getVraOrders(GetVraOrdersRequest $getVraOrdersRequest): JsonResponse
    {
        $requestData = $getVraOrdersRequest->validated();

        $page = $requestData['page'] ?? 1;
        $limit = $requestData['limit'] ?? 20;
        $withPagination = $requestData['withPagination'] ?? false;

        $filters = [];
        $filters['plot_ids'] = json_decode(Arr::get($requestData, 'filters.plot_ids', '[]'));
        $filters['farm_ids'] = json_decode(Arr::get($requestData, 'filters.farm_ids', '[]'));
        $filters['crop_ids'] = json_decode(Arr::get($requestData, 'filters.crop_ids', '[]'));
        $filters['organization_id'] = $requestData['filters']['organization_id'] ?? Auth::user()->lastChosenOrganization->id;

        if (isset($requestData['filters']['farm_year'])) {
            $filters['farm_year'] = $requestData['filters']['farm_year'];
        }
        if (isset($requestData['filters']['element'])) {
            $filters['element'] = $requestData['filters']['element'];
        }

        $response = $this->orderVraService->getVraOrders($filters, $withPagination, $limit, $page);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/vra/{vraOrderId}",
     *     summary="Get soil VRA order",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getSoilVraOrder(int $vraOrderId): JsonResponse
    {
        $filters = [
            'organization_id' => Auth::user()->lastChosenOrganization->id,
            'soil_vra_ids' => [$vraOrderId],
        ];

        $order = OrderSoilVra::getFilteredOrdersQuery($filters);

        return new JsonResponse($order->first(), 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/nvra/{vraOrderId}",
     *     summary="Get satellite VRA order",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     */
    public function getSatelliteVraOrder(int $vraOrderId): JsonResponse
    {
        $filters = [
            'organization_id' => Auth::user()->lastChosenOrganization->id,
            'satellite_vra_ids' => [$vraOrderId],
        ];

        $order = OrderSatelliteVra::getFilteredOrdersQuery($filters);

        return new JsonResponse($order->first(), 200);
    }
}
