<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\CMS\ContractService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\LayerPlot;
use App\Models\Plot;
use App\Services\Plot\PlotsBoundariesTmpTableService;
use App\Services\Plot\PlotService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;

class MapController extends BaseController
{
    private $contractService;
    private $plotService;
    private $boundariesTmpTableService;

    public function __construct(ContractService $contractService, PlotService $plotService, PlotsBoundariesTmpTableService $boundariesTmpTableService)
    {
        $this->contractService = $contractService;
        $this->middleware('verify-write-rights', ['only' => ['postSaveWorkMap']]);
        $this->plotService = $plotService;
        $this->boundariesTmpTableService = $boundariesTmpTableService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/map/select/:longitude/:latitude",
     *     summary="Select plot on map",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @param float $longitude
     * @param float $latitude
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getSelect($longitude = 0.0, $latitude = 0.0)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'longitude' => $longitude,
            'latitude' => $latitude,
        ]), [
            'longitude' => 'numeric',
            'latitude' => 'numeric',
            'extent' => 'nullable|string',
            'layer_name' => 'sometimes|required|string',
            'year' => 'sometimes|required|integer',
            'gid' => 'sometimes|required|integer',
            'gids' => 'sometimes|required|string',
            'farm_ids' => 'sometimes|required|json',
            'crop_ids' => 'sometimes|required|json',
            'contract_id' => 'sometimes|required|string',
            'date' => 'sometimes|required|date_format:Y-m-d',
            'lang' => 'sometimes|required|string|min:2|max:2',
            'file_id' => 'sometimes|required|integer',
            'plot_is_editable' => 'sometimes|required|boolean',
            'start_date' => 'sometimes|required_with:end_date|date_format:Y-m-d',
            'end_date' => 'sometimes|required_with:start_date|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $layerName = Request::get('layer_name');
        $year = Request::get('year');
        $gid = Request::get('gid');
        $gids = Request::get('gids');
        $farmIds = Request::get('farm_ids', '[]');
        $cropIds = Request::get('crop_ids', '[]');
        $date = Request::get('date');
        $startDate = Request::get('start_date');
        $endDate = Request::get('end_date');
        $identityNumber = Request::get('customer_identification');
        $extent = Request::get('extent');
        $language = Request::get('lang', 'en');
        $fileId = Request::get('file_id');
        $plotIsEditable = Request::get('plot_is_editable');
        $orderUuids = json_decode(Request::get('order_uuids', '[]'));

        if ('all_plots_select_layer' == $layerName) { // TODO:: this layer is used only for A2
            return $this->plotService->featuresDataNewOrder($layerName, $longitude, $latitude, $year, $gids, $gid, $extent);
        }

        if ('vector_layer' == $layerName) { // TODO:: this layer is used only for A2
            return $this->plotService->featuresDataEdit($layerName, $longitude, $latitude, $gid, $extent);
        }

        if ('cms_layer' == $layerName) {
            $filter = [];
            if (isset($plotIsEditable)) {
                $filter = ['is_editable' => (bool)$plotIsEditable];
            }

            if (count($orderUuids)) {
                $filter = [
                    'order_uuids' => $orderUuids,
                ];
            }

            if (isset($startDate, $endDate)) {
                $filter['start_date'] = $startDate;
                $filter['end_date'] = $endDate;
            }

            return $this->plotService->getPlotsFeatureData($longitude, $latitude, $identityNumber, $filter, $extent);
        }

        if ('layer_upload_plots_boundaries' === $layerName) {
            return $this->boundariesTmpTableService->getSelectedTmpPlotsBoundariesData($fileId, $longitude, $latitude, $extent);
        }

        if ('layer_editable_plots_boundaries' === $layerName) {
            $farmIds = json_decode($farmIds);

            return $this->plotService->layerEditablePlotsBoundaries($longitude, $latitude, $identityNumber, $startDate, $endDate, $farmIds, $plotIsEditable, $extent);
        }

        if ('layer_approve_results' === $layerName) {
            return $this->plotService->layerApproveResultsData($longitude, $latitude, $extent);
        }

        $filters = [
            'plot_ids' => $gids ? explode(',', $gids) : [],
            'farm_ids' => json_decode($farmIds),
            'crop_ids' => json_decode($cropIds),
            'farm_year' => $year,
            'date' => $date,
        ];

        if ('vector_orders_layer' === $layerName) {
            return $this->plotService->featureData($longitude, $latitude, $filters, $language);
        }

        return $this->plotService->featureData($longitude, $latitude, $filters, $language);
    }

    /**
     * @OA\Get(
     *     path="/apigs/map/last-layer",
     *     summary="Get last layer name",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getLastLayer()
    {
        $validator = Validator::make(Request::all(), [
            'from_date' => 'required|date_format:Y-m-d',
            'to_date' => 'required|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        return LayerPlot::selectRaw('layer_name')
            ->join('su_satellite_plots AS p', 'p.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', $organizationId)
            ->where(DB::raw('date::DATE'), '>=', Request::get('from_date'))
            ->where(DB::raw('date::DATE'), '<=', Request::get('to_date'))
            ->where('type', '=', 'index')
            ->groupBy(DB::raw('date::DATE, layer_name'))
            ->orderBy('date', 'desc')
            ->first();
    }

    /**
     * @OA\Get(
     *     path="/apigs/map/gs-available-dates",
     *     summary="Get available dates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return array
     */
    public function getGsAvailableDates()
    {
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $dates = LayerPlot::selectRaw('satellite_type, date::DATE AS date')
            ->join('su_satellite_plots AS p', 'p.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', $organizationId)
            ->where('type', 'index')
            ->groupBy('satellite_type', DB::raw('date::DATE'))
            ->orderBy('satellite_type')
            ->get();

        if (!$dates->count()) {
            return [];
        }

        return $dates->groupBy('date');
    }

    /**
     * @OA\Get(
     *     path="/apigs/map/extent",
     *     summary="Get work layer extent",
     *
     *     @OA\Response(
     *         response="200",
     *         description="JSON"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|mixed
     */
    public function getExtent()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'sometimes|required|integer',
            'organizationId' => 'sometimes|required|integer',
            'layer_name' => 'sometimes|required|string',
            'year' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Request::get('organizationId') ?? (Auth::user())->lastChosenOrganization->id;
        $layerName = Request::get('layer_name');
        $userId = Auth::user()->id;
        $year = Request::get('year');
        $gid = Request::get('gid');

        return Plot::getExtent($organizationId, $userId, $layerName, $year, $gid, '', true);
    }

    /**
     * @OA\Post(
     *     path="/apigs/map/save-work-map",
     *     summary="Save work map",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function postSaveWorkMap()
    {
        $request = Request::all();

        $validator = Validator::make($request, [
            'add_geoms' => 'array',
            'remove_gids' => 'array',
            'farm_id' => 'integer',
            'name' => 'sometimes|string',
            'plot_id' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $farmId = Request::get('farm_id', null);
        $plotName = Request::get('name', null);
        $plotId = Request::get('plot_id', null);
        $addGeoms = Request::get('add_geoms', []);
        $removeGids = Request::get('remove_gids', []);

        $this->plotService->saveWorkMap($plotName, $farmId, $plotId, $removeGids, $addGeoms);

        return new JsonResponse('Success', 200);
    }
}
