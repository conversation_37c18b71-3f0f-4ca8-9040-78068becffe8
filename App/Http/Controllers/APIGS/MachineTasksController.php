<?php

namespace App\Http\Controllers\APIGS;

use App\Actions\Machine\GetMachineTasksTimelineAction;
use App\Actions\Machine\MachineTaskWorkOperationChartAction;
use App\Actions\Machine\StoreMachineTaskAction;
use App\Actions\Machine\UpdateMachineTaskAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Machines\GetMachineTasksTimelineRequest;
use App\Http\Requests\Machines\IndexMachineTaskRequest;
use App\Http\Requests\Machines\StoreMachineTaskRequest;
use App\Http\Requests\Machines\UpdateMachineTaskRequest;
use App\Http\Requests\Machines\WorkOperationsWithTasksStateRequest;
use App\Http\Resources\Machine\MachineTaskCollection;
use App\Http\Resources\Machine\MachineTaskResource;
use App\Models\MachineTask;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class MachineTasksController extends Controller
{
    public function index(IndexMachineTaskRequest $request): MachineTaskCollection
    {
        $requestData = $request->validated();
        $organizationId = $this->getOrganizationId($requestData);
        $machineTasks = MachineTask::forOrganization($organizationId)->withFilters($requestData)->get();

        return new MachineTaskCollection($machineTasks);
    }

    public function store(StoreMachineTaskRequest $request, StoreMachineTaskAction $storeMachineTaskAction): MachineTaskResource
    {
        $requestData = $request->validated();
        $requestData['organization_id'] = $this->getOrganizationId($requestData);
        $machineTask = $storeMachineTaskAction->withAttributes($requestData);

        return new MachineTaskResource($machineTask);
    }

    public function show(MachineTask $machineTask): MachineTaskResource
    {
        return new MachineTaskResource($machineTask);
    }

    public function update(MachineTask $machineTask, UpdateMachineTaskRequest $request, UpdateMachineTaskAction $updateMachineTaskAction): MachineTaskResource
    {
        $requestData = $request->validated();
        $requestData['organization_id'] = $this->getOrganizationId($requestData);
        $machineTask = $updateMachineTaskAction->forMachineTask($machineTask)->withAttributes($requestData);

        return new MachineTaskResource($machineTask);
    }

    public function getWorkOperationsWithTasksStateChart(
        WorkOperationsWithTasksStateRequest $request,
        MachineTaskWorkOperationChartAction $taskWorkOperationChartAction
    ): JsonResponse {
        $requestData = $request->validated();
        $dataForChart = $taskWorkOperationChartAction->withFilters($requestData)->getResult();

        return new JsonResponse($dataForChart, 200, []);
    }

    public function getMachineTasksTimeline(
        GetMachineTasksTimelineRequest $request,
        GetMachineTasksTimelineAction $action
    ) {
        $requestData = $request->validated();
        $filters = $request->get('filters', []);
        $lang = $request->get('lang', Config::get('app.locale'));

        $organizationId = $this->getOrganizationId($requestData);

        $machineTasksTimeline = $action->withFilters($filters)
            ->forOrganization($organizationId)
            ->forLang($lang)
            ->get();

        return new JsonResponse($machineTasksTimeline, 200);
    }

    private function getOrganizationId(array $requestData): int
    {
        return Arr::get($requestData, 'organization_id', Auth::user()->lastChosenOrganization->id);
    }
}
