<?php

namespace App\Http\Controllers\APIGS;

use App\Actions\StoreScheduledReportAction;
use App\Actions\UpdateScheduledReportAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\IndexScheduledReportRequest;
use App\Http\Requests\StoreScheduledReportRequest;
use App\Http\Requests\UpdateScheduledReportRequest;
use App\Http\Resources\ScheduledReportsCollection;
use App\Http\Resources\ScheduledReportsResource;
use App\Models\ScheduledReport;
use Exception;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class ScheduledReportsController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(ScheduledReport::class, 'scheduled_report');
    }

    public function index(IndexScheduledReportRequest $request): ScheduledReportsCollection
    {
        $scheduledReports = ScheduledReport::with(['owner'])
            ->forUser($request->user())
            ->paginate($request->get('limit', 20));

        return new ScheduledReportsCollection($scheduledReports);
    }

    public function show(ScheduledReport $scheduledReport): ScheduledReportsResource
    {
        return new ScheduledReportsResource($scheduledReport);
    }

    public function store(
        StoreScheduledReportRequest $storeScheduledReportRequest,
        StoreScheduledReportAction $storeScheduledReportAction
    ): ScheduledReportsResource {
        $scheduledReport = $storeScheduledReportAction
            ->forUser($storeScheduledReportRequest->user())
            ->withAttributes($storeScheduledReportRequest->validated());

        return new ScheduledReportsResource($scheduledReport);
    }

    public function update(
        UpdateScheduledReportRequest $updateScheduledReportRequest,
        ScheduledReport $scheduledReport,
        UpdateScheduledReportAction $updateScheduleReportAction
    ): ScheduledReportsResource {
        $updatedScheduledReport = $updateScheduleReportAction
            ->forScheduledReport($scheduledReport)
            ->withAttributes($updateScheduledReportRequest->validated());

        return new ScheduledReportsResource($updatedScheduledReport);
    }

    /**
     * @throws \Illuminate\Validation\ValidationException
     */
    public function destroy(ScheduledReport $scheduledReport): \Illuminate\Http\Response
    {
        try {
            $scheduledReport->delete();
        } catch (Exception $exception) {
            throw ValidationException::withMessages(['scheduled_report' => __('There was a problem deleting the Scheduled Report')]);
        }

        return response()->noContent(Response::HTTP_ACCEPTED);
    }
}
