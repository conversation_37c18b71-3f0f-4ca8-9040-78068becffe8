<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Models\Role;
use OpenApi\Annotations as OA;

class RoleController extends BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/roles/:roleName/abilities",
     *     summary="List abilities for role",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function abilitiesForRole($roleName)
    {
        /** @var Role $role */
        $role = Role::where('name', $roleName)->get()->first();

        return $role->abilities()->get();
    }
}
