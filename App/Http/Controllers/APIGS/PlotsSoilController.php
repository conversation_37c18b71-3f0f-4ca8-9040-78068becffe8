<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\Plot\PlotSoilService;
use Auth;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;

class PlotsSoilController extends BaseController
{
    private $plotSoilService;

    public function __construct(PlotSoilService $plotSoilService)
    {
        $this->middleware('verify-write-rights', ['except' => ['getCultures']]);
        $this->plotSoilService = $plotSoilService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/soil/:fileId/samples",
     *     summary="Plots samples",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @deprecated
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getSamples($plotId, $fileId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
            'fileId' => $fileId,
        ]), [
            'plotId' => 'required|integer',
            'fileId' => 'required|integer',
            'type' => 'string',
            'search_sample' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $type = Request::get('type');

        if ('file' == $type) {
            $searchSample = Request::get('search_sample', null);
            $arrContent = $this->plotSoilService->fileCsvContent($fileId, $searchSample);
        } else {
            $arrContent = $this->plotSoilService->dbSamplesContent($fileId);
        }

        return $arrContent;
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/soil",
     *     summary="List of samples files",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getFiles($plotId)
    {
        $validator = Validator::make([
            'plot_id' => $plotId,
            'from_date' => Request::get('from_date'),
            'to_date' => Request::get('to_date'),
        ], [
            'plot_id' => 'required|integer',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');

        $result = $this->plotSoilService->getFilesOldLogic($plotId, $fromDate, $toDate);

        return [
            'total' => count($result),
            'rows' => $result,
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/soil/sampling-years",
     *     summary="Plots sampling data by years.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getPlotsSamplingYears()
    {
        $validator = Validator::make(Request::all(), [
            'organization_id' => 'sometimes|required|int',
            'plot_uuid' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Request::get('oorganizationId', Auth::user()->lastChosenOrganization->id);
        $filters = [
            'plot_uuid' => Request::get('plot_uuid'),
        ];

        $samplingYears = $this->plotSoilService->getPlotsSamplingYears($organizationId, $filters);

        return new JsonResponse($samplingYears, 200);
    }
}
