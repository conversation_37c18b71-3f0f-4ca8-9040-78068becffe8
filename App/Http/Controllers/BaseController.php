<?php

namespace App\Http\Controllers;

use Config;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;

class BaseController extends Controller
{
    use DispatchesJobs;
    use ValidatesRequests;

    /**
     * @param null $connection
     *
     * @return \Doctrine\ORM\EntityManager
     */
    protected function getEntityManager($connection = null)
    {
        if (is_null($connection)) {
            $country = Config::get('globals.MACHINE');
            $connection = strtoupper($country);
        }
        $managerRegistry = app('registry');

        return $managerRegistry->getManager($connection);
    }

    /**
     * in_arrayi For a case-insensitive in_array().
     *
     * @param  [string] $needle
     * @param  [array] $haystack
     *
     * @return bool
     */
    protected function in_arrayi($needle, $haystack)
    {
        return in_array(strtolower($needle), array_map('strtolower', $haystack));
    }
}
