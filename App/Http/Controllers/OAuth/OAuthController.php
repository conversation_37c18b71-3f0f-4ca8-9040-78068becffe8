<?php
/**
 * Created by <PERSON>pStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 28/09/2018
 * Time: 09:37.
 */

namespace App\Http\Controllers\OAuth;

use App;
use App\Exceptions\ValidationException;
use App\Models\API\DeviceKey;
use App\Models\ConfigParamValue;
use App\Models\Country;
use App\Models\Plot;
use App\Models\User;
use App\Models\UserStation;
use Config;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ServerRequestInterface;
use Socialite;

class OAuthController
{
    /**
     * @OA\Post(
     *     path="/oauth/access_token",
     *     summary="Issue an access token",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Access token"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function accessToken(ServerRequestInterface $request)
    {
        $validator = Validator::make($request->getParsedBody(), [
            'username' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $username = $request->getParsedBody()['username'];
        $password = $request->getParsedBody()['password'];

        try {
            $tokenData = Socialite::driver('keycloak-password-grant')
                ->addTokenFields(['username' => $username, 'password' => $password])
                ->getAccessTokenResponse($code = '');
        } catch (ClientException $ex) {
            return new JsonResponse(
                $ex->getResponse()->getReasonPhrase(),
                $ex->getResponse()->getStatusCode()
            );
        } catch (Exception $ex) {
            throw $ex;
        }

        if (empty($tokenData) || array_key_exists('error', $tokenData)) {
            return new JsonResponse($tokenData['message'], 401);
        }

        $user = $this->getUser($username);
        $this->loadUserData($request, $tokenData, $user);

        return new JsonResponse($tokenData);
    }

    /**
     * @OA\Post(
     *     path="/admin/oauth/access_token",
     *     summary="Issue an access token for admin",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Access token"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function adminAccessToken(ServerRequestInterface $request)
    {
        $validator = Validator::make($request->getParsedBody(), [
            'username' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $username = $request->getParsedBody()['username'];
        $password = $request->getParsedBody()['password'];

        try {
            $tokenData = Socialite::driver('keycloak-password-grant')
                ->addTokenFields(['username' => $username, 'password' => $password])
                ->getAccessTokenResponse($code = '');
        } catch (ClientException $ex) {
            return new JsonResponse(
                $ex->getResponse()->getReasonPhrase(),
                $ex->getResponse()->getStatusCode()
            );
        } catch (Exception $ex) {
            throw $ex;
        }

        if (empty($tokenData) || array_key_exists('error', $tokenData)) {
            return new JsonResponse($tokenData['message'], 401);
        }

        $user = $this->getUser($username);
        $this->loadUserData($request, $tokenData, $user);

        return new JsonResponse($tokenData);
    }

    /**
     * @OA\Post(
     *     path="/cms/oauth/access_token",
     *     summary="Issue an access token for cms user",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Access token"
     *     )
     * )
     *
     * @return JsonResponse
     */
    public function cmsAccessToken(ServerRequestInterface $request)
    {
        $validator = Validator::make($request->getParsedBody(), [
            'username' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $username = $request->getParsedBody()['username'];
        $password = $request->getParsedBody()['password'];

        try {
            $tokenData = Socialite::driver('keycloak-password-grant')
                ->addTokenFields(['username' => $username, 'password' => $password])
                ->getAccessTokenResponse($code = '');
        } catch (ClientException $ex) {
            return new JsonResponse(
                $ex->getResponse()->getReasonPhrase(),
                $ex->getResponse()->getStatusCode()
            );
        } catch (Exception $ex) {
            throw $ex;
        }

        if (empty($tokenData) || array_key_exists('error', $tokenData)) {
            return new JsonResponse($tokenData['message'], 401);
        }

        $user = $this->getUser($username);

        if ($user && !$user->globalUser()->isAn('SUPER_ADMIN', 'SERVICE_ADMIN', 'SERVICE')) {
            return new JsonResponse('Forbidden', 403);
        }

        $this->loadUserData($request, $tokenData, $user);

        return new JsonResponse($tokenData);
    }

    private function loadUserData(ServerRequestInterface $request, &$tokenData, User $user)
    {
        $country = $user->globalUser()->country()->first();
        $isAdmin = $user->globalUser()->isAn('SUPER_ADMIN', 'SERVICE_ADMIN', 'SAMPLER_ADMIN');

        $tokenData['user_id'] = $user->group_id;
        $tokenData['group_id'] = $user->group_id;
        $tokenData['name'] = $user->username;
        $tokenData['fullname'] = $user->name;
        $tokenData['profile_image'] = $user->profile_image;
        $tokenData['comment'] = $user->comment;
        $tokenData['is_admin'] = $isAdmin;
        $tokenData['is_super_admin'] = $user->globalUser()->isAn('SUPER_ADMIN');
        $tokenData['is_trial'] = $user->is_trial;
        $tokenData['due_date'] = $user->due_date;
        $tokenData['is_demo'] = $user->is_demo;
        $tokenData['is_cached'] = $user->is_cached;
        $tokenData['email'] = $user->email;
        $tokenData['address'] = $user->address;
        $tokenData['phone'] = $user->phone;
        $tokenData['gdd_entries_left'] = $user->gdd_entries_left;
        $tokenData['abilities'] = $user->globalUser()->getAbilities();
        $tokenData['forbidden_abilities'] = $user->globalUser()->getForbiddenAbilities();
        $tokenData['roles'] = $user->globalUser()->getRoles();
        $tokenData['organizations'] = $user->organizations()->where('active', true)->get();
        $tokenData['plots_count'] = $user->plotsCount();
        $tokenData['machine'] = $user->globalUser()->country()->get()->first()->iso_alpha_2_code; // Added only for the mobile app, because we don`t choose organization
        $tokenData['service_provider'] = $user->globalUser()->serviceProvider;
        $tokenData['keycloak_uid'] = $user->keycloak_uid;

        // @var ConfigParamValue[] $configParams
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain . '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));
        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $tokenData['available_demo_area'] = max(Config::get('globals.ALLOWED_DEMO_AREA') - (float) $user->ordered_area, 0) * $areaCoef;
        $tokenData['GEOSCAN_MAP_SERVER'] = Config::get('globals.GEOSCAN_MAP_SERVER');
        $tokenData['SERVER_PROFILE_IMG_PATH'] = Config::get('globals.SERVER_PROFILE_IMG_PATH');
        $tokenData['API_METEO_ENDPOINT'] = Config::get('globals.API_METEO_ENDPOINT');
        $tokenData['METEO_IMAGES'] = Config::get('globals.METEO_IMAGES');
        $tokenData['PLOT_REPORTS'] = Config::get('globals.PLOT_REPORTS');
        $tokenData['DEFAULT_LANG'] = Config::get('globals.DEFAULT_LANG'); // Added only for the mobile app, because we don`t choose organization
        $tokenData['MAP_LAYER_ALLOWABLE']['name'] = Config::get('globals.MAP_LAYER_ALLOWABLE');
        $tokenData['MAPCACHE_URL'] = Config::get('globals.MAPCACHE_URL');
        $tokenData['DEFAULT_EXTENT'] = Config::get('globals.DEFAULT_EXTENT');
        $tokenData['WMS_SERVER'] = Config::get('globals.WMS_SERVER');
        $tokenData['SERVER_MAP_PATH'] = Config::get('globals.SERVER_MAP_PATH');
        $tokenData['EPSG_PROJ'] = Config::get('globals.EPSG_PROJ');
        $tokenData['AREA_UNIT'] = Config::get('globals.AREA_UNIT');
        $tokenData['area_unit_label'] = Config::get('globals.AREA_UNIT_LABEL');
        $tokenData['IMAGES_SERVER'] = Config::get('globals.IMAGES_SERVER');
        $tokenData['PROJ_4JS_STRING'] = Config::get('globals.PROJ_4JS_STRING');
        $tokenData['AREA_COEF'] = $areaCoef;
        $tokenData['DATE_LOCALE'] = Config::get('app.DATE_LOCALE');
        $tokenData['DISTANCE_UNIT'] = Config::get('globals.DISTANCE_UNIT');
        $tokenData['WEIGHT_UNIT'] = Config::get('globals.WEIGHT_UNIT');
        $tokenData['PRICE_UNIT'] = Config::get('globals.PRICE_UNIT');
        $tokenData['VOLUME_UNIT'] = Config::get('globals.VOLUME_UNIT');

        if ($user->lastChosenOrganization) {
            $organizationId = $user->lastChosenOrganization->id;
            $tokenData['MAP_LAYER_ALLOWABLE']['extent'] = UserStation::getExtent($user->id, $organizationId);
            $tokenData['SERVER_MAP_PATH'] = Config::get('globals.SERVER_MAP_PATH');
            $tokenData['ORGANIZATION_EXTENT'] = Plot::getExtent($organizationId, $user->id);
            $tokenData['ORGANIZATION_UUID'] = count($tokenData['organizations']) > 0 ? $user->lastChosenOrganization->uuid : null;
        }

        try {
            if (array_key_exists('device_key', $request->getParsedBody()) && '' != $request->getParsedBody()['device_key']) {
                $deviceKey = DeviceKey::find($request->getParsedBody()['device_key']);

                if (!$deviceKey) {
                    $deviceKey = new DeviceKey();
                }

                $deviceKey->group_id = $user->group_id;
                $deviceKey->device_key = $request->getParsedBody()['device_key'];
                if (isset($request->getParsedBody()['devide_platform']) && in_array(strtolower($request->getParsedBody()['devide_platform']), ['android', 'ios'])) {
                    $deviceKey->device_platform = strtolower($request->getParsedBody()['devide_platform']);
                }
                if (isset($request->getParsedBody()['device_platform']) && in_array(strtolower($request->getParsedBody()['device_platform']), ['android', 'ios'])) {
                    $deviceKey->device_platform = strtolower($request->getParsedBody()['device_platform']);
                }
                $deviceKey->save();
            }
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
        }
    }

    private function getUser(string $userName)
    {
        $authUser = App\Models\GlobalUser::where('username', $userName)->first();
        if (!$authUser) {
            return;
        }

        $countryId = $authUser->country;
        /** @var Country $country */
        $country = Country::find($countryId);

        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        // @var User $user
        return User::find($authUser->old_id);
    }
}
