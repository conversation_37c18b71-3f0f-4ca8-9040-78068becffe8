<?php

namespace App\Http\Controllers\Meteo;

use App;
use App\Classes\Meteo\MeteoBlue;
use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\AllowablePlot;
use App\Models\Plot;
use App\Models\UserFavouritePlot;
use App\Models\UserStation;
use Auth;
use Exception;
use Illuminate\Http\JsonResponse;
use Request;
use Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Validator;

class DataController extends BaseController
{
    private $meteo;

    public function __construct(MeteoBlue $meteo)
    {
        $this->meteo = $meteo;
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/agro-spraying-forecast-img",
     *     summary="Agro spraying forecast image for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Image"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getAgroSprayingForecastImg()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'lang' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');
        $lang = Request::get('lang');

        return $this->meteo->agroSprayingForecastImg($gid, $lang);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/spraying-forecast-gchart",
     *     summary="Agro spraying forecast chart data for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|string
     */
    public function getSprayingForecastGchart()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');
        $date = Request::get('date');

        $dataByDays = $this->meteo->forecastByDay($gid);

        if (!is_array($dataByDays)) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        $sprayingData = [];
        foreach ($dataByDays as $key => $value) {
            $calcData = $this->meteo->calcSprayingDay($value);

            $sprayingData['days'][] = [
                'risk' => $calcData['risk'],
                'color' => $calcData['color'],
                'date' => $value['time'],
            ];
        }

        $meteoLocation = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$meteoLocation['longitude'] || !$meteoLocation['latitude']) {
            return Helper::errorResponseMeteoBlue('longitude and latitude not found');
        }

        $heapOptions = [
            'Zone GID' => $meteoLocation['zone_gid'],
            'Type' => 'Forecast',
        ];

        $dataByHours = $this->meteo->forecastByHour($meteoLocation['latitude'], $meteoLocation['longitude'], [$date], $heapOptions);

        if (!is_array($dataByHours)) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        foreach ($dataByHours as $key => $value) {
            $calcData = $this->meteo->calcSprayingHour($value);

            $sprayingData['hours'][] = [
                'risk' => $calcData['risk'],
                'color' => $calcData['color'],
                'date' => $value['time'],
            ];
        }

        return $sprayingData;
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/agro-forecast-img",
     *     summary="Agro forecast image for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Image"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getAgroForecastImg()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'lang' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');
        $lang = Request::get('lang');

        return $this->meteo->agroForecastImg($gid, $lang);
    }

    /**
     * @deprecated
     *
     * @OA\Get(
     *     path="/meteo/data/air-temperature",
     *     summary="History weather data(min., max. temperature, precipitation) 1 year back for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getAirTemperature()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
        ]);
        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = trim(Request::get('gid'));

        return $this->meteo->historyBasicDay($gid);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/gdd",
     *     summary="GDD for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getGdd()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');

        try {
            $result = $this->meteo->getHistoryWeatherData($gid, $fromDate, $toDate);
        } catch (Exception $exception) {
            return new JsonResponse($exception->getMessage(), $exception->getCode());
        }

        return new JsonResponse($result);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/current",
     *     summary="Current weather for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getCurrent()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');

        return $this->meteo->currentMeteoCommon($gid);
    }

    /**
     * @deprecated
     *
     * @OA\Get(
     *     path="/meteo/data/current-for-map",
     *     summary="Current weather for map",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getCurrentForMap()
    {
        $validator = Validator::make(Request::all(), [
            'year' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organization = Auth::user()->lastChosenOrganization;
        $year = Request::get('year');

        $gid = Request::get('gid');

        return $this->meteo->currentForMap($organization, $year);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/forecast-day",
     *     summary="Daily forecast for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getForecastDay()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');

        return $this->meteo->forecastByDay($gid);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/forecast",
     *     summary="Hourly forecast for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getForecast()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required_without:station_id|int',
            'station_id' => 'required_without:gid|int',
            'date' => 'nullable|date_format:Y-m-d',
            'array_output' => 'sometimes|required|bool',
            'lang' => 'sometimes|required|string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lang = Request::get('lang', 'en');
        App::setLocale($lang);

        $stationId = Request::get('station_id');
        $plotId = Request::get('gid');
        $date = Request::get('date');
        $arrayOutput = Request::get('array_output', false);

        $station = !is_null($stationId) ? UserStation::getById($stationId) : UserStation::getByPlotId($plotId);

        if (!$station) {
            new JsonResponse(['error' => 'Station not found'], 400);
        }

        $heapOptions = [
            'Station ID' => $station->id,
            'Type' => 'Forecast',
        ];

        $result = $this->meteo->forecastByHour($station->latitude, $station->longitude, [$date], $heapOptions);

        if ($arrayOutput) {
            return array_values($result);
        }

        return $result;
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/forecast",
     *     summary="Hourly forecast for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getForecastByDateAndHours()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required_without:station_id|int',
            'date' => 'nullable|date_format:Y-m-d',
            'station_id' => 'required_without:gid|int',
            'resolution' => 'sometimes|required|int|in:1,3,6', // number of hours
            'forecast_days' => 'sometimes|required|int|min:1|max:7',
            'lang' => 'sometimes|required|string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lang = Request::get('lang', 'en');
        App::setLocale($lang);

        $stationId = Request::get('station_id');
        $plotId = Request::get('gid');
        $date = Request::get('date');
        $resolution = Request::get('resolution', 1);
        $forecastDays = Request::get('forecast_days', 7);

        $station = !is_null($stationId) ? UserStation::getById($stationId) : UserStation::getByPlotId($plotId);

        if (!$station) {
            new JsonResponse(['error' => 'Station not found'], 400);
        }

        $heapOptions = [
            'Station ID' => $station->id,
            'Type' => 'Forecast',
        ];

        $result = $this->meteo->forecastByHourGroupedByDay($station->latitude, $station->longitude, [$date], $heapOptions, $resolution, $forecastDays);

        return array_values($result);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/forecast-day-by-coordinates",
     *     summary="Daily forecast by coordinates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getForecastDayByCoordinates()
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
            'extent' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrQuery = [
            'lat' => Request::get('latitude'),
            'lon' => Request::get('longitude'),
        ];

        return $this->meteo->forecastDayByCoordinates($arrQuery);
    }

    /**
     * @deprecated
     *
     * @OA\Get(
     *     path="/meteo/data/current-by-coordinates",
     *     summary="Current weather by coordinates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @param null $stationId
     *
     * @throws ValidationException
     *
     * @return array|string
     */
    public function getCurrentByCoordinates($stationId = null)
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $longitude = Request::get('longitude');
        $latitude = Request::get('latitude');

        return $this->meteo->currentMeteoByCoordinates($longitude, $latitude, $stationId);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/current-allowable-by-coordinates",
     *     summary="Current weather for allowable plot by coordinates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getCurrentAllowableByCoordinates()
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
            'extent' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $longitude = Request::get('longitude');
        $latitude = Request::get('latitude');
        $extent = Request::get('extent');

        $organizationId = Auth::user()->organizations->first()->id;

        if (Auth::user()->lastChosenOrganization) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $plot = AllowablePlot::plotOnCoordinates($longitude, $latitude, $extent, $organizationId)->first();

        if (is_null($plot)) {
            throw new HttpException(404, 'No plot on the coordinates!');
        }

        $isFavourite = UserFavouritePlot::isFavouritePlot($plot->elg_ident, Auth::user()->group_id);

        $stationId = $plot->station_id;

        $meteoData = $this->meteo->currentMeteoByCoordinates($longitude, $latitude, $stationId);

        return [
            'elg_ident' => $plot->elg_ident,
            'gid' => $plot->gid,
            'zemlishte' => $plot->zemlishte,
            'geom' => $plot->geom,
            'stationId' => $plot->station_id,
            'isFavourite' => $isFavourite,
            'temperature' => $meteoData['temperature'],
            'windspeed' => $meteoData['windspeed'],
            'pictocode' => $meteoData['pictocode'],
        ];
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/current-by-station",
     *     summary="Current weather by station",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @return array|string
     */
    public function getCurrentByStation()
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
            'station_id' => 'nullable|string',
        ]);

        $longitude = Request::get('longitude');
        $latitude = Request::get('latitude');
        $stationId = Request::get('station_id');

        return $this->meteo->currentMeteoByCoordinates($longitude, $latitude, $stationId);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/spraying-forecast-by-coordinates-gchart",
     *     summary="Agro spraying forecast chart data by coordinates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array|string
     */
    public function getSprayingForecastByCoordinatesGchart()
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
            'date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $sprayingData = [];

        $arrQuery = [
            'lat' => Request::get('latitude'),
            'lon' => Request::get('longitude'),
            'date' => Request::get('date'),
        ];

        $dataByHours = $this->meteo->forecastByCoordinates($arrQuery);

        if (!is_array($dataByHours)) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        foreach ($dataByHours as $key => $value) {
            $calcData = $this->meteo->calcSprayingHour($value);

            $sprayingData['hours'][] = [
                'risk' => $calcData['risk'],
                'color' => $calcData['color'],
                'date' => $value['time'],
            ];
        }

        return $sprayingData;
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/forecast-by-coordinates",
     *     summary="Agro forecast by coordinates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getForecastByCoordinates()
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
            'date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrQuery = [
            'lat' => Request::get('latitude'),
            'lon' => Request::get('longitude'),
            'date' => Request::get('date'),
        ];

        return $this->meteo->forecastByCoordinates($arrQuery);
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/gdd-by-coordinates",
     *     summary="GDD by coordinates",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return array
     */
    public function getGddByCoordinates()
    {
        $validator = Validator::make(Request::all(), [
            'longitude' => 'required|string',
            'latitude' => 'required|string',
            'sowing_date' => 'required|date_format:Y-m-d',
            'crop' => 'string',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $longitude = Request::get('longitude');
        $latitude = Request::get('latitude');
        $sowingDate = Request::get('sowing_date');
        $crop = Request::get('crop');
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');

        return $this->meteo->gddByCoordinates(
            $longitude,
            $latitude,
            $sowingDate,
            $crop,
            $fromDate,
            $toDate
        );
    }

    /**
     * @OA\Get(
     *     path="/meteo/data/forecast/echart",
     *     summary="Get the forecast data (temperature, wind, spraying) in echart format.",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Json"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getForecastDataEchart()
    {
        $validator = Validator::make(Request::all(), [
            'station_id' => 'required_without:plot_id|int',
            'plot_id' => 'required_without:station_id|int',
            'resolution' => 'sometimes|required|int|in:1,3,6', // number of hours
            'forecast_days' => 'sometimes|required|int|min:1|max:7',
            'date' => 'sometimes|required|date',
            'lang' => 'sometimes|required|string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationId = Request::get('station_id');
        $plotId = Request::get('plot_id');
        $resolution = Request::get('resolution', 1);
        $forecastDays = Request::get('forecast_days', 5);
        $date = Request::get('date');

        $lang = Request::get('lang', 'en');
        App::setLocale($lang);

        $station = !is_null($stationId) ? UserStation::getById($stationId) : UserStation::getByPlotId($plotId);

        if (!$station) {
            new JsonResponse(['error' => 'Station not found'], 400);
        }

        $forecastEchart = $this->meteo->forecastByHourEchart($station, $resolution, $forecastDays, $date);

        return new JsonResponse($forecastEchart, 200);
    }

    public function getSprayingForecastEchart()
    {
        $validator = Validator::make(Request::all(), [
            'station_id' => 'required_without:plot_id|int',
            'plot_id' => 'required_without:station_id|int',
            'resolution' => 'sometimes|required|int|in:1,3,6',
            'forecast_days' => 'sometimes|required|int|min:1|max:7',
            'date' => 'sometimes|required|date',
            'lang' => 'sometimes|required|string|min:2|max:2',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationId = Request::get('station_id');
        $plotId = Request::get('plot_id');
        $resolution = Request::get('resolution', 1);
        $forecastDays = Request::get('forecast_days', 5);
        $date = Request::get('date');

        $lang = Request::get('lang', 'en');
        App::setLocale($lang);

        $station = !is_null($stationId) ? UserStation::getById($stationId) : UserStation::getByPlotId($plotId);

        if (!$station) {
            new JsonResponse(['error' => 'Station not found'], 400);
        }

        $forecastEchart = $this->meteo->getSprayingForecastEchart($station, $resolution, $forecastDays, $date);

        return new JsonResponse($forecastEchart, 200);
    }
}
