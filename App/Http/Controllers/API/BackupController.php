<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\BaseController;
use File;
use Request;

class BackupController extends BaseController
{
    public function postData()
    {
        $this->saveData();
    }

    public function uploadFile()
    {
        $destinationPath = $this->saveData();

        $file = Request::file('file');

        // checking file is valid.
        if ($file && $file->isValid()) {
            $fileName = $file->getClientOriginalName(); // renameing image
            $file->move($destinationPath, $fileName); // uploading file to given path
        }
    }

    private function saveData()
    {
        $fileSufix = Request::get('userId') . '_' . Request::get('userName');

        $destinationPath = storage_path() . DIRECTORY_SEPARATOR . 'gs_app_backup' . DIRECTORY_SEPARATOR . time() . '_' . $fileSufix . DIRECTORY_SEPARATOR;

        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0700, true);
        }

        $plotsFileName = 'plotsData_' . $fileSufix . '.txt';
        $pinsFileName = 'pinsData_' . $fileSufix . '.txt';

        File::put($destinationPath . $plotsFileName, Request::get('plotsData'));
        File::put($destinationPath . $pinsFileName, Request::get('pinsData'));

        return $destinationPath;
    }
}
