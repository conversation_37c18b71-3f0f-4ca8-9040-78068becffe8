<?php

namespace App\Http\Controllers\Mobile;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\MachineUnit;
use App\Services\Machine\MachineEChartService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MachinesController extends BaseController
{
    /**
     * @var MachineEChartService
     */
    private $machineEChartService;

    public function __construct(MachineEChartService $machineEChartService)
    {
        $this->machineEChartService = $machineEChartService;
    }

    public function getStateEchart(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'plot_ids' => 'sometimes|required|json',
            'machine_ids' => 'sometimes|required|json',
            'farm_ids' => 'sometimes|required|json',
            'crop_ids' => 'sometimes|required|json',
            'work_operations' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id') ?? Auth::user()->lastChosenOrganization->id;

        $filters = [
            'plot_ids' => json_decode($request->get('plot_ids', '[]')),
            'machine_ids' => json_decode($request->get('machine_ids', '[]')),
            'farm_ids' => json_decode($request->get('farm_ids', '[]')),
            'crop_ids' => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
        ];

        $echart = $this->machineEChartService->getMobileEchartMachinesByState($organizationId, $filters);

        return new JsonResponse($echart, 200);
    }

    public function getMachinesCountByWorkOperationsEchart(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'names' => 'sometimes|required|json',
            'plot_ids' => 'sometimes|required|json',
            'machine_ids' => 'sometimes|required|json',
            'farm_ids' => 'sometimes|required|json',
            'crop_ids' => 'sometimes|required|json',
            'statuses' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id') ?? Auth::user()->lastChosenOrganization->id;

        $filters = [
            'names' => json_decode($request->get('names', '[]')),
            'plot_ids' => json_decode($request->get('plot_ids', '[]')),
            'machine_ids' => json_decode($request->get('machine_ids', '[]')),
            'farm_ids' => json_decode($request->get('farm_ids', '[]')),
            'crop_ids' => json_decode($request->get('crop_ids', '[]')),
            'statuses' => json_decode($request->get('statuses', '[]')),
        ];

        $echart = $this->machineEChartService->getMachinesCountByWorkOperationsEchart($organizationId, $filters);

        return new JsonResponse($echart, 200);
    }

    public function getEventsTypeEchart(Request $request, MachineUnit $machine): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'work_operations' => 'json',
            'types' => 'json',
            'stages' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filters = [
            'machineIds' => [$machine->id],
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]')),
        ];

        $echart = $this->machineEChartService->getMobileEchartMachineEventsByType($organizationId, $filters);

        return new JsonResponse($echart, 200);
    }

    public function getEventsWorkOperationsEchart(Request $request, MachineUnit $machine): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'types' => 'json',
            'stages' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filters = [
            'machineIds' => [$machine->id],
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]')),
        ];

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $echart = $this->machineEChartService->getMobileEchartEventsByWorkOperation($organizationId, $filters);

        return new JsonResponse($echart, 200);
    }
}
