<?php

namespace App\Http\Controllers\Mobile;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\Irrigation\IrrigationPlatformEchartService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class IrrigationPlatformController extends BaseController
{
    private $irrigationPlatformEchartService;

    public function __construct(IrrigationPlatformEchartService $irrigationPlatformEchartService)
    {
        $this->irrigationPlatformEchartService = $irrigationPlatformEchartService;
    }

    public function getIrrigationPlatformStateDataEChart(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'farm_ids' => 'sometimes|required|json',
            'plot_ids' => 'sometimes|required|json',
            'states' => 'sometimes|required|json',
            'platform_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $platformIds = json_decode($request->get('platform_ids', '[]'), true);
        $states = json_decode($request->get('states', '[]'), true);
        $farmIds = json_decode($request->get('farm_ids', '[]'), true);
        $plotIds = json_decode($request->get('plot_ids', '[]'), true);

        $echart = $this->irrigationPlatformEchartService->getMobileEchartIrrigationPlatformByState($organizationId, $platformIds, $states, $farmIds, $plotIds);

        return new JsonResponse($echart, 200);
    }

    public function getPivotStateDataEChart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'farm_ids' => 'sometimes|required|json',
            'plot_ids' => 'sometimes|required|json',
            'states' => 'sometimes|required|json',
            'platform_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $platformIds = $request->get('platform_ids') ? json_decode($request->get('platform_ids'), true) : [];
        $states = $request->get('states') ? json_decode($request->get('states'), true) : [];
        $farmIds = $request->get('farm_ids') ? json_decode($request->get('farm_ids'), true) : [];
        $plotIds = $request->get('plot_ids') ? json_decode($request->get('plot_ids'), true) : [];

        $echart = $this->irrigationPlatformEchartService->getMobileEchartPivotByState($organizationId, $platformIds, $states, $farmIds, $plotIds);

        return new JsonResponse($echart, 200);
    }
}
