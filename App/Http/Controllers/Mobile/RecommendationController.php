<?php

namespace App\Http\Controllers\Mobile;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\Recommendations\RecommendationEChartsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RecommendationController extends BaseController
{
    private $recommendationEChartsService;

    public function __construct(RecommendationEChartsService $recommendationEChartsService)
    {
        $this->middleware('verify-write-rights', ['only' => ['postUpdate']]);
        $this->recommendationEChartsService = $recommendationEChartsService;
    }

    /**
     * @OA\Get(
     *     path="/mobile/recommendations/client/soil-analysis/{element}/echarts",
     *     summary="Get soil analysis area by element in eChart format",
     *
     *     @OA\Response(
     *         response="200",
     *         description="ECharts object"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getSoilAnalysisAreaByElementECharts(Request $request, string $element)
    {
        $validator = Validator::make(array_merge($request->all(), [
            'element' => $element,
        ]), [
            'element' => 'string',
            'plot_ids' => 'json',
            'farm_ids' => 'json',
            'date' => 'date_format:Y-m-d',
            'farm_year' => 'required|integer',
            'crop_ids' => 'json',
            'status' => 'json',
            'package_type' => 'json',
            'organization_id' => 'sometimes|integer',
            'sampling_type_id' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filters = [
            'plot_ids' => json_decode($request->get('plot_ids', '[]')),
            'farm_ids' => json_decode($request->get('farm_ids', '[]')),
            'crop_ids' => json_decode($request->get('crop_ids', '[]')),
            'status' => json_decode($request->get('status', '[]')),
            'package_type' => json_decode($request->get('package_type', '[]')),
            'farm_year' => $request->get('farm_year'),
            'date' => $request->get('date'),
            'organization_id' => $request->get('organization_id'),
            'sampling_type_id' => $request->get('sampling_type_id'),
        ];

        $chartData = $this->recommendationEChartsService->renderSoilAnalysisAreaByElementECharts($filters, $element);

        return new JsonResponse($chartData, 200);
    }
}
