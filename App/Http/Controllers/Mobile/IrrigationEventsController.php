<?php

namespace App\Http\Controllers\Mobile;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\Irrigation\IrrigationEventsService;
use Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class IrrigationEventsController extends BaseController
{
    public function __construct(IrrigationEventsService $irrigationEventService)
    {
        $this->irrigationEventService = $irrigationEventService;
    }

    /**
     * @OA\Get(
     *     path="/mobile/irrigation-events/count/echart"
     *     summary="Get irrigation events count by type (e.g. Irrigation, Movement, PressureAlarm ...) for echart",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getEventsCountEchart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'types' => 'json',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]')),
        ];

        $response = $this->irrigationEventService->getMobileEchartEventsCountByType($organizationId, $filter);

        return new JsonResponse($response, 200);
    }
}
