<?php

namespace App\Http\Controllers\Mobile;

use App\Classes\Echarts\EChartsCropAnalysisFormatter;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\Crop\CropService;
use App\Services\Plot\PlotSoilService;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;

class PlotsController extends BaseController
{
    private $cropService;
    private $plotSoilService;

    public function __construct(CropService $cropService, PlotSoilService $plotSoilService)
    {
        $this->middleware('verify-write-rights', ['only' => ['postUpdate']]);
        $this->cropService = $cropService;
        $this->plotSoilService = $plotSoilService;
    }

    /**
     * @OA\Get(
     *     path="/mobile/plots/crop-analysis/echarts",
     *     summary="Get crop analysis in eChart format",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     */
    public function getCropAnalysisECharts()
    {
        $validator = Validator::make(Request::all(), [
            'year' => 'required|date_format:Y',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        try {
            $user = Auth::user();
            $year = Request::get('year');
            $farmIds = json_decode(Request::get('farm_ids', '[]'));
            $plotIds = json_decode(Request::get('plot_ids', '[]'));

            $cropAnalyses = $this->cropService->getEChartCropAnalyses($user, $year, $farmIds, $plotIds);

            $chartOptions = Config::get('echarts.mobile.crop-analysis.options');
            $chartData = new EChartsCropAnalysisFormatter();
            $chartData->setXAxis($chartOptions['xAxis']);
            $chartData->setYAxis($chartOptions['yAxis']);
            $chartData->setGrid($chartOptions['grid']);
            $chartData->setLegend($chartOptions['legend']);
            $chartData->createSeries($cropAnalyses);
        } catch (Exception $e) {
            throw $e;
        }

        return new JsonResponse($chartData, 200);
    }

    /**
     * @OA\Get(
     *     path="/mobile/plots/:plotId/soil/samples/:element",
     *     summary="Plots samples by element",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws GuzzleException
     *
     * @return array|mixed
     */
    public function getPlotSoilSamplesByElement(int $plotId, string $element)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
            'element' => $element,
        ]), [
            'plotId' => 'required|integer',
            'orderId' => 'required|integer',
            'element' => 'required|string',
            'samplingTypeIds' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = Request::get('orderId');
        $samplingTypeIds = json_decode(Request::get('samplingTypeIds', '[]'));

        try {
            $arrContent = $this->plotSoilService->samplesContentByElement($plotId, $orderId, $element, $samplingTypeIds);
        } catch (ClientException $e) {
            $response = json_decode($e->getResponse()->getBody()->getContents(), true);
            $response = $response['error']['message'] ?? 'Client error!';

            return new JsonResponse($response, $e->getCode());
        }

        return new JsonResponse($arrContent, 200);
    }

    /**
     * @OA\Get(
     *     path="/mobile/plots/:plotId/soil-sampling-dates",
     *     summary="Get stats for plot",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getSoilSamplingDates(int $plotId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
        ]), [
            'plotId' => 'integer|required',
            'farm_year' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $farmYear = Request::get('farm_year');
        $dates = $this->plotSoilService->getPlotSoilSamplingDatesWithElements($plotId, $farmYear);

        return new JsonResponse($dates, 200);
    }
}
