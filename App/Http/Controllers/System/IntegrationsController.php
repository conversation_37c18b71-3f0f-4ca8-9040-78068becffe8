<?php

namespace App\Http\Controllers\System;

use App\Actions\Integration\DeactivateIntegrationAction;
use App\Http\Controllers\BaseController;
use App\Http\Requests\System\DeactivateIntegrationRequest;
use Illuminate\Http\JsonResponse;

class IntegrationsController extends BaseController
{
    /**
     * Deactivate integrations for a specific organization and package.
     */
    public function deactivateIntegration(
        DeactivateIntegrationRequest $request,
        DeactivateIntegrationAction $action
    ): JsonResponse {
        $action->withAttributes($request->validated());

        return new JsonResponse(true, 200);
    }
}
