<?php

namespace App\Http\Controllers\System;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\UserStation;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;

class StationsController extends BaseController
{
    /**
     * @throws ValidationException
     */
    public function deactivateStationsByContract()
    {
        $validator = Validator::make(Request::all(), [
            'contract_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contractId = Request::get('contract_id');

        UserStation::where('contract_id', $contractId)->update(['active' => false]);

        return new JsonResponse(true, 200);
    }

    /**
     * @throws ValidationException
     */
    public function setContractToStations()
    {
        $validator = Validator::make(Request::all(), [
            'contract_id' => 'required|integer',
            'stations_id' => 'required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contractId = Request::get('contract_id');
        $stationsId = Request::get('stations_id');

        foreach ($stationsId as $value) {
            UserStation::where('id', $value)->update(['contract_id' => $contractId]);
        }

        return new JsonResponse(true, 200);
    }
}
