<?php

namespace App\Policies;

use App\Models\ScheduledReport;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ScheduledReportPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any scheduled reports.
     */
    public function viewAny(User $user)
    {
        return $this->allow();
    }

    /**
     * Determine whether the user can view the scheduled report.
     */
    public function view(User $user, ScheduledReport $scheduledReport)
    {
        return $scheduledReport->owner->is($user);
    }

    /**
     * Determine whether the user can create scheduled reports.
     */
    public function create(User $user)
    {
        return $this->allow();
    }

    /**
     * Determine whether the user can update the scheduled report.
     */
    public function update(User $user, ScheduledReport $scheduledReport)
    {
        return $scheduledReport->owner->is($user);
    }

    /**
     * Determine whether the user can delete the scheduled report.
     */
    public function delete(User $user, ScheduledReport $scheduledReport)
    {
        return $scheduledReport->owner->is($user);
    }

    /**
     * Determine whether the user can restore the scheduled report.
     */
    public function restore(User $user, ScheduledReport $scheduledReport) {}

    /**
     * Determine whether the user can permanently delete the scheduled report.
     */
    public function forceDelete(User $user, ScheduledReport $scheduledReport) {}
}
