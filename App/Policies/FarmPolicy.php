<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Policies;

use App\Models\Farm;
use App\Models\User;

class FarmPolicy
{
    public function accessFarm(User $currentUser, Farm $farm)
    {
        if ($currentUser->globalUser()->isAn('SUPER_ADMIN', 'SERVICE_ADMIN')) {
            return true;
        }

        $currentUserOrganization = $currentUser->lastChosenOrganization()->get()->first();
        $farmOrganization = $farm->organization()->get();

        return $farmOrganization->contains($currentUserOrganization);
    }
}
