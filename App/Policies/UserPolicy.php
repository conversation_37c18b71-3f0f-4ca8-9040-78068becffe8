<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    public function update(User $currentUser, User $user)
    {
        $currentUserOrganization = $currentUser->lastChosenOrganization()->get()->first();
        $userOrganizations = $user->organizations()->where('active', true)->get();

        return $userOrganizations->contains($currentUserOrganization);
    }
}
