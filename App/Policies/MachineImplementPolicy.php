<?php

namespace App\Policies;

use App\Models\Integration;
use App\Models\MachineImplement;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MachineImplementPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can manage (uppdate, delete) the machine implement.
     *
     * @param \App\Models\MachineUnit $unit
     *
     * @return bool
     */
    public function manageMachineImplement(User $user, MachineImplement $implement)
    {
        $canManageMachineImplement = $user->organizations()
            ->join('su_integration', 'su_integration.organization_id', '=', 'su_organizations.id')
            ->where('su_integration.id', $implement->integration_id)
            ->where('su_integration.status', Integration::ACTIVE)
            ->where('su_organizations.active', true)
            ->whereNotNull('su_organizations.identity_number')
            ->first();

        return $canManageMachineImplement ? true : false;
    }
}
