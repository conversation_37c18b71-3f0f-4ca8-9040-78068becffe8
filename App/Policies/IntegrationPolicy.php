<?php

namespace App\Policies;

use App\Models\Ability;
use App\Models\Integration;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class IntegrationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can create Farm Track Integration.
     *
     * @return bool
     */
    public function createFTIntegration(User $user)
    {
        return $user->globalUser()->can(Ability::MANAGE_FARM_TRACK_INTEGRATIONS);
    }

    /**
     * Determine whether the user can create Irrigation Management Integration.
     *
     * @return bool
     */
    public function createIMIntegration(User $user)
    {
        return $user->globalUser()->can(Ability::MANAGE_IRRIGATION_MONITORING_INTEGRATIONS);
    }

    /**
     * Determine whether the integration belongs to any of the user's organizations.
     *
     * @return bool
     */
    public function manageIntegration(User $user, Integration $integration)
    {
        $userOrganizationIds = $user->organizations()
            ->select('su_organizations.id')
            ->where('active', true)
            ->whereNotNull('identity_number')
            ->pluck('id')->toArray();

        return in_array($integration->organization_id, $userOrganizationIds);
    }
}
