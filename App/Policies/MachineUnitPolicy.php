<?php

namespace App\Policies;

use App\Models\MachineUnit;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MachineUnitPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can manage (uppdate, delete) the machine unit.
     *
     * @return bool
     */
    public function manageMachineUnit(User $user, MachineUnit $unit)
    {
        $canManageMachineUnit = $user->organizations()
            ->join('su_integration', 'su_integration.organization_id', '=', 'su_organizations.id')
            ->where('su_integration.id', $unit->integration_id)
            ->where('su_organizations.active', true)
            ->whereNotNull('su_organizations.identity_number')
            ->first();

        return $canManageMachineUnit ? true : false;
    }
}
