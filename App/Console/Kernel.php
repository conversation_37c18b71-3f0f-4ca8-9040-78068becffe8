<?php

namespace App\Console;

use App\Console\Commands\AddRasterLayers;
use App\Console\Commands\AutoApproveOrdersCommand;
use App\Console\Commands\CalculateCloudPercentage;
use App\Console\Commands\CalculatePestDiseaseRisks;
use App\Console\Commands\CheckWeatherStationsHealth;
use App\Console\Commands\CmsSaveMissingBarcodes;
use App\Console\Commands\DeleteMeteoImages;
use App\Console\Commands\DeleteReportFiles;
use App\Console\Commands\DropForgottenTmpTables;
use App\Console\Commands\FarmAndOrganizations;
use App\Console\Commands\FillMissingGridPointsInCMS;
use App\Console\Commands\FixUserTree;
use App\Console\Commands\GenerateMapFiles;
use App\Console\Commands\GeneratePinCoverImages;
use App\Console\Commands\GenerateSoilMap;
use App\Console\Commands\GenerateStaticMapFiles;
use App\Console\Commands\ImportAgrobalanceSamples;
use App\Console\Commands\IntegrationReport;
use App\Console\Commands\MigrateUsersFromLocalDBsToMainDB;
use App\Console\Commands\NewCountry;
use App\Console\Commands\ProcessExistingOrder;
use App\Console\Commands\ProcessMeteoOrder;
use App\Console\Commands\ProcessOrder;
use App\Console\Commands\RemakeVRTFiles;
use App\Console\Commands\ReprocessClouds;
use App\Console\Commands\ReprocessOrders;
use App\Console\Commands\SendPushNotifications;
use App\Console\Commands\SetGDDEntriesLeft;
use App\Console\Commands\SyncLandsat;
use App\Console\Commands\SyncRapideye;
use App\Console\Commands\SyncSatellite;
use App\Console\Commands\SyncSentinel;
use App\Console\Commands\UpdateApiEndpointsList;
use App\Console\Commands\UpdateBarcodes;
use App\Jobs\IntegrationReportJob;
use App\Jobs\PrepareScheduledReports;
use App\Models\Country;
use App\Models\IntegrationReportsTypes;
use App\Services\Irrigation\IrrigationDataRawService;
use App\Services\Irrigation\IrrigationUnitService;
use App\Services\Machine\MachineEventService;
use App\Services\Machine\MachineUnitService;
use App\Traits\Country\TenantCountryAware;
use Cron\CronExpression;
use Illuminate\Bus\Batch;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Schema;
use Throwable;

class Kernel extends ConsoleKernel
{
    use TenantCountryAware;
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        SyncRapideye::class,
        ProcessOrder::class,
        NewCountry::class,
        ProcessMeteoOrder::class,
        ProcessExistingOrder::class,
        AddRasterLayers::class,
        SendPushNotifications::class,
        GenerateMapFiles::class,
        GenerateStaticMapFiles::class,
        DropForgottenTmpTables::class,
        DeleteMeteoImages::class,
        SyncSentinel::class,
        SyncLandsat::class,
        SyncSatellite::class,
        RemakeVRTFiles::class,
        ReprocessOrders::class,
        DeleteReportFiles::class,
        GeneratePinCoverImages::class,
        ReprocessClouds::class,
        CalculateCloudPercentage::class,
        ImportAgrobalanceSamples::class,
        SetGDDEntriesLeft::class,
        UpdateBarcodes::class,
        FarmAndOrganizations::class,
        CheckWeatherStationsHealth::class,
        IntegrationReport::class,
        GenerateSoilMap::class,
        CmsSaveMissingBarcodes::class,
        FillMissingGridPointsInCMS::class,
        FixUserTree::class,
        MigrateUsersFromLocalDBsToMainDB::class,
        AutoApproveOrdersCommand::class,
        UpdateApiEndpointsList::class,
        CalculatePestDiseaseRisks::class,
    ];

    private IrrigationDataRawService $irrigationDataRawService;
    private IrrigationUnitService $irrigationUnitService;
    private MachineUnitService $machineUnitService;
    private MachineEventService $machineEventService;

    public function __construct(
        Application $app,
        Dispatcher $events,
        IrrigationDataRawService $irrigationDataRawService,
        IrrigationUnitService $irrigationUnitService,
        MachineUnitService $machineUnitService,
        MachineEventService $machineEventService
    ) {
        parent::__construct($app, $events);

        $this->irrigationDataRawService = $irrigationDataRawService;
        $this->irrigationUnitService = $irrigationUnitService;
        $this->machineUnitService = $machineUnitService;
        $this->machineEventService = $machineEventService;
    }

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('horizon:snapshot')->everyFiveMinutes();

        if (Schema::hasTable('countries') && Schema::hasColumn('countries', 'active')) {
            $schedule->job(PrepareScheduledReports::class)
                ->name('scheduled-reports')
                ->everyFifteenMinutes()
                ->between('6:45', '22:00')
                ->withoutOverlapping();

            $availableCountries = Country::where('active', true)->get();
            $this->integrationReportsTasks($availableCountries);
            $this->systemTasks($schedule, $availableCountries);
        }
    }

    /**
     * Summary of integrationReportsTasks.
     *
     * @param Country[] $availableCountries
     */
    private function integrationReportsTasks($availableCountries)
    {
        foreach ($availableCountries as $country) {
            Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));
            $reports = IntegrationReportsTypes::getIntegrationReportsQuery()->get();

            // Filter reports based on their CRON period
            // $reports = $reports->filter(function ($report) {
            //     return $this->isCronDue($report->period);
            // });

            // if ($reports->isEmpty()) {
            //     continue;
            // }

            $jobs = $reports->map(function (IntegrationReportsTypes $report, $index) use ($country) {
                $tmpTableName = uniqid('tmp_report_') . '_' . $index;

                return new IntegrationReportJob(
                    $country->iso_alpha_2_code,
                    $tmpTableName,
                    $report->id,
                    $report->name,
                    json_decode($report->params, true),
                    $report->url,
                    $report->token,
                    $report->organization_id,
                );
            });
            // dd($jobs);

            try {
                $batch = Bus::batch(
                    $jobs,
                )->then(function (Batch $batch) {
                    // All jobs completed successfully...
                })->catch(function (Batch $batch, Throwable $e) {
                    // First batch job failure detected...
                })->finally(function (Batch $batch) {
                    // The batch has finished executing...
                    // Will be executed also on failed job
                    if ($batch->hasFailures()) {
                    }
                })->name("integration-reports-batch-{$country->iso_alpha_2_code}")->dispatch();
            } catch (Throwable $exception) {
                \Sentry\captureException($exception);
            }
        }
    }

    /**
     * Summary of integrationReportsTasks.
     *
     * @param Country[] $availableCountries
     */
    private function systemTasks(Schedule $schedule, $availableCountries)
    {
        foreach ($availableCountries as $country) {
            $this->loadCountrySpecificConfigs($country);

            $logsPath = storage_path() . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR;

            try {
                $schedule->command('satellite:sync-satellite', [
                    $country->iso_alpha_2_code,
                ])
                    ->name('satellite:sync-satellite')
                    ->appendOutputTo($logsPath . 'sync-satellite.log')
                    ->twiceDaily(1, 13)
                    ->withoutOverlapping();

                $schedule->command('satellite:process-order', [
                    $country->iso_alpha_2_code,
                ])
                    ->appendOutputTo($logsPath . 'process-order.log')
                    ->everyMinute();

                $schedule->command('satellite:send-push-notifications', [
                    $country->iso_alpha_2_code,
                ])
                    ->name('satellite:send-push-notifications')
                    ->appendOutputTo($logsPath . 'send-push-notifications.log')
                    ->dailyAt('09:00')
                    ->withoutOverlapping();

                $schedule->command('drop:forgotten-tmp-tables', [
                    $country->iso_alpha_2_code,
                ])
                    ->appendOutputTo($logsPath . 'droped-forgotten-tmp-tables.log')
                    ->dailyAt('03:00');

                $schedule->command('meteo:set-gdd-entries-left', [
                    $country->iso_alpha_2_code,
                ])
                    ->appendOutputTo($logsPath . 'updated-gdd-entries-left.log')
                    ->dailyAt('00:00');

                $schedule->command('delete:meteo-images', [
                    $country->iso_alpha_2_code,
                ])
                    ->name('delete:meteo-images')
                    ->appendOutputTo($logsPath . 'deleted-meteo-images.log')
                    ->dailyAt('10:00')
                    ->withoutOverlapping();

                $schedule->command('delete:report-files', [
                    $country->iso_alpha_2_code,
                ])
                    ->name('delete:report-files')
                    ->appendOutputTo($logsPath . 'deleted-report-files.log')
                    ->hourly()
                    ->withoutOverlapping();

                $schedule->command('stations:check-weather-stations-health')
                    ->name('stations:check-weather-stations-health')
                    ->appendOutputTo($logsPath . 'check-weather-stations-health.log')
                    ->dailyAt('06:00')
                    ->withoutOverlapping();
            } catch (Throwable $exception) {
                \Sentry\captureException($exception);

                continue;
            }
        }
    }

    private function isCronDue(string $cronExpression): bool
    {
        $cron = CronExpression::factory($cronExpression);

        return $cron->isDue();
    }
}
