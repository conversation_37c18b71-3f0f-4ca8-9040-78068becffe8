<?php

namespace App\Console\Commands;

use App\Models\Satellite;
use Aws\S3\S3Client;
use Config;
use DateTime;
use DB;
use File;
use GuzzleHttp\Client;
use Illuminate\Support\Collection;
use Sentry\EventHint;
use SimpleXMLElement;
use Throwable;

class SyncSentinel extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:sync-sentinel
                                {server : The server name}
                                {--all : Sync all images including downloaded ones.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieves Sentinel Images.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');

        $this->loadCountry(strtoupper($server));

        $this->downloadTilesFromEsaODataService();
    }

    public function downloadTilesFromEsaODataService()
    {
        $features = $this->getTilesMetaDataList();

        if (0 === $features->count()) {
            return;
        }
        $this->info(printf('Tiles found: %s', $features->count()));
        $s3Client = new S3Client([
            'credentials' => [
                'key' => Config::get('eodata.ACCESS_KEY'),
                'secret' => Config::get('eodata.SECRET_ACCESS_KEY'),
            ],
            'endpoint' => Config::get('eodata.ENDPOINT'),
            'use_path_style_endpoint' => true,
            'region' => 'eu-central-1',
            'version' => 'latest',
        ]);

        $features->each(function ($feature) use ($s3Client) {
            $this->info(printf('Download tile: %s', $feature['Name']));
            $re = "/(?<=_)(?'tileid'T[\d]{2}[A-Z]{3})(?=_)/";
            $tileName = $feature['Name'];
            preg_match($re, $tileName, $matches);

            $tileId = $matches['tileid'];
            $tileDate = (new DateTime($feature['OriginDate']))->format('Y-m-d');
            $tilesDir = Config::get('globals.SENTINEL_PATH') . $tileId . DIRECTORY_SEPARATOR . $tileDate . DIRECTORY_SEPARATOR . $tileName;
            $localManifestXmlPath = $tilesDir . DIRECTORY_SEPARATOR . 'manifest.safe';
            $DEFAULT_DB_CRS = Config::get('globals.DEFAULT_DB_CRS');
            $pattern = '/(?<utm_zone>[\d]{2})(?<lat_band>[\w])(?<cell>[\w]{2})/';
            preg_match($pattern, $tileId, $matches);
            $utmZone = $matches['utm_zone'];

            if (!File::exists($tilesDir)) {
                File::makeDirectory($tilesDir, 0755, true);
            }

            list($bucket, $s3Path) = explode('/', trim($feature['S3Path'], '/'), 2);
            $s3Client->getObject(['Bucket' => $bucket,
                'Key' => "{$s3Path}/manifest.safe",
                'SaveAs' => $localManifestXmlPath]);

            $bandsPathsById = $this->getBandsPathsByIdFromManifest($localManifestXmlPath);

            try {
                $bandsPathsById->values()->each(function ($bandPath) use ($s3Client, $bucket, $s3Path, $tilesDir) {
                    $bandFileName = File::basename($bandPath);

                    $s3Client->getObject(['Bucket' => $bucket,
                        'Key' => "{$s3Path}/{$bandPath}",
                        'SaveAs' => "{$tilesDir}/{$bandFileName}"]);
                });
            } catch (Throwable $e) {
                \Sentry\captureException($e, EventHint::fromArray(['extra' => "Error downloading bands for tile {$tileName}. Skipping tile."]));

                return;
            }

            $this->buildVrts($tilesDir, $bandsPathsById);

            $satData = Satellite::firstOrNew([
                'tileid' => $tileId,
                'catid' => $tileName,
            ]);
            $geoFootprint = json_encode($feature['GeoFootprint']);
            $satData->tileid = $tileId;
            $satData->date = $tileDate;
            $satData->catid = $tileName;
            $satData->name = $tileName;
            $satData->extent = DB::raw("ST_Force2D(ST_GeomFromGeoJSON('{$geoFootprint}'::json))");
            $satData->src_srs = 'EPSG:' . substr($DEFAULT_DB_CRS, 0, 3) . $utmZone;
            $satData->type = 'sentinel';
            $satData->path = $tilesDir;
            $satData->save();

            $tileDBId = $satData->id;
            $this->insertCloudsIntoDatabase($tileDBId, $tilesDir, $satData->src_srs, $bandsPathsById);
        });
    }

    /**
     * Calls the ESA OData service to get the list
     * of all available for download tiles from the last 10 days.
     */
    public function getTilesMetaDataList(): Collection
    {
        $client = new Client(['base_uri' => Config::get('globals.SENTINEL_ESA_API_URL')]);
        $startDate = (new DateTime())->modify('-15 day')->format('Y-m-d\\TH:i:s\\Z');
        $endDate = (new DateTime())->format('Y-m-d\\TH:i:s\\Z');
        $esaSearchQuery = Config::get('globals.SENTINEL_ESA_SEARCH_QUERY');

        $response = $client->get('', [
            'query' => [
                '$filter' => "{$esaSearchQuery} and ContentDate/Start ge {$startDate} and ContentDate/Start le {$endDate}",
                '$orderby' => 'ContentDate/Start asc',
            ],
        ]);

        $features = collect([]);
        $featuresResponse = json_decode($response->getBody()->getContents(), true);
        $features = $features->merge($featuresResponse['value']);

        while (true) {
            if (!array_key_exists('@odata.nextLink', $featuresResponse)) {
                break;
            }
            $response = $client->get($featuresResponse['@odata.nextLink']);
            $featuresResponse = json_decode($response->getBody()->getContents(), true);
            $features = $features->merge($featuresResponse['value']);
        }

        return $features;
    }

    public function getBandsPathsByIdFromManifest(string $manifestFile): Collection
    {
        $xpath = "//dataObject[@ID='IMG_DATA_Band_B02_10m_Tile1_Data' 
        or @ID='IMG_DATA_Band_B03_10m_Tile1_Data' 
        or @ID='IMG_DATA_Band_B04_10m_Tile1_Data' 
        or @ID='IMG_DATA_Band_B08_10m_Tile1_Data' 
        or @ID='IMG_DATA_Band_B11_20m_Tile1_Data'
        or @ID='CloudPrbMask_1_Tile1_Data']";

        $manifestXML = simplexml_load_file($manifestFile);

        return collect($manifestXML->xpath($xpath))->flatMap(function (SimpleXMLElement $dataObjectEl) {
            $id = (string)$dataObjectEl->attributes()->ID;
            $href = (string)trim($dataObjectEl->byteStream->fileLocation->attributes()->href, './');

            return [$id => $href];
        });
    }

    /**
     * Stores clouds as vectors generated from raster band.
     */
    protected function insertCloudsIntoDatabase(int $tileDBId, string $tilesDir, string $srcSrs, Collection $bandsPathsById)
    {
        $bandsPathsByIdShort = $bandsPathsById->flatMap(function ($path, $id) {
            return [$id => File::basename($path)];
        });
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $database = "'PG:host={$currentDatabase['host']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']} port={$currentDatabase['port']}'";
        $cloudsRaster = $tilesDir . DIRECTORY_SEPARATOR . $bandsPathsByIdShort->get('CloudPrbMask_1_Tile1_Data');
        $cloudsRasterCalc = $tilesDir . DIRECTORY_SEPARATOR . 'CloudPrbMask_1_Tile1_Data_calc.tiff';
        $cloudsShp = $tilesDir . DIRECTORY_SEPARATOR . 'CloudPrbMask_1_Tile1_Data.shp';

        $command = "{$gdalBinPath}gdal_calc.py -A {$cloudsRaster} --outfile {$cloudsRasterCalc} --format=GTiff --calc=\"1*(A>0)\" --NoDataValue=0 \
        && {$gdalBinPath}gdal_polygonize.py {$cloudsRasterCalc} -b 1 {$cloudsShp} OUTPUT DN 2>&1";
        $this->info($command);
        exec($command, $out);

        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');

        $command = "{$gdalBinPath}ogr2ogr -append -s_srs '{$srcSrs}' -t_srs 'EPSG:{$defaultProj}' -f 'PostgreSQL' {$database} {$cloudsShp} -nln su_satellite_clouds -sql 'SELECT {$tileDBId} AS tile_id, OGR_GEOMETRY, OGR_GEOM_AREA AS area FROM CloudPrbMask_1_Tile1_Data WHERE OGR_GEOM_AREA > 400'";
        $this->info($command);
        system($command);

        unlink($cloudsRasterCalc);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileDBId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->update(['geom' => DB::raw('ST_MakeValid(geom)')]);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileDBId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->delete();
    }

    /**
     * Builds necessary vrt files.
     *
     * @param string $tilesDir
     * @param Collection $bandsPathsById
     *
     * @return bool
     */
    private function buildVrts($tilesDir, $bandsPathsById)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $bandsPathsByIdShort = $bandsPathsById->flatMap(function ($path, $id) {
            return [$id => File::basename($path)];
        });
        $band02 = $bandsPathsByIdShort->get('IMG_DATA_Band_B02_10m_Tile1_Data');
        $band03 = $bandsPathsByIdShort->get('IMG_DATA_Band_B03_10m_Tile1_Data');
        $band04 = $bandsPathsByIdShort->get('IMG_DATA_Band_B04_10m_Tile1_Data');
        $band08 = $bandsPathsByIdShort->get('IMG_DATA_Band_B08_10m_Tile1_Data');
        $band11 = $bandsPathsByIdShort->get('IMG_DATA_Band_B11_20m_Tile1_Data');
        $command = "{$gdalBinPath}gdalbuildvrt -separate {$tilesDir}/RGBNIR.vrt {$tilesDir}/{$band02} {$tilesDir}/{$band03} {$tilesDir}/{$band04} {$tilesDir}/{$band08} {$tilesDir}/{$band11}";

        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdalbuildvrt -separate {$tilesDir}/ALLBANDS.vrt  {$tilesDir}/{$band02} {$tilesDir}/{$band03} {$tilesDir}/{$band04} {$tilesDir}/{$band08} {$tilesDir}/{$band11}";
        $this->info($command);
        system($command);

        $command = "{$pythonPath} {$scriptsPath}trans_rgb_sentinel.py {$tilesDir}/RGBNIR.vrt {$tilesDir}/RGB.vrt  --gdal_bin_path=\"{$gdalBinPath}\"";
        $this->info($command);
        system($command);

        return (bool)(
            File::exists($tilesDir . '/RGBNIR.vrt')
            && File::exists($tilesDir . '/ALLBANDS.vrt')
            && File::exists($tilesDir . '/RGB.vrt')
        );
    }
}
