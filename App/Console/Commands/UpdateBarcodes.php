<?php

namespace App\Console\Commands;

use DB;
use Excel;
use Illuminate\Console\Command;

class UpdateBarcodes extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:barcodes {server : The server name} {fileName}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update bercodes from the file. The file must be in the storage/files/ folder';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');
        $fileName = $this->argument('fileName');

        $this->loadCountry(strtoupper($server));

        $fullFileName = storage_path() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . $fileName;

        $this->info('Opening file!');
        Excel::load($fullFileName, function ($reader) use (&$arrResults) {
            $arrResults = $reader->toArray();
        });

        $this->info('Processing ...');

        $arrResults = array_filter($arrResults, function ($item) {
            return null != $item['old'];
        });

        $arrGids = $this->getGidsAndBarcodes($arrResults);

        $arrForUpdate = array_map(function ($item) use ($arrResults) {
            $eee = array_filter($arrResults, function ($value) use ($item) {
                return (int)$value['old'] == (int)$item->sample_number;
            });

            $new_barcode = array_column($eee, 'new');

            return ['gid' => $item->gid, 'new_barcode' => $new_barcode[0]];
        }, $arrGids);

        $this->multiUpdate($arrForUpdate);

        $this->info('Done.');
    }

    private function getGidsAndBarcodes($arrResults)
    {
        $countResults = count($arrResults);

        if (!$countResults) {
            return;
        }

        $sql = 'SELECT ssn.gid, ssn.sample_number FROM su_satellite_soil_sample_numbers ssn 
                INNER JOIN su_satellite_soil_points sp on sp.gid = ssn.gid
                INNER JOIN su_satellite_orders_plots_rel sopr on sopr.id = sp.sopr_id
                WHERE ';
        $i = 0;
        foreach ($arrResults as $key => $value) {
            $i++;
            $query = "(ssn.sample_number = '" . $value['old'] . "' AND sopr.order_id = " . $value['order'] . ' AND sp.sample_id = ' . $value['cell'] . ') OR ';
            if ($i == $countResults) {
                $query = "(ssn.sample_number = '" . $value['old'] . "' AND sopr.order_id = " . $value['order'] . ' AND sp.sample_id = ' . $value['cell'] . ')';
            }
            $sql .= $query;
        }

        return DB::select($sql);
    }

    private function multiUpdate($arrForUpdate)
    {
        $count = count($arrForUpdate);

        if (!$count) {
            return;
        }

        $i = 0;
        $valuesQuery = '';
        foreach ($arrForUpdate as $key => $value) {
            $i++;
            $query = '(' . $value['gid'] . ", '" . $value['new_barcode'] . "'),";
            if ($i == $count) {
                $query = '(' . $value['gid'] . ", '" . $value['new_barcode'] . "')";
            }
            $valuesQuery .= $query;
        }

        $sql = 'UPDATE su_satellite_soil_sample_numbers as t SET
                sample_number = c.new_number
                FROM (VALUES ' . $valuesQuery . ') as c(gid, new_number) 
                WHERE c.gid = t.gid';

        DB::update($sql);
    }
}
