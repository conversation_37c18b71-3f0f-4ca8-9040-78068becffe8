<?php

namespace App\Console\Commands;

use App\Models\Farm;
use App\Models\File;
use App\Models\Order;
use App\Models\Organization;
use App\Models\Plot;
use App\Models\Recommendation;
use App\Models\User;
use App\Models\UserStation;
use Carbon\Carbon;
use DB;
use Illuminate\Console\Command;

class FarmAndOrganizations extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:farm-and-org {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');
        $countryIsoAlpha2Code = strtoupper($server);
        $this->loadCountry($countryIsoAlpha2Code);

        $this->info('Processing ...');

        $orgs = Organization::select('id')->get();

        if (count($orgs)) {
            $this->error('su_organizations is not empty!');

            return;
        }

        $users = User::select('id', 'username', 'name', 'address', 'email', 'phone')->orderBy('id', 'asc')->get();

        DB::beginTransaction();

        foreach ($users as $key => $user) {
            try {
                $org = new Organization();
                $org->name = $user->name;
                $org->iso_alpha_2_code = $countryIsoAlpha2Code;
                $org->address = $user->address;
                $org->email = $user->email;
                $org->phone = $user->phone;
                $org->save();

                // Insert in su_organizations_users
                $org->users()->attach($user->id);

                $farm = new Farm();
                $farm->name = 'Main farm';
                $farm->comment = '';
                $farm->organization_id = $org->id;
                $farm->created = Carbon::now();
                $farm->save();

                // Insert in su_farms_users
                $farm->users()->attach($user->id);

                // Updates
                Plot::where('user_id', $user->id)->update(['farm_id' => $farm->id]);

                File::where('user_id', $user->id)->update(['farm_id' => $farm->id]);

                Order::where('user_id', $user->id)->update(['organization_id' => $org->id]);

                Recommendation::where('user_id', $user->id)->update(['organization_id' => $org->id]);

                UserStation::where('user_id', $user->id)->update(['organization_id' => $org->id]);
            } catch (Exception $e) {
                DB::rollBack();

                throw $e;
            }
        }

        DB::commit();

        $this->info('Processed.');

        $this->info('Deleting some DB fields ...');

        DB::beginTransaction();

        try {
            // DB::statement('ALTER TABLE su_satellite_plots DROP COLUMN user_id');
            // DB::statement('ALTER TABLE su_satellite_orders DROP COLUMN user_id');
            // DB::statement('ALTER TABLE su_users_recommendations DROP COLUMN user_id');
            // DB::statement('ALTER TABLE su_users_stations DROP COLUMN user_id');
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }

        DB::commit();

        $this->info('Done.');
    }
}
