<?php

namespace App\Console\Commands;

use App\Helpers\Helper;
use Config;
use DB;
use Exception;
use File;
use Illuminate\Console\Command;

class ReprocessClouds extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:reprocess-clouds
                            {server=dev : The server name}
                            {--type=all : The satellite type}
                            ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reprocess clouds.';

    /** @var string $server */
    private $server;
    /** @var string $type */
    private $type;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->type = $this->option('type');
        $this->server = $this->argument('server');
        $this->loadCountry(strtoupper($this->server));

        if ($this->confirm('Type: ' . $this->type . ' Server: ' . $this->server . '. Do you wish to continue?')) {
            $this->info('Start process');
            $tiles = $this->getTiles();
            $this->output->progressStart(count($tiles));
            foreach ($tiles as $tile) {
                $this->setCloudsToDatabase($tile);
                $this->info("\n");
                $this->output->progressAdvance();
                $this->info("\n");
            }

            $this->output->progressFinish();
        }

        return;
    }

    protected function setCloudsToDatabase($tile)
    {
        $tileDir = $tile->path;

        if ('sentinel' == $tile->type) {
            $this->setSentinelCloudsToDatabase($tile->id, $tileDir, $tile->src_srs);
        } elseif ('landsat' == $tile->type) {
            $this->setLandsatCloudsToDatabase($tile->id, $tileDir, $tile->catid);
        }
    }

    protected function setSentinelCloudsToDatabase($tileId, $tilesDir, $srcSrs)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $database = "'PG:host={$currentDatabase['host']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']}'";
        $maskGml = $tilesDir . 'MSK_CLOUDS_B00.gml';
        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');

        if (!Helper::gsHasChildNodes($maskGml, 'eop:maskMembers')) {
            return;
        }

        $command = "{$gdalBinPath}ogr2ogr -append -s_srs '{$srcSrs}' -t_srs 'EPSG:{$defaultProj}' -f 'PostgreSQL' {$database} {$maskGml} -nln su_satellite_clouds -sql 'SELECT {$tileId} AS tile_id, OGR_GEOMETRY, OGR_GEOM_AREA AS area FROM MaskFeature'";
        $this->info($command);
        system($command);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->update(['geom' => DB::raw('ST_MakeValid(geom)')]);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->delete();
    }

    protected function setLandsatCloudsToDatabase($tileId, $tilesDir, $catId)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $database = "'PG:host={$currentDatabase['host']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']}'";

        $bqaTif = $tilesDir . $catId . '_BQA.TIF';
        $maskTif = $tilesDir . 'MSK_CLOUDS.TIF';
        $maskGml = $tilesDir . 'MSK_CLOUDS.gml';

        if (!file_exists($bqaTif)) {
            $this->error('File not exists: ' . $bqaTif);

            return;
        }

        if (!file_exists($maskTif)) {
            $this->error('File not exists: ' . $maskTif);

            return;
        }

        if (!file_exists($maskGml)) {
            $this->error('File not exists: ' . $maskGml);

            return;
        }

        $command = "pymasker -c high -t cloud -s landsat -i {$bqaTif} -o {$maskTif}";
        $this->info($command);
        system($command);

        $minArea = Config::get('globals.LANDSAT_MIN_CLOUD_AREA');
        $pixelArea = 900; // in square metres
        $threshold = intval($minArea / $pixelArea);

        $command = "{$gdalBinPath}gdal_sieve.py {$maskTif} -st {$threshold}";
        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdal_polygonize.py {$maskTif} {$maskGml} -mask {$maskTif}";
        $this->info($command);
        system($command);

        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');

        $command = "{$gdalBinPath}ogr2ogr -append -t_srs 'EPSG:{$defaultProj}' -f 'PostgreSQL' {$database} {$maskGml} -nln su_satellite_clouds -sql 'SELECT {$tileId} AS tile_id, OGR_GEOMETRY, OGR_GEOM_AREA AS area FROM out'";
        $this->info($command);
        system($command);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->update(['geom' => DB::raw('ST_MakeValid(geom)')]);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->delete();

        if (File::exists($maskTif)) {
            File::delete($maskTif);
        }
    }

    protected function getTiles()
    {
        $tiles = $this->dbConn()->table('su_satellite as s')
            ->select(DB::raw('id, tileid, name, catid, path, type, date::DATE, src_srs'))->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('su_satellite_clouds')
                    ->whereRaw('su_satellite_clouds.tile_id = s.id');
            });

        if ('all' !== $this->type) {
            $tiles->where('type', $this->type);
        }

        return $tiles->get();
    }

    protected function dbConn()
    {
        return DB::connection($this->server);
    }

    /**
     * @throws Exception
     *
     * @return string
     */
    protected function getTileDir($type, $tileId, $date, $catId)
    {
        $dir = false;
        switch ($type) {
            case 'sentinel':
                $dir = Config::get('globals.SENTINEL_PATH');

                break;
            case 'landsat':
                $dir = Config::get('globals.LANDSAT_PATH');

                break;
            default:
                $dir = false;

                break;
        }

        if (!$dir) {
            throw new Exception('');
        }

        return $dir . $tileId . DIRECTORY_SEPARATOR . $date . DIRECTORY_SEPARATOR . $catId . DIRECTORY_SEPARATOR;
    }
}
