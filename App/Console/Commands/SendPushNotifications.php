<?php

namespace App\Console\Commands;

use App\Classes\APN\APN;
use App\Classes\GCM\GCM;
use App\Models\API\Notifications;
use Config;
use DB;
use Exception;
use Illuminate\Console\Command;

class SendPushNotifications extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:send-push-notifications {server}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sending push notifications to client devices.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');

        $this->loadCountry(strtoupper($this->serverName));

        $this->info('Getting group ids to send notifications to');

        $notyData = Notifications::on($this->serverName);
        $notyData->where('type', '=', 'new_tiles');
        $notyData->where('is_pushed', '=', false);
        $notifications = $notyData->get();

        if (0 === count($notifications)) {
            return;
        }

        $groupIds = [];
        for ($i = 0; $i < count($notifications); $i++) {
            $groupIds[] = $notifications[$i]->group_id;
        }

        try {
            $this->info('Updating notifications property is_pushed to TRUE');
            for ($i = 0; $i < count($notifications); $i++) {
                $noty = $notifications[$i];
                $noty->is_pushed = true;
                $noty->save();
            }

            $this->info('Try to send push notifications');
            $this->sendPushNotifications($groupIds);

            $this->info('DONE');
        } catch (Exception $e) {
            $this->error('Error updating notifications or sending push notifications');
            echo $e;
        }
    }

    /**
     * Send push notifications to users with new images.
     *
     * @param array $groupIds users group ids
     *
     * @return string response from GCM API
     */
    protected function sendPushNotifications($groupIds)
    {
        $title = trans('notifications.new_tiles', [], Config::get('app.locale'));
        $message = trans('notifications.new_tiles_details', [], Config::get('app.locale'));

        $data['notId'] = time();
        $data['collapse_key'] = 'new_tiles';

        // send notifications to android devices
        $gcm_device_keys = DB::connection($this->serverName)
            ->table('su_users_device_keys')
            ->select('device_key')
            ->where('device_platform', '=', 'android')
            ->whereIn('group_id', $groupIds)
            ->get()->toArray();

        $gcm_devices = array_map(function ($el) {
            return $el->device_key;
        }, $gcm_device_keys);

        $gcm_push = new GCM();
        $gcm_push->setDevices($gcm_devices);
        $gcm_result = $gcm_push->send($title, $message, $data);
        $this->info($gcm_result);

        // send notifications to ios devices
        $apn_device_keys = DB::connection($this->serverName)
            ->table('su_users_device_keys')
            ->select('device_key')
            ->where('device_platform', '=', 'ios')
            ->whereIn('group_id', $groupIds)
            ->get()->toArray();

        $apn_devices = array_map(function ($el) {
            return $el->device_key;
        }, $apn_device_keys);

        $apn_push = new APN();
        $apn_push->setDevices($apn_devices);
        $apn_result = $apn_push->send($title, $message);
        $this->info($apn_result);
    }
}
