<?php

namespace App\Console\Commands;

use App\Models\Satellite;
use Config;
use DB;
use Illuminate\Console\Command;

class RemakeVRTFiles extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:remake-vrt-files
                                {server : The server name}
                                {--satellite_type= : The type of satellite images to be processed (rapideye|sentinel|landsat)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate all vrt files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');
        $this->loadCountry(strtoupper($server));

        $tilesQuery = Satellite::select('name', 'tileid', 'catid', 'path', 'type', DB::raw('date::DATE AS tile_date'))
            ->where(DB::raw('date::DATE'), '>=', '2018-06-10')
            ->orderBy(DB::raw('date::DATE'), 'asc');

        $satelliteType = $this->option('satellite_type');
        if ($satelliteType) {
            $tilesQuery->where('type', $satelliteType);
        }

        $tiles = $tilesQuery->get();

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        for ($i = 0; $i < count($tiles); $i++) {
            $tile = $tiles[$i];
            $tilePath = $tile->path;

            if ('rapideye' == $tile->type) {
                $tileName = $tilePath . $tile->name . '.tif';
                $tileRGBName = $tilePath . 'rgb_' . $tile->name . '.vrt';

                $this->info("{$tileName} -> {$tileRGBName}" . PHP_EOL);

                $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
                $command = "{$pythonPath} {$scriptsPath}trans_rgb_{$tile->type}.py {$tileName} {$tileRGBName} --gdal_bin_path={$gdalBinPath}";

                $this->info($command);
                system($command);
            } elseif ('sentinel' == $tile->type) {
                $tilesDir = $tilePath;

                $command = "{$gdalBinPath}gdalbuildvrt -separate {$tilesDir}RGBNIR.vrt {$tilesDir}B02.jp2 {$tilesDir}B03.jp2 {$tilesDir}B04.jp2 {$tilesDir}B08.jp2 {$tilesDir}B11.jp2";
                $this->info($command);
                system($command);

                $command = "{$gdalBinPath}gdalbuildvrt -separate {$tilesDir}ALLBANDS.vrt {$tilesDir}B02.jp2 {$tilesDir}B03.jp2 {$tilesDir}B04.jp2 {$tilesDir}B05.jp2 {$tilesDir}B08.jp2 {$tilesDir}B11.jp2";
                $this->info($command);
                system($command);

                $command = "{$pythonPath} {$scriptsPath}trans_rgb_sentinel.py {$tilesDir}RGBNIR.vrt {$tilesDir}RGB.vrt  --gdal_bin_path={$gdalBinPath}";
                $this->info($command);
                system($command);
            } elseif ('landsat' == $tile->type) {
                $tilesDir = $tilePath;
                $catId = $tile->catid;

                $command = "{$gdalBinPath}gdalbuildvrt -srcnodata 0 -separate {$tilesDir}RGBNIR.vrt {$tilesDir}{$catId}_B2_REF_TOA.TIF {$tilesDir}{$catId}_B3_REF_TOA.TIF {$tilesDir}{$catId}_B4_REF_TOA.TIF {$tilesDir}{$catId}_B5_REF_TOA.TIF";
                $this->info($command);
                system($command);

                $command = "{$gdalBinPath}gdal_pansharpen.py -nodata 0 -w 0.0863 -w 0.5197 -w 0.3951 -w 0 -of VRT {$tilesDir}{$catId}_B8_REF_TOA.TIF {$tilesDir}RGBNIR.vrt {$tilesDir}RGBNIR_15x15.vrt";
                $this->info($command);
                system($command);

                $command = "{$pythonPath} {$scriptsPath}trans_rgb_landsat.py {$tilesDir}RGBNIR_15x15.vrt {$tilesDir}RGB_15x15.vrt  --gdal_bin_path={$gdalBinPath}";
                $this->info($command);
                system($command);

                $command = "{$gdalBinPath}gdalbuildvrt -srcnodata 0 -separate {$tilesDir}ALLBANDS.vrt {$tilesDir}{$catId}_B2_REF_TOA.TIF {$tilesDir}{$catId}_B3_REF_TOA.TIF {$tilesDir}{$catId}_B4_REF_TOA.TIF {$tilesDir}{$catId}_B5_REF_TOA.TIF {$tilesDir}{$catId}_B8_REF_TOA.TIF";
                $this->info($command);
                system($command);

                $command = "{$gdalBinPath}gdal_pansharpen.py -nodata 0 -w 0.0863 -w 0.5197 -w 0.3951 -w 0 -w 0 -of VRT {$tilesDir}{$catId}_B8_REF_TOA.TIF {$tilesDir}ALLBANDS.vrt {$tilesDir}ALLBANDS_15x15.vrt";
                $this->info($command);
                system($command);
            }
        }
    }
}
