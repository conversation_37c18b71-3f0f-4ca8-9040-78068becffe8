<?php

namespace App\Console\Commands;

use App\Models\Satellite;
use Config;
use DB;
use DOMDocument;
use Illuminate\Console\Command;

class AddRasterLayers extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:add-raster-layers
                                {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Adding geoscan layers by date to mapcache xml.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');
        $this->loadCountry(strtoupper($this->serverName));

        $xmlFile = Config::get('globals.MAPCACHE_XML_FILE');
        $this->xml = simplexml_load_file($xmlFile);

        $sources = $this->xml->xpath('/mapcache/source');
        $base = (string)$this->xml->xpath('/mapcache/cache/base')[0];

        $existingLayers = [];
        for ($i = 0; $i < count($sources); $i++) {
            $layerDate = (string)$sources[$i]->attributes()['name'];
            $existingLayers[] = $layerDate;
        }

        $data = Satellite::select(DB::raw('date::DATE AS tile_date'), DB::raw('ST_Extent(ST_Transform(extent, 3857)) AS extent'), 'type')
            ->orderBy(DB::raw('date::DATE'), 'asc')
            ->groupBy('type', DB::raw('date::DATE'));

        if (count($existingLayers) > 0) {
            $data->whereNotIn(DB::raw("type || '_' || date::DATE"), $existingLayers);
        }

        $tiles = $data->get();

        $extentsForSeeding = [];
        for ($i = 0; $i < count($tiles); $i++) {
            $tile = $tiles[$i];
            $tileSet = $tile->type . '_' . $tile->tile_date;
            if ('rapideye' == $tile->type) {
                $tileSet = 'geo_scan_' . $tile->tile_date;
            }

            $this->addLayerToXml($tile->tile_date, $tile->type, $tileSet);

            $extentsForSeeding[$tileSet] = [
                'layer' => $tile->type . '_by_date',
                'extent' => str_replace(' ', ',', trim($tiles[$i]->extent, 'BOX()')),
            ];
        }

        $dom = new DOMDocument('1.0');
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        $dom->loadXML($this->xml->asXML());
        $dom->save($xmlFile);

        // system('service apache2 reload');

        // TODO: Optimize seeding
        //        foreach ($extentsForSeeding as $t => $data) {
        //            if (app()->environment() === 'production') {
        //                system("/usr/local/bin/mapcache_seed -c {$xmlFile} -n 8 -z 1,15 -t {$t} -g g -l {$data['layer']} -e {$data['extent']}");
        //                system("/bin/chown -R www-data: {$base}{$t}");
        //            }
        //        }
    }

    private function addLayerToXml($date, $type, $tileSet)
    {
        $mapcache = $this->xml->xpath('/mapcache')[0];

        $sourceName = $type . '_' . $date;
        $source = $mapcache->addChild('source', '');
        $source->addAttribute('name', $sourceName);
        $source->addAttribute('type', 'wms');
        $getmap = $source->addChild('getmap', '');

        $params = $getmap->addChild('params', '');
        $params->addChild('FORMAT', 'image/png');
        $params->addChild('LAYERS', $type . '_by_date');
        $params->addChild('TILEDATE', $date);
        $params->addChild('MAP', Config::get('globals.WMS_MAP_PATH') . 'geo_scan.map');
        $params->addChild('TRANSPARENT', 'true');

        $http = $source->addChild('http', '');
        $http->addChild('url', Config::get('globals.HTTP_WMS_SERVER'));

        $tileset = $mapcache->addChild('tileset', '');
        $tileset->addAttribute('name', $tileSet);

        $tileset->addChild('source', $sourceName);
        $tileset->addChild('cache', 'disk');
        $tileset->addChild('grid', 'WGS84');
        $tileset->addChild('grid', 'g');
        $tileset->addChild('format', 'PNGQ_FAST');
    }
}
