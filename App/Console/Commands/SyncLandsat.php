<?php

namespace App\Console\Commands;

use App\Models\Satellite;
use Config;
use DB;
use Exception;
use File;
use Illuminate\Console\Command;

class SyncLandsat extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:sync-landsat 
                                {server : The server name}
                                {--all : Sync all images including downloaded ones.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieves Landsat Images.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');

        $this->loadCountry(strtoupper($server));

        $curl = curl_init();

        $url = Config::get('globals.LANDSAT_API_URL');

        $url .= '&search=' . curl_escape($curl, Config::get('globals.LANDSAT_SEARCH_QUERY'));

        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        // Getting available tiles count
        curl_setopt($curl, CURLOPT_URL, $url . '&limit=0');
        $countData = json_decode(curl_exec($curl));

        if (!$countData) {
            $this->error('Invalid JSON response');

            return;
        }

        $count = $countData->meta->found;

        curl_setopt($curl, CURLOPT_URL, $url . '&limit=' . $count);
        $resultData = json_decode(curl_exec($curl));

        if (!$resultData) {
            $this->error('Invalid JSON response');

            return;
        }

        $features = array_reverse($resultData->results);

        $existingTiles = Satellite::where('type', 'landsat')
            ->pluck('catid');

        for ($i = 0; $i < $count; $i++) {
            $feature = $features[$i];

            $catId = $feature->scene_id;
            if (isset($feature->LANDSAT_PRODUCT_ID)) {
                if (isset($feature->COLLECTION_CATEGORY) && 'RT' != $feature->COLLECTION_CATEGORY) {
                    continue;
                }

                $catId = $feature->LANDSAT_PRODUCT_ID;
            }

            $tileId = $feature->path . '_' . $feature->row;
            $date_simple = $feature->acquisitionDate;
            $name = $catId;
            $coveragePoly = json_encode($feature->data_geometry);

            if (!$this->option('all') && $existingTiles && $existingTiles->contains($catId)) {
                continue;
            }

            $downloadDir = Config::get('globals.LANDSAT_PATH') . $tileId . DIRECTORY_SEPARATOR . $date_simple . DIRECTORY_SEPARATOR;
            $tilesDir = $downloadDir . $catId . DIRECTORY_SEPARATOR;
            $bands = [2, 3, 4, 5, 8];

            $downloaded = $this->download($downloadDir, $catId, $bands);

            if (!$downloaded) {
                continue;
            }

            $this->execRefToa($tilesDir, $catId, $bands);
            $vrtResult = $this->buildVrts($tilesDir, $catId);

            if (!$vrtResult) {
                continue;
            }

            $utmZone = Config::get('globals.DEFAULT_UTM_ZONE');
            $DEFAULT_DB_CRS = Config::get('globals.DEFAULT_DB_CRS');
            $projInfo = $this->getFileProjection("{$tilesDir}RGBNIR.vrt");

            if (isset($projInfo['zone'], $projInfo['zone'][0])) {
                $utmZone = $projInfo['zone'][0];
            }

            $satData = Satellite::firstOrNew([
                'tileid' => $tileId,
                'catid' => $catId,
            ]);

            $satData->tileid = $tileId;
            $satData->date = $feature->acquisitionDate;
            $satData->catid = $catId;
            $satData->name = $name;
            $satData->extent = DB::raw("ST_Force2D(ST_SetSRID(ST_GeomFromGeoJSON('{$coveragePoly}'), 4326))");
            $satData->src_srs = 'EPSG:' . substr($DEFAULT_DB_CRS, 0, 3) . $utmZone;
            $satData->type = 'landsat';
            $satData->path = $tilesDir;
            $satData->save();

            $tileDBId = $satData->id;
            $this->setCloudsToDatabase($tileDBId, $tilesDir, $catId);

            $this->removeOriginalFiles($tilesDir, $catId, $bands);
        }
    }

    /**
     * Returns the raster projection in the following format `array('proj' => '', 'zone' => '', 'datum' => '', 'units' => '')`.
     *
     * @param string $sourceImg the source image file
     *
     * @return array
     */
    protected function getFileProjection($sourceImg)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdalinfo -proj4 -nogcp -nomd -norat -noct {$sourceImg}";
        exec($command . ' 2>&1', $out);
        $projLine = '';
        $outNumLines = count($out);
        $projRegEx = "/\+proj=(?P<proj>[\w]+) \+zone=(?P<zone>[\d]+) \+datum=(?P<datum>[\w]+) \+units=(?P<units>[\w]+)/";
        for ($i = 0; $i < $outNumLines; $i++) {
            $line = $out[$i];
            $nextLine = '';

            if ($i + 1 < $outNumLines) {
                $nextLine = $out[$i + 1];
            }
            if ('PROJ.4 string is:' == $line) {
                $pos = strpos($nextLine, 'south');
                if (false !== $pos) {
                    $projRegEx = "/\+proj=(?P<proj>[\w]+) \+zone=(?P<zone>[\d]+) \+(?<pole>[\w]+) \+datum=(?P<datum>[\w]+) \+units=(?P<units>[\w]+)/";
                }

                preg_match_all($projRegEx, $nextLine, $projData);

                return $projData;
            }
        }

        return [];
    }

    /**
     * Creates Landsat 8 cloud mask using https://github.com/haoliangyu/pymasker.
     */
    protected function setCloudsToDatabase($tileId, $tilesDir, $catId)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $database = "'PG:host={$currentDatabase['host']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']}'";

        $bqaTif = $tilesDir . $catId . '_BQA.TIF';
        $maskTif = $tilesDir . 'MSK_CLOUDS.TIF';
        $maskGml = $tilesDir . 'MSK_CLOUDS.gml';

        if (!file_exists($bqaTif)) {
            $this->error('File not exists: ' . $bqaTif);

            return;
        }

        if (!file_exists($maskTif)) {
            $this->error('File not exists: ' . $maskTif);

            return;
        }

        if (!file_exists($maskGml)) {
            $this->error('File not exists: ' . $maskGml);

            return;
        }

        $command = "pymasker -c high -t cloud -s landsat -i {$bqaTif} -o {$maskTif}";
        $this->info($command);
        system($command);

        $minArea = Config::get('globals.LANDSAT_MIN_CLOUD_AREA');
        $pixelArea = 900; // in square metres
        $threshold = intval($minArea / $pixelArea);

        $command = "{$gdalBinPath}gdal_sieve.py {$maskTif} -st {$threshold}";
        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdal_polygonize.py {$maskTif} {$maskGml} -mask {$maskTif}";
        $this->info($command);
        system($command);

        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');

        $command = "{$gdalBinPath}ogr2ogr -append -t_srs 'EPSG:{$defaultProj}' -f 'PostgreSQL' {$database} {$maskGml} -nln su_satellite_clouds -sql 'SELECT {$tileId} AS tile_id, OGR_GEOMETRY, OGR_GEOM_AREA AS area FROM out'";
        $this->info($command);
        system($command);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->update(['geom' => DB::raw('ST_MakeValid(geom)')]);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->delete();

        if (File::exists($maskTif)) {
            File::delete($maskTif);
        }
    }

    /**
     * Downloads necessary Landsat tiles.
     *
     * @param string $downloadDir
     * @param string $catId
     * @param array $bands
     *
     * @return bool True on success
     */
    private function download($downloadDir, $catId, $bands)
    {
        if (!File::exists($downloadDir)) {
            File::makeDirectory($downloadDir, 755, true);
        }

        $this->info("Downloading Tile: {$catId}");

        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $command = "{$pythonPath} {$scriptsPath}download_satellite.py {$downloadDir} {$catId} landsat --bands " . implode(' ', $bands);
        $this->info($command);
        system($command);

        if (File::exists($downloadDir . $catId . '.tar.bz')) {
            $this->extractFiles($downloadDir, $catId, $bands);
        }

        $tilesDir = $downloadDir . $catId . DIRECTORY_SEPARATOR;

        return (bool)(
            File::exists($tilesDir . $catId . '_B2.TIF')
            && File::exists($tilesDir . $catId . '_B3.TIF')
            && File::exists($tilesDir . $catId . '_B4.TIF')
            && File::exists($tilesDir . $catId . '_B5.TIF')
            && File::exists($tilesDir . $catId . '_B8.TIF')
            && File::exists($tilesDir . $catId . '_BQA.TIF')
            && File::exists($tilesDir . $catId . '_MTL.txt')
        );
    }

    /**
     * Calculate reflectance toa using https://github.com/mapbox/rio-toa.
     */
    private function execRefToa($tilesDir, $catId, $bands)
    {
        for ($i = 0; $i < count($bands); $i++) {
            $band = $bands[$i];
            $command = "rio toa reflectance -t '.*/LC.*\_B{b}.TIF' {$tilesDir}{$catId}_B{$band}.TIF {$tilesDir}{$catId}_MTL.txt {$tilesDir}{$catId}_B{$band}_REF_TOA.TIF";
            $this->info($command);
            system($command);
        }
    }

    /**
     * Delete original files.
     */
    private function removeOriginalFiles($tilesDir, $catId, $bands)
    {
        for ($i = 0; $i < count($bands); $i++) {
            $band = $bands[$i];

            $filePath = $tilesDir . $catId . '_B' . $band . '.TIF';
            if (File::exists($filePath)) {
                File::delete($filePath);
            }
        }
    }

    /**
     * Builds necessary vrt files.
     *
     * @return bool True on success
     */
    private function buildVrts($tilesDir, $catId)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $command = "{$gdalBinPath}gdalbuildvrt -srcnodata 0 -separate {$tilesDir}RGBNIR.vrt {$tilesDir}{$catId}_B2_REF_TOA.TIF {$tilesDir}{$catId}_B3_REF_TOA.TIF {$tilesDir}{$catId}_B4_REF_TOA.TIF {$tilesDir}{$catId}_B5_REF_TOA.TIF";
        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdal_pansharpen.py -nodata 0 -w 0.0863 -w 0.5197 -w 0.3951 -w 0 -of VRT {$tilesDir}{$catId}_B8_REF_TOA.TIF {$tilesDir}RGBNIR.vrt {$tilesDir}RGBNIR_15x15.vrt";
        $this->info($command);
        system($command);

        $command = "{$pythonPath} {$scriptsPath}trans_rgb_landsat.py {$tilesDir}RGBNIR_15x15.vrt {$tilesDir}RGB_15x15.vrt  --gdal_bin_path={$gdalBinPath}";
        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdalbuildvrt -srcnodata 0 -separate {$tilesDir}ALLBANDS.vrt {$tilesDir}{$catId}_B2_REF_TOA.TIF {$tilesDir}{$catId}_B3_REF_TOA.TIF {$tilesDir}{$catId}_B4_REF_TOA.TIF {$tilesDir}{$catId}_B5_REF_TOA.TIF {$tilesDir}{$catId}_B8_REF_TOA.TIF";
        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdal_pansharpen.py -nodata 0 -w 0.0863 -w 0.5197 -w 0.3951 -w 0 -w 0 -of VRT {$tilesDir}{$catId}_B8_REF_TOA.TIF {$tilesDir}ALLBANDS.vrt {$tilesDir}ALLBANDS_15x15.vrt";
        $this->info($command);
        system($command);

        return (bool)(
            File::exists($tilesDir . 'RGBNIR_15x15.vrt')
            && File::exists($tilesDir . 'ALLBANDS_15x15.vrt')
            && File::exists($tilesDir . 'RGB_15x15.vrt')
        );
    }

    /**
     * Extract landsat files.
     *
     * @param string $downloadDir
     * @param string $catId
     * @param array $bands
     */
    private function extractFiles($downloadDir, $catId, $bands)
    {
        try {
            $extractPath = $downloadDir . $catId . '_tmp' . DIRECTORY_SEPARATOR;
            if (!File::exists($extractPath)) {
                File::makeDirectory($extractPath);
            }

            $tarBzFile = $downloadDir . $catId . '.tar.bz';
            $command = "tar -xaf {$tarBzFile} -C {$extractPath}";
            $this->info($command);
            system($command);

            $destinationPath = $downloadDir . $catId . DIRECTORY_SEPARATOR;
            if (!File::exists($destinationPath)) {
                File::makeDirectory($destinationPath);
            }

            File::move($extractPath . $catId . '_BQA.TIF', $destinationPath . $catId . '_BQA.TIF');
            File::move($extractPath . $catId . '_MTL.txt', $destinationPath . $catId . '_MTL.txt');

            for ($i = 0; $i < count($bands); $i++) {
                $fileName = $catId . '_B' . $bands[$i] . '.TIF';
                File::move($extractPath . $fileName, $destinationPath . $fileName);
            }

            if (File::exists($extractPath)) {
                File::deleteDirectory($extractPath);
            }
            if (File::exists($downloadDir . $catId . '.tar.bz')) {
                File::delete($downloadDir . $catId . '.tar.bz');
            }
        } catch (Exception $e) {
            $this->error('Caught exception: ' . $e->getMessage() . PHP_EOL . $e->getTraceAsString() . PHP_EOL);
        }
    }
}
