<?php

namespace App\Console\Commands;

use File;
use Illuminate\Console\Command;

class DeleteMeteoImages extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:meteo-images
                            {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Meteo Images';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');
        $this->loadCountry(strtoupper($this->serverName));

        File::deleteDirectory(config('meteo.METEO_PATH') . config('globals.MACHINE'));
    }
}
