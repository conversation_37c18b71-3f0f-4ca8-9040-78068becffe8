<?php

namespace App\Console\Commands;

use App\Models\ConfigParam;
use App\Models\Country;
use DB;
use File;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;

class NewCountry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'new:country 
                        {name : country full name} 
                        {iso_alpha_2_code : iso alpha 2 code (BG)}
                        {iso_alpha_3_code : iso alpha 3 code (BGR)}
                        {storage : main storage dir (/home/<USER>/qnap_storage)}
                        ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Model::getConnectionResolver()->setDefaultConnection('main');

        $this->countryName = $this->argument('name');
        $this->isoAlpha2Code = strtoupper($this->argument('iso_alpha_2_code'));
        $this->isoAlpha3Code = strtoupper($this->argument('iso_alpha_3_code'));
        $this->storage = $this->argument('storage');

        $country = DB::table('countries')->where('iso_alpha_2_code', $this->isoAlpha2Code)->first();

        if (!$country) {
            $country = $this->insertNewCountry();
        }

        $this->countryId = $country->id;

        $this->configParams = ConfigParam::all()->toArray();

        $this->code = strtolower($this->isoAlpha2Code);

        // Insert Config Params Values and create folders
        $this->insertConfigParamsValues();
    }

    protected function insertConfigParamsValues()
    {
        $STORAGE_PATH_VALUE = '' . $this->storage . '/storage_' . $this->code . '/storage';
        if (!File::exists($STORAGE_PATH_VALUE)) {
            File::makeDirectory($STORAGE_PATH_VALUE, 0755, true);
        }

        $METEO_PATH_VALUE = '' . $STORAGE_PATH_VALUE . '/meteo/';
        if (!File::exists($METEO_PATH_VALUE)) {
            File::makeDirectory($METEO_PATH_VALUE, 0755, true);
        }

        $EXPORTS_PATH_VALUE = '' . $STORAGE_PATH_VALUE . '/files/exports/';
        if (!File::exists($EXPORTS_PATH_VALUE)) {
            File::makeDirectory($EXPORTS_PATH_VALUE, 0755, true);
        }

        $SOILS_PATH_VALUE = '' . $STORAGE_PATH_VALUE . '/soils/';
        if (!File::exists($SOILS_PATH_VALUE)) {
            File::makeDirectory($SOILS_PATH_VALUE, 0755, true);
        }

        $PROCESSED_ORDERS_PATH_VALUE = '' . $STORAGE_PATH_VALUE . '/ordered_plots/';
        if (!File::exists($PROCESSED_ORDERS_PATH_VALUE)) {
            File::makeDirectory($PROCESSED_ORDERS_PATH_VALUE, 0755, true);
        }

        $ORDER_QUEUE_PATH_VALUE = '' . $STORAGE_PATH_VALUE . '/orders_queue/';
        if (!File::exists($ORDER_QUEUE_PATH_VALUE)) {
            File::makeDirectory($ORDER_QUEUE_PATH_VALUE, 0755, true);
        }

        $SERVER_MAP_PATH_VALUE = '' . $STORAGE_PATH_VALUE . '/maps/';
        if (!File::exists($SERVER_MAP_PATH_VALUE)) {
            File::makeDirectory($SERVER_MAP_PATH_VALUE, 0755, true);
        }

        $RAPIDEYE_PATH_VALUE = '' . $this->storage . '/satellite_images/' . $this->code . '/rapideye/';
        if (!File::exists($RAPIDEYE_PATH_VALUE)) {
            File::makeDirectory($RAPIDEYE_PATH_VALUE, 0755, true);
        }

        $LANDSAT_PATH_VALUE = '' . $this->storage . '/satellite_images/' . $this->code . '/landsat/';
        if (!File::exists($LANDSAT_PATH_VALUE)) {
            File::makeDirectory($LANDSAT_PATH_VALUE, 0755, true);
        }

        $SENTINEL_PATH_VALUE = '' . $this->storage . '/satellite_images/' . $this->code . '/sentinel/';
        if (!File::exists($SENTINEL_PATH_VALUE)) {
            File::makeDirectory($SENTINEL_PATH_VALUE, 0755, true);
        }

        DB::table('config_params_values')->insert([
            ['config_param_id' => $this->findConfigId('STORAGE_PATH'), 'value' => $STORAGE_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('METEO_PATH'), 'value' => $METEO_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('EXPORTS_PATH'), 'value' => $EXPORTS_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('SOILS_PATH'), 'value' => $SOILS_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PROCESSED_ORDERS_PATH'), 'value' => $PROCESSED_ORDERS_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('ORDER_QUEUE_PATH'), 'value' => $ORDER_QUEUE_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('MACHINE'), 'value' => $this->isoAlpha2Code, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('DEFAULT_LANG'), 'value' => $this->code, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PRICE_UNIT'), 'value' => '€', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('DEFAULT_EXTENT'), 'value' => '[-1198895.310, 4263042.321, 488843.418, 5415511.162]', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PROJ_4JS_STRING'), 'value' => '+proj=utm +zone=30 +datum=WGS84 +units=m +no_defs', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('EPSG_PROJ'), 'value' => '32630', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PLOT_REPORTS'), 'value' => 'https://api.geoscan.info/reports/', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('METEO_IMAGES'), 'value' => 'https://api.geoscan.info/images/' . $this->code . '/meteo/', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('API_METEO_ENDPOINT'), 'value' => 'https://api.geoscan.info/meteo/', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('SERVER_PROFILE_IMG_PATH'), 'value' => 'https://api.geoscan.info/profile/' . $this->code . '/', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('IMAGES_SERVER'), 'value' => 'https://api.geoscan.info/images/' . $this->code . '/', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('SERVER_MAP_PATH'), 'value' => $SERVER_MAP_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('WMS_SERVER'), 'value' => 'https://api.geoscan.info/cgi-bin/mapserv', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('GEOSCAN_MAP_SERVER'), 'value' => 'https://api.geoscan.info/', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('WMS_MAP_PATH'), 'value' => $SERVER_MAP_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('OSM_CONNECTION'), 'value' => 'none', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('RAPIDEYE_PATH'), 'value' => $RAPIDEYE_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('SAMPLING_REPORT_DEFAULT_EMAILS'), 'value' => '<EMAIL>', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('APP_LOCALE'), 'value' => $this->code, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('APP_TIMEZONE'), 'value' => 'Europe/Madrid', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('EXTENT_WKT_CRS_4326'), 'value' => 'POLYGON((-10.47547 44.23278,-10.37734 35.79610,4.51402 36.90257,3.63084 43.56103,-10.47547 44.23278))', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('EXTENT_WKT_CRS'), 'value' => 'POLYGON((-1136083.027 5474226.991,-1125159.152 4286255.556,464264.699 4422803.997,401452.416 5435993.428,-1136083.027 5474226.991))', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('LANDSAT_SEARCH_QUERY'), 'value' => 'satellite_name:landsat-8 AND date:>=2017-10-01 AND cloud_coverage:<=60 AND ((path:202 AND row:34))', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('LANDSAT_PATH'), 'value' => $LANDSAT_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('SENTINEL_ESA_SEARCH_QUERY'), 'value' => 'platformname:Sentinel-2 AND processinglevel:Level-1C AND beginposition:[NOW-10DAYS TO NOW] AND cloudcoverpercentage:[0 TO 60] AND (filename:*_T29SQB_*)', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('SENTINEL_PATH'), 'value' => $SENTINEL_PATH_VALUE, 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PRICE_PER_DKA_METEO'), 'value' => '0.03', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PRICE_PER_DKA_SOIL'), 'value' => '0.2', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('PRICE_PER_DKA_INDEX'), 'value' => '0.2', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('AREA_UNIT_LABEL'), 'value' => 'ha', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('AREA_UNIT'), 'value' => 'HA', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('DEFAULT_MAX_EXTENT'), 'value' => '-1171585.622,4256214.899,494305.356,5457841.178', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('MAPCACHE_DEFAULT_EXTENT'), 'value' => '-1171585.622,4256214.899,494305.356,5457841.178', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('MAPCACHE_XML_FILE'), 'value' => '' . $this->storage . '/mapcache/' . $this->code . '_mapcache.xml', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('DEFAULT_UTM_ZONE'), 'value' => '30', 'country_id' => $this->countryId],
            ['config_param_id' => $this->findConfigId('DEFAULT_DB_CRS'), 'value' => '32630', 'country_id' => $this->countryId],
        ]);
    }

    protected function insertNewCountry()
    {
        $country = new Country();
        $country->name = $this->countryName;
        $country->iso_alpha_2_code = $this->isoAlpha2Code;
        $country->iso_alpha_3_code = $this->isoAlpha3Code;
        $country->save();

        return $country;
    }

    protected function findConfigId($name)
    {
        $id = null;
        foreach ($this->configParams as $key => $value) {
            $foundKey = array_search($name, $value);

            if ($foundKey) {
                $id = $this->configParams[$key]['id'];
            }
        }

        return $id;
    }
}
