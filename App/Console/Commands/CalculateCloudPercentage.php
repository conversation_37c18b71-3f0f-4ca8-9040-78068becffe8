<?php

namespace App\Console\Commands;

use DB;
use Exception;
use Illuminate\Console\Command;

class CalculateCloudPercentage extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:calculate-cloud-percentage
                            {server=dev : The server name}
                            {--type=all : The satellite type}
                            ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reprocess clouds.';

    /** @var string $server */
    private $server;
    /** @var string $type */
    private $type;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->type = $this->option('type');
        $this->server = $this->argument('server');

        if ($this->confirm('Type: ' . $this->type . ' Server: ' . $this->server . '. Do you wish to continue?')) {
            $this->loadCountry(strtoupper($this->server));

            $this->info('Start process');
            $layerPlots = $this->getLayerPlots();
            $this->output->progressStart(count($layerPlots));
            foreach ($layerPlots as $layerPlot) {
                $percent = $this->getCloudsPercentage($layerPlot->plot_id, $layerPlot->date);
                if ($percent) {
                    DB::table('su_satellite_layers_plots')
                        ->where('id', $layerPlot->id)
                        ->update(['clouds_percent' => $percent]);
                }
                $this->output->progressAdvance();
            }

            $this->output->progressFinish();
        }

        return;
    }

    /**
     * @param null|int $plotId
     * @param null|mixed $date
     *
     * @return null|float percent
     */
    protected function getCloudsPercentage($plotId = null, $date = null)
    {
        if (!$plotId) {
            return;
        }

        try {
            /** @var object $cloudsArea */
            $cloudsArea = $this->dbConn()->table(DB::raw('su_satellite_clouds as sc, su_satellite_plots as sp, su_satellite as s'))
                ->select(DB::raw('100.0*(ST_Area(ST_Intersection(ST_Union(st_makevalid(sc.geom)), st_makevalid(sp.geom)))/ST_Area(st_makevalid(sp.geom))) as percent'))
                ->where('s.id', '=', DB::raw('sc.tile_id::integer'))
                ->where('sp.gid', $plotId)
                ->where(DB::raw('s.date::DATE'), '=', $date)
                ->where(DB::raw('ST_Intersects(sc.geom, sp.geom)'), '=', true)
                ->groupBy('sp.gid')
                ->first();
            if ($cloudsArea) {
                return $cloudsArea->percent;
            }

            return;
        } catch (Exception $e) {
            $this->info('Clouds Percentage Calc Error: ' . $e->getMessage());

            return;
        }
    }

    protected function getLayerPlots()
    {
        $layerPlots = $this->dbConn()->table('su_satellite_layers_plots')
            ->select('id', 'plot_id', 'date');
        if ('all' !== $this->type) {
            $layerPlots->where('satellite_type', $this->type);
        }

        return $layerPlots->get();
    }

    protected function dbConn()
    {
        return DB::connection($this->server);
    }
}
