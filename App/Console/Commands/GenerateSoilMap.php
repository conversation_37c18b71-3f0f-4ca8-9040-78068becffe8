<?php

namespace App\Console\Commands;

use App\Services\Plot\PlotService;
use App\Services\System\SystemService;

class GenerateSoilMap extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:soil-map
                            {server : The server name}
                            {--plot_uuid= : The UUID of the plot}}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates soil map based on the plot uuid.';
    private $plotService;
    private $systemService;

    /**
     * Create a new command instance.
     */
    public function __construct(PlotService $plotService, SystemService $systemService)
    {
        parent::__construct();
        $this->plotService = $plotService;
        $this->systemService = $systemService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');
        $plotUuid = $this->option('plot_uuid');
        $this->loadCountry(strtoupper($server));

        $data = ['plot_uuid' => $plotUuid, 'country' => $server];
        $this->systemService->generateSoilMap($data);

        $this->info('Done');
    }
}
