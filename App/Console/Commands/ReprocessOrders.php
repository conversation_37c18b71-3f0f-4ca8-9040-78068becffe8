<?php

namespace App\Console\Commands;

use App\Models\Order;
use Exception;
use Illuminate\Console\Command;

class ReprocessOrders extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:reprocess-orders
                        {server : The server name} 
                        {--order_id= : The ID of order to be processed}
                        {--order_year= : The year of order to be processed}
                        {--order_type= : The type of order to be processed (index|soil)}
                        {--satellite_type= : The type of satellite images to be processed (rapideye|sentinel|landsat)}
                        {--organization_id= : The ID of organization\'s order to be processed}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reprocess orders.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');
        $this->satelliteType = $this->option('satellite_type');
        $this->orderType = $this->option('order_type');
        $this->orderId = (int)$this->option('order_id');
        $this->orderYear = (int)$this->option('order_year');
        $this->organizationId = (int)$this->option('organization_id');

        if (!$this->orderType && !$this->orderId && !$this->userId) {
            if (!$this->confirm('You are about to reprocess all orders. Type "-h" to see available options. Do you wish to continue?')) {
                return;
            }
        }

        $this->loadCountry(strtoupper($this->serverName));

        $ordersQuery = Order::select('id')
            ->orderBy('id', 'asc');

        if ($this->orderType) {
            $ordersQuery->where('type', $this->orderType);
        }

        if ($this->orderId) {
            $ordersQuery->where('id', $this->orderId);
        }

        if ($this->orderYear) {
            $ordersQuery->where('year', $this->orderYear);
        }

        if ($this->organizationId) {
            $ordersQuery->where('organization_id', $this->organizationId);
        }

        $orders = $ordersQuery->get();

        for ($i = 0; $i < count($orders); $i++) {
            $order = $orders[$i];

            try {
                $this->call('satellite:process-order', [
                    'server' => $this->serverName,
                    '--order_id' => $order->id,
                    '--satellite_type' => $this->satelliteType,
                    '--no-notifications' => true,
                    '--reprocess' => true,
                ]);
            } catch (Exception $e) {
                $this->error('Caught exception: ' . $e->getMessage() . PHP_EOL);
            }
        }
    }
}
