<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 02/05/2018
 * Time: 12:57.
 */

namespace App\Console\Commands;

use App\Traits\Country\TenantCountryAware;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class AbstractBaseCommand extends Command
{
    use TenantCountryAware;

    /**
     * @param string $connection
     *
     * @return \Doctrine\ORM\EntityManager
     */
    protected function getEntityManager($connection = null)
    {
        if (is_null($connection)) {
            $country = Config::get('globals.MACHINE');
            $connection = strtoupper($country);
        }
        $managerRegistry = app('registry');

        return $managerRegistry->getManager($connection);
    }
}
