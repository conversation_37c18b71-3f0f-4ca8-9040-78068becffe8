<?php

namespace App\Console\Commands;

use App\Classes\APN\APN;
use App\Classes\GCM\GCM;
use App\Models\API\Notifications;
use App\Models\LayerPlot;
use App\Models\LayerPlotFile;
use App\Models\Order;
use App\Models\OrderFile;
use App\Models\OrderPlotRel;
use App\Models\Plot;
use Config;
use Exception;
use File;
use Illuminate\Support\Facades\DB;
use View;

class ProcessOrder extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:process-order 
                        {server : The server name} 
                        {--order_id= : The ID of order to be processed}
                        {--order_type= : The type of order to be processed (index|soil)}
                        {--satellite_type= : The type of satellite images to be processed (rapideye|sentinel|landsat)}
                        {--organization_id= : The ID of organization\'s order to be processed}
                        {--no-notifications : Do not send notifications}
                        {--force : Force order to be processed}
                        {--reprocess : Reprocess order}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processing the client order.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');
        $this->orderId = (int)$this->option('order_id');
        $this->orderType = $this->option('order_type');
        $this->satelliteType = $this->option('satellite_type');
        $this->organizationId = (int)$this->option('organization_id');
        $this->noNotifications = $this->option('no-notifications');

        $this->loadCountry(strtoupper($this->serverName));

        $organizationIds = $this->setModelConn(new Order())
            ->where('status', 'processing')
            ->pluck('organization_id');

        if (!$this->option('force') && count($organizationIds) > 4) {
            return;
        }

        $orderQuery = $this->setModelConn(new Order())
            ->select('*', DB::raw("array_to_string(array[st_xmin(extent),st_ymin(extent),st_xmax(extent),st_ymax(extent)], ' ')  as extent"))
            ->whereNotIn('organization_id', $organizationIds)
            ->where('type', '!=', 'soil')
            ->where('type', '!=', 'vra')
            ->orderBy('date');

        if ($this->option('reprocess')) {
            $orderQuery->where('status', 'processed');
        } else {
            $orderQuery->where('status', 'paid');
        }

        if ($this->orderId) {
            $orderQuery->where('id', $this->orderId);
        }

        if ($this->orderType) {
            $orderQuery->where('type', $this->orderType);
        }

        if ($this->organizationId) {
            $orderQuery->where('organization_id', $this->organizationId);
        }

        /** @var Order $order */
        $order = $orderQuery->first();

        if (!$order) {
            return;
        }

        if ($order && 'meteo' == $order->type) {
            $this->call('meteo:process-meteo-order', [
                'server' => $this->serverName,
                'order_id' => $order->id,
                '--no-notifications' => ($this->option('no-notifications')) ? true : false,
                '--reprocess' => ($this->option('reprocess')) ? true : false,
            ]);

            return;
        }

        if ($order && 'soil' == $order->type) {
            return 'The script does not process soil orders.';
        }

        $order->status = 'processing';
        $order->save();

        $this->info('Processing Order ' . $order->id);

        $this->ORDER_QUEUE_PATH = Config::get('globals.ORDER_QUEUE_PATH') . $this->serverName . DIRECTORY_SEPARATOR . $order->id . DIRECTORY_SEPARATOR;
        $this->PROCESSED_ORDERS_PATH = Config::get('globals.PROCESSED_ORDERS_PATH') . $this->serverName . DIRECTORY_SEPARATOR;

        $this->createSystemDirs();

        $this->info('Getting tiles');

        $tiles = $this->getTiles($order->id);

        if (0 === count($tiles)) {
            $order->status = 'no_tile';
            $order->save();

            return;
        }

        $this->info('Found tiles ' . count($tiles));

        $ordered_plots = [];
        for ($i = 0; $i < count($tiles); $i++) {
            $tile = $tiles[$i];
            $layersPlots = [];

            $sourceImg = $this->getSourceImg($tile, $order->id);

            $this->info('Warping');
            $clippedImg = $this->clipTileByOrder($sourceImg, $order->id, $tile);

            $this->info('Calculating');

            if ('sentinel' === $tile->type || 'landsat' === $tile->type) {
                $sourceImg = $this->execGdalCalcNDVI($clippedImg);

                $sourceImgWater = '';
                if ($tile->date >= Config::get('globals.WATER_INDEX_DATE')) {
                    $sourceImgWater = $this->execGdalCalcNDWI($clippedImg);
                }
            } else {
                $sourceImg = $this->execGdalCalcRedEdge($clippedImg);
            }

            $plots = $this->setDBConn()->table('su_satellite_plots as sp')
                ->select(
                    DB::raw('s.date::date AS date'),
                    DB::raw('ST_Extent(sp.geom) AS extent'),
                    DB::raw('ST_Extent(ST_Transform(sp.geom, 3857)) AS extent_3857'),
                    DB::raw('ST_AsGeoJSON(ST_Simplify(sp.geom, 2), 6, 2) AS geom'),
                    DB::raw('(ST_XMax(ST_Extent(ST_Transform(sp.geom, 3857))) - ST_XMin(ST_Extent(ST_Transform(sp.geom, 3857)))) AS extent_width_3857'),
                    DB::raw('(ST_YMax(ST_Extent(ST_Transform(sp.geom, 3857))) - ST_YMin(ST_Extent(ST_Transform(sp.geom, 3857)))) AS extent_height_3857'),
                    'sp.gid'
                )
                ->join('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'sp.gid')
                ->join(
                    'su_satellite as s',
                    DB::raw('ST_Intersects(s.extent, ST_Transform(sp.geom, 4326))'),
                    '=',
                    DB::raw('true')
                )
                ->where(DB::raw('s.date::date'), '=', $tile->date)
                ->where('s.type', '=', $tile->type)
                ->where('sopr.order_id', '=', $order->id)
                ->groupBy(DB::raw('s.date::date'), 'sp.gid')
                ->get();

            $plotsCount = count($plots);

            $this->info("Making PNGs: Plots: {$plotsCount}");
            for ($j = 0; $j < count($plots); $j++) {
                $plot = $plots[$j];

                if (!File::exists($sourceImg) || !$plot->geom) {
                    continue;
                }

                $cloudPercent = $this->getCloudsPercentage($plot->gid, $tile->sat_ids);
                $this->info('Cloud Percentage: ' . $cloudPercent);

                $hasWaterPounds = $this->getHasWaterPounds($tile->date, $plot->gid);
                $this->info('Has Water pounds: ' . $hasWaterPounds);

                // Make Plot Png and Make Plot Stats
                $layersPlots[] = $this->indexActions(
                    $plot,
                    $order->id,
                    $tile,
                    $sourceImg,
                    $cloudPercent,
                    $hasWaterPounds
                );
                if ($sourceImgWater) {
                    $layersPlots[] = $this->indexWaterActions(
                        $plot,
                        $order->id,
                        $tile,
                        $sourceImgWater,
                        $cloudPercent,
                        $hasWaterPounds
                    );
                }

                $ordered_plots[$plot->gid] = strval($plot->gid);
            }

            if (File::exists($sourceImg)) {
                $this->info('Moving');
                $arrMovedImg = $this->moveOrderToFinished(
                    $sourceImg,
                    $sourceImgWater,
                    $order->id,
                    $tile->date,
                    $tile->type
                );
            }

            $plotIds = array_map(function ($plot) {
                return $plot['plot_id'];
            }, $layersPlots);

            $this->info('Deleting old layers plots: ' . count($plotIds));
            if (count($plotIds)) {
                $this->setModelConn(new LayerPlot())
                    ->where('order_id', $order->id)
                    ->whereIn('plot_id', $plotIds)
                    ->where('satellite_type', $tile->type)
                    ->where('date', $tile->date)
                    ->delete();
            }

            $this->info('Inserting the new layers plots: ' . count($layersPlots));

            DB::beginTransaction();

            try {
                foreach ($layersPlots as $layersPlot) {
                    /** @var LayerPlot $layerPlot */
                    $layerPlot = new LayerPlot();
                    $layerPlot->fill($layersPlot);
                    $layerPlot->stats = json_decode($layersPlot['stats']);
                    $layerPlot->save();

                    $layerPlotFile = new LayerPlotFile();
                    $layerPlotFile->fill($layersPlot);
                    $layerPlotFile->type = 'PNG';
                    $layerPlotFile->layerPlot()->associate($layerPlot);
                    $layerPlotFile->save();
                }

                DB::commit();
            } catch (Exception $exception) {
                DB::rollBack();

                throw $exception;
            }

            $satIds = array_values(
                array_filter(
                    array_unique(
                        explode(',', trim($tile->sat_ids, '{}'))
                    )
                )
            );

            $ordersTiles = [];
            for ($k = 0; $k < count($satIds); $k++) {
                $sId = $satIds[$k];

                $ordersTiles[] = [
                    'order_id' => $order->id,
                    'tile_id' => $sId,
                    'tile_date' => $tile->date,
                ];
            }

            if (count($satIds)) {
                $this->info('Deleting old orders tiles: ' . count($satIds));
                $this->setDBConn()->table('su_satellite_orderes_tiles')
                    ->whereIn('tile_id', $satIds)
                    ->where('tile_date', $tile->date)
                    ->where('order_id', $order->id)
                    ->delete();
            }

            $this->info('Inserting the new orders tiles: ' . count($satIds));
            $this->setDBConn()->table('su_satellite_orderes_tiles')->insert($ordersTiles);
        }
        $order->status = 'processed';
        $order->save();
        $this->removeOrderFromQueue();

        try {
            /** @var User[] $users */
            $users = $order->organization->users;

            foreach ($users as $user) {
                $noty = [];
                $noty['data'] = [];

                $noty['data']['plots'] = array_values($ordered_plots);
                $noty['data']['year'] = $order->year;
                $noty['data']['type'] = $order->type;
                $noty['group_id'] = $user->id;
                $noty['order_id'] = $order->id;

                $this->saveNotification($noty);
                $this->sendPushNotification($noty['group_id'], $noty['order_id']);
            }
        } catch (Exception $e) {
            $this->error('Error saving notification or sending push notification');
            echo $e;
        }

        $this->info('DONE');
    }

    public function getTiles($order_id)
    {
        $tilesQuery = $this->setDBConn()->table('su_satellite AS s')
            ->select(
                DB::raw('array_agg(DISTINCT(s.id)) as sat_ids'),
                DB::raw('s.date::date AS date'),
                's.type',
                DB::raw('MAX(s.date) AS date_time'),
                DB::raw('array_to_string("array_agg"(s.path), \',\') AS sat_image_paths')
            )
            ->join(
                'su_satellite_plots AS sp',
                DB::raw('ST_Intersects(ST_Transform(s.extent, ' . Config::get('globals.DEFAULT_DB_CRS') . '), sp.geom)'),
                '=',
                DB::raw('true')
            )
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'sp.gid')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->where('sopr.order_id', '=', $order_id)
            ->where('s.type', '=', 'sentinel')
            ->where(DB::raw('s.date::date'), '>=', DB::raw('so.from_date'))
            ->where(DB::raw('s.date::date'), '<=', DB::raw('so.to_date'))
            ->groupBy(DB::raw('s.date::date, s.type'))
            ->orderBy(DB::raw('s.date::date'), 'DESC');

        return $tilesQuery->get();
    }

    /**
     * Creates a chart image with the NDVI stats of the index image.
     *
     * @param string $tileImg the image with calculated index layer
     * @param string $polygonWKT the polygon in WKT format for which we want to calclulate the index stats
     * @param string $satelliteType The index type (rapideye | sentinel | landsat)
     *
     * @throws Exception
     *
     * @return object the stats of the image as object
     */
    public function makePlotStats($tileImg, $polygonWKT, $satelliteType = 'rapideye')
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');
        // dd($polygonWKT);

        $command = "{$pythonPath} {$scriptsPath}plot_stats.py {$tileImg} '{$polygonWKT}' --area_coef={$areaCoef} --satellite_type={$satelliteType} --proj={$defaultProj}";

        $this->info($command);
        exec($command . ' 2>&1', $output);
        // dd($output);
        try {
            // checking if the output is a valid json.
            return json_decode($output[0]);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Creates an RGB Png file from the shp2img.map file.
     *
     * @param object $plot
     * @param int $userId
     * @param int $orderId
     * @param object $tile
     * @param string $sourceImg
     * @param int $cloudPercent
     * @param bool $hasWaterPounds
     *
     * @return array
     */
    protected function indexActions($plot, $orderId, $tile, $sourceImg, $cloudPercent, $hasWaterPounds)
    {
        $plotPng = $this->makePlotPng($plot, $tile->date, $sourceImg, $tile->type);

        $plotStatsData = $this->makePlotStats($sourceImg, $plot->geom, $tile->type);

        $sopr = OrderPlotRel::where([['plot_id', $plot->gid], ['order_id', $orderId]])->firstOrFail();

        $mean = null;
        $stats = null;

        if (isset($plotStatsData->stats)) {
            $stats = json_encode($plotStatsData->stats);
        }

        if (isset($plotStatsData->mean)) {
            $mean = json_encode($plotStatsData->mean);
        }

        $isViewed = false;
        if ($this->noNotifications) {
            $isViewed = true;
        }

        return [
            'plot_id' => $plot->gid,
            'layer_name' => "{$tile->type}_ndvi_raster_{$tile->date}",
            'date' => $tile->date,
            'date_time' => $tile->date_time,
            'stats' => $stats,
            'mean' => $mean,
            'created_at' => date('Y-m-d H:i:s'),
            'order_id' => $orderId,
            'type' => 'index',
            'satellite_type' => $tile->type,
            'is_viewed' => $isViewed,
            'clouds_percent' => $cloudPercent,
            'has_water_pounds' => $hasWaterPounds,
            'path' => $plotPng['path'],
            'web_path' => $plotPng['web_path'],
            'element' => 'NDVI_N',
            'sopr_id' => $sopr->id,
        ];
    }

    /**
     * Creates an RGB Png file from the shp2img.map file.
     *
     * @param object $plot
     * @param int $orderId
     * @param object $tile
     * @param string $sourceImgWater
     * @param int $cloudPercent
     * @param bool $hasWaterPounds
     *
     * @return array
     */
    protected function indexWaterActions($plot, $orderId, $tile, $sourceImgWater, $cloudPercent, $hasWaterPounds)
    {
        $plotPng = $this->makePlotPng($plot, $tile->date, $sourceImgWater, $tile->type, 'index_water');

        $plotStatsWaterData = $this->makePlotStats($sourceImgWater, $plot->geom, $tile->type . '_water');

        $sopr = OrderPlotRel::where([['plot_id', $plot->gid], ['order_id', $orderId]])->firstOrFail();

        $mean = null;
        $stats = null;

        if (isset($plotStatsWaterData->stats)) {
            $stats = json_encode($plotStatsWaterData->stats);
        }

        if (isset($plotStatsWaterData->mean)) {
            $mean = json_encode($plotStatsWaterData->mean);
        }

        $isViewed = false;
        if ($this->noNotifications) {
            $isViewed = true;
        }

        return [
            'plot_id' => $plot->gid,
            'layer_name' => "{$tile->type}_water_ndwi_raster_{$tile->date}",
            'date' => $tile->date,
            'date_time' => $tile->date_time,
            'stats' => $stats,
            'mean' => $mean,
            'created_at' => date('Y-m-d H:i:s'),
            'order_id' => $orderId,
            'type' => 'index_water',
            'satellite_type' => $tile->type,
            'is_viewed' => $isViewed,
            'clouds_percent' => $cloudPercent,
            'has_water_pounds' => $hasWaterPounds,
            'path' => $plotPng['path'],
            'web_path' => $plotPng['web_path'],
            'element' => 'NDWI',
            'sopr_id' => $sopr->id,
        ];
    }

    /**
     * @param null|int $plotId
     * @param null|array $tileIds
     *
     * @return null|float percent
     */
    protected function getCloudsPercentage($plotId = null, $tileIds = null)
    {
        if (!$plotId || !$tileIds) {
            # TODO Log error
            $this->error('No plotId or tileIds');

            return;
        }

        try {
            /** @var array $ids */
            $ids = explode(',', substr($tileIds, 1, -1));
            /** @var object $cloudsArea */
            $cloudsArea = $this->setDBConn()->table(DB::raw('su_satellite_clouds as sc, su_satellite_plots as sp'))
                ->select(DB::raw('100.0*(ST_Area(ST_Intersection(ST_Union(st_makevalid(sc.geom)), st_makevalid(sp.geom)))/ST_Area(st_makevalid(sp.geom))) as percent'))
                ->where('sp.gid', $plotId)
                ->whereIn('sc.tile_id', $ids)
                ->where(DB::raw('ST_Intersects(sc.geom, sp.geom)'), '=', true)
                ->groupBy('sp.gid')
                ->first();

            if ($cloudsArea) {
                return $cloudsArea->percent;
            }

            return;
        } catch (Exception $e) {
            $this->info('Clouds Percentage Calc Error: ' . $e->getMessage());

            return;
        }
    }

    /**
     * @param null|int $plotId
     * @param date $date
     *
     * @return null|bool $hasWaterPounds
     */
    protected function getHasWaterPounds($date, $plotId = null)
    {
        return false;
        if (!$plotId || !$date) {
            # TODO Log error
            $this->error('No plotId or date');

            return false;
        }

        try {
            $waterIntersection = $this->setDBConn()->table(DB::raw('su_satellite_plots as sp'))
                ->select(DB::raw('sp.gid'))
                ->join(
                    'su_satellite_water_pounds AS wp',
                    DB::raw('ST_Intersects(ST_Transform(wp.geom, ' . Config::get('globals.DEFAULT_DB_CRS') . '), sp.geom)'),
                    '=',
                    DB::raw('true')
                )
                ->where('sp.gid', $plotId)
                ->where('wp.date', $date)
                ->groupBy('sp.gid')
                ->first();

            return (bool)($waterIntersection);
        } catch (Exception $e) {
            $this->info('Has Water pounds Error: ' . $e->getMessage());

            return false;
        }
    }

    protected function createSystemDirs()
    {
        if (!is_dir($this->ORDER_QUEUE_PATH)) {
            mkdir($this->ORDER_QUEUE_PATH, 0700, true);
        }

        if (!is_dir($this->PROCESSED_ORDERS_PATH)) {
            mkdir($this->PROCESSED_ORDERS_PATH, 0700, true);
        }
    }

    /**
     * Creates an RGB Png file from the shp2img.map file.
     *
     * @param object $plot
     * @param string $date the tile date
     * @param string $inputImg the GeoTIFF with calculated index
     * @param string $satelliteType The index type (rapideye | sentinel | landsat)
     *
     * @return string The file name of the generated image
     */
    protected function makePlotPng($plot, $date, $inputImg = '', $satelliteType = 'rapideye', $type = 'index')
    {
        $fileName = 'plot_' . $plot->gid . '_' . $date . '.png';
        if ('index_water' == $type) {
            $fileName = 'plot_' . $plot->gid . '_' . $date . '_water.png';
        }
        $outName = $this->ORDER_QUEUE_PATH . $fileName;

        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $colorTable = storage_path() . DIRECTORY_SEPARATOR . 'color_table' . DIRECTORY_SEPARATOR . "{$type}.txt";
        $command = "{$pythonPath} {$scriptsPath}gs_shp2img.py {$inputImg} {$outName} -m '{$plot->geom}' -c {$colorTable} -w 200";

        $this->info($command);
        $output = [];
        exec($command . ' 2>&1', $output);

        $commonPath = $satelliteType . DIRECTORY_SEPARATOR . 'plots' . DIRECTORY_SEPARATOR . $date . DIRECTORY_SEPARATOR . $fileName;

        // path where files will be moved after processing
        $path = $this->PROCESSED_ORDERS_PATH . $commonPath;
        $webPath = $this->serverName . DIRECTORY_SEPARATOR . $commonPath;

        return [
            'path' => $path,
            'web_path' => $webPath,
        ];
    }

    protected function clipTileByOrder(string $sourceImage, $orderId, $tile)
    {
        $dbHost = Config::get('database.connections.' . $this->serverName . '.host');
        $dbUser = Config::get('database.connections.' . $this->serverName . '.username');
        $dbPass = Config::get('database.connections.' . $this->serverName . '.password');
        $dbName = Config::get('database.connections.' . $this->serverName . '.database');
        $dbPort = Config::get('database.connections.' . $this->serverName . '.port');
        $srid = Config::get('globals.DEFAULT_DB_CRS');
        $cutline = "PG:\"dbname='{$dbName}' host='{$dbHost}' port='{$dbPort}' user='{$dbUser}' password='{$dbPass}'\"";

        $sql = Plot::selectRaw('ST_Union(st_makevalid(su_satellite_plots.geom)) AS geom')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->join('su_satellite as ss', function ($join) use ($srid) {
                $join->whereRaw('ST_Intersects(ST_Transform(ss.extent, ' . $srid . '), su_satellite_plots.geom)');
            })
            ->where('so.id', $orderId)
            ->whereRaw("ss.date::date = '?'", [$tile->date])
            ->toSqlWithBindings();

        $outImgSuffix = $tile->type . '_' . $tile->date;

        return $this->execGdalWarpCommand($sourceImage, $cutline, $sql, $outImgSuffix);
    }

    protected function execGdalWarpCommand($sourceImg, $cutline, $cutlineSql = '', $outImgSuffix = 0)
    {
        $imgNfo = pathinfo($sourceImg);
        $outImg = $this->ORDER_QUEUE_PATH . $imgNfo['filename'] . '_clipped';
        if ($outImgSuffix) {
            $outImg .= '_' . $outImgSuffix;
        }
        $outImg .= '.tiff';

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdalwarp -multi --config GDALWARP_IGNORE_BAD_CUTLINE YES --config GDAL_CACHEMAX 2000 -wm 2000 -of GTiff -dstnodata -999 -cutline {$cutline} -csql \"{$cutlineSql}\" -crop_to_cutline -dstalpha {$sourceImg} {$outImg}";
        $this->info($command);
        system($command);

        return $outImg;
    }

    protected function execGdalCalcRedEdge($sourceImg)
    {
        $imgNfo = pathinfo($sourceImg);
        $outImg = $this->ORDER_QUEUE_PATH . $imgNfo['filename'] . '_calc.' . $imgNfo['extension'];

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdal_calc.py --co=\"COMPRESS=DEFLATE\" --co=\"PREDICTOR=1\" --co=\"TILED=YES\" --A_band=5 --B_band=4 --calc=\"(A.astype(float32)/(B.astype(float32))-full(A.shape, 1.0))\" --type=Float32 -A {$sourceImg} -B {$sourceImg} --outfile={$outImg} --overwrite --NoDataValue=-999";

        $this->info($command);
        system($command);

        return $outImg;
    }

    protected function execGdalCalcNDVI($sourceImg)
    {
        $imgNfo = pathinfo($sourceImg);
        $outImg = $this->ORDER_QUEUE_PATH . $imgNfo['filename'] . '_calc.' . $imgNfo['extension'];

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdal_calc.py --co=\"COMPRESS=DEFLATE\" --co=\"PREDICTOR=1\" --co=\"TILED=YES\" --A_band=4 --B_band=3 --calc=\"((A.astype(float32) - B.astype(float32))/(A.astype(float32) + B.astype(float32)))\" --type=Float32 -A {$sourceImg} -B {$sourceImg} --outfile={$outImg} --overwrite --NoDataValue=-999";

        $this->info($command);
        system($command);

        return $outImg;
    }

    protected function execGdalCalcNDWI($sourceImg)
    {
        $imgNfo = pathinfo($sourceImg);
        $outImg = $this->ORDER_QUEUE_PATH . $imgNfo['filename'] . '_calc_water.' . $imgNfo['extension'];

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdal_calc.py --co=\"COMPRESS=DEFLATE\" --co=\"PREDICTOR=1\" --co=\"TILED=YES\" --A_band=4 --B_band=5 --calc=\"((A.astype(float32) - B.astype(float32))/(A.astype(float32) + B.astype(float32)))\" --type=Float32 -A {$sourceImg} -B {$sourceImg} --outfile={$outImg} --overwrite --NoDataValue=-999";

        $this->info($command);
        system($command);

        return $outImg;
    }

    protected function createUserMapHeadFile($userId)
    {
        $mapFile = $this->MAP_FILE_PATH . $userId . '.map';
        if (file_exists($mapFile)) {
            return;
        }

        $mapStr = View::make('map_head', [
            'wmsserver' => Config::get('globals.WMS_SERVER'),
            'wmssrs' => Config::get('globals.WMS_SRS'),
            'wfsserver' => Config::get('globals.WMS_SERVER'),
            'wfssrs' => Config::get('globals.WMS_SRS'),
            'con' => Config::get('database.connections'),
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
        ])
            ->render();

        file_put_contents($mapFile, $mapStr);
    }

    protected function removeOrderFromQueue()
    {
        system('/bin/rm -r ' . $this->ORDER_QUEUE_PATH);
    }

    protected function moveOrderToFinished($sourceImg, $sourceImgWater, $orderId, $date, $satelliteType)
    {
        $path = $this->PROCESSED_ORDERS_PATH . $satelliteType . DIRECTORY_SEPARATOR;

        $imagePath = $path . $date . '_' . $orderId . '.tif';

        $orderFile = new OrderFile();
        $orderFile->path = $imagePath;
        $orderFile->date = $date;
        $orderFile->layer_type = 'index';
        $orderFile->type = 'TIFF';
        $orderFile->satellite_type = $satelliteType;
        $orderFile->order()->associate(Order::find($orderId));
        $orderFile->save();

        $imagePathWater = '';
        if ($sourceImgWater) {
            $imagePathWater = $path . $date . '_' . $orderId . '_water.tif';
            $orderFileWater = new OrderFile();
            $orderFileWater->path = $imagePathWater;
            $orderFileWater->date = $date;
            $orderFileWater->layer_type = 'index_water';
            $orderFileWater->type = 'TIFF';
            $orderFileWater->satellite_type = $satelliteType;
            $orderFileWater->order()->associate(Order::find($orderId));
            $orderFileWater->save();
        }

        $this->info($imagePath);
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }

        system("/bin/cp {$sourceImg} {$imagePath}");
        if ($sourceImgWater) {
            system("/bin/cp {$sourceImgWater} {$imagePathWater}");
        }

        $destPath = $path . 'plots/' . $date;
        $plotsPath = $this->ORDER_QUEUE_PATH . "plot_*_{$date}.png";
        $plotsPathWater = $this->ORDER_QUEUE_PATH . "plot_*_{$date}_water.png";
        if (!file_exists($destPath)) {
            mkdir($destPath, 0755, true);
        }

        system("/bin/cp {$plotsPath} {$destPath}");
        system("/bin/cp {$plotsPathWater} {$destPath}");

        system("/bin/chmod 755 {$this->PROCESSED_ORDERS_PATH}");
        system("/bin/chmod 755 {$path}");
        system("/bin/chmod 755 {$path}/plots/");
        system("/bin/chmod 755 {$path}/plots/{$date}");

        return ['index' => $imagePath, 'index_water' => $imagePathWater];
    }

    /**
     * Returns the raster projection in the following format `array('proj' => '', 'zone' => '', 'datum' => '', 'units' => '')`.
     *
     * @param string $sourceImg the source image file
     *
     * @return array
     */
    protected function getFileProjection($sourceImg)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdalinfo -proj4 -json -nogcp -nomd -norat -noct {$sourceImg}";
        $out = '';
        exec($command . ' 2>&1', $out);
        $jsonStr = implode("\n", $out);
        $tiffInfo = json_decode($jsonStr, true);

        $projRegEx = "/\+proj=(?P<proj>[\w]+) \+zone=(?P<zone>[\d]+)(?:\s\+(?<pole>[\w]+))? \+datum=(?P<datum>[\w]+) \+units=(?P<units>[\w]+)/";
        preg_match_all($projRegEx, $tiffInfo['coordinateSystem']['proj4'], $projData);

        return $projData;
    }

    protected function reprojectRasterToDefault($sourceImg, $outImgSuffix = 0)
    {
        $imgNfo = pathinfo($sourceImg);
        $fname = $imgNfo['filename'];
        $utmZone = Config::get('globals.DEFAULT_UTM_ZONE');
        $geoPole = Config::get('globals.GEO_POLE');
        $proj35N = '+proj=utm +zone=' . $utmZone . ' +ellps=WGS84 +datum=WGS84 +units=m +no_defs';

        if ('south' == $geoPole) {
            $proj35N = '+proj=utm +zone=' . $utmZone . ' +south +ellps=WGS84 +datum=WGS84 +units=m +no_defs';
        }

        $outImg = $this->ORDER_QUEUE_PATH . $fname . '_reproj';
        if ($outImgSuffix) {
            $outImg .= '_' . $outImgSuffix;
        }
        $outImg .= '.vrt';

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdalwarp -of VRT -multi --config GDAL_CACHEMAX 2000 -wm 2000 -t_srs \"{$proj35N}\" {$sourceImg} {$outImg}";

        $this->info($command);
        system($command);

        return $outImg;
    }

    protected function setDBConn()
    {
        return DB::connection($this->serverName);
    }

    protected function setModelConn($model)
    {
        return $model::on($this->serverName);
    }

    /**
     * Save notification for user with processed order.
     *
     * @param array $noty pushNotification to save
     */
    protected function saveNotification($noty)
    {
        if ($this->option('no-notifications')) {
            return;
        }

        $notyData = new Notifications();
        $notyData->setConnection($this->serverName);
        $notyData->group_id = $noty['group_id'];
        $notyData->title = trans('notifications.processed_order', ['orderId' => $noty['order_id']], Config::get('app.locale'));
        $notyData->message = trans('notifications.processed_order_details', ['orderId' => $noty['order_id']], Config::get('app.locale'));
        $notyData->data = json_encode($noty['data']);
        $notyData->type = 'processed_order';
        $notyData->is_pushed = true;
        $notyData->save();
    }

    /**
     * Send push notification to user with with processed order.
     *
     * @param int $groupId user group id
     * @param int $orderId order id
     */
    protected function sendPushNotification($groupId, $orderId)
    {
        if ($this->option('no-notifications')) {
            return;
        }

        $title = trans('notifications.processed_order', ['orderId' => $orderId], Config::get('app.locale'));
        $message = trans('notifications.processed_order_details', ['orderId' => $orderId], Config::get('app.locale'));
        $data['notId'] = time();

        // send notifications to android devices
        $gcm_device_keys = $this->setDBConn()
            ->table('su_users_device_keys')
            ->select('device_key')
            ->where('device_platform', '=', 'android')
            ->where('group_id', '=', $groupId)
            ->get()->toArray();

        $gcm_devices = array_map(function ($el) {
            return $el->device_key;
        }, $gcm_device_keys);

        $gcm_push = new GCM();
        $gcm_push->setDevices($gcm_devices);
        $gsm_result = $gcm_push->send($title, $message, $data);
        $this->info($gsm_result);

        // send notifications to ios devices
        $apn_device_keys = $this->setDBConn()
            ->table('su_users_device_keys')
            ->select('device_key')
            ->where('device_platform', '=', 'ios')
            ->where('group_id', '=', $groupId)
            ->get()->toArray();

        $apn_devices = array_map(function ($el) {
            return $el->device_key;
        }, $apn_device_keys);

        $apn_push = new APN();
        $apn_push->setDevices($apn_devices);
        $apn_result = $apn_push->send($title, $message);
        $this->info($apn_result);
    }

    /**
     * The method uses the 'gdalbuildvrt' function for creating a virtual raster
     * This is a way faster than merging the tiles physically.
     *
     * @param string $id a unique string for naming the result tile
     * @param string [$outPath='']     A custom path for saving the image
     *
     * @return string the path of the VRT
     */
    protected function execGdalVirtualMerge(array $imgsToMerge, $id, $outPath = '')
    {
        $images = implode(' ', array_unique($imgsToMerge));
        $outName = $this->ORDER_QUEUE_PATH . $id . '.vrt';
        if ($outPath) {
            $outName = $outPath . $id . '.vrt';
        }

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdalbuildvrt -srcnodata \"0 0 0 0 0\" {$outName} {$images}";

        $this->info($command);
        system($command);

        return $outName;
    }

    /**
     * Get source image path.
     *
     * @return null|string
     */
    protected function getSourceImg($tile, $orderId)
    {
        $notExistingTiles = array_filter(explode(',', $tile->sat_image_paths), function ($path) {
            return !is_dir($path);
        });
        if (count($notExistingTiles)) {
            return;
        }
        $tilesPaths = array_map(function ($path) use ($tile) {
            if ('sentinel' === $tile->type) {
                return $path . DIRECTORY_SEPARATOR . 'RGBNIR.vrt';
            }
            if ('landsat' === $tile->type) {
                return $path . DIRECTORY_SEPARATOR . 'RGBNIR_15x15.vrt';
            }

            return $path . '.tif';
        }, explode(',', $tile->sat_image_paths));

        $existingTilesPaths = array_values(array_filter($tilesPaths, function ($path) {
            return is_file($path);
        }));

        if (0 === count($existingTilesPaths)) {
            return;
        }

        $utmZoneDefault = Config::get('globals.DEFAULT_UTM_ZONE');

        for ($j = 0; $j < count($existingTilesPaths); $j++) {
            $sourceFileProj = $this->getFileProjection($existingTilesPaths[$j]);

            if (isset($sourceFileProj['zone']) && (int)$sourceFileProj['zone'][0] != $utmZoneDefault) {
                $this->info('Reprojecting');
                $existingTilesPaths[$j] = $this->reprojectRasterToDefault(
                    $existingTilesPaths[$j],
                    $tile->type . '_' . $tile->date . '_' . $j
                );
            }
        }

        $tiffFiles = array_filter($existingTilesPaths, function ($img) {
            return (bool)(ends_with(strtolower($img), '.tif'));
        });

        // In case there are tiff files and not all files are in tiff format convert tiff to vrt
        if ($tiffFiles && count($tiffFiles) < count($existingTilesPaths)) {
            foreach ($tiffFiles as $key => $imgPath) {
                $existingTilesPaths[$key] = $this->convertTiffToVrt($imgPath, $tile->type . '_' . $tile->date . '_' . $key);
            }
        }

        return $this->execGdalVirtualMerge($existingTilesPaths, $tile->type . '_' . $tile->date . '_' . $orderId);
    }

    /**
     * Converts file from tiff to vrt format using "gdal_translate".
     *
     * @param int $outImgSuffix
     *
     * @return string the path of the VRT
     */
    protected function convertTiffToVrt($sourceImg, $outImgSuffix = 0)
    {
        $imgNfo = pathinfo($sourceImg);
        $fname = $imgNfo['filename'];
        $outImg = $this->ORDER_QUEUE_PATH . $fname . '_converted';
        if ($outImgSuffix) {
            $outImg .= '_' . $outImgSuffix;
        }
        $outImg .= '.vrt';

        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $command = "{$gdalBinPath}gdal_translate -of VRT {$sourceImg} {$outImg}";

        $this->info($command);
        system($command);

        return $outImg;
    }
}
