<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncSatellite extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:sync-satellite {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sentinel and Landsat Images.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $serverName = $this->argument('server');

        $this->loadCountry(strtoupper($serverName));

        $this->info('Sync Sentinel Images');
        $this->call('satellite:sync-sentinel', [
            'server' => $serverName,
        ]);

        $path = base_path() . DIRECTORY_SEPARATOR;
        $logPath = storage_path() . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR;

        $addRasterLayersCommand = "php {$path}artisan satellite:add-raster-layers {$serverName} >> {$logPath}add-raster-layers.log 2>&1 &";
        $this->info("Exec command in background: {$addRasterLayersCommand}");
        exec($addRasterLayersCommand);

        $this->info('Process Existing Orders');
        $processExistingOrderCommand = "php {$path}artisan satellite:process-existing-order {$serverName} >> {$logPath}process-existing-order.log 2>&1 &";
        $this->info("Exec command in background: {$processExistingOrderCommand}");
        exec($processExistingOrderCommand);
    }
}
