<?php

namespace App\Console\Commands;

use App;
use App\Models\OrderPlotRel;
use File;
use Illuminate\Console\Command;

class ImportAgrobalanceSamples extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agrobalance:import-samples-file {server : The server name} {fileName}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Imports samples data from the agrobalance app. The file must be in the storage/files/ folder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');
        $fileName = $this->argument('fileName');

        $this->loadCountry(strtoupper($server));

        $fullFileName = storage_path() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . $fileName;
        // search for the file in storage/files
        $this->info('Opening file!');

        try {
            $contents = File::get($fullFileName);
        } catch (Illuminate\Filesystem\FileNotFoundException $exception) {
            $this->info('The file does not exist!');
            die();
        }

        $importData = json_decode($contents);

        $formattedData = [];
        foreach ($importData as $order_id => $order) {
            foreach ($order as $plot_id => $plotData) {
                foreach ($plotData as $PointNumber => $sampleData) {
                    $sampleData->track = $sampleData->track->d3;
                    $sopr_id = $this->getSoprID($order_id, $plot_id);
                    $formattedData[] = (object)[
                        'order_id' => $order_id,
                        'plot_id' => $plot_id,
                        'sopr_id' => $sopr_id,
                        'sample_data' => (object)[
                            'samples' => $sampleData,
                        ],
                    ];
                }
            }
        }

        App::make('App\Http\Controllers\API\ApiController')->updateSoilSamplesData($formattedData);
    }

    private function getSoprID($order_id, $plot_id)
    {
        $soprId = OrderPlotRel::select('id')
            ->where('order_id', $order_id)
            ->where('plot_id', $plot_id)
            ->get()->toArray();

        return $soprId[0]['id'];
    }
}
