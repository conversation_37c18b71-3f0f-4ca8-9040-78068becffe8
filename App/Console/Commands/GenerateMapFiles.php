<?php

namespace App\Console\Commands;

use App\Events\GenerateAllPlotsLayerMapFileEvent;
use App\Events\GenerateUserMapFileEvent;
use App\Events\GenerateWorkLayerMapFileEvent;

class GenerateMapFiles extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:generate-map-files 
                            {server : The server name}
                            {--run=all : Which map files to generate (all|LayerAllPlotsMapFile)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates user map files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $server = $this->argument('server');
        $this->loadCountry(strtoupper($server));

        $run = $this->option('run');

        switch ($run) {
            case 'all':
                event(new GenerateUserMapFileEvent());
                event(new GenerateWorkLayerMapFileEvent());
                event(new GenerateAllPlotsLayerMapFileEvent());

                break;
            case 'LayerAllPlotsMapFile':
                event(new GenerateAllPlotsLayerMapFileEvent());

                break;
        }
    }
}
