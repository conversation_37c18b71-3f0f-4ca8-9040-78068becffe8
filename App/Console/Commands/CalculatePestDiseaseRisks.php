<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Console\Commands;

use App;
use App\Classes\PestsDisease\RiskCalculator;
use App\Entities\Local\Plot;
use DateTime;
use Exception;

class CalculatePestDiseaseRisks extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pests-disease:calculate-risk {server : The server name} {--date= : Date in format YYYY-MM-DD}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';
    /**
     * @var string
     */
    private $server;

    /**
     * @var DateTime
     */
    private $date;

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
        $this->date = new DateTime();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        echo "Start calculations.\n";

        $this->server = $this->argument('server');

        if ($this->option('date')) {
            $this->date = DateTime::createFromFormat('Y-m-d', $this->option('date'));
        }

        $this->loadCountry(strtoupper($this->server));

        /** @var App\Entities\Local\Order[] $activeMeteoOrders */
        $activeMeteoOrders = $this->getActiveMeteoOrders();

        echo 'Found ' . count($activeMeteoOrders) . " orders.\n";

        echo "Processing...\n";

        foreach ($activeMeteoOrders as $activeMeteoOrder) {
            $orderPlotRelations = $activeMeteoOrder->getOrderPlotRelations();
            foreach ($orderPlotRelations as $orderPlotRelation) {
                $plot = $orderPlotRelation->getPlot();
                if (!empty($plot->getPlotCrops())) {
                    $this->calculate($orderPlotRelation->getPlot());
                }
            }
        }

        echo "Done.\n";

        return true;
    }

    private function calculate(Plot $plot)
    {
        /** @var RiskCalculator $riskCalculator */
        $riskCalculator = App::make(RiskCalculator::class);

        try {
            $riskCalculator->calculate($plot, $this->date);
        } catch (Exception $e) {
            echo $e->getMessage();
            echo "\n";
            if (!$e instanceof App\Exceptions\PestDisease\GeneralException) {
                echo $e->getTraceAsString();
                echo "\n";
            }
        }

        return true;
    }

    private function getActiveMeteoOrders()
    {
        $em = $this->getEntityManager();
        /** @var App\Entities\Local\OrderRepository $ordersRepository */
        $ordersRepository = $em->getRepository(App\Entities\Local\Order::class);

        return $ordersRepository->findAllActiveMeteo();
    }
}
