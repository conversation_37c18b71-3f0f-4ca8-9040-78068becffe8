<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Http\UploadedFile;

class ValidateGridUploadedFile implements Rule
{
    private static $allowedMimeTypes = [
        'application/zip',
        'application/x-zip',
        'application/x-zip-compressed',
    ];

    private $uploadedFile;

    /**
     * Create a new rule instance.
     */
    public function __construct(UploadedFile $uploadedFile)
    {
        $this->uploadedFile = $uploadedFile;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return in_array($this->uploadedFile->getClientMimeType(), self::$allowedMimeTypes);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The file must be a file of type: zip';
    }
}
