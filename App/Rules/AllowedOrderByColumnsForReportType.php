<?php

namespace App\Rules;

use App\Models\ScheduledReport;
use Illuminate\Contracts\Validation\Rule;

class AllowedOrderByColumnsForReportType implements Rule
{
    /**
     * @var null|string
     */
    private $reportType;

    /**
     * @var string
     */
    private $attribute;

    /**
     * Create a new rule instance.
     *
     * @param ?string $reportType
     */
    public function __construct(?string $reportType)
    {
        $this->reportType = $reportType;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     */
    public function passes($attribute, $value): bool
    {
        $this->attribute = $attribute;

        if (!is_array($value)) {
            return false;
        }

        foreach ($value as $orderColumn => $orderDirection) {
            if (!in_array($orderColumn, ScheduledReport::getAllowedOrderByColumnsFor($this->reportType))) {
                return false;
            }

            if (!in_array($orderDirection, ['asc', 'desc'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        $allowedColumnsMessage = filled(ScheduledReport::getAllowedOrderByColumnsFor($this->reportType))
            ? implode(',', ScheduledReport::getAllowedOrderByColumnsFor($this->reportType))
                . ' and order directions asc, desc'
            : "None allowed columns for report type: {$this->reportType}";

        $message = "One or more of the {$this->attribute} is not allowed. Allowed order columns are: "
            . $allowedColumnsMessage;

        if (blank($this->reportType)) {
            $message = "You must provide the type for the report if you want to use {$this->attribute}. Allowed types are: "
                . implode(',', ScheduledReport::getAllReportTypes());
        }

        if (!in_array($this->reportType, ScheduledReport::getAllReportTypes())) {
            $message = "You must provide a valid type for the report if you want to use {$this->attribute}. Allowed types are: "
                . implode(',', ScheduledReport::getAllReportTypes());
        }

        return $message;
    }
}
