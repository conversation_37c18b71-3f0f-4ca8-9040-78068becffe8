<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Http\UploadedFile;

class ValidateBoundaryUploadedFile implements Rule
{
    private static $allowedMimeTypes = [
        'application/vnd.google-earth.kml+xml',
        'application/vnd.google-earth.kmz',
        'application/zip',
        'application/x-zip',
        'application/x-zip-compressed',
        'text/xml',
    ];

    private $uploadedFile;

    /**
     * Create a new rule instance.
     */
    public function __construct(UploadedFile $uploadedFile)
    {
        $this->uploadedFile = $uploadedFile;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return in_array($this->uploadedFile->getClientMimeType(), self::$allowedMimeTypes)
            || in_array(mime_content_type($value->path()), self::$allowedMimeTypes);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The file must be a file of type: zip, kml, kmz.';
    }
}
