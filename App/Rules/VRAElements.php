<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Config;

class VRAElements implements Rule
{
    private $element;

    /**
     * Create a new rule instance.
     */
    public function __construct() {}

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $soilElements = array_keys(Config::get('globals.ELEMENT_CLASSES.soil'));
        $satelliteElements = array_keys(Config::get('globals.ELEMENT_CLASSES.satellite'));
        $this->element = $value;

        return in_array($value, array_merge($soilElements, $satelliteElements));
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return "Invalid VRA element '{$this->element}'!";
    }
}
