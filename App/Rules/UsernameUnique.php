<?php

namespace App\Rules;

use App\Models\GlobalUser;
use Illuminate\Contracts\Validation\Rule;

class UsernameUnique implements Rule
{
    /**
     * Create a new rule instance.
     */
    public function __construct() {}

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return 0 === GlobalUser::where('username', $value)->count();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The Username already exists';
    }
}
