<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ArrayStringKeys implements Rule
{
    /**
     * Create a new rule instance.
     */
    public function __construct() {}

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     */
    public function passes($attribute, $value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        $allKeysCount = count(array_keys($value));
        $stringKeysCount = count(array_filter(array_keys($value), 'is_string'));

        return $stringKeysCount === $allKeysCount;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return "The keys of ':attribute' parameter must be of type string";
    }
}
