<?php

namespace App\Providers;

use App\Classes\Meteo\MeteoBlue;
use App\Classes\Meteo\MeteoCache;
use Config;
use Illuminate\Support\ServiceProvider;

class MeteoBlueServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(MeteoBlue::class, function () {
            $heap = $this->app->make('App\Classes\Heap');

            $configMeteoBlue = Config::get('meteo');
            $meteoCache = new MeteoCache($configMeteoBlue, $heap);

            return new MeteoBlue($meteoCache, $heap);
        });
    }
}
