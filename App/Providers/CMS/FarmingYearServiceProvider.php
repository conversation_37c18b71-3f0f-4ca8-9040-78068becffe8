<?php

namespace App\Providers\CMS;

use App\Classes\CMS\FarmingYearService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class FarmingYearServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(FarmingYearService::class, function ($app) {
            $request = app(Request::class);

            return new FarmingYearService($request);
        });
    }
}
