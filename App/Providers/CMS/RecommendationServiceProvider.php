<?php

namespace App\Providers\CMS;

use App\Classes\CMS\RecommendationService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class RecommendationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(RecommendationService::class, function ($app) {
            $request = app(Request::class);

            return new RecommendationService($request);
        });
    }
}
