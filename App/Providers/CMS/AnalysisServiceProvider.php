<?php

namespace App\Providers\CMS;

use App\Classes\CMS\AnalysisService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class AnalysisServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(AnalysisService::class, function ($app) {
            $request = app(Request::class);

            return new AnalysisService($request);
        });
    }
}
