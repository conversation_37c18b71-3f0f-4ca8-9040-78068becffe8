<?php

namespace App\Providers\CMS;

use App\Classes\CMS\AnalysisReportService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class AnalysisReportServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(AnalysisReportService::class, function ($app) {
            $request = app(Request::class);

            return new AnalysisReportService($request);
        });
    }
}
