<?php

namespace App\Providers\CMS;

use App\Classes\CMS\PlotService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class PlotServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(PlotService::class, function ($app) {
            $request = app(Request::class);

            return new PlotService($request);
        });
    }
}
