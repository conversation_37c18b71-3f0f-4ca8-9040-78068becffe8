<?php

namespace App\Providers;

use App\Classes\CouchDBClient;
use Config;
use Illuminate\Support\ServiceProvider;

class CouchDBServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton('App\Classes\CouchDBClient', function () {
            $config = Config::get('couchdb');

            return new CouchDBClient($config);
        });
    }
}
