<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to the controller routes in your routes file.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     */
    public function boot()
    {
        parent::boot();
    }

    /**
     * Define the routes for the application.
     */
    public function map(Router $router)
    {
        $this->mapSSERoutes();
        $this->mapKeycloakRoutes();
        $this->mapWebRoutes();
        $this->mapSystemRoutes();
        $this->mapMobileRoutes();
        $this->mapApiRoutes();
        $this->mapApigsRoutes();
        $this->mapMeteoRoutes();
        $this->mapAdminRoutes();
        $this->mapTfcRoutes();
        $this->mapCmsRoutes();
    }

    protected function mapKeycloakRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data'],
            'namespace' => $this->namespace . '\KeycloakAuth',
            'prefix' => 'keycloak',
        ], function () {
            require base_path('routes/keycloak.php');
        });
    }

    protected function mapSSERoutes()
    {
        Route::group([
            // 'middleware' => [],
            'namespace' => $this->namespace . '\SSE',
            'prefix' => 'sse',
        ], function () {
            require base_path('routes/sse.php');
        });
    }

    protected function mapWebRoutes()
    {
        Route::group([
            'middleware' => 'web',
            'namespace' => $this->namespace,
        ], function () {
            require base_path('routes/web.php');
        });
    }

    protected function mapSystemRoutes()
    {
        Route::group([
            'middleware' => ['system-auth:system'],
            'namespace' => $this->namespace . '\System',
            'prefix' => 'system',
        ], function () {
            require base_path('routes/system.php');
        });
    }

    protected function mapApiRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-organization-data', 'verify-write-rights', 'api'],
            'namespace' => $this->namespace . '\API',
            'prefix' => 'api',
        ], function () {
            require base_path('routes/api.php');
        });
    }

    protected function mapApigsRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-organization-data', 'set-lang', 'api'],
            'namespace' => $this->namespace . '\APIGS',
            'prefix' => 'apigs',
        ], function () {
            require base_path('routes/apigs.php');
        });

        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-organization-data', 'set-lang', 'api'],
            'namespace' => $this->namespace . '\APIGS\v2',
            'prefix' => 'apigs/v2',
        ], function () {
            require base_path('routes/apigsV2.php');
        });
    }

    protected function mapMeteoRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-organization-data', 'api'],
            'namespace' => $this->namespace . '\Meteo',
            'prefix' => 'meteo',
        ], function () {
            require base_path('routes/meteo.php');
        });
    }

    protected function mapMobileRoutes()
    {
        require base_path('routes/mobile.php');
    }

    protected function mapAdminRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-admin-user-data', 'set-lang', 'role:SUPER_ADMIN|SERVICE_ADMIN|SAMPLER_ADMIN|SERVICE', 'api'],
            'namespace' => $this->namespace . '\APIGS\Admin',
            'prefix' => 'apigs/admin',
        ], function () {
            require base_path('routes/admin.php');
        });

        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-admin-user-data', 'set-lang', 'role:SUPER_ADMIN|SERVICE_ADMIN|SAMPLER_ADMIN|SERVICE', 'api'],
            'namespace' => $this->namespace . '\APIGS\Admin\v2',
            'prefix' => 'apigs/admin/v2',
        ], function () {
            require base_path('routes/adminV2.php');
        });
    }

    protected function mapTfcRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-organization-data', 'set-lang', 'api'],
            'namespace' => $this->namespace . '\APIGS',
            'prefix' => 'tfc',
        ], function () {
            require base_path('routes/tfc.php');
        });
    }

    protected function mapCmsRoutes()
    {
        Route::group([
            'middleware' => ['auth:api', 'set-user-data', 'set-organization-data', 'set-lang', 'api'],
            'namespace' => $this->namespace . '\CMS',
            'prefix' => 'cms',
        ], function () {
            require base_path('routes/cms.php');
        });
    }
}
