<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Providers;

use App\Classes\CMS\ContractService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class ContractServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(ContractService::class, function ($app) {
            $request = app(Request::class);

            return new ContractService($request);
        });
    }
}
