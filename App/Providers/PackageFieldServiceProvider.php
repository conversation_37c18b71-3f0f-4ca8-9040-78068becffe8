<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Providers;

use App\Classes\CMS\PackageFieldService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class PackageFieldServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(PackageFieldService::class, function ($app) {
            $request = app(Request::class);

            return new PackageFieldService($request);
        });
    }
}
