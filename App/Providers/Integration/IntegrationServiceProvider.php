<?php

namespace App\Providers\Integration;

use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageService;
use App\Services\Integration\IntegrationService;
use App\Services\Wialon\WialonService;
use Illuminate\Support\ServiceProvider;

class IntegrationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(IntegrationService::class, function () {
            $wialonService = app(WialonService::class);
            $contractService = app(ContractService::class);
            $packageService = app(PackageService::class);

            return new IntegrationService($contractService, $wialonService, $packageService);
        });
    }
}
