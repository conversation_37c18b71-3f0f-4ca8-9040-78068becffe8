<?php

namespace App\Providers\Common;

use App\Services\Common\TFCommonService;
use Illuminate\Support\ServiceProvider;

class TFCommonServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(TFCommonService::class, function ($app) {
            return new TFCommonService();
        });
    }
}
