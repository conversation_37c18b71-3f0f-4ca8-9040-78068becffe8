<?php

namespace App\Providers\Common;

use App\Models\OrderPlotRel;
use App\Services\Common\MailService;
use Illuminate\Support\ServiceProvider;

class MailServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(MailService::class, function ($app) {
            $orderPlotRel = new OrderPlotRel();

            return new MailService($orderPlotRel);
        });
    }
}
