<?php

namespace App\Providers;

use App\Models\Farm;
use App\Models\Integration;
use App\Models\MachineImplement;
use App\Models\MachineUnit;
use App\Models\ScheduledReport;
use App\Models\User;
use App\Policies\FarmPolicy;
use App\Policies\IntegrationPolicy;
use App\Policies\MachineImplementPolicy;
use App\Policies\MachineUnitPolicy;
use App\Policies\ScheduledReportPolicy;
use App\Policies\UserPolicy;
use Illuminate\Contracts\Auth\Access\Gate as GateContract;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        User::class => UserPolicy::class,
        Farm::class => FarmPolicy::class,
        ScheduledReport::class => ScheduledReportPolicy::class,
        Integration::class => IntegrationPolicy::class,
        MachineUnit::class => MachineUnitPolicy::class,
        MachineImplement::class => MachineImplementPolicy::class,
    ];

    /**
     * Register any application authentication / authorization services.
     */
    public function boot(GateContract $gate)
    {
        parent::registerPolicies();
    }

    public function register() {}
}
