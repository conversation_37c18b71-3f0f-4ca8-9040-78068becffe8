<?php

namespace App\Providers;

use App\Classes\Meteo\MeteoCache;
use App\Classes\Meteo\Virtual;
use Config;
use Illuminate\Support\ServiceProvider;

class VirtualServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton('App\Classes\Meteo\Virtual', function ($app, $params) {
            $heap = $this->app->make('App\Classes\Heap');

            $configMeteoBlue = Config::get('meteo');
            $meteoCache = new MeteoCache($configMeteoBlue, $heap);

            $config = Config::get('virtual');

            return new Virtual($config, $meteoCache, $params['stationModel'], $heap);
        });
    }
}
