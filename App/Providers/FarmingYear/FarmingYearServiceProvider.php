<?php

namespace App\Providers\FarmingYear;

use App\Classes\CMS\FarmingYearService as CMSFarmingYearService;
use App\Services\FarmingYear\FarmingYearService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class FarmingYearServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(FarmingYearService::class, function ($app) {
            $request = app(Request::class);
            $cMSFarmingYearService = new CMSFarmingYearService($request);

            return new FarmingYearService($cMSFarmingYearService);
        });
    }
}
