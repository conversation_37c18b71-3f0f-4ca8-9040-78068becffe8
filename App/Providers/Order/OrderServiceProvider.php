<?php

namespace App\Providers\Order;

use App\Classes\CMS\AnalysisService;
use App\Classes\CMS\ContractService;
use App\Classes\CMS\FarmingYearService as CmsFarmingYearService;
use App\Classes\PlotShape;
use App\Services\FarmingYear\FarmingYearService;
use App\Services\Order\OrderService;
use Config;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class OrderServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(OrderService::class, function ($app) {
            $request = app(Request::class);
            $contractService = new ContractService($request);
            $analysisService = new AnalysisService($request);
            $plotShape = new PlotShape(Config::get('globals.GDAL_BIN_PATH'));
            $farmingYearService = new FarmingYearService(new CmsFarmingYearService($request));

            return new OrderService($contractService, $plotShape, $analysisService, $farmingYearService);
        });
    }
}
