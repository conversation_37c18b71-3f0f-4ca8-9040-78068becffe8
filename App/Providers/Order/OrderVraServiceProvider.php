<?php

namespace App\Providers\Order;

use App\Services\Adapt\AdaptService;
use App\Services\Order\OrderVraService;
use Illuminate\Support\ServiceProvider;

class OrderVraServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(OrderVraService::class, function ($app) {
            return new OrderVraService(new AdaptService());
        });
    }
}
