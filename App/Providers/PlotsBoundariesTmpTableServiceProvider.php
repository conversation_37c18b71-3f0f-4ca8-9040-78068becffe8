<?php

namespace App\Providers;

use App\Services\Plot\PlotsBoundariesTmpTableService;
use Illuminate\Support\ServiceProvider;

class PlotsBoundariesTmpTableServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(PlotsBoundariesTmpTableService::class, function ($app) {
            return new PlotsBoundariesTmpTableService();
        });
    }
}
