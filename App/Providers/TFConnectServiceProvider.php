<?php

namespace App\Providers;

use App\Classes\TFConnect;
use Config;
use Illuminate\Support\ServiceProvider;

class TFConnectServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     */
    public function boot() {}

    /**
     * Register services.
     */
    public function register()
    {
        $this->app->bind(TFConnect::class, function () {
            $url = Config::get('tfc.url');
            $username = Config::get('tfc.username');
            $password = Config::get('tfc.password');

            return new TFConnect($url, $username, $password);
        });
    }
}
