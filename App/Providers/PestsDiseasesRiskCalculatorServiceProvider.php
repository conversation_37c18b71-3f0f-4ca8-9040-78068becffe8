<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Providers;

use App\Classes\Meteo\MeteoBlue;
use App\Classes\PestsDisease\RiskCalculator;
use Illuminate\Support\ServiceProvider;

class PestsDiseasesRiskCalculatorServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(RiskCalculator::class, function () {
            return new RiskCalculator($this->app->make(MeteoBlue::class));
        });
    }
}
