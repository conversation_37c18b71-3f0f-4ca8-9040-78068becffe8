<?php

namespace App\Providers\Organization;

use App\Services\Organization\OrganizationService;
use Illuminate\Support\ServiceProvider;

class OrganizationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(OrganizationService::class, function ($app) {
            return new OrganizationService();
        });
    }
}
