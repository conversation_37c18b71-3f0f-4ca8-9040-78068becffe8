<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 9/15/2020
 * Time: 4:40 PM.
 */

namespace App\Providers\Implement;

use App\Services\Implement\MachineImplementService;
use Illuminate\Support\ServiceProvider;

class MachineImplementServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(MachineImplementService::class, function () {
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');

            return new MachineImplementService($wialonService);
        });
    }
}
