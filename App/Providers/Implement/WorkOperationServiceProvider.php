<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 1/12/2021
 * Time: 8:46 AM.
 */

namespace App\Providers\Implement;

use App\Services\Implement\WorkOperationService;
use Illuminate\Support\ServiceProvider;

class WorkOperationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(WorkOperationService::class, function () {
            return new WorkOperationService();
        });
    }
}
