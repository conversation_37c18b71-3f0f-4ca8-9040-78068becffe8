<?php

namespace App\Providers\GuidanceLines;

use App\Services\Common\TFCommonService;
use App\Services\GuidanceLines\GuidanceLineService;
use Illuminate\Support\ServiceProvider;

class GuidanceLineServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(GuidanceLineService::class, function ($app) {
            $tfCommonService = $this->app->make(TFCommonService::class);

            return new GuidanceLineService($tfCommonService);
        });
    }
}
