<?php

namespace App\Providers;

use App\Classes\StaticMap;
use Config;
use Illuminate\Support\ServiceProvider;

class StaticMapServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
        $this->app->bind(StaticMap::class, function () {
            $url = Config::get('globals.STATIC_MAP_API_URL');
            $apiKey = Config::get('globals.STATIC_MAP_API_KEY');

            return new StaticMap($url, $apiKey);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot() {}
}
