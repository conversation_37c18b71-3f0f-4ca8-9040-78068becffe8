<?php

namespace App\Providers;

use App\Classes\Heap;
use Config;
use Illuminate\Support\ServiceProvider;

class HeapServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(Heap::class, function () {
            $appId = Config::get('heap.app_id');
            $url = Config::get('heap.url');

            return new Heap($appId, $url);
        });
    }
}
