<?php

namespace App\Providers;

use App\Classes\Plot3D;
use App\Classes\PlotShape;
use App\Models\Plot;
use Config;
use Illuminate\Support\ServiceProvider;

class PlotServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot()
    {
        // On created
        Plot::created(function ($plot) {
            $stationId = Plot::getClosestStationId($plot)->first();

            if (empty($stationId)) {
                return;
            }

            // Update with closest station_id
            $plot->station_id = $stationId;
            $plot->save();
        });
    }

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->bind(Plot3D::class, function () {
            $url = Config::get('globals.3D_TERRAINS_API_BASE_URL');

            return new Plot3D($url);
        });

        $this->app->singleton(PlotShape::class, function () {
            $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');

            return new PlotShape($gdalBinPath);
        });
    }
}
