<?php

namespace App\Providers\System;

use App\Services\System\SystemService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class SystemServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(SystemService::class, function () {
            $request = app(Request::class);

            return new SystemService($request);
        });
    }
}
