<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 1/19/2021
 * Time: 10:01 AM.
 */

namespace App\Providers\Pin;

use App\Services\Pin\PinService;
use Illuminate\Support\ServiceProvider;

class PinServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(PinService::class, function () {
            $pinModel = $this->app->make('App\Models\Pin');

            return new PinService($pinModel);
        });
    }
}
