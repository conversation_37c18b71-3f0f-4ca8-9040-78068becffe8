<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 2/16/2021
 * Time: 8:28 AM.
 */

namespace App\Providers\FarmTrackReportsLog;

use App\Services\FarmTrackReportsLog\FarmTrackReportsLogService;
use Illuminate\Support\ServiceProvider;

class FarmTrackReportsLogServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(FarmTrackReportsLogService::class, function () {
            return new FarmTrackReportsLogService();
        });
    }
}
