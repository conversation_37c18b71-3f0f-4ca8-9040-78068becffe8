<?php

namespace App\Providers;

use App\Classes\Meteo\OnSite2;
use Config;
use Illuminate\Support\ServiceProvider;

class OnSite2ServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(OnSite2::class, function ($app, $params) {
            $config = Config::get('onsite2');
            $heap = $this->app->make('App\Classes\Heap');
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');
            $reportService = $this->app->make('App\Services\Wialon\ReportService');

            return new OnSite2($config, $params['stationModel'], $heap, $wialonService, $reportService);
        });
    }
}
