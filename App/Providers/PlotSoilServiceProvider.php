<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 3/31/2021
 * Time: 4:34 PM.
 */

namespace App\Providers;

use App\Classes\CMS\FarmingYearService as CmsFarmingYearService;
use App\Services\FarmingYear\FarmingYearService;
use App\Services\Plot\PlotSoilService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class PlotSoilServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(PlotSoilService::class, function () {
            $request = app(Request::class);
            $plotService = $this->app->make('App\Classes\CMS\PlotService');
            $analysisService = $this->app->make('App\Classes\CMS\AnalysisService');
            $farmingYearService = new FarmingYearService(new CmsFarmingYearService($request));

            return new PlotSoilService($plotService, $analysisService, $farmingYearService);
        });
    }
}
