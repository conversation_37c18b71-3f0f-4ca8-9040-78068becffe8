<?php

namespace App\Providers\Irrigation;

use App\Services\Irrigation\IrrigationReportService;
use Illuminate\Support\ServiceProvider;

class IrrigationReportServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(IrrigationReportService::class, function () {
            return new IrrigationReportService();
        });
    }
}
