<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>non<PERSON>
 * Date: 6/9/2020
 * Time: 1:46 PM.
 */

namespace App\Providers\Irrigation;

use App\Services\Irrigation\IrrigationPlatformService;
use Illuminate\Support\ServiceProvider;

class IrrigationPlatformServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(IrrigationPlatformService::class, function ($app) {
            return new IrrigationPlatformService();
        });
    }
}
