<?php

namespace App\Providers\Irrigation;

use App\Services\Irrigation\IrrigationUnitService;
use Illuminate\Support\ServiceProvider;

class IrrigationUnitServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(IrrigationUnitService::class, function () {
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');

            return new IrrigationUnitService($wialonService);
        });
    }
}
