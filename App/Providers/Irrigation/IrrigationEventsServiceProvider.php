<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 1/12/2021
 * Time: 12:12 PM.
 */

namespace App\Providers\Irrigation;

use App\Services\Irrigation\IrrigationDataRawService;
use App\Services\Irrigation\IrrigationEventsService;
use App\Services\Wialon\ReportService;
use Illuminate\Support\ServiceProvider;

class IrrigationEventsServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton('App\Services\Irrigation\IrrigationEventsService', function () {
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');
            $reportService = new ReportService($wialonService);
            $irrigationDataRawService = new IrrigationDataRawService();

            return new IrrigationEventsService($reportService, $irrigationDataRawService);
        });
    }
}
