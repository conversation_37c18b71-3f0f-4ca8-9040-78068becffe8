<?php

namespace App\Providers\Irrigation;

use App\Services\Irrigation\IrrigationDataRawService;
use Illuminate\Support\ServiceProvider;

class IrrigationDataRawServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton('App\Services\Irrigation\IrrigationDataRawService', function () {
            return new IrrigationDataRawService();
        });
    }
}
