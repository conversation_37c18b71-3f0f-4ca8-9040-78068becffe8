<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'App\Events\GenerateWorkLayerMapFileEvent' => [
            'App\Listeners\GenerateListener',
        ],
        'App\Events\GenerateUserMapFileEvent' => [
            'App\Listeners\GenerateListener',
        ],
        'App\Events\SoilOrderPushNotificationEvent' => [
            'App\Listeners\SoilOrderPushNotificationListener',
        ],
        'App\Events\GenerateStaticMapFilesEvent' => [
            'App\Listeners\GenerateListener',
        ],
        'App\Events\GenerateAllPlotsLayerMapFileEvent' => [
            'App\Listeners\GenerateListener',
        ],
        'App\Events\GeneratePlotImgEvent' => [
            'App\Listeners\GenerateListener',
        ],
        'App\Events\PushNotificationEvent' => [
            'App\Listeners\PushNotificationListener',
        ],
        \Illuminate\Mail\Events\MessageSent::class => [
            \App\Listeners\ProcessSentMailMessageListener::class,
        ],
        \SocialiteProviders\Manager\SocialiteWasCalled::class => [
            \SocialiteProviders\Keycloak\KeycloakExtendSocialite::class . '@handle',
            \App\Listeners\KeycloakSocialiteListener::class . '@handle',
        ],
        'App\Events\FarmTrackReportEvent' => [
            'App\Listeners\FarmTrackReportLogListener',
        ],
    ];

    /**
     * Register any other events for your application.
     */
    public function boot()
    {
        parent::boot();
    }
}
