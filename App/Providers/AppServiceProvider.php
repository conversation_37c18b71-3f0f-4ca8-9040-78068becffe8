<?php

namespace App\Providers;

use App\Classes\CMS\PlotService;
use App\Http\Middleware\RequestLogger;
use App\Jobs\UpdateFieldFarmIdInCMS;
use App\Models\Ability;
use App\Models\GlobalUser;
use App\Models\Role;
use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;
use Silber\Bouncer\BouncerFacade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot()
    {
        BouncerFacade::dontCache();
        BouncerFacade::useAbilityModel(Ability::class);
        BouncerFacade::useRoleModel(Role::class);
        BouncerFacade::useUserModel(GlobalUser::class);

        Collection::macro('transformToCamelCaseKeys', function () {
            return $this->keyBy(function ($value, $key) {
                return camel_case($key);
            });
        });

        $this->app->bindMethod([UpdateFieldFarmIdInCMS::class, 'handle'], function ($job, $app) {
            return $job->handle($app->make(PlotService::class));
        });
    }

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton('log-request', function ($app) {
            return new RequestLogger();
        });
    }
}
