<?php

namespace App\Providers;

use App\Services\Wialon\WialonService;
use Illuminate\Support\ServiceProvider;

class WialonServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton('App\Services\Wialon\WialonService', function () {
            return new WialonService();
        });
    }
}
