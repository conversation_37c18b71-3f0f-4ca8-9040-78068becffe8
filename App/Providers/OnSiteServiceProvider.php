<?php

namespace App\Providers;

use App\Classes\Meteo\OnSite;
use Config;
use Illuminate\Support\ServiceProvider;

class OnSiteServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(OnSite::class, function ($app, $params) {
            $config = Config::get('onsite');
            $heap = $this->app->make('App\Classes\Heap');

            return new OnSite($config, $params['stationModel'], $heap);
        });
    }
}
