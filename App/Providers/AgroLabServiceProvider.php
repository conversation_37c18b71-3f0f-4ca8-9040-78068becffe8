<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Providers;

use App\Classes\AgroLab\AgroLabService;
use App\Classes\CouchDBClient;
use Config;
use Illuminate\Support\ServiceProvider;

class AgroLabServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(AgroLabService::class, function ($app) {
            $config = Config::get('couchdb');
            $couchDBClient = new CouchDBClient($config);

            return new AgroLabService($couchDBClient);
        });
    }
}
