<?php

namespace App\Providers\Machine;

use App\Services\Machine\MachineUnitService;
use App\Services\Wialon\ReportService;
use Illuminate\Support\ServiceProvider;

class MachineUnitServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(MachineUnitService::class, function () {
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');
            $reportService = new ReportService($wialonService);

            return new MachineUnitService($wialonService, $reportService);
        });
    }
}
