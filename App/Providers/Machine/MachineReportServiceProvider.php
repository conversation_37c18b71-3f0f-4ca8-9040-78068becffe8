<?php

namespace App\Providers\Machine;

use App\Services\Machine\MachineEventService;
use App\Services\Machine\MachineReportService;
use App\Services\Wialon\ReportService;
use Illuminate\Support\ServiceProvider;

class MachineReportServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(MachineReportService::class, function () {
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');
            $reportService = new ReportService($wialonService);
            $machineEventService = new MachineEventService();

            return new MachineReportService($reportService, $machineEventService);
        });
    }
}
