<?php

namespace App\Providers\Machine;

use App\Services\Machine\MachineEventService;
use Illuminate\Support\ServiceProvider;

class MachineEventServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(MachineEventService::class, function () {
            return new MachineEventService();
        });
    }
}
