<?php

namespace App\Providers\Adapt;

use App\Services\Adapt\AdaptService;
use Illuminate\Support\ServiceProvider;

class AdaptServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(AdaptService::class, function ($app) {
            return new AdaptService();
        });
    }
}
