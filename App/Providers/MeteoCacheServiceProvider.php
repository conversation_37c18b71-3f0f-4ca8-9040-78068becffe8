<?php

namespace App\Providers;

use App\Classes\Meteo\MeteoCache;
use Config;
use Illuminate\Support\ServiceProvider;

class MeteoCacheServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(MeteoCache::class, function () {
            $configMeteoBlue = Config::get('meteo');

            $heap = $this->app->make('App\Classes\Heap');

            return new MeteoCache($configMeteoBlue, $heap);
        });
    }
}
