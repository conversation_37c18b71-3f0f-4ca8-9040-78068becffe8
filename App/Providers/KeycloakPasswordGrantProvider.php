<?php

namespace App\Providers;

use SocialiteProviders\Keycloak\Provider;

class KeycloakPasswordGrantProvider extends Provider
{
    private $fields = [
        'grant_type' => 'password',
    ];

    public function addTokenFields(array $additionalFields)
    {
        $this->fields = array_merge($additionalFields, $this->fields);

        return $this;
    }

    /**
     * Undocumented function.
     *
     * @param [type] $code
     */
    protected function getTokenFields($code)
    {
        return array_merge(parent::getTokenFields($code), $this->fields);
    }
}
