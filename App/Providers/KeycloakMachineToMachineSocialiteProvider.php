<?php

namespace App\Providers;

use SocialiteProviders\Keycloak\Provider;

class KeycloakMachineToMachineSocialiteProvider extends Provider
{
    /**
     * Undocumented function.
     *
     * @param [type] $code
     */
    protected function getTokenFields($code)
    {
        return array_merge(parent::getTokenFields($code), [
            'grant_type' => 'client_credentials',
        ]);
    }
}
