<?php

namespace App\Providers;

use App\Classes\Meteo\Pessl;
use Config;
use Illuminate\Support\ServiceProvider;

class PesslServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot() {}

    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->singleton(Pessl::class, function ($app, $params) {
            $config = Config::get('pessl');
            $heap = $this->app->make('App\Classes\Heap');

            return new Pessl($config, $params['stationModel'], $heap);
        });
    }
}
