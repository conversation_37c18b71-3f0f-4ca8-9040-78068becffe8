<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 1/14/2021
 * Time: 8:20 AM.
 */

namespace App\Providers\Crop;

use App\Services\Crop\CropService;
use Illuminate\Support\ServiceProvider;

class CropServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(CropService::class, function () {
            $plot = $this->app->make('App\Models\Plot');

            return new CropService($plot);
        });
    }
}
