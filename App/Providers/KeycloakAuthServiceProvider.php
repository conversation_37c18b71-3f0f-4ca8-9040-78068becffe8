<?php

namespace App\Providers;

use App\Models\Farm;
use App\Models\Integration;
use App\Models\MachineImplement;
use App\Models\MachineUnit;
use App\Models\ScheduledReport;
use App\Models\User;
use App\Policies\FarmPolicy;
use App\Policies\IntegrationPolicy;
use App\Policies\MachineImplementPolicy;
use App\Policies\MachineUnitPolicy;
use App\Policies\ScheduledReportPolicy;
use App\Policies\UserPolicy;
use App\Services\Auth\KeycloakGuard;
use App\Services\Auth\KeycloakResourceServer;
use App\Services\Auth\TokenValidator;
use Illuminate\Auth\CreatesUserProviders;
use Illuminate\Contracts\Auth\Access\Gate as GateContract;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class KeycloakAuthServiceProvider extends ServiceProvider
{
    use CreatesUserProviders;

    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        User::class => UserPolicy::class,
        Farm::class => FarmPolicy::class,
        ScheduledReport::class => ScheduledReportPolicy::class,
        Integration::class => IntegrationPolicy::class,
        MachineUnit::class => MachineUnitPolicy::class,
        MachineImplement::class => MachineImplementPolicy::class,
    ];

    /**
     * Register any application authentication / authorization services.
     */
    public function boot(GateContract $gate)
    {
        parent::registerPolicies();

        Auth::provider('keycloak', function ($app, array $config) {
            return new KeycloakUserProvider($app['hash'], $config['model']);
        });

        $this->publishes([config_path() . '/keycloak.php' => config_path('keycloak.php')], 'config');

        Auth::extend('keycloak', function ($app, $name, array $config) {
            return new KeycloakGuard(
                Auth::createUserProvider($config['provider']),
                $app->make('request'),
                new KeycloakResourceServer(
                    new TokenValidator()
                )
            );
        });
    }
}
