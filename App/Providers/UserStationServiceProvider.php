<?php

namespace App\Providers;

use App\Models\UserStation;
use Illuminate\Support\ServiceProvider;

class UserStationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot()
    {
        // On saved
        UserStation::saved(function ($station) {
            if (!$station->active) {
                UserStation::removeStationIds($station);

                return;
            }

            if (empty($station->geom)) {
                return;
            }

            // Set station_id at su_satellite_plots
            UserStation::setStationIds($station);
        });

        // On deleted
        UserStation::deleted(function ($station) {
            UserStation::removeStationIds($station);
        });
    }

    /**
     * Register the application services.
     */
    public function register() {}
}
