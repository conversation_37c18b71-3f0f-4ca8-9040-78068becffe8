<?php

namespace App\Models;

use DateTime;
use DB;
use Illuminate\Database\Eloquent\Builder;
use phpseclib\Math\BigInteger;

/**
 * Class IrrigationDataRaw.
 *
 * @property BigInteger $irrigation_unit_id
 * @property DateTime $start_time
 * @property DateTime $end_time
 * @property BigInteger $irrigation_event_id
 * @property string $type
 * @property int $platform_id
 * @property int $unit_speed
 * @property int $pressure
 * @property int $water_rate
 * @property string $segment
 */
class IrrigationDataRaw extends BaseModel
{
    public const MISALIGNMENT_ALARM = 'MisalignmentAlarm';
    public const PRESSURE_ALARM = 'PressureAlarm';
    public const MOVEMENT = 'Movement';
    public const IRRIGATION = 'Irrigation';
    public const WARNING = 'Warning';

    public const WARNING_ANGLE = 'Warning Angle';
    public const WARNING_SPEED = 'Warning Speed';
    public const WARNING_PRESSURE = 'Warning Pressure';
    public const WARNING_COORDINATES = 'Warning Coordinates';
    public const WARNING_READING_SENSORS = 'Warning Reading Sensors';
    public const WARNING_DIRECTION = 'Warning Direction';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_data_raw';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['irrigation_unit_id', 'platform_id', 'irrigation_event_id', 'start_time', 'end_time', 'type', 'unit_speed', 'pressure', 'water_rate', 'segment'];

    public function platform()
    {
        return $this->hasMany('App\Models\IrrigationPlatform', 'platform_id', 'id');
    }

    public function irrigationEvent()
    {
        return $this->hasMany('App\Models\IrrigationEvent', 'irrigation_event_id', 'id');
    }

    public function unit()
    {
        return $this->hasMany('App\Models\IrrigationUnit', 'irrigation_unit_id', 'id');
    }

    public static function createIrrigationEventsFromRawData()
    {
        $datesQuery = self::join('su_irrigation_units AS siu', 'siu.id', '=', 'su_irrigation_data_raw.irrigation_unit_id')
            ->join('su_organization_time_offsets AS sote', 'sote.organization_id', '=', 'siu.organization_id')
            ->join('su_irrigation_platforms AS sip', function ($join) {
                $join->on('sip.id', '=', 'su_irrigation_data_raw.platform_id');
                $join->on('sip.status', '=', DB::raw('true'));
            })
            ->select(
                'su_irrigation_data_raw.id AS irrigation_data_raw_id',
                'irrigation_unit_id',
                'start_time',
                'end_time',
                'platform_id',
                'unit_speed',
                'pressure',
                'angle',
                'water_rate',
                'su_irrigation_data_raw.segment AS coverage',
                'su_irrigation_data_raw.type',
                DB::raw('date(end_time - sote.irrigation_offset) AS date'),
                'su_irrigation_data_raw.state_description_type',
                'su_irrigation_data_raw.forward',
                'su_irrigation_data_raw.rearward'
            )
            ->whereNull('su_irrigation_data_raw.irrigation_event_id');

        $tasksQuery = self::from('dates')
            ->select(
                'irrigation_unit_id',
                DB::raw('array_agg(irrigation_data_raw_id) AS irrigation_data_raw_ids'),
                'platform_id',
                DB::raw('lag(platform_id, 1) OVER (PARTITION BY irrigation_unit_id ORDER BY irrigation_unit_id, min(start_time), platform_id) AS prev_platform_id'),
                DB::raw('st_union(coverage) AS coverage'),
                'type',
                DB::raw('min(start_time) AS start_time'),
                DB::raw('max(end_time) AS end_time'),
                DB::raw('round(avg(unit_speed)::numeric, 2) AS unit_speed'),
                DB::raw('avg(pressure) AS pressure'),
                DB::raw('avg(angle)::integer AS angle'),
                DB::raw('avg(water_rate) AS water_rate'),
                DB::raw('round((st_area(st_union(coverage))/ 1000)::numeric, 2) AS area'),
                'date',
                'state_description_type',
                'forward',
                'rearward'
            )
            ->groupBy(['platform_id', 'irrigation_unit_id', 'type', 'date', 'forward', 'rearward', 'state_description_type'])
            ->orderBy('irrigation_unit_id')
            ->orderBy('start_time')
            ->orderBy('platform_id')
            ->orderBy('date')
            ->orderBy('type');

        $irrigationEventsToInsertQuery = self::from('tasks')
            ->select(
                'irrigation_unit_id',
                'irrigation_data_raw_ids',
                'start_time',
                'end_time',
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN prev_platform_id
                        ELSE platform_id
                    END AS platform_id
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN platform_id
                        ELSE NULL
                    END AS destination_platform_id
                '),
                DB::raw("
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN 'Transportation'::irrigation_events_types_enum
                        ELSE type
                    END AS type
                "),
                'date',
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE area
                    END AS area
                '),
                'state_description_type',
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE forward
                    END AS forward
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE rearward
                    END AS rearward
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE unit_speed
                    END AS unit_speed
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE angle
                    END AS angle
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE round(pressure, 2)
                    END AS pressure
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE round(water_rate)
                    END AS water_rate
                '),
                DB::raw('
                    CASE
                        WHEN prev_platform_id NOTNULL
                        AND prev_platform_id <> platform_id THEN NULL
                        ELSE coverage
                    END AS coverage
                ')
            )
            ->orderBy('irrigation_unit_id', 'ASC')
            ->orderBy('start_time', 'ASC')
            ->orderByRaw('destination_platform_id NULLS FIRST')
            ->orderBy('type', 'ASC')
            ->distinct();

        $insertIrrigationEventsQueryStr = 'INSERT INTO 
            su_irrigation_events (
                irrigation_unit_id,
                start_time,
                end_time, 
                platform_id, 
                destination_platform_id,
                unit_speed, 
                pressure,
                angle, 
                water_rate, 
                coverage, 
                type, 
                date, 
                area, 
                state_description_type,
                forward, 
                rearward
            )
            SELECT
                irrigation_unit_id,
                start_time, 
                end_time, 
                platform_id,
                destination_platform_id,
                unit_speed, 
                pressure,
                angle, 
                water_rate, 
                coverage, 
                type, 
                date, 
                area, 
                state_description_type, 
                forward, 
                rearward
            FROM
                irrigation_events_to_insert ON CONFLICT DO NOTHING
            RETURNING 
                id AS irrigation_event_id,
                irrigation_unit_id,
                platform_id,
                type,
                date, 
                area, 
                start_time, 
                end_time, 
                destination_platform_id,
                unit_speed,
                pressure,
                angle, 
                water_rate, 
                coverage, 
                state_description_type, 
                forward,
                rearward
            ';

        $insertedIrrigationEventsQuery = DB::table(DB::raw('insert_irrigation_events AS iie'))
            ->join(DB::raw('irrigation_events_to_insert AS ieti'), function ($join) {
                $join->on('iie.irrigation_unit_id', '=', 'ieti.irrigation_unit_id');
                $join->on('iie.platform_id', '=', 'ieti.platform_id');
                $join->on('iie.type', '=', 'ieti.type');
                $join->on('iie.area', '=', 'ieti.area');
                $join->on('iie.date', '=', 'ieti.date');
                $join->on('iie.start_time', '=', 'ieti.start_time');
                $join->on('iie.end_time', '=', 'ieti.end_time');
                $join->on('iie.coverage', '=', 'ieti.coverage');
                $join->on('iie.forward', '=', 'ieti.forward');
                $join->on('iie.rearward', '=', 'ieti.rearward');
            })
            ->select(
                'irrigation_event_id',
                'irrigation_data_raw_ids'
            );

        self::withExpression('dates', $datesQuery)
            ->withExpression('tasks', $tasksQuery)
            ->withExpression('irrigation_events_to_insert', $irrigationEventsToInsertQuery)
            ->withExpression('insert_irrigation_events', $insertIrrigationEventsQueryStr)
            ->withExpression('inserted_irrigation_events', $insertedIrrigationEventsQuery)
            ->join('inserted_irrigation_events', 'su_irrigation_data_raw.id', '=', DB::raw('ANY(inserted_irrigation_events.irrigation_data_raw_ids)'))
            ->updateFrom([
                'irrigation_event_id' => DB::raw('inserted_irrigation_events.irrigation_event_id'),
            ]);
    }

    public static function insertIrrigationDataRaw(Builder $query): void
    {
        $querySql = $query->toSqlWithBindings();

        $querySqlDoNothing = "({$querySql}) ON CONFLICT DO NOTHING";

        self::insertUsing([
            'irrigation_unit_id',
            'start_time',
            'end_time',
            'platform_id',
            'unit_speed',
            'pressure',
            'angle',
            'water_rate',
            'segment',
            'type',
            'forward',
            'rearward',
            'state_description_type',
        ], $querySqlDoNothing);
    }

    public static function deleteIrrDataRaw(string $date, ?int $wialonUnitId)
    {
        $query = self::join('su_irrigation_units as siu', 'siu.id', '=', 'su_irrigation_data_raw.irrigation_unit_id')
            ->join('su_organization_time_offsets AS sote', 'sote.organization_id', '=', 'siu.organization_id')
            ->where(DB::raw('date(su_irrigation_data_raw.end_time - sote.irrigation_offset)'), $date);

        if ($wialonUnitId) {
            $query->where('siu.wialon_unit_id', '=', $wialonUnitId);
        }

        $query->delete();
    }
}
