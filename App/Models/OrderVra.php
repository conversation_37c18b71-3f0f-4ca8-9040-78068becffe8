<?php

namespace App\Models;

use Auth;
use DateTime;

/**
 * Class OrderVra.
 *
 * @property int $order_id
 * @property int $plot_id
 * @property int $layer_id
 * @property DateTime $created
 * @property int $type
 * @property int $class_number
 * @property int $flat_rate
 * @property string $data
 * @property int $flat_rate_total
 * @property int $variable_rate_total
 * @property int $difference
 * @property int $difference_percent
 * @property Plot $plot
 */
class OrderVra extends BaseModel
{
    public $timestamps = false;
    protected $table = 'su_satellite_orders_vra';

    public function getVraOrdersByPlots($plotId, $farmYear)
    {
        return self::getVraOrdersByPlotsQuery($plotId, $farmYear)->orderBy('su_satellite_orders_vra.id', 'desc')->get();
    }

    public function plot()
    {
        return $this->belongsTo('App\Models\Plot', 'plot_id', 'gid');
    }

    public static function getVraOrdersByPlotsQuery($plotId, $farmYear)
    {
        return self::select([
            'su_satellite_orders_vra.*',
            'so.status',
            'slp.date',
            'slp.satellite_type',
            'so.type as order_type',
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_satellite_orders_vra.order_id')
            ->join('su_satellite_layers_plots as slp', 'slp.id', '=', 'su_satellite_orders_vra.layer_id')
            ->join('su_satellite_plots as p', 'p.gid', '=', 'slp.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('so.year', $farmYear)
            ->where('su_satellite_orders_vra.plot_id', $plotId)
            ->where('so.type', 'vra')
            ->whereNotIn(
                'so.status',
                array_keys(
                    Order::getOrderStatuses()
                        ->filter(function ($val) {
                            if ('Error' === $val || 'Canceled' === $val) {
                                return $val;
                            }
                        })
                        ->toArray()
                )
            );
    }

    public function loadVraByOrderId($orderId)
    {
        return self::loadVraByOrderIdQuery($orderId)->first();
    }

    public static function loadVraByOrderIdQuery($orderId)
    {
        return self::select([
            'su_satellite_orders_vra.*',
            'so.status',
            'slp.date',
            'slp.satellite_type',
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_satellite_orders_vra.order_id')
            ->join('su_satellite_layers_plots as slp', 'slp.id', '=', 'su_satellite_orders_vra.layer_id')
            ->where('su_satellite_orders_vra.order_id', $orderId)
            ->where('so.type', 'vra');
    }
}
