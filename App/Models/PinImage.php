<?php

namespace App\Models;

use DateTime;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PinImage.
 *
 * @property int $id
 * @property string $pin_id
 * @property string $image_name
 * @property DateTime $created_at
 * @property DateTime $updated_at
 */
class PinImage extends Model
{
    public $timestamps = true;
    protected $table = 'su_users_pins_images';

    public function pinId()
    {
        return $this->belongsTo(Pin::class);
    }

    public static function removeImages($pinId, $fileName = '')
    {
        if (strlen($fileName)) {
            $fileInfo = pathinfo($fileName);
            $baseFileName = $fileInfo['basename'];

            $fileName = str_replace('thumb_', '', $baseFileName);
        }

        $paths = self::pinImagePaths($pinId, $fileName);

        if (!$paths) {
            return;
        }

        // delete from file system
        foreach ($paths as $key => $value) {
            if (isset($value['path']) && file_exists($value['path'])) {
                unlink($value['path']);
            }
        }

        // delete from DB
        $pathsIds = array_column($paths, 'id');
        if ($pathsIds) {
            self::whereIn('id', $pathsIds)->delete();
        }
    }

    public static function pinImagePaths($pinId, $fileName = '')
    {
        $queryPaths = self::select('su_users_pins_images.id', 'su_users_pins_images_files.path')
            ->leftJoin('su_users_pins_images_files', 'su_users_pins_images.id', '=', 'su_users_pins_images_files.image_id')
            ->where('su_users_pins_images.pin_id', $pinId);

        if (strlen($fileName)) {
            $queryPaths = $queryPaths->where('su_users_pins_images_files.path', 'ILIKE', trim('%' . $fileName . '%'));
        }

        return $queryPaths->get()->toArray();
    }
}
