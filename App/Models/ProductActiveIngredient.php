<?php

namespace App\Models;

use App\Models\Products\Product;
use App\Models\UnitOfMeasure\UnitOfMeasure;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductActiveIngredient extends BaseModel
{
    use TimestampsTrait;

    public $timestamps = true;

    protected $table = 'su_product_active_ingredients';

    protected $primaryKey = 'id';

    protected $fillable = ['product_id', 'active_ingredient_id', 'quantity', 'unit_id'];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id', 'id');
    }

    public function activeIngredient(): BelongsTo
    {
        return $this->belongsTo(ActiveIngredient::class, 'active_ingredient_id', 'id');
    }
}
