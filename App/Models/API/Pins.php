<?php

namespace App\Models\API;

use App\Models\BaseModel;
use DB;

/**
 * This model describes the 'su_users_pins' table.
 */
class Pins extends BaseModel
{
    public $timestamps = false;

    protected $table = 'su_users_pins';
    protected $hidden = ['group_id'];
    protected $appends = ['id', 'isNew', 'isSynced'];

    public static function generateRandomId()
    {
        return base_convert(time() . '000', 10, 36) . substr(base_convert(mt_rand(
            1000000000,
            mt_getrandmax()
        ) . mt_rand(1000000000, mt_getrandmax()), 10, 36), 0, 12);
    }

    public function getIdAttribute()
    {
        return $this->attributes['id'];
    }

    public function getImagesAttribute()
    {
        return json_decode($this->attributes['images']);
    }

    public function getIsNewAttribute()
    {
        return false;
    }

    public function getIsSyncedAttribute()
    {
        return true;
    }

    public function farm()
    {
        return $this->belongsTo(Farm::class);
    }

    public static function getPinsWithImages($groupId, $withoutPinIds = [])
    {
        $queryPins = self::select(
            'su_users_pins.id',
            'su_users_pins.group_id',
            'su_users_pins.title',
            'su_users_pins.comment',
            'su_users_pins.date',
            'su_users_pins.lon',
            'su_users_pins.lat',
            'su_users_pins.isDeleted',
            'su_users_pins.type',
            'su_users_pins.sopr_id',
            'su_users_pins.farm_id',
            DB::raw("CASE WHEN ARRAY_AGG(upf.web_path) = '{NULL}' THEN '[]' ELSE json_agg(json_build_object('name', split_part(upf.web_path, '_', 2), 'path', upf.web_path)) END as images")
        )
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'su_users_pins.farm_id')
            ->leftJoin('su_users_pins_images as upi', 'upi.pin_id', '=', 'su_users_pins.id')
            ->leftJoin('su_users_pins_images_files AS upf', function ($join) {
                $join->on('upf.image_id', '=', 'upi.id');
                $join->where('upf.type', 'thumb');
            })
            ->where('fu.user_id', $groupId)
            ->groupBy('su_users_pins.id');

        if ($withoutPinIds && count($withoutPinIds)) {
            $queryPins = $queryPins->whereNotIn('su_users_pins.id', $withoutPinIds);
        }

        return $queryPins->get()->toArray();
    }
}
