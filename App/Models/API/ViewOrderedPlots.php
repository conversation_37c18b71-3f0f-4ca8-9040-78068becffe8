<?php

namespace App\Models\API;

use Config;
use Illuminate\Database\Eloquent\Model;

/**
 * This model describes the 'api_ordered_plots' view.
 */
class ViewOrderedPlots extends Model
{
    public $timestamps = false;

    protected $table = 'api_ordered_plots2';
    protected $hidden = ['user_id', 'images', 'dates', 'compound_arr', 'soil_type_arr', 'satellite_type_arr', 'pad_type_arr', 'clouds_percent_arr'];

    public function getGeomAttribute()
    {
        return json_decode($this->attributes['geom']);
    }

    public function getOprIdAttribute()
    {
        return $this->attributes['opr_id'];
    }

    public function getImagesAttribute()
    {
        return explode(',', $this->attributes['images']);
    }

    public function getDatesAttribute()
    {
        return explode(',', $this->attributes['dates']);
    }

    public function getCompoundArrAttribute()
    {
        return explode(',', $this->attributes['compound_arr']);
    }

    public function getSoilTypeArrAttribute()
    {
        return explode(',', $this->attributes['soil_type_arr']);
    }

    public function getSatelliteTypeArrAttribute()
    {
        return explode(',', $this->attributes['satellite_type_arr']);
    }

    public function getPadTypeArrAttribute()
    {
        return explode(',', $this->attributes['pad_type_arr']);
    }

    public function getImagesDataAttribute()
    {
        return array_map(function ($UrlDateArr) {
            return array_combine(['url', 'date', 'compound', 'soil_type', 'satellite_type', 'pad_type', 'clouds_percent'], $UrlDateArr);
        }, array_map(
            null,
            $this->images,
            $this->getDatesAttribute(),
            $this->compound_arr,
            $this->soil_type_arr,
            $this->satellite_type_arr,
            $this->pad_type_arr,
            $this->clouds_percent_arr
        ));
    }

    public function getExtentAttribute()
    {
        return explode(' ', $this->attributes['extent']);
    }

    public function getStatsAttribute()
    {
        $orderType = $this->attributes['type'];
        $dataArr = [];

        if ('index' === $orderType) {
            $stats = json_decode($this->attributes['stats']);
            $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

            if (!is_array($stats)) {
                return $dataArr;
            }

            foreach ($stats as $key => $value) {
                foreach ($value as $class => $area) {
                    $dataArr[$key][] = round($area * $areaCoef, 3);
                }
            }
        }

        return $dataArr;
    }

    public function getCloudsPercentArrAttribute()
    {
        return explode(',', $this->attributes['clouds_percent_arr']);
    }
}
