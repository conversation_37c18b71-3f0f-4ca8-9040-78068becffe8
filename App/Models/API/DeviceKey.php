<?php

namespace App\Models\API;

use Illuminate\Database\Eloquent\Model;

class Device<PERSON>ey extends Model
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    protected $primaryKey = 'device_key';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_device_keys';

    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'group_id');
    }
}
