<?php

namespace App\Models\Products;

use App\Models\ActiveIngredient;
use App\Models\BaseModel;
use App\Models\Organization;
use App\Models\UnitOfMeasure\UnitOfMeasure;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

class Product extends BaseModel
{
    use TimestampsTrait;

    public const APPLICATION_RATE_PER_HA = 'per ha';
    public const APPLICATION_RATE_PER_DKA = 'per dka';
    public const APPLICATION_RATE_PER_KM = 'per km';
    public const APPLICATION_RATE_PER_100_km = 'per 100km';

    public const STATUS_ACTIVE = 'Active';
    public const STATUS_INACTIVE = 'Inactive';

    public $timestamps = true;

    protected $table = 'su_products';

    protected $primaryKey = 'id';

    protected $fillable = [
        'name', 'rate', 'status', 'organization_id', 'default_price', 'type_id', 'unit_id',
        'quarantine_period', 'information', 'application_rate',
    ];

    protected $casts = [
        'unit' => 'object',
        'type' => 'object',
        'active_ingredients' => 'object',
        'organization' => 'object',
    ];

    public function tasks(): BelongsToMany
    {
        return $this->belongsToMany(
            MachineTask::class,
            'su_machine_task_products',
            'product_id',
            'task_id'
        )->withTimestamps();
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(ProductType::class, 'type_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id', 'id');
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function activeIngredients(): belongsToMany
    {
        return $this->belongsToMany(
            ActiveIngredient::class,
            'su_product_active_ingredients',
            'product_id',
            'active_ingredient_id',
        );
    }

    public function scopeForOrganization(Builder $builder, int $organizationId): Builder
    {
        return $builder->where('so.id', $organizationId);
    }

    public function scopeForProduct(Builder $builder, int $productId): Builder
    {
        return $builder->where('su_products.id', $productId);
    }

    public static function getApplicationRates(): array
    {
        return [
            self::APPLICATION_RATE_PER_KM,
            self::APPLICATION_RATE_PER_DKA,
            self::APPLICATION_RATE_PER_HA,
            self::APPLICATION_RATE_PER_100_km,
        ];
    }

    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ACTIVE,
            self::STATUS_INACTIVE,
        ];
    }

    public static function listProductsCustom()
    {
        return self::select(
            'su_products.id',
            'su_products.name',
            'su_products.rate',
            'su_products.status',
            'su_products.application_rate',
            'su_products.default_price',
            'su_products.quarantine_period',
            'su_products.information',
            'su_products.created_at',
            'su_products.updated_at',
            DB::raw("
                jsonb_build_object(
                    'id', so.id,
                    'name', so.\"name\",
                    'address', so.address,
                    'email', so.email,
                    'phone', so.phone,
                    'active', so.active,
                    'identity_number', so.identity_number,
                    'vat_number', so.vat_number
                ) as organization
            "),
            DB::raw("
                jsonb_build_object(
                    'id',spt.id,
                    'name', spt.name,
                    'icon', spt.icon
                ) as type
            "),
            DB::raw("
                case when count(sai.id) = 0
                then '[]'
                else
                    jsonb_agg(
                        case when sai.id notnull
                            then 
                            jsonb_build_object(
                                'id', sai.id,
                                'name', sai.name,
                                'quantity', spai.quantity,
                                'unit', jsonb_build_object(
                                            'id', suom1.id,
                                            'full_name', suom1.full_name,
                                            'short_name', suom1.short_name 
                                        )
                            )
                        end 
                    )
                end as active_ingredients
            "),
            DB::raw("
                jsonb_build_object(
                    'id', suom2.id,
                    'full_name', suom2.full_name,
                    'short_name', suom2.short_name,
                    'category_id', suom2.category_id,
                    'coefficient', suom2.coefficient
                )as unit
            ")
        )
            ->join('su_products_type as spt', 'spt.id', '=', 'su_products.type_id')
            ->join('su_units_of_measure as suom2', 'suom2.id', '=', 'su_products.unit_id')
            ->join('su_organizations as so', 'so.id', '=', 'su_products.organization_id')
            ->leftJoin('su_product_active_ingredients as spai', 'spai.product_id', '=', 'su_products.id')
            ->leftJoin('su_units_of_measure as suom1', 'suom1.id', '=', 'spai.unit_id')
            ->leftJoin('su_active_ingredients as sai', 'sai.id', '=', 'spai.active_ingredient_id')
            ->groupBy('su_products.id', 'spt.id', 'suom2.id', 'so.id')
            ->orderBy('su_products.id');
    }
}
