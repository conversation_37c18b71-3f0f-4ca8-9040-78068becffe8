<?php

namespace App\Models\Products;

use App\Models\BaseModel;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductType extends BaseModel
{
    use TimestampsTrait;

    public const PPP = 'ppp';
    public const FERTILISER = 'fertiliser';

    public $timestamps = true;

    protected $table = 'su_products_type';

    protected $primaryKey = 'id';

    protected $fillable = ['name', 'icon'];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'type_id');
    }

    public static function getProductTypeIdByName(string $typeName)
    {
        return self::where(['name' => $typeName])->pluck('id')->first();
    }

    public static function getProductTypeIdsByNames(array $typeNames)
    {
        return self::whereIn('name', $typeNames)->pluck('id');
    }
}
