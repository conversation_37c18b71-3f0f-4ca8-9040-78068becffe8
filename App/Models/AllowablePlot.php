<?php

namespace App\Models;

use Config;
use DB;

class AllowablePlot extends BaseModel
{
    public $timestamps = false;
    protected $table = 'layer_allowable_final';
    protected $primaryKey = 'gid';

    /**
     * The users that belong to the plot.
     */
    public function users()
    {
        return $this->belongsToMany('App\Models\User', 'su_users_favourite_plots', 'plot_id', 'group_id');
    }

    public function extent()
    {
        return $this->selectRaw('ST_Extent(ST_Buffer(geom, 75)) AS extent')->where('elg_ident', '=', $this->elg_ident);
    }

    public function asGeoJSON()
    {
        return $this->selectRaw('ST_AsGeoJSON(geom) AS geom_json')->where('elg_ident', '=', $this->elg_ident);
    }

    public static function plotOnCoordinates($lon, $lat, $extent, $organizationId)
    {
        $query = self::select(
            DB::raw('st_astext(st_transform(st_setsrid(layer_allowable_final.geom, ' . Config::get('globals.DEFAULT_DB_CRS') . '), 3857)) as geom'),
            'gid',
            'elg_ident',
            'zemlishte',
            'station.id as station_id'
        )
            ->leftJoin('su_users_stations AS station', function ($join) use ($lon, $lat, $organizationId) {
                $join->on(DB::raw("ST_Distance(station.geom, st_transform(st_setsrid(st_makepoint({$lon}, {$lat}), 4326), " . Config::get('globals.DEFAULT_DB_CRS') . '))'), '<=', DB::raw('((station.radius*1000))'))
                    ->on('station.organization_id', '=', DB::raw($organizationId));
            })
            ->whereRaw("st_intersects(
            st_transform(st_setsrid(st_makepoint({$lon}, {$lat}), 4326), " . Config::get('globals.DEFAULT_DB_CRS') . '),
            layer_allowable_final.geom)
            ');

        if (!empty($extent)) {
            $query->whereRaw("layer_allowable_final.geom && st_transform(st_setsrid(ST_MakeEnvelope({$extent}),3857), " . Config::get('globals.DEFAULT_DB_CRS') . ')');
        }

        return $query;
    }
}
