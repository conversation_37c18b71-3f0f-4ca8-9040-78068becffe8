<?php

namespace App\Models;

use Illuminate\Support\Facades\Schema;

/**
 * Class Integration.
 *
 * @property string $token
 * @property string $organization_id
 * @property string $contract_id
 * @property string $package_id
 * @property string package_slug
 * @property string $integration_address
 * @property string $status
 */
class Integration extends BaseModel
{
    public const ACTIVE = 'Active';
    public const INACTIVE = 'Inactive';
    public const PACKAGE_SLUG_SHORT_FT = 'FT';
    public const PACKAGE_SLUG_SHORT_IM = 'IM';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_integration';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['token', 'organization_id', 'package_id', 'package_slug_short', 'integration_address', 'status'];

    public function integrationAddress()
    {
        return $this->belongsTo('App\Models\IntegrationAddress', 'integration_address', 'id');
    }

    public function reports()
    {
        return $this->hasMany('App\Models\IntegrationsReports', 'integration_id', 'id');
    }

    public function reportsTypes()
    {
        return $this->hasManyThrough('App\Models\IntegrationReportsTypes', 'App\Models\IntegrationsReports', 'integration_id', 'id', 'id', 'integration_reports_types_id');
    }

    public function deactivate()
    {
        $this->status = self::INACTIVE;

        return $this;
    }

    /**
     * @param null|mixed $packageSlugShort
     * @param null|mixed $status
     *
     * @return \Illuminate\Database\Query\Builder
     */
    public static function getLinkedQuery($organizationIds, $packageSlugShort = null, $status = null)
    {
        $query = self::with('integrationAddress')
            ->join('su_organizations as org', 'org.id', '=', 'su_integration.organization_id')
            ->whereIn('org.id', $organizationIds);

        if ($packageSlugShort) {
            $query->where('su_integration.package_slug_short', $packageSlugShort);
        }

        if ($status) {
            $query->where('su_integration.status', $status);
        }

        return $query;
    }

    /**
     * @param int $organizationId
     * @param string $packageSlugShort
     * @param string $status
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getIntegrationsDataForSync($organizationId, $packageSlugShort, $status = Integration::ACTIVE, $reportTypesFilter = [])
    {
        $query = self::getLinkedQuery([$organizationId], $packageSlugShort, $status)
            ->select('su_integration.id', 'su_integration.token', 'su_integration.integration_address', 'su_integrations_reports.id as integrations_report_id')
            ->join('su_integrations_reports', function ($query) {
                $query->on('su_integrations_reports.integration_id', '=', 'su_integration.id');
            })
            ->join('su_integration_reports_types', 'su_integration_reports_types.id', '=', 'su_integrations_reports.integration_reports_types_id');

        if (isset($reportTypesFilter) && count($reportTypesFilter) > 0) {
            $query = $query->whereIn('su_integration_reports_types.name', $reportTypesFilter);
        }

        return $query->get();
    }

    public static function getTableColumns()
    {
        return Schema::getColumnListing((new Integration())->getTable());
    }

    public static function findByToken(string $token)
    {
        return self::where('token', $token)->first();
    }
}
