<?php

namespace App\Models;

class Recommendation extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_recommendations';

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'id', 'organization_id');
    }
}
