<?php

namespace App\Models\StationsGS;

use Config;
use DB;

class SensorLastData extends BaseStationModel
{
    protected $connection = 'weather_stations';
    protected $table = 'sens_last';

    public static function getSensorData($stationId, $sensorId)
    {
        return self::select(
            'sens_last.stat_id',
            'sens_last.sens_id',
            'sens_last.owner',
            'sens_last.ts',
            'sens_last.value',
            'sens_last.recieved',
            's.multiplier'
        )
            ->join('sensors as s', 's.id', '=', 'sens_last.sens_id')
            ->where('sens_last.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sens_last.stat_id', $stationId)
            ->where('sens_last.sens_id', $sensorId);
    }

    public static function getAllSensorsData($stationId, $sensors = [])
    {
        $query = self::select(
            'sens_last.stat_id',
            'sens_last.sens_id',
            'sens_last.owner',
            'sens_last.ts',
            'sens_last.value',
            'sens_last.recieved',
            's.multiplier',
            's.name',
            DB::RAW('s.multiplier * sens_last.value as current_value')
        )
            ->join('sensors as s', 's.id', '=', 'sens_last.sens_id')
            ->where('sens_last.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sens_last.stat_id', $stationId)
            ->whereRaw(' sens_last.ts=(SELECT MAX(sens_last.ts) FROM sens_last WHERE sens_last.stat_id = ' . $stationId . ' )');

        if (!empty($sensors)) {
            $query->whereIn('sens_id', $sensors);
        }

        return $query;
    }

    public static function getPrecipitationData($stationId, $sensorId)
    {
        $subQueryThis = DB::raw('
            (SELECT * FROM sens_data FORCE INDEX (ssotv)
            where sens_id = ' . $sensorId . '
            and stat_id = ' . $stationId . '
            ORDER BY ts desc limit 1) as this');

        $subQueryPrev = DB::raw('
            (SELECT * FROM sens_data FORCE INDEX (ssotv)
            where sens_id = ' . $sensorId . '
            and stat_id = ' . $stationId . '
            ORDER BY ts desc limit 2) as prev');

        return self::select(
            'this.ts as this_ts',
            'prev.ts as prev_ts',
            DB::RAW('IFNULL(ABS(this.value - prev.value) * s.multiplier, 0) AS precipitation')
        )
            ->from($subQueryThis)
            ->leftJoin($subQueryPrev, 'prev.ts', '<', 'this.ts')
            ->join('sensors as s', 's.id', '=', 'this.sens_id');
    }

    public static function getDailyPrecipitationData($stationId, $sensorId, $fromTS)
    {
        $subQueryThis = DB::raw('
            (SELECT * FROM sens_data
            where sens_id = ' . $sensorId . '
            and stat_id = ' . $stationId . '
            and ts >= ' . $fromTS . '
            ORDER BY ts desc limit 1) as last');

        $subQueryPrev = DB::raw('
            (SELECT * FROM sens_data
            where sens_id = ' . $sensorId . '
            and stat_id = ' . $stationId . '
            and ts >= ' . $fromTS . '
            ORDER BY ts limit 1) as first_today');

        return self::select(
            'last.ts as last_ts',
            'first_today.ts as first_today_ts',
            DB::RAW('IFNULL(ABS(last.value - first_today.value) * s.multiplier, 0) AS precipitation')
        )
            ->from($subQueryThis)
            ->leftJoin($subQueryPrev, 'first_today.ts', '<', 'last.ts')
            ->join('sensors as s', 's.id', '=', 'last.sens_id');
    }
}
