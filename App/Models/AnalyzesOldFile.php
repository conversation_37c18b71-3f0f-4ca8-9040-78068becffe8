<?php

namespace App\Models;

use DB;

class AnalyzesOldFile extends BaseModel
{
    public $timestamps = false;
    protected $table = 'su_analyzes_old_files';

    public function plot()
    {
        return $this->hasOne('App\Models\Plot', 'gid', 'plot_id');
    }

    public function orderPlotRel()
    {
        return $this->belongsTo('App\Models\OrderPlotRel', 'order_plot_rel_id', 'id');
    }

    public function samplingType()
    {
        return $this->hasOne('App\Models\OrderPlotSamplingType', 'id', 'sopst_id');
    }

    public static function getFilesAndTypeForSamples()
    {
        $query = AnalyzesOldFile::select(
            'su_analyzes_old_files.id as id',
            DB::raw("'file' as type")
        )
            ->leftJoin(
                'su_satellite_orders_plots_rel AS sopr',
                'sopr.id',
                '=',
                'su_analyzes_old_files.order_plot_rel_id'
            )
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->join('su_satellite_layers_plots AS slp', function ($join) {
                $join->on('slp.plot_id', '=', 'su_analyzes_old_files.plot_id')
                    ->on('slp.date', '=', 'su_analyzes_old_files.date');
            })
            ->leftJoin('su_satellite_orders_plots_sampling_types as sopst', 'sopst.id', '=', 'su_analyzes_old_files.sopst_id')
            ->where('so.status', '!=', 'canceled')
            ->distinct();

        return $query;
    }
}
