<?php

namespace App\Models;

use App\Helpers\Helper;
use Auth;
use Config;
use DB;
use Request;

class Order extends BaseModel
{
    public const TYPE_INDEX = 'index';
    public const TYPE_SOIL = 'soil';
    public const TYPE_METEO = 'meteo';
    public const TYPE_VRA = 'vra';
    public const TYPE_INDEX_WATER = 'index_water';
    public const TYPE_SOIL_VRA = 'soil_vra';

    public const STATUS_PROCESSED = 'processed';
    public const STATUS_NO_TILE = 'no_tile';
    public const STATUS_PAID = 'paid';
    public const STATUS_PROCESSING = 'processing';
    public const STATUS_CANCELED = 'canceled';

    public $timestamps = false;

    protected $table = 'su_satellite_orders';
    protected $appends = [];
    protected $casts = [
        'from_date' => 'datetime:Y-m-d',
        'to_date' => 'datetime:Y-m-d',
    ];

    public function plots()
    {
        return $this->belongsToMany('App\Models\Plot', 'su_satellite_orders_plots_rel', 'order_id', 'plot_id')
            ->withPivot('price', 'note');
    }

    public function vraPlots()
    {
        return $this->belongsToMany('App\Models\Plot', 'su_satellite_orders_vra', 'order_id', 'plot_id');
    }

    public function orderPlots()
    {
        return $this->hasMany('App\Models\OrderPlotRel', 'order_id', 'id');
    }

    public function orderVraQuery()
    {
        return $this->hasOne('App\Models\OrderVra', 'order_id', 'id');
    }

    public function payments()
    {
        return $this->hasMany('App\Models\Payment', 'order_id', 'id');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function files()
    {
        return $this->hasMany(OrderFile::class, 'order_id', 'id');
    }

    public function approvedBy()
    {
        return $this->belongsTo('App\Models\User', 'approved_by', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id');
    }

    public static function findByUuid(string $uuid): self
    {
        return self::where('uuid', $uuid)->first();
    }

    public static function getOrderStatuses()
    {
        return collect([
            'new' => 'New',
            'waiting_payment' => 'Awaiting Payment',
            'paid' => 'Paid',
            'processing' => 'Processing',
            'processed' => 'Processed',
            'error' => 'Error',
            'canceled' => 'Canceled',
            'no_tile' => 'No satellite image',
        ]);
    }

    public function getContactPerson()
    {
        return $this->join('su_organizations_contact_persons AS ocp', function ($join) {
            $join->on('ocp.organization_id', '=', 'su_satellite_orders.organization_id');
            $join->on('is_representative', '=', DB::raw('true'));
        })
            ->where('su_satellite_orders.id', $this->id)
            ->select('ocp.email', 'ocp.name')
            ->first();
    }

    public function getOrders()
    {
        $request = Request::all();
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $orders = DB::table('su_satellite_orders AS so')->select(
            'so.id',
            'so.year',
            'so.from_date',
            'so.to_date',
            'so.type',
            'org.name as organization_name',
            DB::raw("(CASE WHEN so.type = 'vra' THEN round(((sum(ST_Area(sp_vra.geom))/1000)*{$areaCoef})::numeric, 3) ELSE  round(((sum(ST_Area(sp.geom))/1000)*{$areaCoef})::numeric, 3) END) as area"),
            DB::raw('sum(sopr.price) AS price'),
            DB::raw("(CASE WHEN so.type = 'vra' THEN count(vra.plot_id) WHEN so. TYPE = 'soil_vra' THEN COUNT (soil_vra.plot_id) ELSE count(sopr.id) END) as plots_num"),
            DB::raw('SUM(CASE WHEN sopr.is_soil_sample_completed = TRUE THEN 1 ELSE 0 END) as completed_plots'),
            'so.status',
            'so.date',
            'so.id as order_id',
            'so.color',
            'so.extent',
            'so.note',
            'so.company_name',
            'so.company_mol',
            'so.company_eik',
            'so.company_address',
            'so.end_price',
            'so.salesman'
        )
            ->join('su_organizations AS org', 'org.id', '=', 'so.organization_id')
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', 'so.id', '=', 'sopr.order_id')
            ->leftJoin('su_orders_satellite_vra AS vra', 'so.id', '=', 'vra.order_id')
            ->leftJoin('su_orders_soil_vra AS soil_vra', 'so.id', '=', 'soil_vra.order_id')
            ->leftJoin('su_satellite_plots AS sp', 'sp.gid', '=', 'sopr.plot_id')
            ->leftJoin('su_satellite_plots AS sp_vra', 'sp_vra.gid', '=', 'vra.plot_id')
            ->where(
                'org.service_provider_id',
                Auth::user()->globalUser()->serviceProvider->id
            )
            ->groupBy('so.id', 'org.name');

        $sort = Helper::getSortRequest('-order_id');
        $orders = $this->scopeRestOrderBy($orders, $sort);

        // Sampler Admin constraint
        if (Auth::user()->globalUser()->isAn('SAMPLER_ADMIN')) {
            $orders = $orders->where('so.type', '=', 'soil');
        }

        // Filter
        if (isset($request['id']) && $request['id']) {
            $orders = $orders->where('so.id', '=', $request['id']);
        }
        if (isset($request['status'])) {
            $orders = $orders->where('so.status', '=', $request['status']);
        }
        if (isset($request['farm'])) {
            $orders = $orders->where('so.farm', '=', $request['farm']);
        }
        if (isset($request['year'])) {
            $orders = $orders->where('so.year', '=', $request['year']);
        }
        if (isset($request['salesman'])) {
            $orders = $orders->where('so.salesman', '=', $request['salesman']);
        }
        if (isset($request['organization_name'])) {
            $orders = $orders->where('org.name', 'ILIKE', '%' . $request['organization_name'] . '%');
        }
        if (isset($request['type'])) {
            $orders = $orders->where('so.type', '=', $request['type']);
        }

        if (isset($request['date'])) {
            $orders = $orders->whereRaw('so.date::date = ?', [date('Y-m-d', strtotime($request['date']))]);
        }

        return $orders;
    }

    public function getOrderStatusEnum()
    {
        return DB::table('pg_enum AS e')->select('e.enumlabel')
            ->join('pg_type AS t', 'e.enumtypid', '=', 't.oid')
            ->where('t.typname', '=', 'su_satellite_orders_status_enum')
            ->get();
    }

    public function createVraOrder($data)
    {
        return DB::transaction(function () use ($data) {
            $this->saveVraOrder($data);

            return $this->saveVraData($data);
        });
    }

    public static function cancelOrder(int $orderId, int $userId): bool
    {
        $order = self::select('su_satellite_orders.*')
            ->join('su_organizations AS org', 'org.id', '=', 'su_satellite_orders.organization_id')
            ->join('su_organizations_users AS u', 'u.organization_id', '=', 'org.id')
            ->where('su_satellite_orders.id', $orderId)
            ->where('u.user_id', $userId)->firstOrFail();

        $order->status = self::STATUS_CANCELED;
        $order->save();

        return true;
    }

    public static function orderedPolygons(Organization $organization, $year)
    {
        return self::selectRaw('gid, ST_X(ST_Transform (ST_Centroid(geom), 3857)) AS longitude, ST_Y(ST_Transform (ST_Centroid(geom), 3857)) AS latitude')
            ->join(
                'su_satellite_orders_plots_rel',
                'su_satellite_orders.id',
                '=',
                'su_satellite_orders_plots_rel.order_id'
            )
            ->join('su_satellite_plots', 'su_satellite_plots.gid', '=', 'su_satellite_orders_plots_rel.plot_id')
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->whereRaw('su_satellite_orders.status = ?', ['processed'])
            ->where('su_satellite_orders.type', '=', 'meteo')
            ->where('su_satellite_orders.organization_id', $organization->id)
            ->where('su_satellite_orders.year', $year)
            ->where('fu.is_visible', true)
            ->where('fu.user_id', \Illuminate\Support\Facades\Auth::user()->id)
            ->groupBy('gid')
            ->get();
    }

    public static function getFarmingYears(int $currentYear, ?Organization $organization = null)
    {
        $qb = self::select([
            'su_satellite_orders.year as title',
            'su_satellite_orders.year as id',
            DB::raw("case when su_satellite_orders.\"year\" = {$currentYear} then true else false end as default"),
            'su_satellite_orders.year as year',
            DB::raw("concat(su_satellite_orders.\"year\"-1,'-10-01') as from_date"),
            DB::raw("concat(su_satellite_orders.\"year\",'-09-30') as to_date"),
            DB::raw("concat(su_satellite_orders.\"year\"-1,'/',su_satellite_orders.\"year\") as farming_year"),
        ]);

        if ($organization && $organization->id) {
            $qb->where('su_satellite_orders.organization_id', $organization->id);
        }

        return $qb->where('su_satellite_orders.status', '!=', 'canceled')
            ->whereIn('su_satellite_orders.type', ['index', 'soil', 'vra', 'meteo'])
            ->groupBy('su_satellite_orders.year')
            ->orderBy('su_satellite_orders.year', 'asc')
            ->get();
    }

    public static function getOrdersQuery()
    {
        return self::join('su_satellite_orders_plots_rel', 'su_satellite_orders_plots_rel.order_id', '=', 'su_satellite_orders.id');
    }

    private function saveVraOrder($data)
    {
        $date = date('Y-m-d h:i:sa', time());

        $this->organization_id = $data['order']['organization'] ?? Auth::user()->lastChosenOrganization->id;
        $this->farm = $data['order']['farm'];
        $this->area = (int) $data['order']['area'];
        $this->date = $date;
        $this->year = $data['order']['year'];
        $this->status = $data['order']['status'];
        $this->type = $data['order']['type'];
        $this->createdBy()->associate(Auth::user());

        $this->save();
    }

    private function saveVraData($data)
    {
        $vraOrder = 'soil_vra' == $data['order']['type'] ? new OrderSoilVra() : new OrderSatelliteVra();

        $vraOrder->order_id = $this->id;
        $vraOrder->plot_id = (int) $data['plot_id'];
        $vraOrder->created = date('Y-m-d h:i:sa', time());
        $vraOrder->layer_id = $data['layer_id'];
        $vraOrder->class_number = (int) $data['class_number'];
        $vraOrder->flat_rate = (int) $data['flat_rate'];
        $vraOrder->data = json_encode($data['data']);
        $vraOrder->vector_data = json_encode($data['vector_data']);
        $vraOrder->flat_rate_total = (int) $data['flat_rate_total'];
        $vraOrder->variable_rate_total = (int) $data['variable_rate_total'];
        $vraOrder->difference = (int) $data['difference'];
        $vraOrder->difference_percent = (int) $data['difference_percent'];
        $vraOrder->product_percent = (int) $data['product_percent'];
        $vraOrder->tiff_path = $data['tiff_path'];
        $vraOrder->name = $data['name'];
        if (array_key_exists('product_text', $data) && '' != $data['product_text']) {
            $vraOrder->product_text = $data['product_text'];
        }

        $vraOrder->save();

        return $vraOrder->id;
    }
}
