<?php

namespace App\Models;

use phpseclib\Math\BigInteger;

/**
 * Class MachineImplementWorkOperation.
 *
 * @property BigInteger $id
 * @property string $implement_id
 * @property string $name
 */
class MachineImplementWorkOperation extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machines_implements_work_operations';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['implement_id', 'work_operation_id'];

    /** @var array */
    protected $casts = [
        'work_operations' => 'array',
    ];
}
