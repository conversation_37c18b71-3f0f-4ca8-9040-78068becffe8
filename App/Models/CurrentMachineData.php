<?php

namespace App\Models;

use App\Models\StaticModels\FarmingYear;
use DB;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CurrentMachineData.
 *
 * @property int $organization_id
 * @property string $geojson
 */
class CurrentMachineData extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_current_machines_data';

    /**
     * @var int
     */
    protected $primaryKey = 'organization_id';

    /** @var array */
    protected $fillable = ['organization_id', 'geojson'];

    protected $casts = [
        'geojson' => 'array',
        'echart' => 'array',
        'machines_count_by_work_operations' => 'array',
    ];

    public static function createMachinesCurrentGeoJsonFromReport(string $tmpTable, int $organizationId)
    {
        $machines = CurrentMachineData::machinesQuery($tmpTable, $organizationId);
        $plots = CurrentMachineData::plotsQuery($organizationId);
        $machinesPlots = CurrentMachineData::machinesPlotsQuery();

        return self::from('machines_plots')
            ->withExpression('machines', $machines)
            ->withExpression('plots', $plots)
            ->withExpression('machines_plots', $machinesPlots)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', json_agg(
                        json_build_object(
                            'type', 'Feature',
                            'geometry', ST_AsGeoJSON(st_transform(coordinates, 3857) )::json,
                            'properties', json_build_object(
                                'name', name,
                                'unit_id', unit_id,
                                'wialon_unit_imei', wialon_unit_imei,
                                'status', status,
                                'speed', speed,
                                'driver', driver,
                                'trailer', CASE WHEN trailer NOTNULL AND trailer <> '' AND trailer_status = 'Active'
                                    THEN trailer
                                    ELSE 'No trailer'
                                END,
                                'last_communication', time,
                                'plot_name', plot_name,
                                'plot_id', plot_id,
                                'farm_id', farm_id,
                                'unit_type', type
                            )
                        )
                    )
                ) as geojson")
            );
    }

    public static function machinesQuery(string $tmpTable, int $organizationId)
    {
        // Machines
        return self::from('su_machine_units as smu')
            ->select(
                DB::raw('distinct on (smu.wialon_unit_imei) CASE WHEN tmp.wialon_unit_imei ISNULL THEN smu.wialon_unit_imei ELSE tmp.wialon_unit_imei::bigint END as wialon_unit_imei'),
                'smu.id as unit_id',
                'smu.name',
                DB::raw("
                    CASE 
                        WHEN tmp.time ISNULL THEN to_char(smu.last_communication, 'YYYY-MM-DD HH24:MI:SS')
                        ELSE to_char(tmp.time, 'YYYY-MM-DD HH24:MI:SS') 
                    END as time
                "),
                'tmp.speed',
                'tmp.value as ignition',
                DB::raw("
                CASE WHEN tmp.value = '1.00' AND TO_NUMBER(tmp.speed, '999') > 0 THEN 'Moving'
                WHEN tmp.value = '0.00' THEN 'Off'
                WHEN tmp.value = '1.00' AND TO_NUMBER(tmp.speed, '999') = 0 THEN 'Idle' 
                WHEN tmp.value ISNULL THEN 'Offline' END as status"),
                DB::raw('CASE WHEN tmp.coordinates ISNULL THEN smu.last_position ELSE tmp.coordinates END as coordinates'),
                'tmp.driver',
                'tmp.trailer',
                'smu.type',
                'smi.status as trailer_status'
            )
            ->leftJoin($tmpTable . ' as tmp', DB::raw('tmp.wialon_unit_imei::bigint'), '=', 'smu.wialon_unit_imei')
            // TODO: Change this to join by id when it is available
            ->leftJoin('su_machines_implements as smi', 'smi.name', '=', 'tmp.trailer')
            ->where('smu.organization_id', '=', $organizationId)
            ->where(function ($query) {
                $query->where(DB::raw('LOWER(tmp.sensor)'), '=', 'ignition');
                $query->orWhereNull('tmp.sensor');
            })
            ->orderBy('smu.wialon_unit_imei')
            ->orderBy('time', 'desc');
    }

    public static function plotsQuery(int $organizationId)
    {
        $currentFarmYear = FarmingYear::getAll()->where('default', true)->pluck('year')->first();

        return self::from('su_satellite_plots AS ssp')
            ->select(
                'ssp.gid AS plot_id',
                'ssp.name AS plot_name',
                'ssp.geom',
                'ssp.farm_id',
                'f.organization_id'
            )
            ->join('su_farms AS f', function ($join) use ($organizationId) {
                $join->on('f.id', '=', 'ssp.farm_id')
                    ->where('f.organization_id', '=', $organizationId);
            })
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'ssp.gid')
            ->join('su_satellite_orders as so', function ($join) use ($currentFarmYear) {
                $join->on('so.id', '=', 'sopr.order_id')
                    ->where('so.year', '=', $currentFarmYear)
                    ->where('so.type', '=', 'index')
                    ->whereIn('so.status', ['processed', 'processing', 'paid', 'no_tile']);
            });
    }

    public static function machinesPlotsQuery()
    {
        // Machines Plots
        return self::from('machines')
            ->select(
                DB::raw('distinct on (machines.wialon_unit_imei) wialon_unit_imei'),
                'machines.unit_id',
                'machines.name',
                'machines.time',
                'machines.speed',
                'machines.ignition',
                'machines.status',
                'machines.coordinates',
                'machines.driver',
                'machines.trailer',
                'machines.type',
                'machines.trailer_status',
                'plots.plot_id',
                'plots.plot_name',
                'plots.geom',
                'plots.farm_id',
                DB::raw('ST_Distance(machines.coordinates, plots.geom) as dist')
            )
            ->leftJoin('plots', function ($join) {
                $join->on(DB::raw('ST_DWithin(machines.coordinates, plots.geom, 100)'), DB::raw('true::boolean'));
            })
            ->orderBy('machines.wialon_unit_imei', 'desc')
            ->orderBy('dist', 'asc');
    }

    public static function getMachinesCountByWorkOperations(int $organizationId, array $filters = []): array
    {
        return self::getMachinesCurrentQuery($organizationId, $filters)
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'work_operations', feature->'properties'->'work_operations',
                    'value',COALESCE(count(feature->'properties'->>'unit_id'), 0)
                ) AS machines_count_by_work_operations
            ")
            ->groupBy(DB::raw("feature->'properties'->'work_operations'"))
            ->get()
            ->pluck('machines_count_by_work_operations')
            ->toArray();
    }

    /**
     * Get current machine data by state for echart.
     *
     * @param array $filters = [
     *                       'plot_ids' => int[],
     *                       'machine_ids' => int[],
     *                       'farm_ids' => int[]
     *                       ]
     */
    public static function getEchartMachinesByState(int $organizationId, array $filters = []): array
    {
        return self::getMachinesCurrentQuery($organizationId, $filters)
            ->selectRaw("
                JSON_BUILD_OBJECT(
                    'name', feature->'properties'->>'status',
                    'value', COALESCE(COUNT(feature->'properties'->>'unit_id'), 0),
                    'status', feature->'properties'->>'status'
                ) as echart
            ")
            ->groupBy(DB::raw("feature->'properties'->>'status'"))
            ->orderBy(DB::raw("feature->'properties'->>'status'"))
            ->get()
            ->pluck('echart')
            ->toArray();
    }

    /**
     * Get current machine data by organization id.
     *
     * @param number $organizationId
     *
     * @return null|CurrentMachineData
     */
    public static function getByOrganizationId($organizationId)
    {
        return self::where('organization_id', $organizationId)->first();
    }

    public static function getMachinesCurrentGeoJson(int $organizationId, array $filters = []): CurrentMachineData
    {
        return self::getMachinesCurrentQuery($organizationId, $filters)
            ->select(
                DB::raw("
                    JSONB_BUILD_OBJECT(
                        'type', 'FeatureCollection',
                        'features', COALESCE(
                            JSONB_AGG(feature),
                            '[]'::JSONB
                        )
                    ) AS geojson
                ")
            )
            ->first();
    }

    /**
     * Get current data geojson by filters.
     *
     * @param array $filters = array(
     *                       'plot_ids'      => int[],
     *                       'machine_ids'   => int[],
     *                       'farm_ids'      => int[],
     *                       'trailers'      => string[],
     *                       'statuses'      => string[]
     *
     * @return CurrentMachineData
     */
    private static function getMachinesCurrentQuery(int $organizationId, array $filters = []): Builder
    {
        $currentDataQuery = self::select(
            DB::raw("
                current_data_feature || JSONB_BUILD_OBJECT(
                    'properties', (
                        current_data_feature->'properties' || JSONB_BUILD_OBJECT(
                            'work_operations', JSONB_AGG(DISTINCT
                                JSONB_BUILD_OBJECT(
                                    'id', swo.id,
                                    'name', swo.name,
                                    'color', swo.color
                                )
                            ),
                            'work_operations_ids', JSONB_AGG(DISTINCT
                                 swo.id
                            )
                        )
                    )
                ) AS feature
        ")
        )
            ->crossJoin(DB::raw("jsonb_array_elements((su_current_machines_data.geojson->'features')::JSONB) current_data_feature"))
            ->leftJoin('su_machines_implements AS smi', function ($join) {
                $join->on('smi.organization_id', '=', 'su_current_machines_data.organization_id');
                $join->on('smi.name', '=', DB::raw("current_data_feature->'properties'->>'trailer'"));
            })
            ->leftJoin('su_machines_implements_work_operations AS smiwo', 'smiwo.implement_id', '=', 'smi.id')
            ->join('su_work_operations AS swo', function ($join) {
                $join->on('swo.id', '=', 'smiwo.work_operation_id');
                $join->orWhere(function ($query) {
                    $query->whereNull('smiwo.work_operation_id');
                    $query->where('swo.name', DB::raw("'Unknown'::work_operations_types_enum"));
                });
            })
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) {
                $join->on('sspc.plot_id', '=', DB::raw("(current_data_feature->'properties'->>'plot_id')::integer"))
                    ->whereBetween(DB::raw('now()'), [DB::raw('sspc.from_date'), DB::raw('sspc.to_date')])
                    ->where('sspc.is_primary', '=', DB::raw('true'));
            })
            ->where('su_current_machines_data.organization_id', $organizationId)
            ->groupBy('current_data_feature', 'smi.id');

        $machinesCurrentQuery = self::from('machines_current')
            ->withExpression('machines_current', $currentDataQuery);

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $machinesCurrentQuery = parent::queryWhereInWithNullCheck($machinesCurrentQuery, "(feature->'properties'->>'plot_id')::integer", $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $machinesCurrentQuery = parent::queryWhereInWithNullCheck($machinesCurrentQuery, "(feature->'properties'->>'farm_id')::integer", $filters['farm_ids']);
        }

        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $machinesCurrentQuery->whereIn('sspc.crop_id', $filters['crop_ids']);
        }

        $workOperations = $filters['work_operations'] ?? [];
        if (is_array(reset($workOperations))) {
            /*
             * @var $workOperations array[array[int]]
             * example : [[31, 42], [23], ...]
             */
            $machinesCurrentQuery->where(function ($query) use ($workOperations) {
                foreach ($workOperations as $compoundWorkOperation) {
                    $query->orWhere(function ($query) use ($compoundWorkOperation) {
                        $compoundWorkOperationJson = json_encode($compoundWorkOperation);
                        $query->where(DB::raw("(feature->'properties'->'work_operations_ids')"), '@>', DB::raw("'{$compoundWorkOperationJson}'::JSONB"));
                        $query->where(DB::raw("(feature->'properties'->'work_operations_ids')"), '<@', DB::raw("'{$compoundWorkOperationJson}'::JSONB"));
                    });
                }
            });
        }

        if (is_int(reset($workOperations))) {
            /**
             * @var $workOperations array[int]
             * example : [31, 42, ...]
             */
            $workOperationsJson = json_encode($workOperations);
            $machinesCurrentQuery->where(DB::raw("(feature->'properties'->'work_operations_ids')"), '@>', DB::raw("'{$workOperationsJson}'::JSONB"));
        }

        if (isset($filters['machine_ids']) && count($filters['machine_ids']) > 0) {
            $machinesCurrentQuery->whereIn(DB::raw("feature->'properties'->>'unit_id'"), $filters['machine_ids']);
        }

        if (isset($filters['statuses']) && count($filters['statuses']) > 0) {
            $machinesCurrentQuery->whereIn(DB::raw("feature->'properties'->>'status'"), $filters['statuses']);
        }

        return $machinesCurrentQuery;
    }
}
