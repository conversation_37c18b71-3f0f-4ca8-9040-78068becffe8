<?php

namespace App\Models;

use App\Services\Irrigation\IrrigationReportService;
use App\Services\Machine\MachineProductsReportService;
use App\Services\Machine\MachineReportService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScheduledReport extends Model
{
    public const FILE_TYPE_PDF = 'pdf';
    public const FILE_TYPE_XLS = 'xls';

    public const REPORT_DATE_RANGE_TODAY = 'today';
    public const REPORT_DATE_RANGE_YESTERDAY = 'yesterday';
    public const REPORT_DATE_RANGE_LAST_WEEK = 'last_week';
    public const REPORT_DATE_RANGE_THIS_MONTH = 'this_month';

    public const DELIVERY_FREQUENCY_DAILY = 'daily';
    public const DELIVERY_FREQUENCY_WEEKLY = 'weekly';
    public const DELIVERY_FREQUENCY_MONTHLY = 'monthly';

    public const DELIVERY_DAY_START_OF_MONTH = 'start_of_month';
    public const DELIVERY_DAY_END_OF_MONTH = 'end_of_month';

    public const REPORT_TYPE_MACHINES_TASKS = 'machines_tasks_report';
    public const REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS = 'tasks_products_and_costs_report';
    public const REPORT_TYPE_IRRIGATION_TASKS = 'irrigation_tasks_report';

    public static $reportTypes = [
        self::REPORT_TYPE_MACHINES_TASKS => MachineReportService::class,
        self::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS => MachineProductsReportService::class,
        self::REPORT_TYPE_IRRIGATION_TASKS => IrrigationReportService::class,
    ];
    protected $fillable = [
        'name',
        'type',
        'subject',
        'body',
        'locale',
        'file_type',
        'report_parameters',
        'report_date_range',
        'recipient_emails',
        'delivery_frequency',
        'delivery_time',
        'delivery_days',
        'scheduled_to_send_at',
        'last_sent_at',
    ];

    protected $casts = [
        'recipient_emails' => 'array',
        'report_parameters' => 'array',
        'delivery_days' => 'array',
        'scheduled_to_send_at' => 'datetime',
        'last_sent_at' => 'datetime',
    ];

    protected $appends = [
        'is_using_default_service_provider_locale',
    ];

    public function getIsUsingDefaultServiceProviderLocaleAttribute(): bool
    {
        return filled($this->attributes['locale']);
    }

    public static function getAllFileTypes(): array
    {
        return [
            self::FILE_TYPE_PDF,
            self::FILE_TYPE_XLS,
        ];
    }

    public static function getAllReportDateRanges(): array
    {
        return [
            self::REPORT_DATE_RANGE_TODAY,
            self::REPORT_DATE_RANGE_YESTERDAY,
            self::REPORT_DATE_RANGE_LAST_WEEK,
            self::REPORT_DATE_RANGE_THIS_MONTH,
        ];
    }

    public static function getAllDeliveryFrequencies(): array
    {
        return [
            self::DELIVERY_FREQUENCY_DAILY,
            self::DELIVERY_FREQUENCY_WEEKLY,
            self::DELIVERY_FREQUENCY_MONTHLY,
        ];
    }

    public static function getAllReportTypes(): array
    {
        return [
            self::REPORT_TYPE_MACHINES_TASKS,
            self::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS,
            self::REPORT_TYPE_IRRIGATION_TASKS,
        ];
    }

    public static function getAllTimeSlots(): array
    {
        $intervals = CarbonPeriod::between('07:00', '22:00')->interval('30 minutes');

        return collect($intervals)->map(function (Carbon $interval) {
            return $interval->format('H:i');
        })->toArray();
    }

    public static function getAllDeliveryDaysForMonthlyFrequency(): array
    {
        return [
            self::DELIVERY_DAY_START_OF_MONTH,
            self::DELIVERY_DAY_END_OF_MONTH,
        ];
    }

    public static function getAllowedGroupByValuesFor(string $reportType): array
    {
        switch ($reportType) {
            case self::REPORT_TYPE_MACHINES_TASKS:
                $allowedGroupByValues = [
                    'machine',
                    'driver',
                    'implement',
                    'farm',
                    'plot',
                    'crop',
                ];

                break;
            case self::REPORT_TYPE_TASKS_PRODUCTS_AND_COSTS:
                $allowedGroupByValues = [
                    'machine',
                    'farm',
                    'plot',
                    'crop',
                    'product',
                ];

                break;
            case self::REPORT_TYPE_IRRIGATION_TASKS:
                $allowedGroupByValues = [
                    'platform',
                    'plot',
                    'crop',
                    'farm',
                ];

                break;
            default:
                $allowedGroupByValues = [];
        }

        return $allowedGroupByValues;
    }

    public static function getAllowedOrderByColumnsFor(?string $reportType): array
    {
        switch ($reportType) {
            case self::REPORT_TYPE_MACHINES_TASKS:
                $allowedOrderByColumns = [
                    'covered_area',
                    'traveled_distance',
                    'fuel_consumed_driving',
                    'fuel_consumed_stay',
                    'fuel_consumed_ha',
                    'fuel_consumed_km',
                ];

                break;
            case self::REPORT_TYPE_IRRIGATION_TASKS:
                $allowedOrderByColumns = [
                    'duration',
                    'rate',
                    'covered_area',
                    'water_amount',
                ];

                break;
            default:
                $allowedOrderByColumns = [];
        }

        return $allowedOrderByColumns;
    }

    public function getParametersForFilter(): array
    {
        return collect($this->getParameters())
            ->transformToCamelCaseKeys()
            ->except(['groupBy', 'orderBy'])
            ->merge([
                'from' => $this->startsAt()->getTimestamp(),
                'to' => $this->endsAt()->getTimestamp(),
            ])
            ->toArray();
    }

    /**
     * @return array|mixed|string
     */
    public function getParameters(string $forKey = null)
    {
        if (blank($forKey)) {
            return $this->report_parameters;
        }

        return data_get($this->report_parameters, snake_case($forKey), []);
    }

    public function getLocale(): string
    {
        if (blank($this->locale)) {
            return $this->owner->globalUser()->serviceProvider->getLocaleByCountryCode();
        }

        return strtolower($this->locale);
    }

    public function startsAt(): \Illuminate\Support\Carbon
    {
        switch ($this->report_date_range) {
            case self::REPORT_DATE_RANGE_TODAY:
                $startsAt = now()->startOfDay();

                break;
            case self::REPORT_DATE_RANGE_YESTERDAY:
                $startsAt = now()->subDay()->startOfDay();

                break;
            case self::REPORT_DATE_RANGE_LAST_WEEK:
                $startsAt = now()->subWeek()->startOfWeek();

                break;
            case self::REPORT_DATE_RANGE_THIS_MONTH:
                $startsAt = now()->startOfMonth();

                break;
            default:
                $startsAt = now()->startOfDay();
        }

        return $startsAt;
    }

    public function endsAt(): \Illuminate\Support\Carbon
    {
        switch ($this->report_date_range) {
            case self::REPORT_DATE_RANGE_TODAY:
            case self::REPORT_DATE_RANGE_THIS_MONTH:
                $endsAt = now()->endOfDay();

                break;
            case self::REPORT_DATE_RANGE_YESTERDAY:
                $endsAt = now()->subDay()->endOfDay();

                break;
            case self::REPORT_DATE_RANGE_LAST_WEEK:
                $endsAt = now()->subWeek()->endOfWeek();

                break;
            default:
                $endsAt = now()->endOfDay();
        }

        return $endsAt;
    }

    public function scopeForUser(Builder $builder, User $user): Builder
    {
        return $builder->where('user_id', $user->getKey());
    }

    public function isADailyReport(): bool
    {
        return self::DELIVERY_FREQUENCY_DAILY === $this->delivery_frequency;
    }

    public function isAWeeklyReport(): bool
    {
        return self::DELIVERY_FREQUENCY_WEEKLY === $this->delivery_frequency;
    }

    public function isAMonthlyReport(): bool
    {
        return self::DELIVERY_FREQUENCY_MONTHLY === $this->delivery_frequency;
    }

    public function dailyDeliveryDaysIncludeToday(): bool
    {
        return in_array(now()->englishDayOfWeek, $this->delivery_days);
    }

    public function weeklyDeliveryDayIsToday(): bool
    {
        return in_array(now()->englishDayOfWeek, $this->delivery_days);
    }

    public function monthlyDeliveryDayIsToday(): bool
    {
        if (in_array(self::DELIVERY_DAY_START_OF_MONTH, $this->delivery_days)) {
            return now()->isSameDay(now()->firstOfMonth()) && now()->isCurrentMonth(true);
        }

        return now()->isLastOfMonth() && now()->isCurrentMonth(true);
    }

    public function hasNeverBeenScheduledToSend(): bool
    {
        return blank($this->scheduled_to_send_at);
    }

    public function hasNeverBeenSentUntilNow(): bool
    {
        return blank($this->last_sent_at);
    }

    public function scheduledToSendAtHasPassed(): bool
    {
        return $this->scheduled_to_send_at->isPast();
    }

    public function isNotLastSentToday(): bool
    {
        return !Carbon::parse($this->last_sent_at)->isToday();
    }

    public function isNotLastSentThisWeek(): bool
    {
        return !Carbon::parse($this->last_sent_at)->isBetween(now()->startOfWeek(), now()->endOfWeek());
    }

    public function isNotLastSentThisMonth(): bool
    {
        return !Carbon::parse($this->last_sent_at)->isCurrentMonth(true);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
