<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 27/04/2018
 * Time: 16:53.
 */

namespace App\Models;

/**
 * Class ConfigParam.
 */
class ConfigParam extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'main';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'config_params';

    public function configParamValues()
    {
        return $this->hasMany('App\Models\ConfigParamValue', 'config_param_id', 'id');
    }
}
