<?php

namespace App\Models;

class SoilGridParams extends BaseModel
{
    public const CUSTOM_GRID = 'customGrid';
    public const CUSTOM = 'custom';
    public const USE_EXISTING = 'useExisting';
    public $timestamps = false;

    protected $table = 'su_satellite_soil_grid_params';
    protected $primaryKey = 'id';

    public static function getByOrderId($orderId)
    {
        return self::where('order_id', $orderId);
    }
}
