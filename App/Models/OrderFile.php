<?php

namespace App\Models;

class OrderFile extends BaseModel
{
    public $timestamps = true;
    protected $table = 'su_satellite_orders_files';

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public static function getFilePathsByOrderIds(array $orderIds = [])
    {
        return self::whereIn('order_id', $orderIds)
            ->select('path')
            ->distinct();
    }
}
