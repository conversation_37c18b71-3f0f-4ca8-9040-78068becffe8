<?php

namespace App\Models\UnitOfMeasure;

use App\Models\ActiveIngredient;
use App\Models\BaseModel;
use App\Models\Organization;
use App\Models\Products\Product;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UnitOfMeasure extends BaseModel
{
    use TimestampsTrait;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_units_of_measure';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['full_name', 'short_name', 'service_provider_id', 'organization_id', 'category_id', 'base_unit_id', 'coefficient', 'numerator_unit_id', 'denominator_unit_id'];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasureCategory::class, 'category_id', 'id');
    }

    public function baseUnit(): BelongsTo
    {
        return $this->belongsTo(self::class, 'base_unit_id', $this->primaryKey);
    }

    public function childrenBaseUnit(): HasMany
    {
        return $this->hasMany(self::class, 'base_unit_id');
    }

    public function numeratorUnit(): BelongsTo
    {
        return $this->belongsTo(self::class, 'numerator_unit_id', $this->primaryKey);
    }

    public function childrenDenominatorUnit(): HasMany
    {
        return $this->hasMany(self::class, 'denominator_unit_id');
    }

    public function denominatorUnit(): BelongsTo
    {
        return $this->belongsTo(self::class, 'denominator_unit_id', $this->primaryKey);
    }

    public function childrenNumeratorUnit(): HasMany
    {
        return $this->hasMany(self::class, 'numerator_unit_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'unit_id');
    }

    public function activeIngredients(): belongsToMany
    {
        return $this->belongsToMany(
            ActiveIngredient::class,
            'su_product_active_ingredients',
            'unit_id',
            'active_ingredient_id',
        );
    }

    public function scopeForOrganization(Builder $builder, int $organizationId): Builder
    {
        return $builder->where('organization_id', $organizationId)->orWhereNull('organization_id');
    }

    public function scopeForServiceProvider(Builder $builder, int $serviceProviderId): Builder
    {
        return $builder->where('service_provider_id', $serviceProviderId);
    }

    public function scopeProductsByOrganization(Builder $builder, int $organizationId): Builder
    {
        return $builder->with([
            'products' => function ($products) use ($organizationId) {
                return $products->where('organization_id', $organizationId);
            },
        ]);
    }

    public static function getBaseUnits()
    {
        return self::whereNull(['organization_id', 'base_unit_id', 'numerator_unit_id', 'denominator_unit_id'])->get();
    }

    public static function getCompoundUnits()
    {
        return self::whereNotNull(['numerator_unit_id', 'denominator_unit_id'])->get();
    }
}
