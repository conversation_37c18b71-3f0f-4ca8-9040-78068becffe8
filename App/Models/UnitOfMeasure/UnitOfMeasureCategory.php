<?php

namespace App\Models\UnitOfMeasure;

use App\Models\BaseModel;
use App\Models\BigInteger;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UnitOfMeasureCategory extends BaseModel
{
    use TimestampsTrait;

    public const TYPE_COMPOUND = 'Compound';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_units_of_measure_categories';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['name', 'icon'];

    public function units(): HasMany
    {
        return $this->hasMany(UnitOfMeasure::class, 'category_id');
    }

    public static function isCompoundCategory($categoryId): bool
    {
        return $categoryId === UnitOfMeasureCategory::where('name', UnitOfMeasureCategory::TYPE_COMPOUND)->pluck('id')->first();
    }
}
