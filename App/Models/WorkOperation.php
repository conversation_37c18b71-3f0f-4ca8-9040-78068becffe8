<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use phpseclib\Math\BigInteger;

/**
 * Class WorkOperation.
 *
 * @property BigInteger $id
 * @property string $name
 * @property string $color
 */
class WorkOperation extends BaseModel
{
    public const MACHINE_TASKS = 'machine-tasks';
    public const MACHINE_EVENTS = 'machine-events';
    public const WORK_OPERATION_UNKNOWN = 'Unknown';
    public const WORK_OPERATION_TRANSPORT = 'Transport';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_work_operations';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['name', 'color'];

    /** @var array */
    protected $hidden = ['pivot'];

    protected $casts = [
        'work_operations' => 'array',
    ];

    public static function forInstance(): array
    {
        return [
            self::MACHINE_EVENTS,
            self::MACHINE_TASKS,
        ];
    }

    public static function getNameEnumsData()
    {
        $resultColumns = DB::select('SELECT unnest(enum_range(NULL::work_operations_types_enum))');

        return array_map(function ($column) {
            return $column->unnest;
        }, $resultColumns);
    }

    public static function getNamesByIds(array $ids): Collection
    {
        return self::select('name')->whereIn('id', $ids)->pluck('name');
    }

    public static function getWorkOperationsForMachineTasks(array $filters): Builder
    {
        $query = self::join('su_machine_tasks as smt', 'su_work_operations.id', '=', DB::raw('ANY(smt.work_operation_ids)'))
            ->leftJoin('su_machine_units as smu', 'smu.id', '=', 'smt.machine_unit_id')
            ->leftJoin('su_farms as sf', function ($query) use ($filters) {
                $query->on('sf.organization_id', '=', 'smu.organization_id');

                $farmIds = json_decode(Arr::get($filters, 'farm_ids', '[]'), true);
                $query->when(count($farmIds), function ($query) use ($farmIds) {
                    $query->whereIn('sf.id', $farmIds);
                });
            })
            ->when(Arr::get($filters, 'start_date'), function ($query, $startDate) {
                return $query->where(DB::raw("to_char(smt.start_date, 'YYYY-MM-DD')"), '>=', $startDate);
            })
            ->when(Arr::get($filters, 'end_date'), function ($query, $endDate) {
                return $query->where(DB::raw("to_char(smt.end_date, 'YYYY-MM-DD')"), '<=', $endDate);
            });

        $plotIds = json_decode(Arr::get($filters, 'plot_ids', '[]'), true);
        $query->when(count($plotIds), function ($query) use ($plotIds) {
            $query->whereIn('smt.plot_id', $plotIds);
        });

        return $query;
    }

    public static function getWorkOperationsForMachineEvents(array $filters): Builder
    {
        $query = self::join('su_machine_events as sme', 'su_work_operations.id', '=', DB::raw('ANY(sme.work_operation_ids)'))
            ->leftJoin('su_machine_units as smu', 'smu.id', '=', 'sme.machine_id')
            ->leftJoin('su_farms as sf', function ($query) use ($filters) {
                $query->on('sf.organization_id', '=', 'smu.organization_id');

                $farmIds = json_decode(Arr::get($filters, 'farm_ids', '[]'), true);
                $query->when(count($farmIds), function ($query) use ($farmIds) {
                    $query->whereIn('sf.id', $farmIds);
                });
            })
            ->when(Arr::get($filters, 'start_date'), function ($query, $startDate) {
                return $query->where(DB::raw("to_char(sme.start_date, 'YYYY-MM-DD')"), '>=', $startDate);
            })
            ->when(Arr::get($filters, 'end_date'), function ($query, $endDate) {
                return $query->where(DB::raw("to_char(sme.end_date, 'YYYY-MM-DD')"), '<=', $endDate);
            });

        $plotIds = json_decode(Arr::get($filters, 'plot_ids', '[]'), true);
        $query->when(count($plotIds), function ($query) use ($plotIds) {
            $query->whereIn('sme.plot_id', $plotIds);
        });

        return $query;
    }
}
