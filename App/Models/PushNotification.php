<?php

namespace App\Models;

class PushNotification extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_notifications';

    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'group_id');
    }
}
