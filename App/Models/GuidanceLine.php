<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use MStaack\LaravelPostgis\Eloquent\PostgisTrait;

class GuidanceLine extends BaseModel
{
    use PostgisTrait;

    public const AB_LINE = 'AB line';
    public const HEADLAND = 'Headland';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_guidance_line';
    protected $primaryKey = 'gid';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'type', 'plot_id', 'shift', 'offset', 'geom'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['geom_json' => 'array'];

    protected $postgisFields = [
        'geom',
    ];

    protected $postgisTypes = [
        'geom' => [
            'geomtype' => 'geometry',
            'srid' => 32635,
        ],
    ];

    public static function getAllGuidanceLineTypes(): array
    {
        return [
            self::AB_LINE,
            self::HEADLAND,
        ];
    }

    public function scopeSelectWithGeomJSON(Builder $builder): Builder
    {
        return $builder->selectRaw('su_guidance_line.gid , su_guidance_line."name", su_guidance_line."type", su_guidance_line.plot_id, su_guidance_line.shift, su_guidance_line."offset", json_build_object(
                    \'type\', \'Feature\',
                    \'geometry\', ST_AsGeoJSON(ST_Transform(su_guidance_line.geom, 4326))::json
                ) as geom_json ');
    }

    public function scopeFilterByPlotId(Builder $builder, ?int $plotId): Builder
    {
        return $builder->where('su_guidance_line.plot_id', $plotId);
    }

    public function scopeForOrganization(Builder $builder, int $organizationId): Builder
    {
        return $builder->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'su_guidance_line.plot_id')
            ->join('su_farms AS sf', 'sf.id', '=', 'ssp.farm_id')
            ->where('sf.organization_id', $organizationId);
    }

    public function plot(): BelongsTo
    {
        return $this->belongsTo(Plot::class, 'plot_id');
    }
}
