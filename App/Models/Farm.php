<?php

namespace App\Models;

use Auth;
use DateTime;

/**
 * Class Farm.
 *
 * @property int $id
 * @property string $name
 * @property string $comment
 * @property int $organization_id
 * @property DateTime $created
 */
class Farm extends BaseModel
{
    public const CREATED_AT = 'created';

    public $timestamps = false;

    protected $table = 'su_farms';
    protected $fillable = ['name', 'comment', 'organization_id', 'uuid'];
    protected $hidden = ['users', 'organization', 'plots', 'pins', 'createdBy'];

    public function __construct(array $attributes = [])
    {
        $this->created = new DateTime();
        $this->createdBy()->associate(Auth::user());
        parent::__construct($attributes);
    }

    public function users()
    {
        return $this->belongsToMany('App\Models\User', 'su_farms_users');
    }

    public function organization()
    {
        return $this->belongsTo('App\Models\Organization', 'organization_id', 'id');
    }

    public function plots()
    {
        return $this->hasMany('App\Models\Plot', 'farm_id', 'id');
    }

    public function pins()
    {
        return $this->hasMany(Pin::class, 'farm_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id');
    }

    public static function getByUuids(array $uuids)
    {
        return self::whereIn('uuid', $uuids)->get();
    }

    public static function getByName(Organization $organization, string $name): ?self
    {
        return self::where('su_farms.name', $name)
            ->where('su_farms.organization_id', $organization->id)
            ->first();
    }

    public static function updateFarm($farmId, $organizationId, $newName, $newComment)
    {
        return self::where([
            ['id', $farmId],
            ['organization_id', $organizationId]])
            ->update(['name' => $newName, 'comment' => $newComment]);
    }

    /**
     * @param $plotUuid
     */
    public static function getFarmsWithPlots(int $organizationId)
    {
        return self::select(
            'su_farms.id',
            'su_farms.name',
            'su_farms.organization_id',
            'su_farms.created_by',
            'ssp.uuid as plot_uuid',
            'ssp.name as plot_name',
            'ssp.gid as plot_id',
            'ssp.area as plot_area'
        )
            ->join('su_satellite_plots as ssp', 'ssp.farm_id', 'su_farms.id')
            ->where('su_farms.organization_id', '=', $organizationId)
            ->get()->toArray();
    }

    public static function getUserFarms(int $userId)
    {
        return self::select('su_farms.id', 'su_farms.name', 'su_farms.organization_id', 'su_farms.created_by')
            ->join('su_farms_users as sfu', function ($join) use ($userId) {
                $join->on('sfu.farm_id', '=', 'su_farms.id')
                    ->where('sfu.user_id', '=', $userId);
            })
            ->get();
    }

    public static function getOrganizationFarms(int $organizationID)
    {
        return self::select('su_farms.id', 'su_farms.name', 'su_farms.organization_id', 'su_farms.created_by')
            ->where('su_farms.organization_id', '=', $organizationID)
            ->get();
    }
}
