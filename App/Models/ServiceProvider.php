<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceProvider extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'service_providers';

    protected $connection = 'main';

    protected $fillable = [
        'name',
        'slug',
        'logo',
        'country_id',
        'company_info',
    ];

    public function getLocaleByCountryCode($countryCode = null): string
    {
        if (filled($countryCode)) {
            return strtolower($countryCode);
        }

        return strtolower($this->country->iso_alpha_2_code);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
