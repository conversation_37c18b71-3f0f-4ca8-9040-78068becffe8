<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use phpseclib\Math\BigInteger;

/**
 * Class IrrigationPlatform.
 *
 * @property BigInteger $contract_id
 * @property BigInteger $organization_id
 * @property string $name
 * @property float $width
 */
class MachineImplement extends BaseModel
{
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_INACTIVE = 'Inactive';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machines_implements';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['organization_id', 'integration_id', 'wialon_unit_id', 'name', 'width', 'status'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'width' => 'float',
        'work_operations' => 'array',
    ];

    public function workOperations()
    {
        return $this->belongsToMany(WorkOperation::class, MachineImplementWorkOperation::class, 'implement_id', 'work_operation_id', 'id', 'id');
    }

    /**
     * @param int $organizationId
     * @param array $filters = array(
     *                       'name' => string,
     *                       'organization_id' => int,
     *                       'integration_id' => int
     *                       )
     *
     * @return Builder $query
     */
    public static function getFilteredMachineImplementsQuery(array $filters = []): Builder
    {
        $query = MachineImplement::select(
            'su_machines_implements.*',
            DB::raw("
                COALESCE(
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'id', swo.id,
                                'name', swo.name,
                                'color', swo.color
                            )
                        ) FILTER (WHERE smiwo.id NOTNULL),
                        '[]'::JSONB
                ) AS work_operations
            ")
        )
            ->leftJoin('su_machines_implements_work_operations AS smiwo', 'smiwo.implement_id', '=', 'su_machines_implements.id')
            ->leftJoin('su_work_operations AS swo', 'swo.id', '=', 'smiwo.work_operation_id');

        if (isset($filters['organization_id'])) {
            $query->where('su_machines_implements.organization_id', $filters['organization_id']);
        }

        if (isset($filters['integration_id'])) {
            $query->where('su_machines_implements.integration_id', $filters['integration_id']);
        }

        if (isset($filters['name'])) {
            $searchStr = '%' . $filters['name'] . '%';
            $query->where('su_machines_implements.name', 'ILIKE', $searchStr);
        }

        $query->groupBy('su_machines_implements.id');

        return $query;
    }

    public static function getTableName()
    {
        return with(new static())->getTable();
    }
}
