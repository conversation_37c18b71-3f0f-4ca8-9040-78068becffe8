<?php

namespace App\Models;

use App\Models\Products\Product;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use phpseclib\Math\BigInteger;

class MachineTaskProduct extends BaseModel
{
    use TimestampsTrait;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machine_task_products';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'report_json' => 'object',
    ];

    protected $guarded = [];

    public function machineTask(): BelongsTo
    {
        return $this->belongsTo(MachineTask::class, 'task_id', 'id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
