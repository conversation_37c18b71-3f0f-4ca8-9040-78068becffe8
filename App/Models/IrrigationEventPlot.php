<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use phpseclib\Math\BigInteger;

/**
 * Class IrrigationEventPlot.
 *
 * @property BigInteger $event_id
 * @property int $crop_id
 * @property int $plot_id
 * @property float $event_area
 * @property string $geom
 * @property string $thumbnail (xml)
 */
class IrrigationEventPlot extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_events_plots';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['event_id', 'crop_id', 'plot_id', 'event_area', 'geom', 'thumbnail'];

    public function irrigationEvent()
    {
        return $this->hasMany('App\Models\IrrigationEvent', 'event_id', 'id');
    }

    public static function insertIrrigationEventPlot(Builder $eventsPlotsQuery): void
    {
        $querySql = $eventsPlotsQuery->toSqlWithBindings();
        $querySqlDoNothing = "({$querySql}) ON CONFLICT DO NOTHING";

        self::insertUsing([
            'event_id',
            'crop_id',
            'plot_id',
            'event_area',
            'geom',
            'thumbnail',
        ], $querySqlDoNothing);
    }

    /**
     * Removes all irrigation events plots for the specified plots and orders.
     */
    public static function removeByOrderUuidsAndPlotUuids(array $plotUuids, array $orderUuids)
    {
        self::join('su_irrigation_events AS sie', 'sie.id', '=', 'su_irrigation_events_plots.event_id')
            ->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'su_irrigation_events_plots.plot_id')
            ->join('su_satellite_orders_plots_rel AS ssopr', 'ssopr.plot_uuid', '=', 'ssp.uuid')
            ->join('su_satellite_orders AS sso', 'sso.uuid', '=', 'ssopr.order_uuid')
            ->whereIn('ssopr.plot_uuid', $plotUuids)
            ->whereIn('ssopr.order_uuid', $orderUuids)
            ->whereBetween('sie.date', [DB::raw('sso.from_date'), DB::raw('sso.to_date')])
            ->whereIn('ssp.uuid', $plotUuids)
            ->delete();
    }
}
