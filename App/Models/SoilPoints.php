<?php

namespace App\Models;

use DB;

class SoilPoints extends BaseModel
{
    public $timestamps = false;
    protected $table = 'su_satellite_soil_points';
    protected $primaryKey = 'gid';

    public static function getTableName()
    {
        return with(new static())->getTable();
    }

    /**
     * @deprecated Used in a1(admin) project
     */
    public function loadProbeContent($soprId)
    {
        return self::loadProbeContentQuery($soprId)->orderBy('su_satellite_soil_points.sample_id', 'asc')->get()->toArray();
    }

    /**
     * @deprecated Used in a1(admin) project
     */
    public static function loadProbeContentQuery($soprId)
    {
        return self::select([
            'su_satellite_soil_points.sample_id',
            'ssn.sample_number as barcode',
            'sopst.sampling_type_name as treatment_type',
            'ad.lab_number',
            'ad.lab_date',
            'ad.data',
        ])
            ->leftJoin('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'su_satellite_soil_points.gid')
            ->leftJoin('su_satellite_orders_plots_sampling_types as sopst', 'sopst.id', '=', 'ssn.sopst_id')
            ->join('su_analyzes_data as ad', 'ad.id', '=', 'ssn.analyzes_data_id')
            ->where('su_satellite_soil_points.sopr_id', $soprId);
    }

    public static function dataBysoprId($soprId)
    {
        return self::select([
            'su_satellite_soil_points.sample_id',
            DB::raw('ssn.sample_number as barcode'),
        ])
            ->join('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'su_satellite_soil_points.gid')
            ->where('su_satellite_soil_points.sopr_id', $soprId)
            ->get()->toArray();
    }

    public static function getBySoprId(array $soprIds)
    {
        return self::whereIn('sopr_id', $soprIds);
    }

    public static function pointsAndBarcodesQuery(array $select, array $pointGids)
    {
        return self::select($select)
            ->leftJoin('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'su_satellite_soil_points.gid')
            ->leftJoin('su_satellite_orders_plots_sampling_types as sopst', 'sopst.id', '=', 'ssn.sopst_id')
            ->leftJoin('su_satellite_orders_plots_rel as sopr', 'sopr.id', '=', 'su_satellite_soil_points.sopr_id')
            ->whereIn('su_satellite_soil_points.gid', $pointGids);
    }

    public static function getGridPointsDataBySoprIds(array $soprIds)
    {
        return self::select([
            'su_satellite_soil_points.uuid as pointUuid',
            'su_satellite_soil_points.sample_id as sampleId',
            'ssgp.type as gridType',
            'sopr.plot_uuid as plotUuid',
            'sopr.order_uuid as orderUuid',
            'ad.barcode',
            'ad.lab_number as labNumber',
            'ad.data',
        ])
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.id', '=', 'su_satellite_soil_points.sopr_id')
            ->join('su_satellite_soil_grid_params as ssgp', 'ssgp.order_id', '=', 'sopr.order_id')
            ->leftJoin('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'su_satellite_soil_points.gid')
            ->leftJoin('su_analyzes_data as ad', 'ad.barcode', '=', 'ssn.sample_number')
            ->whereIn('sopr.id', $soprIds)
            ->where('ssgp.status', '<>', 'error')->get()->toArray();
    }
}
