<?php

namespace App\Models;

use Auth;
use DB;

class PlotCrop extends BaseModel
{
    public $timestamps = false;

    /**
     * Table name.
     *
     * @var string
     */
    protected $table = 'su_satellite_plots_crops';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'farm_year' => 'object',
        'analysis_crop_data' => 'array',
    ];

    public static function setIrrigatedBy(array $param, $irrigated)
    {
        if (isset($param['id'])) {
            self::where('id', '=', $param['id'])->update(['irrigated' => $irrigated]);
        }

        if (isset($param['plot_ids'])) {
            self::whereIn('plot_id', $param['plot_ids'])->update(['irrigated' => $irrigated]);
        }
    }

    /**
     * Get crop history by plot.
     *
     * @param int $plotId The plot's id
     * @param string $lang The crop language (e.g. 'en', 'bg' etc.)
     */
    public static function getCropHistoryByPlot(int $plotId, string $lang = 'en'): array
    {
        return self::from('su_satellite_plots_crops as spc')
            ->select(
                'scc.id as crop_id',
                DB::raw('scc.crop_name_' . $lang . ' as crop'),
                DB::raw("
                    JSON_BUILD_OBJECT(
                        'id', spc.year,
                        'year', spc.year,
                        'title', spc.year,
                        'farming_year', (spc.year - 1) || '/' || spc.year,
                        'from_date', spc.from_date,
                        'to_date', spc.to_date,
                        'default', spc.year = date_part('year', now()),
                        'is_primary', spc.is_primary
                    ) as farm_year
                ")
            )
            ->join('su_crop_codes as scc', 'scc.id', '=', 'spc.crop_id')
            ->where('spc.plot_id', $plotId)
            ->get()->toArray();
    }

    public static function getCropDataByPlotsForAnalysis(array $plotIds, array $farmYears, string $lang = 'en'): array
    {
        $cropDataQuery = self::from('su_satellite_plots_crops as spc')
            ->selectRaw(
                "DISTINCT
                spc.plot_id as plot_id,
                scc.id as crop_id,    
                scc.crop_name_{$lang} as crop_name,
                (spc.year - 1) || '/' || spc.year as farming_year"
            )
            ->join('su_crop_codes as scc', 'scc.id', '=', 'spc.crop_id')
            ->join('su_satellite_plots as ssp', 'ssp.gid', '=', 'spc.plot_id')
            ->join('su_farms AS sf', function ($join) {
                $join->on('sf.id', '=', 'ssp.farm_id')
                    ->where('sf.organization_id', Auth::user()->lastChosenOrganization->id);
            })
            ->when(count($farmYears), function ($query) use ($farmYears) {
                return $query->whereIn('spc.year', $farmYears);
            })
            ->when(count($plotIds), function ($query) use ($plotIds) {
                return $query->whereIn('spc.plot_id', $plotIds);
            })
            ->where('spc.is_primary', true);

        $query = self::withExpression('filtered_plots', $cropDataQuery)
            ->from('filtered_plots')
            ->selectRaw(
                "
                json_object_agg(
                    CONCAT(plot_id, '_', farming_year), json_build_object(
                                                                        'crop_name', CASE WHEN crop_name LIKE 'unknown' THEN initcap(crop_name) ELSE crop_name END,
                                                                        'crop_id',crop_id,
                                                                        'farm_years', farming_year
                                                                    )
                ) analysis_crop_data"
            );

        return $query->pluck('analysis_crop_data')->first() ?? [];
    }
}
