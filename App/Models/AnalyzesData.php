<?php

namespace App\Models;

use Auth;
use DB;

class AnalyzesData extends BaseModel
{
    public $timestamps = true;
    protected $table = 'su_analyzes_data';
    protected $primaryKey = 'id';

    public static function getTableName()
    {
        return with(new static())->getTable();
    }

    public static function loadAnalyzesData($analyzeId, $userId)
    {
        $query = self::loadAnalyzesDataQuery();

        if ($analyzeId) {
            $query->where('su_analyzes_data.analyzes_id', $analyzeId);
        }
        // super_admin constraint
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN')) {
            $query->where('a.user_id', $userId);
        }

        return $query->orderBy('id', 'asc')->get()->toArray();
    }

    public static function loadAnalyzesDataQuery()
    {
        return self::select([
            'su_analyzes_data.id',
            'su_analyzes_data.analyzes_id',
            'su_analyzes_data.order_id',
            'su_analyzes_data.sopr_id',
            'sp.name',
            'su_analyzes_data.lab_number',
            'su_analyzes_data.lab_date',
            'su_analyzes_data.client_name',
            'su_analyzes_data.sample_id',
            'su_analyzes_data.barcode',
            'su_analyzes_data.data',
            DB::raw('su_analyzes_data.created_at::DATE'),
        ])
            ->join('su_analyzes as a', 'a.id', '=', 'su_analyzes_data.analyzes_id')
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.id', '=', 'su_analyzes_data.sopr_id')
            ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id');
    }

    public static function loadSoprIds($analyzeId)
    {
        return self::select([
            DB::raw('DISTINCT sopr.id'),
        ])
            ->join('su_satellite_soil_sample_numbers as ssn', 'ssn.sample_number', '=', 'su_analyzes_data.barcode')
            ->join('su_satellite_soil_points as ssp', 'ssp.gid', '=', 'ssn.gid')
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.id', '=', 'ssp.sopr_id')
            ->where('su_analyzes_data.analyzes_id', $analyzeId)
            ->get()->toArray();
    }

    public static function soprIdCounts($analyzeId)
    {
        return self::select([
            'su_analyzes_data.sopr_id',
            DB::raw('count(su_analyzes_data.sopr_id) as count'),
            'su_analyzes_data.order_id',
            DB::raw('string_agg(DISTINCT(su_analyzes_data.client_name)::text, \', \') as client_name'),
        ])
            ->where('su_analyzes_data.analyzes_id', $analyzeId)
            ->groupBy('su_analyzes_data.sopr_id', 'su_analyzes_data.order_id')
            ->get()->toArray();
    }

    public static function pointsData($analyzeId)
    {
        return self::select([
            'su_analyzes_data.sample_id',
            'su_analyzes_data.barcode',
        ])
            ->where('su_analyzes_data.analyzes_id', $analyzeId)
            ->get()->toArray();
    }
}
