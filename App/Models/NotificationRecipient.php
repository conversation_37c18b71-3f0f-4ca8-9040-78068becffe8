<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Models;

/**
 * Class NotificationDomain.
 *
 * @property int $id
 * @property int $organization_id
 * @property int $channel_id
 * @property int $domain_id
 * @property string $recipient
 */
class NotificationRecipient extends BaseModel
{
    public $timestamps = false;
    protected $table = 'notification_recipients';

    public function organization()
    {
        $this->belongsTo(Organization::class);
    }

    public function channel()
    {
        $this->belongsTo(NotificationChannel::class);
    }

    public function domain()
    {
        $this->belongsTo(NotificationDomain::class);
    }
}
