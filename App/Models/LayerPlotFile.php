<?php

namespace App\Models;

class LayerPlotFile extends BaseModel
{
    public $timestamps = true;
    protected $table = 'su_satellite_layers_plots_files';
    protected $fillable = ['path', 'web_path'];

    public function layerPlot()
    {
        return $this->belongsTo(LayerPlot::class);
    }

    public static function getLayerPlotFilePathsByOrderIds(array $orderIds)
    {
        return self::join('su_satellite_layers_plots', 'su_satellite_layers_plots.id', '=', 'su_satellite_layers_plots_files.layer_plot_id')
            ->whereIn('su_satellite_layers_plots.order_id', $orderIds)
            ->select('su_satellite_layers_plots.path')
            ->distinct();
    }
}
