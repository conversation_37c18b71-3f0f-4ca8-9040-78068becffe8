<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Model;

class OrganizationDevice extends Model
{
    public $timestamps = true;
    protected $table = 'su_organizations_devices';
    protected $primaryKey = 'id';

    public function getByOrgId($orgId)
    {
        return $this->where('organization_id', $orgId)
            ->orderBy('created_at', 'desc');
    }

    public function getByActiveDevice($deviceId, $organizationId, $from, $to)
    {
        return DB::select("
			SELECT su_organizations_devices.*, su_organizations.name as organization_name  FROM su_organizations_devices
			JOIN su_organizations ON su_organizations.id = su_organizations_devices.organization_id
			WHERE (device_id = {$deviceId} AND is_active = 'true' AND organization_id != {$organizationId})
			AND ((('{$from}' BETWEEN active_from AND active_to)  OR ('{$to}' BETWEEN active_from AND active_to))
			OR ((active_from BETWEEN '{$from}' AND '{$to}') AND (active_to BETWEEN '{$from}' AND '{$to}')));
		");
    }

    public function exists($deviceId, $organizationId)
    {
        return $this->where([['device_id', $deviceId], ['organization_id', $organizationId]])->exists();
    }

    public function changeStatus($deviceId, $organizationId, $status)
    {
        return $this->where([['device_id', $deviceId], ['organization_id', $organizationId]])
            ->update(['is_active' => $status]);
    }

    public function deleteOrgDevice($deviceId, $organizationId)
    {
        return $this->where([['device_id', $deviceId], ['organization_id', $organizationId]])
            ->delete();
    }

    public function updateActiveToDate($deviceId, $organizationId, $activeToDate)
    {
        return $this->where([['device_id', $deviceId], ['organization_id', $organizationId], ['active_from', '<=', $activeToDate]])
            ->update(['active_to' => $activeToDate]);
    }
}
