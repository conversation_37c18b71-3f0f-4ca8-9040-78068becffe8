<?php

namespace App\Models;

use App\Exceptions\ValidationException;
use Auth;

class CropCategory extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_crop_categories';

    /**
     * Get the categories for current service provider by crop.
     *
     * @param int $cropId The crop id
     */
    public static function getByCrop(int $cropId): array
    {
        $currentServiceProvider = Auth::user()->globalUser()->serviceProvider;

        return self::select([
            'id',
            'category',
            'crop_id',
        ])
            ->where([
                ['crop_id', $cropId],
                ['service_provider_id', $currentServiceProvider->id],
            ])
            ->get()
            ->toArray();
    }

    /**
     * @param int cropId The crop_id from su_satellite_orders_plots_rel
     * @param int cropCategoryId The Id of cropCategory to validate
     *
     * @throws ValidationException
     */
    public static function validateCropCategory(int $cropId, int $cropCategoryId): CropCategory
    {
        $cropCategory = CropCategory::findOrFail($cropCategoryId);
        $currentServiceProvider = Auth::user()->globalUser()->serviceProvider;

        if ($cropCategory->service_provider_id !== $currentServiceProvider->id) {
            throw new ValidationException('No such category for this service provider.');
        }

        if ($cropCategory->crop_id !== $cropId) {
            throw new ValidationException('No such category for this crop.');
        }

        return $cropCategory;
    }
}
