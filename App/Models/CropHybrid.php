<?php

namespace App\Models;

use App\Exceptions\ValidationException;

class CropHybrid extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_crop_hybrid';

    /**
     * @param int cropId The crop_id from su_satellite_orders_plots_rel
     * @param int cropHybridId The id of cropHybrid to validate
     *
     * @throws ValidationException
     */
    public static function validateCropHybrid(int $cropId, int $cropHybridId): CropHybrid
    {
        $cropHybrid = CropHybrid::findOrFail($cropHybridId);

        if ($cropHybrid->crop_id !== $cropId) {
            throw new ValidationException('No such hybrid for this crop.');
        }

        return $cropHybrid;
    }
}
