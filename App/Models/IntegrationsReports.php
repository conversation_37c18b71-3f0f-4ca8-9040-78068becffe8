<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class FarmTrackReportsLog.
 *
 * @property int $integration_id
 * @property int $integration_reports_types_id
 */
class IntegrationsReports extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_integrations_reports';

    /**
     * Set the fillable attributes for the model.
     *
     * @var array
     */
    protected $fillable = ['integration_id', 'integration_reports_types_id'];

    public function integration(): HasOne
    {
        return $this->hasOne(Integration::class, 'id', 'integration_id');
    }

    public static function getIntegrationReportId(?int $organizationId = null, string $name = 'irrigation_units', string $status = 'Active', string $execution = 'scheduled')
    {
        $query = self::select(
            'su_integrations_reports.id'
        )
            ->join('su_integration_reports_types', 'su_integration_reports_types.id', '=', 'su_integrations_reports.integration_reports_types_id')
            ->join('su_integration as i', 'i.id', '=', 'su_integrations_reports.integration_id')
            ->where('i.status', $status)
            ->where('su_integration_reports_types.execution', $execution)
            ->where('su_integration_reports_types.name', $name);

        if ($organizationId) {
            $query->where('i.organization_id', $organizationId);
        }

        return $query;
    }
}
