<?php

namespace App\Models;

use DB;

/**
 * Class IntegrationAddress.
 *
 * @property string $name
 * @property string $url
 */
class IntegrationAddress extends BaseModel
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'integrations_address';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    protected $connection = 'main';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'url'];

    public function getBaseUrl()
    {
        if (!$this->url) {
            return;
        }

        $integrationAddressUrlInfo = parse_url($this->url);
        $scheme = $integrationAddressUrlInfo['scheme'] ?? null;
        $host = $integrationAddressUrlInfo['host'] ?? null;

        if (!$scheme || !$host) {
            return;
        }

        return "{$scheme}://{$host}";
    }

    public static function queryList()
    {
        return self::select(
            'integrations_address.id',
            'integrations_address.name',
            'integrations_address.url',
            DB::raw("substring(integrations_address.url FROM '[a-zA-Z?://]+[^/]*') as base_url"),
            'integrations_address.created_at',
            'integrations_address.updated_at'
        );
    }
}
