<?php

namespace App\Models;

use App\Services\Auth\HasInMemoryTokens;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Kalnoy\Nestedset\NodeTrait;
use Silber\Bouncer\Database\HasRolesAndAbilities;

/**
 * Class GlobalUser.
 *
 * @property string $username
 * @property string $password
 * @property string $name
 * @property string $email
 * @property int $old_id
 * @property int $old_group_id
 * @property int $country
 * @property bool $is_superadmin
 * @property string $hash
 * @property int $parent_id
 * @property int $group_id
 * @property bool $active
 * @property int $_lft
 * @property int $_rgt
 */
class GlobalUser extends Authenticatable implements
    AuthorizableContract,
    CanResetPasswordContract
{
    use NodeTrait;
    use Authorizable;
    use CanResetPassword;
    use HasRolesAndAbilities;
    use HasInMemoryTokens;

    public const LEVEL_ADMIN = 1;
    public const LEVEL_CLIENT = 2;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'su_users';

    protected $connection = 'main';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'email', 'password'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = ['password', 'remember_token'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'ip_white_list' => 'array',
    ];

    protected $appends = ['role'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function parent()
    {
        return $this->belongsTo('App\Models\GlobalUser', 'parent_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo('App\Models\Country', 'country', 'id');
    }

    public function serviceProvider()
    {
        return $this->belongsTo('App\Models\ServiceProvider', 'service_provider_id', 'id');
    }

    /**
     * Get the user's role.
     *
     * @return ["name" => string, "title" => string]
     */
    public function getRoleAttribute()
    {
        return $this->roles->first();
    }

    public static function getUsersByRole($role, $searchText)
    {
        $query = self::whereIs(...explode('|', $role))
            ->where('service_provider_id', \Illuminate\Support\Facades\Auth::user()->globalUser()->serviceProvider->id)
            ->where('country', \Illuminate\Support\Facades\Auth::user()->globalUser()->country)
            ->where('active', true);

        if ($searchText) {
            $query->where(function ($q) use ($searchText) {
                $q->where('username', 'ILIKE', trim('%' . $searchText . '%'));
                $q->orWhere('name', 'ILIKE', trim('%' . $searchText . '%'));
                $q->orWhere('email', 'ILIKE', trim('%' . $searchText . '%'));
            });
        }

        return $query->get();
    }
}
