<?php

namespace App\Models;

use App\Models\Products\Product;
use App\Models\UnitOfMeasure\UnitOfMeasure;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ActiveIngredient extends BaseModel
{
    use TimestampsTrait;

    public $timestamps = true;

    protected $table = 'su_active_ingredients';

    protected $primaryKey = 'id';

    protected $fillable = ['name'];

    public function products(): belongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            'su_product_active_ingredients',
            'active_ingredient_id',
            'product_id',
        )->withTimestamps();
    }

    public function unit(): belongsToMany
    {
        return $this->belongsToMany(
            UnitOfMeasure::class,
            'su_product_active_ingredients',
            'active_ingredient_id',
            'unit_id',
        )->withTimestamps();
    }
}
