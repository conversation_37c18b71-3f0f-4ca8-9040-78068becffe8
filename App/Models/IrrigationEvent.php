<?php

namespace App\Models;

use DateTime;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use phpseclib\Math\BigInteger;
use <PERSON>audenmeir\LaravelCte\Query\Builder;

/**
 * Class IrrigationEvent.
 *
 * @property BigInteger $irrigation_unit_id
 * @property DateTime $start_time
 * @property DateTime $end_time
 * @property string $type
 * @property int $platform_id
 * @property int $destination_platform_id
 * @property int $unit_speed
 * @property int $pressure
 * @property int $water_rat
 * @property string $coverage
 */
class IrrigationEvent extends BaseModel
{
    public const TYPE = 'type';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_events';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['irrigation_unit_id', 'start_time', 'end_time', 'type', 'platform_id', 'destination_platform_id', 'unit_speed', 'pressure', 'water_rate', 'coverage', 'date'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'report_json' => 'object',
    ];

    public function platform()
    {
        return $this->hasMany('App\Models\IrrigationPlatform', 'platform_id', 'id');
    }

    public function destinationPlatform()
    {
        return $this->hasMany('App\Models\IrrigationPlatform', 'destination_platform_id', 'id');
    }

    public static function getEventsHistory(?int $platformId = null, int $limit = 5)
    {
        $query = DB::table('su_irrigation_events')
            ->select(
                'type as event_type',
                'id as event_id',
                DB::raw('to_char(start_time, \'dd-mm-YYYY HH24:MI:SS\') as start_time'),
                DB::raw('to_char(end_time, \'dd-mm-YYYY HH24:MI:SS\') as end_time'),
                DB::raw('(st_area(coverage)/ 10000)::numeric(10,3) area'),
                'unit_speed as speed',
                DB::raw('(water_rate * (st_area(coverage ))/ 10000)::numeric(10,2) as water_amount'),
                'water_rate as rate',
                DB::raw('(ST_AsGeoJSON(st_transform(coverage, 3857), 5)::json) as geom')
            )
            ->groupBy('irrigation_unit_id', 'type', 'start_time', 'end_time', 'coverage', 'unit_speed', 'water_rate', 'platform_id', 'id');

        if ($platformId) {
            $query->where('platform_id', $platformId);
        }

        //        $data = $query->paginate($limit, ['*']);
        //
        //        return [
        //            'total' => $data->total(),
        //            'rows' => array_map(function ($item) {
        //                $item->geom = json_decode($item->geom, true);
        //                return $item;
        //            }, $data->items())
        //        ];

        // TODO:: must be removed!
        $data = $query->get()->map(function ($item) {
            $item->geom = json_decode($item->geom, true);

            return $item;
        })->toArray();

        return [
            'total' => count($data),
            'rows' => $data,
        ];
    }

    public static function getIrrigationEventsPlotsQuery(array $eventIds = []): EloquentBuilder
    {
        $eventTypesColorsQuery = self::getEventTypesColorsQuery();

        $eventsPlotsDataQuery = self::join('event_types_colors AS etc', 'etc.event_type', '=', 'su_irrigation_events.type')
            ->join('su_irrigation_units AS siu', 'siu.id', '=', 'su_irrigation_events.irrigation_unit_id')
            ->leftJoin('su_irrigation_platforms as sip', 'sip.id', '=', 'su_irrigation_events.platform_id')
            ->join('su_satellite_plots AS ssp', function ($join) {
                $join->on(DB::raw('st_intersects(ssp.geom , su_irrigation_events.coverage)'), '=', DB::raw('true'))
                    ->where('su_irrigation_events.type', '!=', DB::raw("'Transportation'::irrigation_events_types_enum"));
                $join->orOn(DB::raw('st_intersects(ssp.geom , sip.centre)'), '=', DB::raw('true'))
                    ->where('su_irrigation_events.type', DB::raw("'Transportation'::irrigation_events_types_enum"));
            })
            ->join('su_satellite_plots_crops AS sspc', 'sspc.plot_id', '=', 'ssp.gid')
            ->whereRaw('su_irrigation_events.date BETWEEN sspc.from_date AND sspc.to_date')
            ->where('sspc.is_primary', '=', DB::raw('true'))
            ->select(
                'su_irrigation_events.id AS event_id',
                'su_irrigation_events.coverage AS event_coverage',
                'etc.color AS event_color',
                'su_irrigation_events.platform_id',
                'su_irrigation_events.destination_platform_id',
                'ssp.gid AS plot_id',
                'siu.length AS unit_length',
                'sip.centre AS platform_centre_geom',
                DB::raw('st_intersection(ssp.geom , su_irrigation_events.coverage) AS geom'),
                'sspc.crop_id'
            );

        if (isset($eventIds) && count($eventIds) > 0) {
            $eventsPlotsDataQuery->whereIn('su_irrigation_events.id', $eventIds);
        }

        return self::from('events_plots_data')
            ->withExpression('event_types_colors', $eventTypesColorsQuery)
            ->withExpression('events_plots_data', $eventsPlotsDataQuery)
            ->select(
                DB::raw('DISTINCT on (plot_id, event_id) event_id'),
                'crop_id',
                'plot_id',
                DB::raw('round((ST_Area(geom)/1000)::numeric, 3) AS event_area'),
                'geom',
                DB::raw("
                    CASE WHEN destination_platform_id NOTNULL THEN
                        '<svg width=''55'' height=''55'' viewBox=''0 0 30 30'' fill=''none'' xmlns=''http://www.w3.org/2000/svg''>
                        <ellipse cx=''17.5281'' cy=''17.5'' rx=''17.5281'' ry=''17.5'' fill=''none''/>
                        <path d=''M26.1625 9.25817L23.3275 7.84067C23.065 7.68317 22.75 7.89317 22.75 8.20817V9.25817C18.865 9.67817 16.0825 11.0957 15.295 13.1432C14.875 14.1932 14.875 15.9257 17.1325 17.9207C18.13 18.8132 19.2325 20.1782 18.7075 21.5432C17.9725 23.5382 14.2975 24.8507 9.625 24.8507C9.31 24.8507 9.1 25.0607 9.1 25.3757C9.1 25.6907 9.31 25.9007 9.625 25.9007C14.8225 25.9007 18.76 24.3257 19.705 21.9107C20.125 20.8607 20.125 19.1282 17.8675 17.1332C16.87 16.2407 15.7675 14.8757 16.2925 13.5107C16.9225 11.9357 19.39 10.7282 22.75 10.3082V11.0432C22.75 11.3582 23.065 11.5682 23.38 11.4107L26.215 9.99317C26.4775 9.83567 26.4775 9.41567 26.1625 9.25817Z'' fill=''#163D6A''/>
                        </svg>'::xml
                    ELSE
                        gs_irrigation_event_svg_thumbnail(
                            event_coverage,
                            geom,
                            ST_BUFFER(platform_centre_geom, unit_length, 10),
                            event_color,
                            55, 55
                        )
                    END AS thumbnail
                ")
            );
    }

    /**
     * Get the number of events by type. If specified $types - get only for types that are in the array, else get for all types;.
     *
     * @param int $organizationId The id of organization
     * @param array $filter Use this array to filter the data by 'from', 'to', 'farmIds', 'plotIds', 'cropIds', 'platformIds', 'types'
     *
     * @return Builder
     */
    public static function getEventsCountByTypeQuery(int $organizationId, array $filter = [])
    {
        $irrigationEventTypesQuery = 'SELECT UNNEST(enum_range(NULL::irrigation_events_types_enum)) AS type';

        $eventsCountQuery = self::join('su_irrigation_events_plots AS siep', 'siep.event_id', '=', 'su_irrigation_events.id')
            ->join('su_irrigation_units AS siu', 'siu.id', '=', 'su_irrigation_events.irrigation_unit_id')
            ->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'siep.plot_id')
            ->where('siu.organization_id', $organizationId);

        if (isset($filter['from'], $filter['to'])) {
            $eventsCountQuery->whereRaw(
                'su_irrigation_events.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date',
                [$filter['from'], $filter['to']]
            );
        }

        if (isset($filter['farmIds']) && count($filter['farmIds']) > 0) {
            $eventsCountQuery->whereIn('ssp.farm_id', $filter['farmIds']);
        }

        if (isset($filter['plotIds']) && count($filter['plotIds']) > 0) {
            $eventsCountQuery->whereIn('ssp.gid', $filter['plotIds']);
        }

        if (isset($filter['cropIds']) && count($filter['cropIds']) > 0) {
            $eventsCountQuery->whereIn('siep.crop_id', $filter['cropIds']);
        }

        if (isset($filter['platformIds']) && count($filter['platformIds']) > 0) {
            $eventsCountQuery->whereIn('su_irrigation_events.platform_id', $filter['platformIds']);
        }

        $eventsCountQuery->select(
            DB::raw('su_irrigation_events.type'),
            DB::raw('COUNT(siep.event_id)')
        )
            ->groupBy('su_irrigation_events.type');

        $eventsCountByTypeQuery = self::from('events_count')
            ->withExpression('irrigation_event_types', $irrigationEventTypesQuery)
            ->withExpression('events_count', $eventsCountQuery)
            ->rightJoin('irrigation_event_types AS iet', 'iet.type', '=', 'events_count.type');

        if (isset($filter['types']) && count($filter['types']) > 0) {
            $eventsCountByTypeQuery->whereIn('iet.type', $filter['types']);
        }

        return $eventsCountByTypeQuery;
    }

    /**
     * Get irrigation events average water rate and area by dates.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data by 'from', 'to', 'farmIds', 'plotIds', 'cropIds', 'platformIds', 'types'
     *
     * @return Builder
     */
    public static function getIrrigationEventsAvgRateQuery(int $organizationId, array $filter)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = self::join('su_irrigation_events_plots AS siep', 'siep.event_id', '=', 'su_irrigation_events.id')
            ->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'siep.plot_id')
            ->join('su_irrigation_units AS siu', 'siu.id', '=', 'su_irrigation_events.irrigation_unit_id')
            ->where('siu.organization_id', $organizationId);

        if (isset($filter['types']) && count($filter['types']) > 0) {
            $query->whereIn('su_irrigation_events.type', $filter['types']);
        }

        if (isset($filter['from'], $filter['to'])) {
            $query->whereRaw('su_irrigation_events.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date', [$filter['from'], $filter['to']]);
        }

        if (isset($filter['farmIds']) && count($filter['farmIds']) > 0) {
            $query->whereIn('ssp.farm_id', $filter['farmIds']);
        }

        if (isset($filter['plotIds']) && count($filter['plotIds']) > 0) {
            $query->whereIn('ssp.gid', $filter['plotIds']);
        }

        if (isset($filter['cropIds']) && count($filter['cropIds']) > 0) {
            $query->whereIn('siep.crop_id', $filter['cropIds']);
        }

        if (isset($filter['platformIds']) && count($filter['platformIds']) > 0) {
            $query->whereIn('su_irrigation_events.platform_id', $filter['platformIds']);
        }

        $query->select(
            DB::raw("
                ROUND(AVG(
                    CASE WHEN su_irrigation_events.type = 'Irrigation' 
                        THEN COALESCE(su_irrigation_events.water_rate, 0) 
                        ELSE 0
                    END    
                )::NUMERIC, 2) AS avg_water_rate
            "),
            DB::raw("
                ROUND(SUM(
                    CASE WHEN su_irrigation_events.type = 'Irrigation' 
                        THEN siep.event_area * {$areaCoef}
                        ELSE 0
                    END    
                    )::numeric, 2) AS event_plot_area"),
            DB::raw('EXTRACT(epoch FROM su_irrigation_events.date)::BIGINT AS date')
        )
            ->groupBy('su_irrigation_events.date')
            ->orderBy('su_irrigation_events.date');

        return $query;
    }

    /**
     * Get irrigation events average water rate and area by dates for echart.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data by 'from', 'to', 'farmIds', 'plotIds', 'cropIds', 'platformIds', 'types'
     *
     * @return array The return value is formatted for echart. It contains the keys 'series' and 'xAxis'.
     */
    public static function getIrrigationEventsAvgRateEchart(int $organizationId, array $filter)
    {
        $irrigationEventsAvgRateQuery = self::getIrrigationEventsAvgRateQuery($organizationId, $filter);

        $query = DB::table('irrigation_events_avg_rate')
            ->withExpression('irrigation_events_avg_rate', $irrigationEventsAvgRateQuery)
            ->select(DB::raw("
                json_build_object(
                    'data', json_build_object(
                        'series', json_build_array(
                            json_build_object(
                                'name', 'AVG Water rate',
                                'type', 'bar',
                                'barGap', 0,
                                'data', coalesce(json_agg(avg_water_rate), '[]'::json)
                            ),
                            json_build_object(
                                'name', 'Irrigated area',
                                'type', 'bar',
                                'barGap', 0,
                                'data', coalesce(json_agg(event_plot_area), '[]'::json)
                            )
                        ),
                        'xAxis', json_build_array(
                            json_build_object(
                                'type', 'category',
                                'data', coalesce(json_agg(date), '[]'::json)
                            )
                        )
                    ),
                    'avgRate', round(avg(avg_water_rate)::numeric, 2),
                    'totalIrrigatedArea', round(sum(event_plot_area)::numeric, 2)
                ) AS echart_data
            "));

        $echartData = $query->pluck('echart_data')->first();

        return json_decode($echartData, true);
    }

    /**
     * Get irrigation events.
     *
     * @param int $organizationId the id of organization
     * @param array $filter Use this array to filter the data
     */
    public static function getListQuery(int $organizationId, array $filter): EloquentBuilder
    {
        $query = self::join('su_irrigation_platforms AS ip', 'ip.id', '=', 'su_irrigation_events.platform_id')
            ->join('su_irrigation_units AS iu', 'iu.id', '=', 'su_irrigation_events.irrigation_unit_id')
            ->leftJoin('su_farms AS f', 'f.id', '=', 'ip.farm_id')
            ->leftJoin('su_irrigation_events_plots AS iep', 'iep.event_id', '=', 'su_irrigation_events.id')
            ->leftJoin('su_satellite_plots AS sp', 'sp.gid', '=', 'iep.plot_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'iep.plot_id')
                    ->where(DB::raw('su_irrigation_events.date::DATE'), '>=', DB::raw('spc.from_date::DATE'))
                    ->where(DB::raw('su_irrigation_events.date::DATE'), '<=', DB::raw('spc.to_date::DATE'));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'spc.crop_id')
            ->where('iu.organization_id', $organizationId);

        if (isset($filter['from'], $filter['to'])) {
            $query->whereRaw('su_irrigation_events.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date', [$filter['from'], $filter['to']]);
        }

        if (isset($filter['farmIds']) && count($filter['farmIds']) > 0) {
            $query->whereIn('ip.farm_id', $filter['farmIds']);
        }

        if (isset($filter['plotIds']) && count($filter['plotIds']) > 0) {
            $query->whereIn('iep.plot_id', $filter['plotIds']);
        }

        if (isset($filter['cropIds']) && count($filter['cropIds']) > 0) {
            $query->whereIn('spc.crop_id', $filter['cropIds']);
        }

        if (isset($filter['platformIds']) && count($filter['platformIds']) > 0) {
            $query->whereIn('su_irrigation_events.platform_id', $filter['platformIds']);
        }

        if (isset($filter['types']) && count($filter['types']) > 0) {
            $query->whereIn('su_irrigation_events.type', $filter['types']);
        }

        return $query;
    }

    public static function deleteEvents(int $organizationId, string $date, ?int $wialonUnitId)
    {
        $query = self::join('su_irrigation_units as siu', 'siu.id', '=', 'su_irrigation_events.irrigation_unit_id')
            ->where([
                ['su_irrigation_events.date', $date],
                ['siu.organization_id', $organizationId],
            ]);

        if ($wialonUnitId) {
            $query->where('siu.wialon_unit_id', '=', $wialonUnitId);
        }

        $query->delete();
    }

    public static function getEventTypesColorsQuery(): EloquentBuilder
    {
        return self::from(DB::raw('UNNEST(enum_range(NULL::irrigation_events_types_enum)) AS event_type'))
            ->select(
                'event_type',
                DB::raw("
                    CASE 
                        WHEN event_type::text IN ('NoData', 'PressureAlarm', 'PressureUnknown', 'PositionUnknown', 'SpeedAlarm', 'Alert', 'MisalignmentAlarm', 'Warning')
                            THEN '#C42A2C'
                        WHEN event_type::text IN ('Transportation', 'Off')
                            THEN '#163D6A'
                        WHEN event_type::text = 'Irrigation'
                            THEN '#01A1FE'
                        WHEN event_type::text = 'Movement'
                            THEN '#B3D3E6'
                    END color
                ")
            );
    }
}
