<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use MStaack\LaravelPostgis\Eloquent\PostgisTrait;
use phpseclib\Math\BigInteger;

/**
 * Class MachineUnit.
 *
 * @property BigInteger $organization_id
 * @property string $name
 * @property BigInteger $wialon_unit_imei
 * @property BigInteger $wialon_unit_id
 * @property string $type
 */
class MachineUnit extends BaseModel
{
    use PostgisTrait;

    public const TRACK_FORMAT_GEOJSON = 'geojson';
    public const TRACK_FORMAT_GPX = 'gpx';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machine_units';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /**
     * @var array
     */
    protected $fillable = ['organization_id', 'integration_id', 'name', 'wialon_unit_imei', 'type', 'last_communication', 'last_position', 'wialon_unit_id'];

    protected $postgisFields = [
        'last_position',
    ];

    protected $postgisTypes = [
        'last_position' => [
            'geomtype' => 'geometry',
            'srid' => 32635,
        ],
    ];

    public function tasks(): HasMany
    {
        return $this->hasMany(MachineTask::class, 'machine_unit_id', 'id');
    }

    public function events(): HasMany
    {
        return $this->hasMany(MachineEvent::class, 'machine_id', 'id');
    }

    public static function getUnits()
    {
        return self::select('id', 'organization_id', 'integration_id', 'name', 'wialon_unit_imei', 'type', 'last_communication', 'last_position', 'wialon_unit_id');
    }

    public static function getUnitTypes()
    {
        $types = DB::select('SELECT UNNEST(ENUM_RANGE(null::machine_unit_types_enum)) as type');

        return array_map(function ($item) {
            return $item->type;
        }, $types);
    }

    public static function updateFromReportTmpTable($tmpTable)
    {
        $unitsToUpdate = DB::table($tmpTable)->select(
            DB::raw('distinct on (wialon_unit_imei) wialon_unit_imei'),
            'grouping',
            'coordinates',
            DB::raw('time::timestamp')
        )
            ->where('sensor', '=', 'Ignition')
            ->orderBy('wialon_unit_imei')
            ->orderBy('time', 'desc');

        return self::withExpression('units_to_update', $unitsToUpdate)
            ->join('units_to_update', DB::raw('units_to_update.wialon_unit_imei::bigint'), '=', 'su_machine_units.wialon_unit_imei')
            ->updateFrom([
                'name' => DB::raw('units_to_update.grouping'),
                'last_position' => DB::raw('units_to_update.coordinates'),
                'last_communication' => DB::raw('units_to_update.time'),
            ]);
    }

    /**
     * Get all machine units by organization.
     *
     * @param int $organizationId The id of the organization
     */
    public static function getByOrganization(int $organizationId): Collection
    {
        return self::where('organization_id', $organizationId)->get();
    }

    public static function getTableName()
    {
        return with(new static())->getTable();
    }
}
