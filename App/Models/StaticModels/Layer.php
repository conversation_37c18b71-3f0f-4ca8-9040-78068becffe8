<?php

namespace App\Models\StaticModels;

class Layer implements StaticModel
{
    public static function getAll()
    {
        $layers = [
            [
                'name' => 'layer_satellite_work',
                'title' => 'Work Layer',
                'farming_year' => '',
                'year' => null,
                'from_date' => null,
                'to_date' => null,
                'default' => false,
            ],
        ];

        FarmingYear::getAll()->each(function ($item) use (&$layers) {
            $layers[] = [
                'name' => 'layer_satellite_orders_' . $item['year'],
                'title' => 'Layer',
                'farming_year' => $item['farming_year'],
                'year' => $item['year'],
                'from_date' => $item['from_date'],
                'to_date' => $item['to_date'],
                'default' => $item['default'],
            ];
        });

        return collect($layers);
    }
}
