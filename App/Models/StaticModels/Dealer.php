<?php

namespace App\Models\StaticModels;

class Dealer implements StaticModel
{
    public static function getAll()
    {
        return collect([
            ['id' => 1, 'name' => 'Вера Гостева'],
            ['id' => 2, 'name' => '<PERSON>в<PERSON><PERSON>'],
            ['id' => 3, 'name' => 'Светлин Караджов'],
            ['id' => 4, 'name' => 'Татяна Цацева'],
            ['id' => 5, 'name' => 'Цветан Атанасов'],
            ['id' => 6, 'name' => 'Димитър Янев'],
            ['id' => 7, 'name' => 'Радостин Василев'],
            ['id' => 8, 'name' => 'Николай Овчаров'],
            ['id' => 9, 'name' => 'Златина Христова'],
            ['id' => 10, 'name' => 'Георги Георгиев'],
            ['id' => 11, 'name' => 'Д<PERSON><PERSON><PERSON>н Христов'],
            ['id' => 12, 'name' => 'Теодор Тодоров'],
            ['id' => 13, 'name' => 'Янко Янков'],
            ['id' => 14, 'name' => 'Илкер Юдаим'],
            ['id' => 15, 'name' => 'Димитър  Атанасов'],
            ['id' => 16, 'name' => 'Други'],
        ]);
    }
}
