<?php

namespace App\Models\StaticModels;

use App\Models\Order;
use App\Models\Organization;

use function getdate;

use Illuminate\Support\Collection;

class FarmingYear implements StaticModel
{
    public static function getAll(): Collection
    {
        $month = (int)date('m');
        $currentYear = (int)date('Y');

        if ($month > 9) {
            $currentYear++;
        }

        return Order::getFarmingYears($currentYear);
    }

    public static function byOrganization(Organization $organization, string $currentYear): Collection
    {
        $farmingYearsRangeResults = Order::getFarmingYears($currentYear, $organization);

        if (!count($farmingYearsRangeResults)) {
            return collect([]);
        }

        return $farmingYearsRangeResults;
    }

    public static function getYearbyDate(string $date)
    {
        $farmYear = (int)date('Y', strtotime($date));
        $month = (int)date('n', strtotime($date));

        if ($month > 9) {
            $farmYear = $farmYear + 1;
        }

        return $farmYear;
    }

    /**
     * Get farming years starting with $startDate and ending with $endDate.
     */
    public static function getFarmingYearsByRange(string $startDate, string $endDate): array
    {
        $start = getdate(strtotime($startDate));
        $end = getdate(strtotime($endDate));
        $current = getdate();

        $startYear = $start['mon'] > 9 ? $start['year'] + 1 : $start['year'];
        $endYear = $end['mon'] > 9 ? $end['year'] + 1 : $end['year'];
        $currentYear = $current['mon'] > 9 ? $current['year'] + 1 : $current['year'];

        $farmingYears = [];
        for ($year = $startYear; $year <= $endYear; $year++) {
            $farmingYears[] = [
                'id' => $year,
                'title' => $year,
                'default' => ($year === $currentYear),
                'year' => $year,
                'from_date' => $year === $startYear ? $startDate : strval($startYear) . '-10-01',
                'to_date' => $year === $endYear ? $endDate : strval($year) . '-09-30',
                'farming_year' => ($year - 1) . '/' . $year,
            ];

            if ($year !== $startYear) {
                $startYear++;
            }
        }

        return $farmingYears;
    }

    public static function get(int $year)
    {
        $month = (int)date('m');
        $currentYear = (int)date('Y');

        if ($month > 9) {
            $currentYear++;
        }

        return [
            'title' => $year,
            'id' => $year,
            'default' => ($year == $currentYear),
            'year' => $year,
            'from_date' => ($year - 1) . '-10-01',
            'to_date' => $year . '-09-30',
            'farming_year' => ($year - 1) . '/' . $year,
        ];
    }
}
