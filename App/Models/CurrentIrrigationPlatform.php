<?php

namespace App\Models;

use Config;
use DB;

/**
 * Class CurrentIrrigationPlatform.
 *
 * @property int $organization_id
 * @property string $geojson
 * @property string $current_data
 */
class CurrentIrrigationPlatform extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_current_irrigation_platforms_data';

    /**
     * @var int
     */
    protected $primaryKey = 'organization_id';

    /** @var array */
    protected $fillable = ['organization_id', 'geojson', 'current_data'];

    protected $casts = [
        'geojson' => 'array',
        'echart' => 'array',
    ];

    public static function formatCurrentIrrigationUnitReportByState($tmpTableName)
    {
        $sensorsData = CurrentIrrigationPlatform::sensorsDataQuery($tmpTableName);
        $sensorsByState = CurrentIrrigationPlatform::SensorsByStateQuery();

        return DB::table('sensors_by_state')
            ->withExpression('sensors_data', $sensorsData)
            ->withExpression('sensors_by_state', $sensorsByState)
            ->select(
                DB::raw("
                jsonb_agg(
                    json_build_object(
                        'farm_id', farm_id,
                        'platform_id', platform_id,
                        'plot_ids', plot_ids,
                        'state', state
                    )) as current_data
                ")
            );
    }

    /**
     * @return string
     */
    public static function sensorsDataQuery($tmpTableName)
    {
        return '
                select DISTINCT ON(ct.unit) 
                    case
                        when ct.alarm::numeric = 1 then \'' . IrrigationDataRaw::MISALIGNMENT_ALARM . '\'
                        when ct.state::numeric = 1 and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1) and ct.pressure notnull and ct.pressure::numeric / 14.503773773 > 0 and ct.pressure::numeric / 14.503773773 < 0.7
                             and (ct.speed::numeric)::int > 0 and ct.alarm::numeric = 0 and ct.angle::numeric between 0 and 360 and ST_IsValid(ct.coordinates) then \'' . IrrigationDataRaw::PRESSURE_ALARM . '\'
                        when ct.state::numeric = 1 and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1) and ct.pressure notnull and ct.pressure::numeric / 14.503773773 = 0 
                             and (ct.speed::numeric)::int > 0 and ct.alarm::numeric = 0 and ct.angle::numeric between 0 and 360 and ST_IsValid(ct.coordinates) then \'' . IrrigationDataRaw::MOVEMENT . '\'
                        when ct.state::numeric = 1 and (ct.fwd::numeric = 1 or ct.rwd::numeric = 1) and ct.pressure notnull and ct.pressure::numeric / 14.503773773 >= 0.7 
                             and (ct.speed::numeric)::int > 0 and ct.alarm::numeric = 0 and ct.angle::numeric between 0 and 360 and ST_IsValid(ct.coordinates) then \'' . IrrigationDataRaw::IRRIGATION . '\'
                    else \'' . IrrigationDataRaw::WARNING . '\'
                    end state,
                    sip.id as platform_id,
                    ssp.gid,
                    f.id as farm_id
                from
                    crosstab($$
                    select
                        (time::text || wialon_unit_imei::text) rowid, "time", "coordinates", "wialon_unit_imei", "sensor" , "value"
                    from
                        ' . $tmpTableName . ' ps
                    where
                        sensor notnull
                    order by
                        2, 3 $$, $$
                    values (\'pressure\'), (\'angle\'), (\'timer_speed\'), (\'state\'), (\'lenght\'), (\'fwd\'), (\'rev\'), (\'alarm\')$$) 
                    as ct(rowid varchar, time timestamp, coordinates geometry, unit varchar, pressure varchar, angle varchar, speed varchar, state varchar, length varchar,
                    fwd varchar, rwd varchar, alarm varchar)
                join su_irrigation_platforms sip on st_intersects(centre_buff, ct.coordinates) and sip.status = true
                join su_satellite_plots ssp on st_intersects(ssp.geom , sip.centre)
                join su_farms f on f.id = ssp.farm_id
                where (ct.state::numeric = 0 and ct.alarm::numeric = 1) or ct.state::numeric = 1 /* - no Off state any more */
                order by
                    ct.unit,
                    ct.time desc
        ';
    }

    public static function SensorsByStateQuery()
    {
        return DB::table('sensors_data')
            ->select(
                'farm_id',
                'platform_id',
                'state',
                DB::raw('json_agg(gid)as plot_ids')
            )
            ->groupBy('farm_id', 'platform_id', 'state');
    }

    public static function formatCurrentIrrigationUnitReportByPlatformGeoJSON(string $tmpTable, int $organizationId)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        // Sensors
        $sensors = DB::table('su_irrigation_platforms as sip')
            ->select(
                DB::raw('distinct on (sip.id, sensor) sip.id'),
                'tmp.grouping as wialon_unit_name',
                'wialon_unit_imei',
                DB::raw('to_char(time, \'YYYY-mm-dd HH24:MI:SS\') as last_communication'),
                'sensor',
                'value',
                'coordinates',
                'ssp.gid as plot_id'
            )
            ->join('su_satellite_plots as ssp', DB::raw('st_intersects(ssp.geom , sip.centre)'), '=', DB::raw('true::boolean'))
            ->join('su_farms as f', 'f.id', '=', 'ssp.farm_id')
            ->leftJoin($tmpTable . ' as tmp', function ($join) {
                $join->on(DB::raw('st_intersects(centre_buff, tmp.coordinates)'), '=', DB::raw('true::boolean'));
                $join->on('sip.status', '=', DB::raw('true::boolean'));
            })
            ->where(function ($q) {
                $q->whereIn('sensor', ['state', 'timer_speed', 'pressure', 'rev', 'fwd', 'lenght', 'rate', 'angle', 'alarm']);
                $q->orWhere('sensor', null);
            })
            ->where(function ($q) {
                $q->where('value', '!=', '-----');
                $q->orWhere('value', null);
            })
            ->where('sip.status', DB::raw('true::boolean'))
            ->where('f.organization_id', $organizationId)
            ->groupBy('coordinates', 'time', 'grouping', 'wialon_unit_imei', 'sensor', 'value', 'sip.id', 'ssp.gid')
            ->orderBy('sip.id', 'desc')
            ->orderBy('sensor', 'asc')
            ->orderBy('tmp.grouping', 'asc')
            ->orderBy('tmp.time', 'desc')
            ->orderBy('sensor', 'desc');

        // Sensors formatted
        $sensorsFormatted = DB::table('sensors as s')
            ->select(
                's.wialon_unit_name',
                's.wialon_unit_imei',
                's.last_communication',
                's.coordinates',
                's.id as platform_id',
                DB::raw('json_agg(distinct s.plot_id) as plot_ids'),
                DB::raw('coalesce(json_object_agg(s.sensor, s.value) filter (where s.sensor is not null)) as sensors')
            )
            ->groupBy('s.wialon_unit_name', 's.wialon_unit_imei', 's.coordinates', 's.last_communication', 's.id');

        $query = DB::table('su_irrigation_platforms AS plt')
            ->withExpression('sensors', $sensors)
            ->withExpression('sensors_formatted', $sensorsFormatted);

        $query->select(
            'f.organization_id',
            DB::raw("json_build_object(
                'type', 'FeatureCollection', 
                'features', json_agg(
                    json_build_object(
                        'type',       'Feature',
                        'geometry',   ST_AsGeoJSON(st_transform(plt.centre, 3857) )::json,
                        'properties', json_build_object(
                            'platform_name', plt.name,
                            'platform_id', sf.platform_id,
                            'unit', CASE when (sf.sensors notnull) and (((sf.sensors->>'state')::numeric = 0 and (sf.sensors->>'alarm')::numeric = 1) or (sf.sensors->>'state')::numeric = 1) then json_build_object(
                                'covered_area', round((((3.14 * (sf.sensors->>'lenght')::numeric) ^ 2) / " . $areaCoef . "), 2),
                                'unit_name', sf.wialon_unit_name,
                                'speed', case when sf.sensors->'timer_speed' notnull then concat(round((sf.sensors->>'timer_speed')::numeric),' %') else null end,
                                'pressure', case when sf.sensors->'pressure' notnull then concat(round(((sf.sensors->>'pressure')::numeric / 14.503773773)::numeric,1),' bar') else null end,
                                'state',  (
                                    case
                                        when (sf.sensors->>'alarm')::numeric = 1 then '" . IrrigationDataRaw::MISALIGNMENT_ALARM . "'
                                        when (sf.sensors->>'state')::numeric = 1 and ((sf.sensors->>'fwd')::numeric = 1 or (sf.sensors->>'rev')::numeric = 1) and (sf.sensors->>'pressure') notnull
                                            and (sf.sensors->>'pressure')::numeric / 14.503773773 > 0 and (sf.sensors->>'pressure')::numeric / 14.503773773 < 0.7 and ((sf.sensors->>'timer_speed')::numeric)::int > 0
                                            and (sf.sensors->>'alarm')::numeric = 0 and (sf.sensors->>'angle')::numeric between 0 and 360 then '" . IrrigationDataRaw::PRESSURE_ALARM . "'
                                        when (sf.sensors->>'state')::numeric = 1 and ((sf.sensors->>'fwd')::numeric = 1 or (sf.sensors->>'rev')::numeric = 1) and (sf.sensors->>'pressure') notnull
                                            and (sf.sensors->>'pressure')::numeric / 14.503773773 = 0 and ((sf.sensors->>'timer_speed')::numeric)::int > 0 and (sf.sensors->>'alarm')::numeric = 0
                                            and (sf.sensors->>'angle')::numeric between 0 and 360 then '" . IrrigationDataRaw::MOVEMENT . "'
                                        when (sf.sensors->>'state')::numeric = 1 and ((sf.sensors->>'fwd')::numeric = 1 or (sf.sensors->>'rev')::numeric = 1) and (sf.sensors->>'pressure') notnull
                                            and (sf.sensors->>'pressure')::numeric / 14.503773773 >= 0.7 and ((sf.sensors->>'timer_speed')::numeric)::int > 0 and (sf.sensors->>'alarm')::numeric = 0
                                            and (sf.sensors->>'angle')::numeric between 0 and 360 then '" . IrrigationDataRaw::IRRIGATION . "'
                                        else '" . IrrigationDataRaw::WARNING . "'
                                    end),
                                'state_description', (
                                    case
                                        when (sf.sensors->>'state')::numeric = 1 and (sf.sensors->>'angle' is null or (sf.sensors->>'angle')::numeric not between 0 and 360) then '" . IrrigationDataRaw::WARNING_ANGLE . "'
                                        when (sf.sensors->>'state')::numeric = 1 and sf.sensors->>'timer_speed' is null then '" . IrrigationDataRaw::WARNING_SPEED . "'
                                        when (sf.sensors->>'state')::numeric = 1 and sf.sensors->>'pressure' is null then '" . IrrigationDataRaw::WARNING_PRESSURE . "'
                                        when (sf.sensors->>'state')::numeric = 1 and (sf.coordinates isnull or not ST_IsValid(sf.coordinates)) then '" . IrrigationDataRaw::WARNING_COORDINATES . "'
                                        when (sf.sensors->>'state')::numeric = 1 and ((sf.sensors->>'fwd' is null or (sf.sensors->>'rev')::numeric = 0)
                                            and (sf.sensors->>'rev' is null or (sf.sensors->>'fwd')::numeric = 0)) then '" . IrrigationDataRaw::WARNING_DIRECTION . "'
                                        when (sf.sensors->>'state')::numeric = 1 and (((sf.sensors->>'timer_speed')::numeric)::int = 0 and (sf.sensors->>'pressure')::numeric > 0) 
                                            then '" . IrrigationDataRaw::WARNING_READING_SENSORS . "'
                                        when (sf.sensors->>'state')::numeric = 1 and (((sf.sensors->>'timer_speed')::numeric)::int = 0 and (sf.sensors->>'pressure')::numeric = 0
                                            and (sf.sensors->>'angle')::numeric between 0 and 360) then '" . IrrigationDataRaw::WARNING_READING_SENSORS . "'
                                    end),
                                'direction', CASE  WHEN sf.sensors->>'rev' = '1.00' THEN  'RWD' WHEN sf.sensors->>'fwd' = '1.00' THEN  'FWD' ELSE null END,
                                'angle', sf.sensors->>'angle',
                                'last_communication', sf.last_communication,
                                'length', case when sf.sensors->'lenght' notnull then (sf.sensors->>'lenght')::numeric else null end,
                                'rate', case when
                                    sf.sensors->>'rate' isnull
                                then '- m3/ha'
                                when
                                    (sf.sensors->>'state')::numeric = 1
                                    and ((sf.sensors->>'fwd')::numeric = 1 or (sf.sensors->>'rev')::numeric = 1)
                                    and (sf.sensors->>'pressure')::numeric / 14.503773773 >= 0.7
                                    and sf.sensors->>'timer_speed' notnull
                                    and (sf.sensors->>'timer_speed')::numeric > 0
                                    and (sf.sensors->>'angle')::numeric between 0 and 360
                                then concat(round((sf.sensors->>'rate')::numeric*10), ' m3/ha')
                                else concat(0, ' m3/ha') end
                            ) else null end,
                            'farm_id', f.id,
                            'plot_ids', sf.plot_ids,
                            'farm_name', f.name,
                            'state', case when sf.sensors notnull
                                then 'Active'
                                else 'Not active'
                            end
                        )
                    )
                )
            ) as geoJSON")
        )
            ->join('su_farms AS f', 'f.id', '=', 'plt.farm_id')
            ->join('sensors_formatted as sf', 'sf.platform_id', '=', 'plt.id')
            ->where('f.organization_id', $organizationId)
            ->groupBy('f.organization_id');

        return $query;
    }

    /**
     * Get pivots current state in eChart format.
     *
     * @param ?string $organizationId
     */
    public static function getPivotCurrentDataStateEchart(?string $organizationId = null, array $platformIds = [], array $states = [], array $farmIds = [], array $plotIds = [])
    {
        $currentDataQuery = self::select(DB::raw('jsonb_array_elements(current_data) as json'), 'organization_id');

        return self::getCurrentDataStateEchart($currentDataQuery, $organizationId, $platformIds, $states, $farmIds, $plotIds);
    }

    /**
     * Get platforms current state in eChart format.
     */
    public static function getPlatformCurrentDataState(string $organizationId = null, array $platformIds = [], array $states = [], array $farmIds = [], array $plotIds = [])
    {
        $currentDataQuery = self::select(DB::raw("json_array_elements(geojson->'features')->'properties' as json"), 'organization_id');

        return self::getCurrentDataStateEchart($currentDataQuery, $organizationId, $platformIds, $states, $farmIds, $plotIds);
    }

    /**
     * Get current irrigation platforms data (geojson) by organizationId.
     */
    public static function getPlatformsDataGeoJsonByOrganizationId(int $organizationId)
    {
        $irrigationPlatformsData = self::select('geojson')
            ->where('organization_id', $organizationId);

        $defaultData = "SELECT json_build_object(
                'type', 'FeatureCollection',
                'features', '[]'::jsonb
            ) AS geojson";

        $query = DB::table('irrigation_platforms_data AS ipd')
            ->withExpression('irrigation_platforms_data', $irrigationPlatformsData)
            ->withExpression('default_data', $defaultData)
            ->rightJoin('default_data AS dd', DB::raw('true'), '=', DB::raw('true'))
            ->select(DB::raw('
                coalesce(ipd.geojson, dd.geojson) AS geojson
            '));

        $result = $query->pluck('geojson')->first();

        return json_decode($result, true);
    }

    public static function getCurrentPivotPosition(int $organizationId, int $irrigationPlatformId)
    {
        return self::select(DB::raw("json_build_object(
                'type', 'Feature',
                'geometry', ST_AsGeoJSON(st_transform(gs_pivot_current_position_geom(st_transform(ST_SetSRID(ST_GeomFromGeoJSON(features->>'geometry'), 3857), " . Config::get('globals.DEFAULT_DB_CRS') . "), (features->'properties'->'unit'->>'length')::numeric, (features->'properties'->'unit'->>'angle')::numeric), 3857))::jsonb,
                'properties', json_build_object(
                    'platform_id', features->'properties'->>'platform_id'
                )
            ) AS geojson"))
            ->crossJoin(DB::raw("json_array_elements(su_current_irrigation_platforms_data.geojson->'features') features"))
            ->where('organization_id', $organizationId)
            ->where(DB::raw("features->'properties'->>'platform_id'"), $irrigationPlatformId)
            ->whereNotNull(DB::raw("features->'properties'->>'unit'"))
            ->whereIn(DB::raw("features->'properties'->'unit'->>'state'"), ['Irrigation', 'Movement', 'Alarm'])
            ->pluck('geojson')->first();
    }

    /**
     * Get current state in eChart format.
     */
    private static function getCurrentDataStateEchart($currentDataQuery, string $organizationId = null, array $platformIds = [], array $states = [], array $farmIds = [], array $plotIds = [])
    {
        $currentDataState = DB::table('currentData')
            ->select(
                DB::raw("json->>'state' as state"),
                DB::raw('count(*) as count')
            );

        if ($organizationId) {
            $currentDataState->where('organization_id', $organizationId);
        }

        if (count($platformIds) > 0) {
            $currentDataState->whereIn(DB::raw("json->>'platform_id'"), $platformIds);
        }

        if (count($states) > 0) {
            $currentDataState->whereIn(DB::raw("json->>'state'"), $states);
        }

        if (count($farmIds) > 0) {
            $currentDataState->whereIn(DB::raw("json->>'farm_id'"), $farmIds);
        }

        if (count($plotIds) > 0) {
            $currentDataState->where(DB::raw("(json->'plot_ids')::jsonb"), '@>', DB::raw('to_jsonb(array' . json_encode($plotIds) . ')'));
        }

        $currentDataState->groupBy(DB::raw("json->>'state'"));

        $echartDataQuery = self::from('currentDataState')
            ->withExpression('currentData', $currentDataQuery)
            ->withExpression('currentDataState', $currentDataState)
            ->selectRaw("
             json_build_object(
                    'name', state,
                    'value', count,
                    'state', state
                ) as echart
            ");

        return $echartDataQuery->get()->pluck('echart')->toArray();
    }
}
