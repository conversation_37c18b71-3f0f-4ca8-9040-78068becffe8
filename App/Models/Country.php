<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Country.
 *
 * @property int $id
 * @property string $iso_alpha_2_code
 * @property string $iso_alpha_3_code
 * @property bool $active
 * @property string $database_name
 */
class Country extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'main';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'countries';

    /**
     * Set the fillable attributes for the model.
     *
     * @param array $fillable
     *
     * @return $this
     */
    protected $fillable = [
        'name',
        'iso_alpha_2_code',
        'iso_alpha_3_code',
        'active',
        'database_name',
    ];

    public function configParams(): HasMany
    {
        return $this->hasMany(ConfigParamValue::class, 'country_id', 'id');
    }

    public function serviceProviders()
    {
        return $this->hasMany(ServiceProvider::class);
    }
}
