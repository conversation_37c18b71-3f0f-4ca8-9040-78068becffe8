<?php

namespace App\Models;

use App\Services\Auth\HasInMemoryTokens;
use Auth;
use DateTime;
use DB;
use Exception;
use Hash;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Http\Request;
use LaravelDoctrine\ORM\Types\Json;

/**
 * Class User.
 *
 * @property string $username
 * @property string $password
 * @property string $name
 * @property string $address
 * @property string $phone
 * @property string $email
 * @property string $comment
 * @property bool $is_superadmin
 * @property string $database
 * @property string $hash
 * @property int $parent_id
 * @property int $can_create
 * @property int $level
 * @property int $group_id
 * @property bool $active
 * @property int $server
 * @property DateTime $start_date
 * @property DateTime $due_date
 * @property bool $entry_flag
 * @property int $entries_left
 * @property bool $date_flag
 * @property int $map_type
 * @property bool $is_trial
 * @property int $allowed_farmings
 * @property string $track_username
 * @property string $track_password
 * @property DateTime $creation_date
 * @property DateTime $last_login_date
 * @property string $last_login_ip
 * @property string $app_version
 * @property bool $app_critical_upd
 * @property DateTime $paid_support
 * @property bool $is_demo
 * @property bool $is_cached
 * @property bool $ip_filter
 * @property Json $ip_white_list
 * @property string $profile_image
 * @property float $ordered_area
 * @property int $gdd_entries_left
 * @property string $email_bcc
 * @property int $last_chosen_organization_id
 */
class User extends Authenticatable implements
    AuthorizableContract,
    CanResetPasswordContract
{
    use HasFactory;

    use Authorizable;
    use CanResetPassword;
    use HasInMemoryTokens;

    public const LEVEL_ADMIN = 1;
    public const LEVEL_CLIENT = 2;

    public const ROLE_ORGANIZATION_MANAGER = 'ORGANIZATION_MANAGER';
    public const ROLE_SERVICE_ADMIN = 'SERVICE_ADMIN';
    public const ROLE_SERVICE_MANAGER = 'SERVICE';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'su_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'email', 'password'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = ['password', 'remember_token'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'ip_white_list' => 'array',
    ];

    /**
     * Get the files for the user.
     */
    public function files()
    {
        return $this->hasMany('App\Models\File', 'group_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany('App\Models\Order', 'user_id', 'id');
    }

    public function recommendations()
    {
        return $this->hasMany('App\Models\Recommendation', 'user_id', 'id');
    }

    public function parent()
    {
        return $this->belongsTo('App\Models\User', 'parent_id', 'id');
    }

    public function stations()
    {
        return $this->hasMany('App\Models\UserStation', 'user_id', 'id')->orderBy('id', 'desc');
    }

    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'su_organizations_users')->orderBy('id', 'desc');
    }

    public function farms(): BelongsToMany
    {
        return $this->belongsToMany(Farm::class, 'su_farms_users')->withPivot(['is_visible']);
    }

    public function lastChosenOrganization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'last_chosen_organization_id', 'id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(self::class, 'created_by', 'id');
    }

    public function plotsCount()
    {
        return self::join('su_farms_users as fu', 'fu.user_id', '=', 'su_users.id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->join('su_satellite_plots as p', 'p.farm_id', '=', 'f.id')
            ->where('su_users.id', $this->id)
            ->count();
    }

    public function farmsByOrganization(Organization $organization = null)
    {
        $query = self::select('f.id', 'f.name', 'fu.is_visible', 'f.uuid')
            ->join('su_farms_users as fu', 'fu.user_id', '=', 'su_users.id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->where('su_users.id', $this->id);

        if ($organization) {
            $query->where('f.organization_id', $organization->id);
        }

        $query->orderBy('id', 'desc');

        return $query->get();
    }

    public function allFarmsByOrganization(Organization $organization = null)
    {
        $query = Farm::selectRaw('DISTINCT su_farms.id, su_farms.name, su_farms.uuid, COALESCE(fu.is_visible, false) is_visible, fu.user_id notnull as is_attached')
            ->leftJoin('su_farms_users as fu', function ($join) {
                $join->on('fu.farm_id', '=', 'su_farms.id');
                $join->where('fu.user_id', '=', $this->id);
            });

        if ($organization) {
            $query->where('su_farms.organization_id', $organization->id);
        }

        $query->orderBy('id', 'desc');

        return $query->get();
    }

    /**
     * @return GlobalUser
     */
    public function globalUser()
    {
        return GlobalUser::where('old_id', $this->id)->where('username', $this->username)->first();
    }

    public function getDescendants()
    {
        /** @var GlobalUser $globalUser */
        $globalUser = $this->globalUser();

        /** @var Collection $globalUsers */
        $globalUsers = GlobalUser::where('_lft', '>', $globalUser->getLft())
            ->where('_rgt', '<', $globalUser->getRgt())
            ->get();

        $globalUsersIds = $globalUsers->map(function ($user) {
            return $user->only(['old_id']);
        });

        /** @var Collection $users */
        $users = self::whereIn('id', $globalUsersIds)->orderBy('id', 'desc')->with('farms')->get();
        $users->map(function ($user) use ($globalUsers) {
            $roles = [];
            $forbiddenAbilities = [];
            /** @var GlobalUser[] $tmp */
            $tmp = $globalUsers->filter(function ($globalUser) use ($user) {
                return $globalUser->old_id == $user->id;
            });

            foreach ($tmp as $item) {
                $roles[] = $item->roles;
                $forbiddenAbilities[] = $item->getForbiddenAbilities();
            }

            $user->roles = reset($roles);
            $user->forbidden_abilities = reset($forbiddenAbilities);
        });

        return $users;
    }

    public static function createUser(Request $request)
    {
        DB::beginTransaction();

        try {
            /** @var User $currentUser */
            $currentUser = Auth::user();
            $currentGlobalUser = $currentUser->globalUser();

            $newUser = new User();
            $newUser->username = $request->get('username');
            $newUser->password = Hash::make($request->get('password'));
            $newUser->email = $request->get('email');
            $newUser->name = $request->get('name');
            $newUser->phone = $request->get('phone');
            $newUser->parent()->associate($currentUser);
            $newUser->level = User::LEVEL_CLIENT;
            $newUser->save();
            $newUser->group_id = $newUser->id;
            $newUser->save();

            $newUser->organizations()->attach($currentUser->lastChosenOrganization->id);
            foreach ($request->get('farms') as $farm) {
                $newUser->farms()->attach($farm['id']);
            }

            $newGlobalUser = new GlobalUser();
            $newGlobalUser->username = $request->get('username');
            $newGlobalUser->password = Hash::make($request->get('password'));
            $newGlobalUser->email = $request->get('email');
            $newGlobalUser->name = $request->get('name');
            $newGlobalUser->country = $currentGlobalUser->country;
            $newGlobalUser->old_id = $newUser->id;
            $newGlobalUser->old_group_id = $newUser->group_id;
            $newGlobalUser->parent()->associate($currentUser->globalUser()->id);
            $newGlobalUser->service_provider_id = $currentUser->globalUser()->serviceProvider->id;
            $newGlobalUser->save();

            $newGlobalUser->group_id = $newGlobalUser->id;
            $newGlobalUser->save();

            $newGlobalUser->assign('STAFF');

            foreach ($request->get('abilities') as $ability) {
                if ($ability['allowed']) {
                    $newGlobalUser->unforbid($ability['name']);
                } else {
                    $newGlobalUser->forbid($ability['name']);
                }
            }

            GlobalUser::fixTree();

            DB::table('su_system_users')->insert(
                ['user_id' => $newUser->id, 'username' => $newUser->username, 'password' => $request->get('password')]
            );

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return $newUser;
    }

    public static function search($value)
    {
        $serviceProviderId = Auth::user()->globalUser()->serviceProvider->id;

        return self::selectRaw('distinct su_users.id, su_users.username, su_users.name, su_users.email')
            ->join('su_organizations_users as sou', 'su_users.id', '=', 'sou.user_id')
            ->join('su_organizations as so', function ($join) use ($serviceProviderId) {
                $join->on('so.id', '=', 'sou.organization_id')
                    ->where('so.service_provider_id', '=', $serviceProviderId);
            })
            ->where('su_users.username', 'ILIKE', trim('%' . $value . '%'))
            ->orWhere('su_users.name', 'ILIKE', trim('%' . $value . '%'))
            ->orWhere('su_users.email', 'ILIKE', trim('%' . $value . '%'))
            ->orderBy('username', 'asc')
            ->get();
    }

    public function getLastAddedOrganizationId()
    {
        $lastAddedOrgData = $this->selectRaw('su_users.id as user_id, MAX(sou.id) AS lst, sou.organization_id as last_org_id')
            ->join('su_organizations_users as sou', 'su_users.id', '=', 'sou.user_id')
            ->where('su_users.id', $this->id)
            ->groupBy(['su_users.id', 'sou.organization_id'])
            ->orderBy('lst', 'DESC')
            ->first();

        if ($lastAddedOrgData) {
            return $lastAddedOrgData->last_org_id;
        }

        return;
    }

    /**
     * Returns sql string with bind values.
     *
     * @param object $query query Builder object
     *
     * @return query Builder object
     *
     * @internal param string $sort
     */
    public function scopeToSqlWithBindings($query)
    {
        $sql = $query->toSql();

        $sql = str_replace(['%', '?', '"'], ['%%', '%s', ''], $sql);

        return vsprintf($sql, $query->getBindings());
    }

    /**
     * allowedSatelliteTypes Get User Allowed Satellite Types.
     *
     * @return array
     */
    public static function allowedSatelliteTypes()
    {
        $user = Auth::user();

        $allowedSatelliteTypes = [];
        if ($user->globalUser()->can('use_rapideye')) {
            array_push($allowedSatelliteTypes, 'rapideye');
        }
        if ($user->globalUser()->can('use_sentinel')) {
            array_push($allowedSatelliteTypes, 'sentinel');
        }
        if ($user->globalUser()->can('use_landsat')) {
            array_push($allowedSatelliteTypes, 'landsat');
        }

        return $allowedSatelliteTypes;
    }
}
