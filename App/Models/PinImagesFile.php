<?php

namespace App\Models;

use Image;

class PinImagesFile extends BaseModel
{
    public $timestamps = true;
    protected $table = 'su_users_pins_images_files';

    public function pinId()
    {
        return $this->belongsTo(PinImage::class);
    }

    public static function arrInsertResult($imageId, $type, $path, $webPath)
    {
        return [
            'image_id' => $imageId,
            'type' => $type,
            'path' => $path,
            'web_path' => $webPath,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
    }

    public static function resizeImages($originalPath, $thumbPath, $coverPath)
    {
        // resize thumb
        $imgThumb = Image::make($originalPath);
        // resize the image to a width of 100 and constrain aspect ratio (auto height)
        $imgThumb->resize(100, null, function ($constraint) {
            $constraint->aspectRatio();
        });
        // resize image to new height but do not exceed original size
        $imgThumb->heighten(100, function ($constraint) {
            $constraint->upsize();
        });
        $imgThumb->save($thumbPath);

        // resize cover
        $imgCover = Image::make($originalPath);
        // resize the image to a width of 360 and constrain aspect ratio (auto height)
        $imgCover->resize(320, null, function ($constraint) {
            $constraint->aspectRatio();
        });
        $imgCover->save($coverPath);
    }
}
