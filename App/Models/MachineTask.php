<?php

namespace App\Models;

use App\Casts\PgIntArray;
use App\Traits\Common\TimestampsTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MachineTask extends BaseModel
{
    use TimestampsTrait;

    public const STATE_PLANNED = 'Planned';
    public const STATE_SCHEDULED = 'Scheduled';
    public const STATE_ONGOING = 'Ongoing';
    public const STATE_DONE_PROPOSED = 'Done (proposed)';
    public const STATE_DONE_APPROVED = 'Done (approved)';
    public const STATE_CANCELED = 'Canceled';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machine_tasks';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    protected $guarded = ['products'];

    protected $casts = [
        'work_operations' => 'array',
        'report_json' => 'object',
        'guidance_line_ids' => PgIntArray::class,
        'work_operation_ids' => PgIntArray::class,
        'vra_order_ids' => PgIntArray::class,
        'dates' => 'array',
    ];

    public function organization(): HasOne
    {
        return $this->hasOne(Organization::class, 'id', 'organization_id');
    }

    public function plot(): HasOne
    {
        return $this->hasOne(Plot::class, 'gid', 'plot_id');
    }

    public function machineUnit(): HasOne
    {
        return $this->hasOne(MachineUnit::class, 'id', 'machine_unit_id');
    }

    public function machineEvent(): HasOne
    {
        return $this->hasOne(MachineEvent::class, 'id', 'machine_event_id');
    }

    public function machineImplement(): HasOne
    {
        return $this->hasOne(MachineImplement::class, 'id', 'machine_implement_id');
    }

    public function taskProducts(): HasMany
    {
        return $this->hasMany(MachineTaskProduct::class, 'task_id', 'id');
    }

    public function guidanceLines(): Collection
    {
        return GuidanceLine::whereIn('gid', $this->guidance_line_ids ?? [])->get();
    }

    public function workOperations(): Collection
    {
        return WorkOperation::whereIn('id', $this->work_operation_ids ?? [])->get();
    }

    public function vraOrders(): Collection
    {
        if (!$this->vra_order_ids) {
            return collect([]);
        }

        $orderSoilVra = OrderSoilVra::whereIn('order_id', $this->vra_order_ids ?? [])
            ->where(['plot_id' => $this->plot_id])
            ->get();

        $orderSatelliteVra = OrderSatelliteVra::whereIn('order_id', $this->vra_order_ids ?? [])
            ->where(['plot_id' => $this->plot_id])
            ->get();

        return $orderSoilVra->merge($orderSatelliteVra);
    }

    public function scopeForOrganization(Builder $builder, int $organizationId): Builder
    {
        return $builder->where('organization_id', $organizationId);
    }

    public function scopeWithFilters(Builder $builder, array $filters): Builder
    {
        if (isset($filters['plot_ids'])) {
            $builder->whereIn('plot_id', json_decode($filters['plot_ids']));
        }

        if (isset($filters['states'])) {
            $builder->whereIn(
                'state',
                json_decode($filters['states'], true)
            );
        }

        if (isset($filters['work_operation_ids'])) {
            $builder->where(
                'work_operation_ids',
                '&&',
                DB::raw("array{$filters['work_operation_ids']}")
            );
        }

        if (isset($filters['start_date'], $filters['end_date'])) {
            $builder->whereRaw(
                "tsrange(start_date, end_date, '[]') && tsrange(to_timestamp(?)::timestamp, to_timestamp(?)::timestamp, '[]')",
                [$filters['start_date'], $filters['end_date']]
            );
        }

        return $builder;
    }

    public static function getAvailableStateForStore(): array
    {
        return [
            self::STATE_PLANNED,
            self::STATE_SCHEDULED,
            self::STATE_DONE_APPROVED,
        ];
    }

    public static function getAvailableStateForOngoingTasks(): array
    {
        return [
            self::STATE_PLANNED,
            self::STATE_SCHEDULED,
        ];
    }

    public static function getAvailableStateForUpdate(): array
    {
        return [
            self::STATE_PLANNED,
            self::STATE_SCHEDULED,
            self::STATE_ONGOING,
            self::STATE_DONE_PROPOSED,
            self::STATE_DONE_APPROVED,
            self::STATE_CANCELED,
        ];
    }

    public static function getAvailableStateToMatchTaskToEvent(): array
    {
        return [
            self::STATE_PLANNED,
            self::STATE_SCHEDULED,
            self::STATE_ONGOING,
        ];
    }

    public static function getFilteredMachineTaskForPlots(array $filters): Builder
    {
        $qb = self::selectRaw("
            su_machine_tasks.organization_id,
            su_machine_tasks.plot_id,
            su_machine_tasks.id,
            su_machine_tasks.state, 
            su_machine_tasks.start_date, 
            su_machine_tasks.end_date,
            case when COUNT(swo.id) > 0
                then JSONB_AGG( 
                    JSONB_BUILD_OBJECT(
                        'id', swo.id,
                        'name', swo.name
                    )
                )  
                else '[]'::JSONB
            end as work_operations
        ")
            ->leftJoin('su_work_operations as swo', DB::raw('array[swo.id]'), '&&', DB::raw('su_machine_tasks.work_operation_ids::INT[]'))
            ->groupBy(
                'su_machine_tasks.plot_id',
                'su_machine_tasks.id',
                'su_machine_tasks.state',
                'su_machine_tasks.start_date',
                'su_machine_tasks.end_date'
            )
            ->orderBy('su_machine_tasks.start_date');

        if (isset($filters['organization_id'])) {
            $qb->where('su_machine_tasks.organization_id', '=', $filters['organization_id']);
        }

        if (isset($filters['start_date'], $filters['end_date'])) {
            $qb->whereRaw(
                "tsrange(su_machine_tasks.start_date, su_machine_tasks.end_date, '[]') && tsrange(to_timestamp(?)::TIMESTAMP, to_timestamp(?)::TIMESTAMP, '[]')",
                [$filters['start_date'], $filters['end_date']]
            );
        }

        if (isset($filters['farm_year'])) {
            $qb->where('su_machine_tasks.farm_year', $filters['farm_year']);
        }

        if (isset($filters['plot_ids']) && count($filters['plot_ids'])) {
            $qb->whereIn('su_machine_tasks.plot_id', $filters['plot_ids']);
        }

        if (isset($filters['work_operation_ids']) && count($filters['work_operation_ids'])) {
            $wo = json_encode($filters['work_operation_ids']);
            $qb->where(
                'su_machine_tasks.work_operation_ids',
                '&&',
                DB::raw("array{$wo}")
            );
        }

        if (isset($filters['task_states']) && count($filters['task_states'])) {
            $qb->whereIn('su_machine_tasks.state', $filters['task_states']);
        }

        return $qb;
    }

    public static function getReportQuery(int $organizationId, array $filter): Builder
    {
        $query = self::leftJoin('su_machine_task_products AS smtp', 'smtp.task_id', '=', 'su_machine_tasks.id')
            ->leftJoin('su_products AS p', 'p.id', '=', 'smtp.product_id')
            ->leftJoin('su_units_of_measure as suom', 'suom.id', '=', 'p.unit_id')
            ->leftJoin('su_machine_units AS mu', 'mu.id', '=', 'su_machine_tasks.machine_unit_id')
            ->leftJoin('su_satellite_plots AS sp', 'sp.gid', '=', 'su_machine_tasks.plot_id')
            ->leftJoin('su_machine_events', 'su_machine_tasks.machine_event_id', '=', 'su_machine_events.id')
            ->leftJoin('su_machines_implements AS mi', 'mi.id', '=', 'su_machine_tasks.machine_implement_id')
            ->leftJoin('su_farms AS f', 'f.id', '=', 'sp.farm_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_machine_tasks.plot_id')
                    ->where(DB::raw('su_machine_tasks.start_date::DATE'), '>=', DB::raw('spc.from_date::DATE'))
                    ->where(DB::raw('su_machine_tasks.end_date::DATE'), '<=', DB::raw('spc.to_date::DATE'));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'spc.crop_id')
            ->where('mu.organization_id', $organizationId);

        if (isset($filter['from'], $filter['to'])) {
            $query->where('su_machine_tasks.start_date', '>=', Carbon::createFromTimestamp(($filter['from']))->format('Y-m-d H:i:s'));
            $query->where('su_machine_tasks.end_date', '<=', Carbon::createFromTimestamp(($filter['to']))->format('Y-m-d H:i:s'));
        }

        $query->when(count($filter['farmIds']), function ($query) use ($filter) {
            $query->whereIn('sp.farm_id', $filter['farmIds']);
        });

        $query->when(count($filter['plotIds']), function ($query) use ($filter) {
            $query->whereIn('sp.gid', $filter['plotIds']);
        });

        $query->when(count($filter['machineIds']), function ($query) use ($filter) {
            $query->whereIn('su_machine_events.machine_id', $filter['machineIds']);
        });

        $query->when(count($filter['implements']), function ($query) use ($filter) {
            $query->whereIn('mi.name', $filter['implements']);
        });

        $query->when(count($filter['workOperations']), function ($query) use ($filter) {
            $wo = json_encode($filter['workOperations']);
            $query->where('su_machine_tasks.work_operation_ids', '&&', DB::raw("array{$wo}"));
        });

        $query->when(count($filter['taskStates']), function ($query) use ($filter) {
            $query->whereIn('su_machine_tasks.state', $filter['taskStates']);
        });

        $query->when(count($filter['productIds']), function ($query) use ($filter) {
            $query->whereIn('p.id', $filter['productIds']);
        });

        $query->when(isset($filter['driver']), function ($query) use ($filter) {
            $query->where(DB::raw("COALESCE(su_machine_events.driver, '')"), 'ilike', trim('%' . $filter['driver'] . '%'));
        });

        $query->when(count($filter['cropIds']), function ($query) use ($filter) {
            $query->whereIn('spc.crop_id', $filter['cropIds']);
        });

        return $query;
    }

    public function getStartDateAttribute($value): string
    {
        return Carbon::parse($value)->setTimezone(config('app.timezone'))->toIso8601String();
    }

    public function getEndDateAttribute($value): string
    {
        return Carbon::parse($value)->setTimezone(config('app.timezone'))->toIso8601String();
    }

    public function getCompletionDateAttribute($value): ?string
    {
        return $value ? Carbon::parse($value)->setTimezone(config('app.timezone'))->toIso8601String() : null;
    }

    public static function removeByOrderUuidsAndPlotUuids(array $plotUuids, array $orderUuids)
    {
        self::join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'su_machine_tasks.plot_id')
            ->join('su_satellite_orders_plots_rel AS ssopr', 'ssopr.plot_uuid', '=', 'ssp.uuid')
            ->join('su_satellite_orders AS sso', 'sso.uuid', '=', 'ssopr.order_uuid')
            ->whereIn('ssopr.plot_uuid', $plotUuids)
            ->whereIn('ssopr.order_uuid', $orderUuids)
            ->whereBetween('su_machine_tasks.start_date', [DB::raw('sso.from_date'), DB::raw('sso.to_date')])
            ->whereBetween('su_machine_tasks.end_date', [DB::raw('sso.from_date'), DB::raw('sso.to_date')])
            ->delete();
    }
}
