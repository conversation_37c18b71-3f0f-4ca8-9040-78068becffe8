<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Models;

use DateTime;

/**
 * Class NotificationDomain.
 *
 * @property int $id
 * @property string $title
 * @property string $message
 * @property string $data
 * @property bool $is_sent
 * @property int $organization_id
 * @property int $domain_id
 * @property DateTime $created_at
 * @property DateTime $updated_at
 */
class Notification extends BaseModel
{
    public $timestamps = true;
    protected $table = 'notifications';

    public function organization()
    {
        $this->belongsTo(Organization::class);
    }

    public function domain()
    {
        $this->belongsTo(NotificationDomain::class);
    }
}
