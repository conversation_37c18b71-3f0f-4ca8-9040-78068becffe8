<?php

namespace App\Models;

use DateTime;

/**
 * Class ApiEndpoint.
 *
 * @property int $id
 * @property string $class
 * @property string $method
 * @property string $description
 * @property DateTime $created_at
 * @property DateTime $updated_at
 */
class ApiEndpoint extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'api_endpoints';

    protected $connection = 'main';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['class', 'method', 'description'];

    public function abilities()
    {
        return $this->belongsToMany('App\Models\Ability', 'api_endpoints_abilities', 'api_endpoint_id', 'ability_id');
    }
}
