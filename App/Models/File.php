<?php

namespace App\Models;

class File extends BaseModel
{
    public const ERROR_RUNTIME = 7;
    public const LOADING_FILE_NOW = 19;
    public const LOADING_FILE = 0;
    public const SUCCESSFULLY_TREATED = 1;
    public const ERROR_INVALID_SHAPE = 2;
    public const ERROR_INVALID_DBF = 3;
    public const ERROR_INVALID_ARCHIVE = 4;
    public const ERROR_INVALID_GEOMETRY = 5;
    public const ERROR_INVALID_ISAK_FILE = 6;
    public const ERROR_INVALID_TABLE_STRUCTURE = 8;
    public const ERROR_INVALID_FILE_DATA = 9;
    public const ERROR_WAITING_DEFINITION = 10;
    public const ERROR_WAITING_COPYING = 11;
    public const ERROR_INVALID_CRS = 12;
    public const ERROR_NOT_ALLOWED_ADDING = 13;
    public const ERROR_INCORRECT_ENCODING = 14;
    public const ERROR_MISSING_COLUMN = 15;
    public const ERROR_INCORRECT_ENCODING_FIELD = 16;
    public const PARTIALLY_PROCESSED = 17;
    public const INCONSISTENT_FILE_TYPE = 18;
    public const ERROR_READING_SHAPE_OBJECT = 20;
    public const ERROR_GEOMETRY_COLLECTION = 21;
    public const ERROR_OGRINFO = 22;
    public const ERROR_SHP2PSQL_NOT_FOUND = 23;

    // Device Types
    public const DEVICE_UNKNOWN = 0;
    public const DEVICE_OSZ = 3;

    // File types
    public const SHP = 'shp';
    public const KML = 'kml';
    public const KMZ = 'kmz';
    public const ZIP = 'application/zip';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_files';

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = ['errors'];

    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'user_id');
    }

    public function farm()
    {
        return $this->belongsTo('App\Models\Farm', 'id', 'farm_id');
    }
}
