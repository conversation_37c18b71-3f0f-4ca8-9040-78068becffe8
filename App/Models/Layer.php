<?php

namespace App\Models;

class Layer extends BaseModel
{
    public const LAYER_TYPE_SATELLITE_WORK = 8;
    public const LAYER_TYPE_SOIL_GRID = 9;
    public const LAYER_TYPE_SOIL_POINTS = 10;
    public const LAYER_TYPE_TRACKS = 11;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_layers';

    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'user_id');
    }
}
