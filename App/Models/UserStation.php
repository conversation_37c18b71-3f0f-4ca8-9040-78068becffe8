<?php

namespace App\Models;

use App\Helpers\Helper;
use Auth;
use Config;
use DB;
use Illuminate\Database\Eloquent\Builder;

class UserStation extends BaseModel
{
    public const LONGITUDE_INDEX = 0;
    public const LATITUDE_INDEX = 1;
    public const PRECISION = 7;

    public const TYPE_ONSITE2 = 'OnSite2';
    public const TYPE_VIRTUAL = 'Virtual';
    public const TYPE_PESSL = 'Pessl';
    public const TYPE_ONSITE = 'OnSite';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_stations';

    protected $fillable = ['name', 'latitude', 'longitude', 'radius', 'last_communication', 'active', 'geom', 'type',
        'install_date', 'organization_id', 'custom_nameype', 'contract_id'];

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'id', 'organization_id');
    }

    /**
     * Get UserStation by station id.
     */
    public static function getById(int $stationId): ?self
    {
        $user = Auth::user();

        return self::join('su_organizations_users as ou', 'ou.organization_id', '=', 'su_users_stations.organization_id')
            ->where([
                ['su_users_stations.organization_id', $user->lastChosenOrganization->id],
                ['su_users_stations.id', $stationId],
            ])
            ->select('su_users_stations.*')
            ->first();
    }

    /**
     * Get station types.
     *
     * @param array $exclude Station types to exclude
     */
    public static function getStationTypes(array $exclude = []): array
    {
        $query = self::select('type')->distinct();

        if (count($exclude) > 0) {
            $query->whereNotIn('type', $exclude);
        }

        return $query->pluck('type')->toArray();
    }

    /**
     * Get UserStation by plot id.
     */
    public static function getByPlotId(int $plotId): ?self
    {
        $user = Auth::user();

        return self::join('su_organizations_users as ou', 'ou.organization_id', '=', 'su_users_stations.organization_id')
            ->join('su_satellite_plots as ssp', 'ssp.station_id', '=', 'su_users_stations.id')
            ->where([
                ['su_users_stations.organization_id', $user->lastChosenOrganization->id],
                ['ssp.gid', $plotId],
            ])
            ->select('su_users_stations.*')
            ->first();
    }

    public static function inRadiusOfStation($lon, $lat, $organizationId = null)
    {
        $query = self::select(
            '*'
        );

        if (!$organizationId) {
            $query
                ->join('su_organizations_users as ou', 'ou.organization_id', '=', 'su_users_stations.organization_id')
                ->where('ou.user_id', Auth::user()->id);
        }

        if ($organizationId) {
            $query->where('su_users_stations.organization_id', $organizationId);
        }
        $query->whereRaw("ST_Distance(su_users_stations.geom, st_transform(st_setsrid(st_makepoint({$lon}, {$lat}), 4326), " . Config::get('globals.DEFAULT_DB_CRS') . ')) <= ((su_users_stations.radius*1000))');

        return $query;
    }

    public static function setStationIds($station)
    {
        // remove
        UserStation::removeStationIds($station);

        // Get found gids in station radius
        $foundGids = Plot::select([
            'su_satellite_plots.gid',
        ])
            ->join('su_farms AS f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users_stations AS us', 'us.organization_id', '=', 'f.organization_id')
            ->where('us.id', $station->id)
            ->where('f.organization_id', $station->organization_id)
            ->where(DB::raw('ST_Distance(us.geom, su_satellite_plots.geom)'), '<=', (intval($station->radius) * 1000))
            ->get()->pluck('gid')->toArray();

        $sqlFoundGids = 'SELECT su_satellite_plots.gid FROM su_satellite_plots
        INNER JOIN su_farms f on f.id = su_satellite_plots.farm_id
        INNER JOIN su_users_stations us on us.organization_id = f.organization_id
        WHERE us.id = ' . $station->id . ' AND f.organization_id = ' . $station->organization_id . ' AND ST_Distance(us.geom, su_satellite_plots.geom) <= ' . $station->radius . ' * 1000';

        $subQuery = 'SELECT su_satellite_plots.gid, us.id, ST_Distance(us.geom, su_satellite_plots.geom) as distance, us.name  FROM su_satellite_plots
        INNER JOIN su_farms f on f.id = su_satellite_plots.farm_id
        INNER JOIN su_users_stations us on us.organization_id = f.organization_id
        WHERE gid IN (' . $sqlFoundGids . ') AND f.organization_id = ' . $station->organization_id . ' AND ST_Distance(us.geom, su_satellite_plots.geom) <= us.radius * 1000
        ORDER BY distance asc';

        // Get station_ids on MIN distance
        $sqlMain = 'SELECT gid, id as station_id, MIN(distance) as min_distance FROM (' . $subQuery . ') as sub GROUP BY sub.gid, sub.id ORDER BY MIN(distance) ASC LIMIT ' . count($foundGids) . '';

        $arrResult = DB::select($sqlMain);

        // Multi Update
        $keyName = 'gid';
        $columnNameToUpdate = 'station_id';
        $tableName = 'su_satellite_plots';
        Helper::multiUpdate($tableName, $arrResult, $keyName, [$columnNameToUpdate]);
    }

    public static function removeStationIds($station)
    {
        $farmIds = Farm::select([
            'id',
        ])
            ->where('organization_id', $station->organization_id)
            ->get()->pluck('id')->toArray();

        // remove
        if (count($farmIds)) {
            DB::table('su_satellite_plots')
                ->where('station_id', $station->id)
                ->whereIn('farm_id', $farmIds)
                ->update(['station_id' => null]);
        }
    }

    public static function getForMap(int $userId, int $organizationId): array
    {
        $forMapQuery = self::getForMapQuery($userId, $organizationId);

        return $forMapQuery->get([
            'su_users_stations.id',
            'su_users_stations.organization_id',
            DB::raw('CASE WHEN su_users_stations.custom_name IS NOT NULL THEN su_users_stations.custom_name ELSE su_users_stations.name END as name'),
            'su_users_stations.latitude',
            'su_users_stations.longitude',
            'su_users_stations.radius',
            'su_users_stations.last_communication',
            'su_users_stations.type',
            'su_users_stations.active',
            DB::raw('ST_AsText(ST_Transform(su_users_stations.geom, 3857)) AS geom'),
        ])->toArray();
    }

    /**
     * @return array $geoJson
     */
    public static function getForMapGeoJson(int $userId, int $organizationId): array
    {
        $forMapQuery = self::getForMapQuery($userId, $organizationId);

        $stationsForMap = $forMapQuery->select(DB::raw("
            json_build_object(
                'type', 'FeatureCollection',
                'features', json_agg(
                    json_build_object(
                        'type',       'Feature',
                        'geometry',   ST_AsGeoJSON(ST_Transform(su_users_stations.geom, 3857))::json,
                        'properties', json_build_object(
                            'id', su_users_stations.id,
                            'organization_id', su_users_stations.organization_id,
                            'name', CASE WHEN su_users_stations.custom_name IS NOT NULL
                                THEN su_users_stations.custom_name
                                ELSE su_users_stations.name 
                            END,
                            'real_name', su_users_stations.name,
                            'latitude', su_users_stations.latitude,
                            'longitude', su_users_stations.longitude,
                            'radius', su_users_stations.radius,
                            'last_communication', su_users_stations.last_communication,
                            'type', su_users_stations.type,
                            'active', su_users_stations.active
                        )
                    )
                )
            ) as geojson
        "));

        return json_decode($stationsForMap->pluck('geojson')->first(), true);
    }

    public static function getExtent($userId, $organizationId)
    {
        $extentQuery = self::selectRaw('ST_Extent(ST_Transform(su_users_stations.geom, 3857)) AS extent')
            ->join('su_organizations_users as ou', 'ou.organization_id', '=', 'su_users_stations.organization_id')
            ->where('ou.organization_id', $organizationId)
            ->where('ou.user_id', $userId)
            ->where('su_users_stations.active', true);

        $extent = $extentQuery->pluck('extent');

        return Helper::parseExtentToOpenLayer($extent, true);
    }

    public static function getByOrganizationsAndContracts(array $contractsId)
    {
        $query = self::select('su_users_stations.*')
            ->join('su_organizations as org', 'org.id', '=', 'su_users_stations.organization_id')
            ->whereIn('contract_id', $contractsId);

        return $query->get();
    }

    public function getStationApi()
    {
        return Helper::resolveObject('App\Classes\Meteo\\' . $this->attributes['type'], ['stationModel' => $this]);
    }

    public static function getStationsByOrganization($organizationId, $stationType = '')
    {
        $stations = self::join(
            'su_organizations_users as ou',
            'ou.organization_id',
            '=',
            'su_users_stations.organization_id'
        )
            ->where('ou.organization_id', $organizationId)
            ->where('ou.user_id', Auth::user()->id)
            ->where('su_users_stations.active', true);

        if ($stationType) {
            $stations = $stations->where('su_users_stations.type', $stationType);
        }

        return $stations->orderBy('su_users_stations.custom_name', 'asc')->get([
            'su_users_stations.id',
            'su_users_stations.organization_id',
            DB::raw('CASE WHEN su_users_stations.custom_name IS NOT NULL THEN su_users_stations.custom_name ELSE su_users_stations.name END as name'),
            'su_users_stations.name as real_name',
            'su_users_stations.latitude',
            'su_users_stations.longitude',
            'su_users_stations.radius',
            'su_users_stations.last_communication',
            'su_users_stations.type',
            'su_users_stations.active',
            DB::raw('ST_AsText(ST_Transform(su_users_stations.geom, 3857)) AS geom'),
        ]);
    }

    public static function getForCropDevelopment($farmIds = [], $plotIds = [])
    {
        $stations = self::join('su_satellite_plots', 'su_satellite_plots.station_id', '=', 'su_users_stations.id')
            ->join('su_farms as f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->where('o.id', Auth::user()->lastChosenOrganization->id);

        if (count($farmIds) > 0) {
            $stations->whereIn('f.id', $farmIds);
        }

        if (count($plotIds) > 0) {
            $stations->whereIn('su_satellite_plots.gid', $plotIds);
        }

        return $stations->groupBy('su_users_stations.id')->get(['su_users_stations.id'])->toArray();
    }

    private static function getForMapQuery(int $userId, int $organizationId): Builder
    {
        return self::join(
            'su_organizations_users as ou',
            'ou.organization_id',
            '=',
            'su_users_stations.organization_id'
        )
            ->where('ou.organization_id', $organizationId)
            ->where('ou.user_id', $userId)
            ->where('su_users_stations.active', true);
    }
}
