<?php

namespace App\Models;

use App\Exceptions\ValidationException;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Ramsey\Uuid\Uuid;

class BaseModel extends Model
{
    use \Staudenmeir\LaravelCte\Eloquent\QueriesExpressions;

    public function resolveRouteBinding($value, $field = null)
    {
        if ('uuid' === $field && !Uuid::isValid($value)) {
            throw new ValidationException('Invalid uuid.');
        }

        return parent::resolveRouteBinding($value, $field);
    }

    /**
     * Returns sql string with bind values.
     *
     * @param object $query query Builder object
     *
     * @return query Builder object
     *
     * @internal param string $sort
     */
    public function scopeToSqlWithBindings($query)
    {
        $sql = $query->toSql();

        $sql = str_replace(['%', '?', '"'], ['%%', '%s', ''], $sql);

        return vsprintf($sql, $query->getBindings());
    }

    /**
     * Add 'order by' statement in the query Builder object.
     *
     * @param object $query query Builder object
     * @param string $sort
     * @param mixed $nulls Sets NULLS {FIRST | LAST} to order by statement
     *
     * @return query Builder object
     */
    public function scopeRestOrderBy($query, $sort, $nulls = null)
    {
        if (!$sort || '' == trim($sort)) {
            return $query->orderBy($this->table . '.' . $this->primaryKey, 'desc');
        }

        // $sort could be for example: -priority,created_at
        $arrSortData = $this->processSortData($sort);

        for ($i = 0; $i < count($arrSortData); $i++) {
            switch (strtolower($nulls)) {
                case 'first':
                    $nullsStr = ' nulls first';

                    break;
                case 'last':
                    $nullsStr = ' nulls last';

                    break;
                default:
                    $nullsStr = '';
            }

            $option = '"' . $arrSortData[$i]['column'] . '" ' . $arrSortData[$i]['direction'] . $nullsStr;

            $query = $query->orderByRaw($option);
        }

        return $query;
    }

    /**
     * This function adds whereIn clause to query. If the array with values contains null then adds orWhereNull to the query.
     *
     * @param Builder $query Query
     * @param string $column Column to filter
     * @param array $values Array with values for specified column
     */
    public static function queryWhereInWithNullCheck(Builder $query, string $column, array $values): Builder
    {
        return $query->where(function ($q) use ($column, $values) {
            $nullValueIdx = array_search(null, $values);

            // Check if there is null value in $values array
            if (false !== $nullValueIdx) {
                unset($values[$nullValueIdx]); // remove null value from array
                $q->whereIn(DB::raw($column), $values)
                    ->orWhereNull(DB::raw($column));

                return;
            }

            $q->whereIn(DB::raw($column), $values);
        });
    }

    /**
     * Closes all connections to given database.
     *
     * @param object $query query Builder object
     * @param string $dbName
     *
     * @return query Builder object
     */
    public function scopeCloseConnectionsToDb($query, $dbName)
    {
        $connection = DB::connection();

        $dbVersion = (float)$connection->select('SHOW SERVER_VERSION')[0]->server_version;
        if ($dbVersion > 9.1) {
            $connection->select("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE pid <> pg_backend_pid() AND datname = '{$dbName}';");
        } else {
            $connection->select("SELECT pg_terminate_backend(procpid) FROM pg_stat_activity WHERE procpid <> pg_backend_pid() AND datname = '{$dbName}';");
        }
    }

    /**
     * Process Sort Data.
     *
     * @param string $strSortData
     *
     * @return array
     */
    private function processSortData($strSortData = '')
    {
        $arrSort = explode(',', $strSortData);

        $arrSortData = [];
        for ($i = 0; $i < count($arrSort); $i++) {
            if (strlen($arrSort[$i])) {
                $arrSortData[$i]['direction'] = 'asc';
                $pos = strpos($arrSort[$i], '-');
                if (false !== $pos) {
                    // The string '-' was found in the string '$arrSort[$i]'
                    $arrSortData[$i]['direction'] = 'desc';
                }

                $arrSortData[$i]['column'] = str_replace('-', '', $arrSort[$i]);
            }
        }

        // reset the array
        $newArrSortData = [];
        foreach ($arrSortData as $key => $value) {
            $newArrSortData[] = $value;
        }

        return $newArrSortData;
    }
}
