<?php

namespace App\Models;

use DateTime;
use phpseclib\Math\BigInteger;

/**
 * Class IrrigationTransportationDataRaw.
 *
 * @property BigInteger $irrigation_unit_id
 * @property DateTime $start_time
 * @property DateTime $end_time
 * @property int $origin_platform_id
 * @property int $destination_platform_id
 */
class IrrigationTransportationDataRaw extends BaseModel
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_transportation_data_raw';

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['irrigation_unit_id', 'start_time', 'end_time', 'origin_platform_id', 'destination_platform_id'];

    public function originPlatform()
    {
        return $this->hasMany('App\Models\IrrigationPlatform', 'origin_platform_id', 'id');
    }

    public function destinationPlatform()
    {
        return $this->hasMany('App\Models\IrrigationPlatform', 'destination_platform_id', 'id');
    }

    public function unit()
    {
        return $this->hasMany('App\Models\IrrigationUnit', 'irrigation_unit_id', 'id');
    }
}
