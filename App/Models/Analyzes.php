<?php

namespace App\Models;

use Auth;
use DB;

class Analyzes extends BaseModel
{
    public $timestamps = true;
    protected $table = 'su_analyzes';
    protected $primaryKey = 'id';

    public static function getTableName()
    {
        return with(new static())->getTable();
    }

    public function loadAnalyzes($userId, $originalName, $username, $comment, $sort)
    {
        return self::loadAnalyzesQuery($userId, $originalName, $username, $comment)->restOrderBy($sort);
    }

    public static function loadAnalyzesQuery($userId, $originalName, $username, $comment)
    {
        $query = self::select([
            'su_analyzes.id',
            'u.username',
            'su_analyzes.analyzes_count',
            'su_analyzes.missing_barcodes',
            'su_analyzes.missing_analyzes',
            'su_analyzes.comment',
            'su_analyzes.original_name',
            DB::raw('created_at::DATE as date'),
        ])
            ->join('su_users as u', 'u.id', '=', 'su_analyzes.user_id');

        // Filter
        if ($comment) {
            $query->where('su_analyzes.comment', 'ILIKE', trim('%' . $comment . '%'));
        }

        if ($username) {
            $query->where('u.username', 'ILIKE', trim('%' . $username . '%'));
        }

        if ($originalName) {
            $query->where('su_analyzes.original_name', 'ILIKE', trim('%' . $originalName . '%'));
        }

        // super_admin constraint
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN')) {
            $query->where('su_analyzes.user_id', '=', $userId);
        }

        return $query;
    }
}
