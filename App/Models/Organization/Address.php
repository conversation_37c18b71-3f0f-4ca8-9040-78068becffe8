<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Models\Organization;

use App\Models\BaseModel;

/**
 * Class Address.
 *
 * @property int $id
 * @property string $address
 * @property bool $is_main
 * @property string $note
 */
class Address extends BaseModel
{
    public $timestamps = true;
    protected $table = 'su_organizations_addresses';
    protected $primaryKey = 'id';
    protected $fillable = ['address', 'is_main', 'note', 'city', 'country'];
    protected $hidden = [];

    public function organization()
    {
        return $this->belongsTo('App\Models\Organization', 'organization_id', 'id');
    }
}
