<?php

namespace App\Models;

use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use DateTime;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class LayerPlot extends BaseModel
{
    public const TYPE_INDEX = 'index';
    public const TYPE_SOIL = 'soil';
    public const TYPE_METEO = 'meteo';
    public const TYPE_VRA = 'vra';
    public const TYPE_INDEX_WATER = 'index_water';
    public const TYPE_SOIL_VRA = 'soil_vra';

    public const SATELLITE_TYPE_LANDSAT = 'landsat';
    public const SATELLITE_TYPE_SENTINEL = 'sentinel';
    public const SATELLITE_TYPE_RAPIDEYE = 'rapideye';
    public $timestamps = true;

    protected $table = 'su_satellite_layers_plots';
    protected $casts = [
        'stats' => 'array',
        'sampling_type' => 'array',
    ];

    protected $fillable = [
        'plot_id',
        'layer_name',
        'date',
        'date_time',
        'stats',
        'mean',
        'created_at',
        'order_id',
        'type',
        'satellite_type',
        'is_viewed',
        'clouds_percent',
        'has_water_pounds',
        'sopst_id',
        'element',
        'unit',
        'stats_type',
        'sopr_id',
    ];

    public function orderVra()
    {
        return $this->hasMany('App\Models\OrderVra.php', 'layer_id', 'id');
    }

    public function layerPlotFiles()
    {
        return $this->hasMany(LayerPlotFile::class, 'layer_plot_id', 'id');
    }

    public function orderPlotRel()
    {
        return $this->belongsTo('App\Models\OrderPlotRel', 'sopr_id', 'id');
    }

    public function getStatsInChartFormatAttribute()
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'tooltip']]];

        if (!is_array($this->stats)) {
            return $dataArr;
        }

        foreach ($this->stats as $key => $value) {
            $area = round($value * $areaCoef, 3);

            if ('index' === $this->order_type) {
                $indexColors = collect(Config::get('globals.INDEX_COLORS')[10]);

                if (isset($indexColors[$key])) {
                    $arrRow = [$key, (float)$area, $indexColors[$key][0], (float)($area) . ' ' . $areaLabel];
                    $dataArr[] = $arrRow;
                }
            } elseif ('index_water' === $this->order_type) {
                $indexColors = collect(Config::get('globals.WATER_INDEX_COLORS')[0]);

                if (isset($indexColors[$key])) {
                    $arrRow = [$key, (float)$area, $indexColors[$key][0], (float)($area) . ' ' . $areaLabel];
                    $dataArr[] = $arrRow;
                }
            } elseif ('soil' === $this->order_type) {
                $index = explode('|', $key);

                $name = explode('-', $this->compound);

                $colorSchemeFile = Config::get('globals.SOILS_PATH') . $name[0] . '_' . $this->type . '.txt';
                $indexColors = collect(Helper::getSoilColorScheme($colorSchemeFile));

                if (isset($index[1], $indexColors[$index[0]])) {
                    $arrRow = [$index[1], (float)$area, $indexColors[$index[0]], (float)($area) . ' ' . $areaLabel];
                    $dataArr[] = $arrRow;
                }
            }
        }

        return $dataArr;
    }

    public function getStatsInSoilChartFormatAttribute()
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = trans('general.' . strtolower(Config::get('globals.AREA_UNIT')));

        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'annotation']]];

        if (!is_array($this->stats)) {
            return $dataArr;
        }

        $stats = array_reverse($this->stats);

        foreach ($stats as $key => $value) {
            $area = round($value * $areaCoef, 3);

            $index = explode('|', $key);

            $name = explode('-', $this->compound);
            $compound = $name[0];

            $colorSchemeFile = Config::get('globals.SOILS_PATH') . $compound . '_' . $this->type . '.txt';
            $indexColors = collect(Helper::getSoilColorScheme($colorSchemeFile));
            $indexLabels = collect(Helper::getSoilLabelScheme($colorSchemeFile));

            if (isset($index[1], $indexColors[$index[0]])) {
                $annotation = (float)($area) . ' ' . $areaLabel;

                if (isset($indexLabels[$index[0]])) {
                    $label = $indexLabels[$index[0]];
                    $annotation = trans("soils.{$compound}.{$label}") . ' - ' . $annotation;
                }

                $arrRow = [$index[1], (float)$area, $indexColors[$index[0]], $annotation];
                $dataArr[] = $arrRow;
            }
        }

        return $dataArr;
    }

    public function imagesDates(
        int $farmYear,
        string $from,
        string $to,
        array $farmIds,
        array $plotIds,
        array $cropIds,
        ?int $maxCloudCoveragePercent = 100
    ) {
        $startFarmingYear = (new DateTime($farmYear - 1 . '-10-01 00:00:00'))->getTimestamp();
        $endFarmingYear = (new DateTime($farmYear . '-09-30 23:59:59'))->getTimestamp();

        if ($to > $endFarmingYear || $to < $startFarmingYear || $from > $endFarmingYear) {
            throw new ValidationException('Dates are out of farming year rage.');
        }

        if ($from < $startFarmingYear) {
            $toDateMinusFourteenDays = (new DateTime())->setTimestamp($to)->modify('-14 day')->getTimestamp();
            $from = $startFarmingYear > $toDateMinusFourteenDays ? $startFarmingYear : $toDateMinusFourteenDays;
        }

        $imagesDatesQuery = self::select([
            'su_satellite_layers_plots.date',
            'su_satellite_layers_plots.satellite_type',
            DB::raw('AVG(su_satellite_layers_plots.clouds_percent) as clouds_percent'),
            DB::raw('
                ROUND(
                    (SUM(sp.area * COALESCE(su_satellite_layers_plots.clouds_percent, 0)) / SUM(sp.area))::NUMERIC,
                    2
                ) AS weigthed_avg_clouds_percent
            '),
            DB::raw('AVG(su_satellite_layers_plots.mean)::integer as avg_mean'),
        ])
            ->join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($farmYear) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id');
                $join->on('spc.is_primary', '=', DB::raw('true'));
                $join->on('spc.year', '=', DB::raw($farmYear));
            })
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('so.status', 'processed')
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('su_satellite_layers_plots.satellite_type', '=', 'sentinel')
            ->whereBetween('su_satellite_layers_plots.date', [DB::raw("to_timestamp({$from})::date"), DB::raw("to_timestamp({$to})::date")])
            ->groupBy('su_satellite_layers_plots.date', 'su_satellite_layers_plots.satellite_type')
            ->orderBy('su_satellite_layers_plots.date', 'ASC');

        if (isset($farmIds) && count($farmIds) > 0) {
            $imagesDatesQuery->whereIn('sp.farm_id', $farmIds);
        }

        if (isset($plotIds) && count($plotIds) > 0) {
            $imagesDatesQuery->whereIn('sp.gid', $plotIds);
        }

        if (is_array($cropIds) && count($cropIds) > 0) {
            $imagesDatesQuery->where(function ($q) use ($cropIds) {
                if (in_array(null, $cropIds)) {
                    $q->whereNull('spc.crop_id');

                    $cropIds = array_filter($cropIds, function ($item) {
                        return isset($item);
                    });

                    if (count($cropIds) > 0) {
                        $q->orWhereIn('spc.crop_id', $cropIds);
                    }

                    return $q;
                }

                return $q->whereIn('spc.crop_id', $cropIds);
            });
        }

        $query = self::withExpression('images_dates', $imagesDatesQuery)
            ->from('images_dates')
            ->select(
                'date',
                'satellite_type',
                'clouds_percent',
                'avg_mean'
            );

        if (isset($maxCloudCoveragePercent)) {
            $query->where(DB::raw('COALESCE(weigthed_avg_clouds_percent, 0)'), '<=', $maxCloudCoveragePercent);
        }

        return $query;
    }

    /**
     * Get Index Аnalysis.
     *
     * @param $userId
     */
    public static function imagesDatesByPlotAndFarmingYear(int $plotId, int $farmYear)
    {
        return self::select([
            'so.id as order_id',
            'so.uuid as order_uuid',
            'sp.uuid as plot_uuid',
            'sp.gid as plot_id',
            'su_satellite_layers_plots.date',
        ])
            ->join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->whereIn('so.status', ['processed', 'waiting_payment', 'paid'])
            ->where('su_satellite_layers_plots.plot_id', $plotId)
            ->where('su_satellite_layers_plots.type', self::TYPE_SOIL)
            ->where('so.year', $farmYear)
            ->groupBy('so.id', 'so.uuid', 'sp.uuid', 'sp.gid', 'su_satellite_layers_plots.date')
            ->orderBy('su_satellite_layers_plots.date', 'DESC');
    }

    /**
     * Get Avg Index.
     *
     * @param $userId
     */
    public function avgIndexQuery(User $user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)
    {
        $query = self::join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($year) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id');
                $join->on('spc.is_primary', '=', DB::raw('true'));
                $join->on('spc.year', '=', DB::raw($year));
            })
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', $user->id)
            ->where('f.organization_id', $user->lastChosenOrganization->id)
            ->where('so.status', 'processed')
            ->where('so.year', $year)
            ->where('su_satellite_layers_plots.type', $type)
            ->where('su_satellite_layers_plots.satellite_type', $satelliteType)
            ->where('su_satellite_layers_plots.date', $date)
            ->whereNotNull('stats');

        if (is_array($cropIds) && count($cropIds) > 0) {
            $query->where(function ($q) use ($cropIds) {
                if (in_array(null, $cropIds)) {
                    $q->whereNull('spc.crop_id');

                    $cropIds = array_filter($cropIds, function ($item) {
                        return isset($item);
                    });

                    if (count($cropIds) > 0) {
                        $q->orWhereIn('spc.crop_id', $cropIds);
                    }

                    return $q;
                }

                return $q->whereIn('spc.crop_id', $cropIds);
            });
        }

        if (is_array($farmIds) && count($farmIds) > 0) {
            $query->whereIn('sp.farm_id', $farmIds);
        }

        if (is_array($plotIds) && count($plotIds) > 0) {
            $query->whereIn('sp.gid', $plotIds);
        }

        return $query;
    }

    /**
     * Get Avg Index ECharts.
     */
    public function avgIndexECharts(User $user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)
    {
        return $this->avgIndexQuery($user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)->select([
            DB::raw('round(SUM((stats::json->>\'0-10\')::NUMERIC)::NUMERIC, 0) AS "0-10"'),
            DB::raw('round(SUM((stats::json->>\'10-20\')::NUMERIC)::NUMERIC, 0) AS "10-20"'),
            DB::raw('round(SUM((stats::json->>\'20-30\')::NUMERIC)::NUMERIC, 0) AS "20-30"'),
            DB::raw('round(SUM((stats::json->>\'30-40\')::NUMERIC)::NUMERIC, 0) AS "30-40"'),
            DB::raw('round(SUM((stats::json->>\'40-50\')::NUMERIC)::NUMERIC, 0) AS "40-50"'),
            DB::raw('round(SUM((stats::json->>\'50-60\')::NUMERIC)::NUMERIC, 0) AS "50-60"'),
            DB::raw('round(SUM((stats::json->>\'60-70\')::NUMERIC)::NUMERIC, 0) AS "60-70"'),
            DB::raw('round(SUM((stats::json->>\'70-80\')::NUMERIC)::NUMERIC, 0) AS "70-80"'),
            DB::raw('round(SUM((stats::json->>\'80-90\')::NUMERIC)::NUMERIC, 0) AS "80-90"'),
            DB::raw('round(SUM((stats::json->>\'90-100\')::NUMERIC)::NUMERIC, 0) AS "90-100"'),
            DB::raw('round(SUM((stats::json->>\'100-110\')::NUMERIC)::NUMERIC, 0) AS "100-110"'),
        ]);
    }

    /**
     * Get Avg Index GCharts.
     */
    public function avgIndexGCharts(User $user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)
    {
        return $this->avgIndexQuery($user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)->select([
            DB::raw('round(SUM(su_satellite_layers_plots.mean*sp.area)/SUM(sp.area)) as mean'),
            DB::raw('round(SUM((stats::json->>\'0-10\')::NUMERIC)::NUMERIC, 0) AS "0-10"'),
            DB::raw('round(SUM((stats::json->>\'10-20\')::NUMERIC)::NUMERIC, 0) AS "10-20"'),
            DB::raw('round(SUM((stats::json->>\'20-30\')::NUMERIC)::NUMERIC, 0) AS "20-30"'),
            DB::raw('round(SUM((stats::json->>\'30-40\')::NUMERIC)::NUMERIC, 0) AS "30-40"'),
            DB::raw('round(SUM((stats::json->>\'40-50\')::NUMERIC)::NUMERIC, 0) AS "40-50"'),
            DB::raw('round(SUM((stats::json->>\'50-60\')::NUMERIC)::NUMERIC, 0) AS "50-60"'),
            DB::raw('round(SUM((stats::json->>\'60-70\')::NUMERIC)::NUMERIC, 0) AS "60-70"'),
            DB::raw('round(SUM((stats::json->>\'70-80\')::NUMERIC)::NUMERIC, 0) AS "70-80"'),
            DB::raw('round(SUM((stats::json->>\'80-90\')::NUMERIC)::NUMERIC, 0) AS "80-90"'),
            DB::raw('round(SUM((stats::json->>\'90-100\')::NUMERIC)::NUMERIC, 0) AS "90-100"'),
            DB::raw('round(SUM((stats::json->>\'100-110\')::NUMERIC)::NUMERIC, 0) AS "100-110"'),
        ]);
    }

    /**
     * Get Avg Index.
     *
     * @param $userId
     */
    public function statsInChartFormat($avgData)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $totalArea = 0;
        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'annotation'], ['role' => 'tooltip']]];

        if (!isset($avgData[0]['0-10'])) {
            return $dataArr;
        }

        $stats = $avgData[0];

        foreach ($stats as $key => $value) {
            $area = round($value * $areaCoef, 3);

            $indexColors = collect(Config::get('globals.INDEX_COLORS')[10]);

            if (isset($indexColors[$key])) {
                $arrRow = [
                    $key,
                    (float)$area,
                    $indexColors[$key][0],
                    (float)($area) . ' ' . $areaLabel,
                    $key . ': ' . (float)($area) . ' ' . $areaLabel,
                ];
                $dataArr[] = $arrRow;
                $totalArea = $totalArea + $value * $areaCoef;
            }
        }

        $totalArea = round($totalArea) . ' ' . $areaLabel;
        $meanIndex = 0;

        if (isset($stats['mean'])) {
            $meanIndex = $stats['mean'];
        }

        return ['total_area' => $totalArea, 'chart_data' => $dataArr, 'mean_index' => $meanIndex];
    }

    public static function getPlotIdsByDates($beginDate, $endDate)
    {
        return self::select('plot_id')->whereBetween('date', [$beginDate, $endDate])->distinct()->get();
    }

    public static function updateHasWaterPounds($beginDate, $endDate, $tiles)
    {
        $tilesIds = array_unique(array_column($tiles, 'tileId'));

        self::whereBetween('date', [$beginDate, $endDate])
            ->update(['has_water_pounds' => 'false']);

        self::join('su_satellite_plots AS sp', 'sp.gid', '=', 'plot_id')
            ->join(
                'su_satellite_water_pounds AS wp',
                DB::raw('ST_Intersects(ST_Transform(wp.geom, ' . Config::get('globals.DEFAULT_DB_CRS') . '), sp.geom)'),
                '=',
                DB::raw('true')
            )
            ->whereBetween('su_satellite_layers_plots.date', [$beginDate, $endDate])
            ->update(['has_water_pounds' => 'true']);
    }

    public static function plotIndexImagePath($plotId, $indexDate, $satelliteType)
    {
        return self::select([
            'slpf.path',
        ])
            ->join('su_satellite_layers_plots_files AS slpf', 'slpf.layer_plot_id', '=', 'su_satellite_layers_plots.id')
            ->where('su_satellite_layers_plots.plot_id', $plotId)
            ->where('su_satellite_layers_plots.date', $indexDate)
            ->where('su_satellite_layers_plots.satellite_type', $satelliteType)
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('slpf.type', 'PNG')
            ->first();
    }

    public function getAvgMeanForDate(
        int $year,
        string $fromDate,
        string $toDate,
        array $cropIds,
        array $farmIds,
        array $plotIds,
        ?int $maxCloudCoveragePercent = 100
    ) {
        $avgDataQuery = $this->select(
            DB::raw('avg(su_satellite_layers_plots.mean)::integer as avg_mean'),
            DB::raw('(extract(epoch from su_satellite_layers_plots.date)*1000)::BIGINT as dates'),
            DB::raw('
                ROUND(
                    (SUM(sp.area * COALESCE(su_satellite_layers_plots.clouds_percent, 0)) / SUM(sp.area))::NUMERIC,
                    2
                ) AS weigthed_avg_clouds_percent
            ')
        )
            ->join('su_satellite_orders', 'su_satellite_orders.id', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms', 'su_farms.organization_id', 'su_satellite_orders.organization_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($year) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id');
                $join->on('spc.is_primary', '=', DB::raw('true'));
                $join->on('spc.year', '=', DB::raw($year));
            })
            ->whereBetween('su_satellite_layers_plots.date', [DB::raw('to_timestamp(' . $fromDate . ')::date'), DB::raw('to_timestamp(' . $toDate . ')::date')])
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('su_satellite_orders.organization_id', Auth::user()->lastChosenOrganization->id);

        if (is_array($farmIds) && count($farmIds) > 0) {
            $avgDataQuery->whereIn('su_farms.id', $farmIds);
        }

        if (is_array($plotIds) && count($plotIds) > 0) {
            $avgDataQuery->whereIn('su_satellite_layers_plots.plot_id', $plotIds);
        }

        if (is_array($cropIds) && count($cropIds) > 0) {
            $avgDataQuery->where(function ($q) use ($cropIds) {
                if (in_array(null, $cropIds)) {
                    $q->whereNull('spc.crop_id');

                    $cropIds = array_filter($cropIds, function ($item) {
                        return isset($item);
                    });

                    if (count($cropIds) > 0) {
                        $q->orWhereIn('spc.crop_id', $cropIds);
                    }

                    return $q;
                }

                return $q->whereIn('spc.crop_id', $cropIds);
            });
        }

        $avgDataQuery->groupBy('su_satellite_layers_plots.date');
        $avgDataQuery->orderBy('dates', 'asc');

        $query = self::from('avgData')
            ->withExpression('avgData', $avgDataQuery)
            ->select(
                DB::raw('json_agg(avg_mean) as avg_mean'),
                DB::raw('json_agg(dates) as dates'),
                DB::raw('json_agg(weigthed_avg_clouds_percent) as weigthed_avg_clouds_percent')
            );

        if (isset($maxCloudCoveragePercent)) {
            $query->where(DB::raw('COALESCE(weigthed_avg_clouds_percent, 0)'), '<=', $maxCloudCoveragePercent);
        }

        $data = $query->first()->toArray();

        return [
            'avg_mean' => json_decode($data['avg_mean'] ?? '[]'),
            'dates' => json_decode($data['dates'] ?? '[]'),
            'avg_clouds_percent' => json_decode($data['weigthed_avg_clouds_percent'] ?? '[]'),
        ];
    }

    public static function getDataQuery()
    {
        return self::join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id')
                    ->on('spc.from_date', '<=', 'su_satellite_layers_plots.date')
                    ->on('spc.to_date', '>=', 'su_satellite_layers_plots.date')
                    ->where('spc.is_primary', '=', true);
            });
    }

    /**
     * Create soil analysis chart structure.
     */
    public static function getSoilAnalysisChartDataQuery(array $filters, array $soilElementsRanges, string $tableName = 'chart_raw_data')
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $elements_data_laravel = self::from('su_satellite_layers_plots as sspl')
            ->select(
                DB::raw('element::text'),
                DB::raw("
                    case
                        when(nullif(data[2], ''))::decimal(5,1) = 0 then null
                        when (nullif(data[2], ''))::decimal(5,1) is null then (nullif(data[3], ''))::decimal(5,1)
                        else (nullif(data[2], ''))::decimal(5,1)
                    end as from"),
                DB::raw("
                    case
                        when (nullif(data[2], ''))::decimal(5,1) is null then null
                        else (nullif(data[3], ''))::decimal(5,1)
                    end as to
                "),
                DB::raw('round((sum(ranges.value::decimal) * ' . $areaCoef . ')::numeric, 3) as area')
            )
            ->leftJoin('su_satellite_orders_plots_sampling_types as sopst', 'sopst.id', '=', 'sspl.sopst_id')
            ->crossJoin(DB::raw('json_each_text(sspl.stats::json) as ranges'))
            ->crossJoin(DB::raw("regexp_split_to_array(ranges.key, '(\||\-|>=)') as data"))
            ->leftJoin('su_satellite_plots as ssp', 'sspl.plot_id', '=', 'ssp.gid')
            ->leftJoin('su_satellite_orders as sso', 'sspl.order_id', '=', 'sso.id')
            ->leftJoin('su_satellite_plots_crops as sspc', function ($q) use ($filters) {
                $q->on('sspc.plot_id', '=', 'ssp.gid')
                    ->where('sspc.is_primary', true);

                if (isset($filters['farm_year']) && $filters['farm_year']) {
                    $q->where('sspc.year', $filters['farm_year']);
                }
            })
            ->where([
                ['sspl.type', '=', 'soil'],
                ['sspl.stats_type', '=', 'summarized'],
                [DB::raw('ranges.value::decimal'),  '>', 0],
            ])
            ->whereIn('sspl.element', $filters['elements']) // Filter by elements from CMS
            ->whereRaw('gs_is_json(sspl.stats)')
            ->where('sso.type', '=', 'soil');

        if (isset($filters['sampling_type_id'])) {
            $elements_data_laravel->where('sopst.sampling_type_id', $filters['sampling_type_id']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $elements_data_laravel->whereIn('ssp.farm_id', $filters['farm_ids']);
        }

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $elements_data_laravel->whereIn('ssp.gid', $filters['plot_ids']);
        }

        if (isset($filters['plot_name']) && $filters['plot_name']) {
            $elements_data_laravel->where('ssp.name', $filters['plot_name']);
        }

        if (isset($filters['farm_year']) && $filters['farm_year']) {
            $elements_data_laravel->where('sso.year', $filters['farm_year']);
        }

        if (isset($filters['organization_id']) && $filters['organization_id']) {
            $elements_data_laravel->where('sso.organization_id', $filters['organization_id']);
        }

        if (isset($filters['order_uuids']) && count($filters['order_uuids']) > 0) {
            $elements_data_laravel->whereIn('sso.uuid', $filters['order_uuids']);
        }

        if (isset($filters['date']) && $filters['date']) {
            $elements_data_laravel->where('sspl.date', $filters['date']);
        }

        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $elements_data_laravel->whereIn('sspc.crop_id', $filters['crop_ids']);
        }

        $elements_data_laravel->groupBy('element', 'data')
            ->orderBy('element', 'ASC')
            ->orderBy('to', 'ASC');

        // Insert elements with ranges from CMS
        $elements_data_cms = DB::table(DB::raw('json_each(\'' . json_encode($soilElementsRanges) . '\') a'))
            ->selectRaw('
                    a.key as element,
                    (b.value->>\'from\')::decimal(5,1) "from",
                    (b.value->>\'to\')::decimal(5,1) "to",
                    b.value->>\'color\' "color",
                    b.value->>\'label\' "label",
                    (b.value->>\'element_visual_order\')::integer "element_visual_order"
                ')
            ->crossJoin(DB::raw('json_array_elements(a.value) b'))
            ->orderBy('element_visual_order', 'ASC')
            ->orderBy('to', 'ASC');

        $chart_raw_data = DB::table('elements_data_laravel as l')
            ->selectRaw('
            c.element::text,
            c.from,
            c.to,
            c.color,
            c.label,
            coalesce(l.area, 0) area,
            ("c"."element_visual_order")::integer
            ')
            ->join('elements_data_cms as c', function ($q) {
                $q->on('l.element', '=', 'c.element')
                    ->where(function ($quert) {
                        $quert->where('l.element', '=', DB::raw('c.element'))
                            ->where('l.from', '=', DB::raw('c.from'))
                            ->where('l.to', '=', DB::raw('c.to'));
                    })
                    ->orOn(function ($query) {
                        $query->where('l.element', '=', DB::raw('c.element'))
                            ->where('l.from', '=', DB::raw('c.from'))
                            ->whereNull('l.to')
                            ->whereNull('c.to');
                    })
                    ->orOn(function ($query) {
                        $query->where('l.element', '=', DB::raw('c.element'))
                            ->where('l.to', '=', DB::raw('c.to'))
                            ->whereNull('l.from')
                            ->whereNull('c.from');
                    });
            })
            ->orderBy('c.element_visual_order', 'ASC')
            ->orderBy('l.to', 'ASC');

        return DB::table($tableName)
            ->withExpression('elements_data_laravel', $elements_data_laravel)
            ->withExpression('elements_data_cms', $elements_data_cms)
            ->withExpression('chart_raw_data', $chart_raw_data);
    }

    /**
     * Get soil analysis chart data.
     */
    public static function getSoilAnalysisChartData(array $filters, array $soilElementsRanges)
    {
        $chartQuery = self::getSoilAnalysisChartDataQuery($filters, $soilElementsRanges, 'xAxis');

        $elementsForXAxis = DB::table('chart_raw_data')
            ->selectRaw('"element"')
            ->groupBy('element', 'element_visual_order')
            ->orderBy('element_visual_order', 'ASC');

        $allElementsArr = DB::table('elements_for_xAxis')
            ->selectRaw('array_agg("element") elements');

        $chartRawDataGrouped = DB::table('chart_raw_data')->selectRaw('
            label,
            element,
            "from",
            "to",
            color,
            array_fill(0, array[array_position(aea.elements, "element")-1])::numeric[] || array["area"] || array_fill(0, array[array_length(aea.elements, 1) - array_position(aea.elements, "element")])::numeric[] "data"
        ')
            ->leftJoin('all_elements_arr as aea', 'aea.elements', '@>', DB::raw('array["element"]'));

        $series = DB::table('chart_raw_data_grouped')
            ->selectRaw("jsonb_build_object(
                        'name', label,
                        'type', 'bar',
                        'stack', 'total',
                        'label', jsonb_build_object('show', false),
                        'data', array_to_json(\"data\"),
                        'itemStyle', jsonb_build_object('color', color)
                    ) as series
                    ");

        $xAxis = DB::table('elements_for_xAxis')
            ->selectRaw('jsonb_agg(element) as elements');

        $chartQuery->withExpression('elements_for_xAxis', $elementsForXAxis)
            ->withExpression('all_elements_arr', $allElementsArr)
            ->withExpression('chart_raw_data_grouped', $chartRawDataGrouped)
            ->withExpression('series', $series)
            ->withExpression('xAxis', $xAxis)
            ->selectRaw("
            jsonb_build_object(
                'xAxis', json_build_object(
                    'type', 'category',
                    'data', \"xAxis\".elements
                ),
                'yAxis', json_build_object(
                    'type', 'value'
                ), 
                'series', array_agg(
                    series.series
                )
            )")
            ->crossJoin(DB::raw('series'))
            ->groupBy('xAxis.elements');

        $result = $chartQuery->pluck('jsonb_build_object')->first();

        return json_decode($result, true) ?? [];
    }

    public static function getSoilAnalysisAreaByElementChartData(array $filters, array $soilElementsRanges)
    {
        $chartQuery = self::getSoilAnalysisChartDataQuery($filters, $soilElementsRanges);

        $chartQuery->selectRaw("
            jsonb_build_object(
                'xAxis', json_build_object(
                    'type', 'category', 
                    'data', coalesce(jsonb_agg(label order by \"from\" NULLS FIRST), '[]'::jsonb),
                    'show', true
                ),
                'yAxis', json_build_object(
                    'type', 'value',
                    'show', false
                ), 
                'series', ARRAY[
                    jsonb_build_object(
                        'type', 'bar', 
                        'stack', 'total', 
                        'label', jsonb_build_object('show', false), 
                        'data', coalesce(
                            jsonb_agg(
                                json_build_object(
                                    'value', area,
                                    'itemStyle', jsonb_build_object('color', color)
                                )
                                order by \"from\" NULLS FIRST
                            ),
                            '[]'::jsonb
                        )
                    )
                ]
            )
        ");

        $result = $chartQuery->pluck('jsonb_build_object')->first();

        return json_decode($result, true);
    }
}
