<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Models;

class Ability extends \Silber\Bouncer\Database\Ability
{
    public const MANAGE_FIELDS = 'manage_fields';
    public const MANAGE_FIELDS_SUBSCRIPTIONS = 'manage_fields_subscriptions';
    public const SELECT_FOR_SAMPLING = 'select_for_sampling';
    public const MANAGE_WEATHER_STATIONS = 'manage_weather_stations';
    public const MANAGE_FARM_TRACK_INTEGRATIONS = 'manage_farm_track_integrations';
    public const MANAGE_IRRIGATION_MONITORING_INTEGRATIONS = 'manage_irrigation_monitoring_integrations';
    public const APPROVE_RESULTS = 'approve_results';
    public const MANAGE_AGRONOMIC_TASKS = 'manage_agronomic_tasks';

    protected $connection = 'main';
}
