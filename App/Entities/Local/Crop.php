<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Crop.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="su_crop_codes")
 */
class Crop
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="crop_code", type="string", length=6)
     */
    protected $cropCode;

    /**
     * @var string
     *
     * @ORM\Column(name="crop_name", type="string", length=100)
     */
    protected $cropName;

    /**
     * @var GddCollection
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Gdd<PERSON>")
     *
     * @ORM\JoinColumn(name="gdd_collection_id", referencedColumnName="id")
     */
    protected $gddCollection;

    /**
     * @var PlotCrop[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\PlotCrop", mappedBy="crop")
     */
    protected $plotCrops;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCropCode(): string
    {
        return $this->cropCode;
    }

    public function setCropCode(string $cropCode): Crop
    {
        $this->cropCode = $cropCode;

        return $this;
    }

    public function getCropName(): string
    {
        return $this->cropName;
    }

    public function setCropName(string $cropName): Crop
    {
        $this->cropName = $cropName;

        return $this;
    }

    /**
     * @return GddCollection
     */
    public function getGddCollection()
    {
        return $this->gddCollection;
    }

    public function setGddCollection(GddCollection $gddCollection): Crop
    {
        $this->gddCollection = $gddCollection;

        return $this;
    }

    /**
     * @return PlotCrop[]
     */
    public function getPlotCrops(): array
    {
        return $this->plotCrops;
    }

    /**
     * @param PlotCrop[] $plotCrops
     */
    public function setPlotCrops(array $plotCrops): Crop
    {
        $this->plotCrops = $plotCrops;

        return $this;
    }
}
