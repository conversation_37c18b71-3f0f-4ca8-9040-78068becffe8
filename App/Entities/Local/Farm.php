<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Farm.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="su_farms")
 */
class Farm
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    protected $name;

    /**
     * @var string
     *
     * @ORM\Column(name="comment", type="string", length=255)
     */
    protected $comment;

    /**
     * @var Organization
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Organization")
     *
     * @ORM\Jo<PERSON><PERSON><PERSON>(name="organization_id", referencedColumnName="id")
     */
    protected $organization;

    /**
     * @var FarmUser
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\FarmUser", mappedBy="farm")
     */
    protected $farmUsers;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="created", type="datetime")
     */
    protected $created;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): Farm
    {
        $this->name = $name;

        return $this;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function setComment(string $comment): Farm
    {
        $this->comment = $comment;

        return $this;
    }

    public function getOrganization(): Organization
    {
        return $this->organization;
    }

    public function setOrganization(Organization $organization): Farm
    {
        $this->organization = $organization;

        return $this;
    }

    public function getCreated(): DateTime
    {
        return $this->created;
    }

    public function setCreated(DateTime $created): Farm
    {
        $this->created = $created;

        return $this;
    }
}
