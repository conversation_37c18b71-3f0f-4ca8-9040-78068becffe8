<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use DateTime;
use Doctrine\ORM\EntityRepository;

class PlotRepository extends EntityRepository
{
    public function getPlotCrop(Plot $plot)
    {
        $qb = $this->createQueryBuilder('plot')
            ->join('plot.plotCrops', 'plotCrops')
            ->where('plot = :plot')
            ->setParameter('plot', $plot)
            ->andWhere('plotCrops.isPrimary = true')
            ->andWhere('plotCrops.fromDate <= :today')
            ->andWhere('plotCrops.toDate >= :today')
            ->setParameter('today', (new DateTime()));

        return $qb->getQuery()->getResult();
    }
}
