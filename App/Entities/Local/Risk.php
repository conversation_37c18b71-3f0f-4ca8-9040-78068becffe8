<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Risk.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="risks")
 */
class Risk
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Condition
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Condition")
     *
     * @ORM\JoinColumn(name="conditions_id", referencedColumnName="id")
     */
    protected $condition;

    /**
     * @var RiskGroup
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\RiskGroup", inversedBy="risks")
     *
     * @ORM\Jo<PERSON>Column(name="risk_groups_id", referencedColumnName="id")
     */
    protected $riskGroup;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCondition(): Condition
    {
        return $this->condition;
    }

    public function setCondition(Condition $condition): Risk
    {
        $this->condition = $condition;

        return $this;
    }

    public function getRiskGroup(): RiskGroup
    {
        return $this->riskGroup;
    }

    public function setRiskGroup(RiskGroup $riskGroup): Risk
    {
        $this->riskGroup = $riskGroup;

        return $this;
    }
}
