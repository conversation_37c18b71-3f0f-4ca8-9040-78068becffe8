<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\EntityRepository;
use Illuminate\Support\Facades\Auth;

class CalculatedRiskRepository extends EntityRepository
{
    public function getCalculations($options)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $qb = $this->createQueryBuilder('calculatedRisk');
        $qb
            ->join('calculatedRisk.plot', 'plot')
            ->join('plot.farm', 'farm')
            ->join('farm.farmUsers', 'farmUser')
            ->join('farmUser.user', 'user')
            ->where('calculatedRisk.date between :startDate and :endDate')->setParameters([
                'startDate' => $options['start_date'],
                'endDate' => $options['end_date'],
            ])
            ->andWhere('user.id = :userId')->setParameter('userId', $user->id)
            ->andWhere('farm.organization = user.lastChosenOrganization');

        if (!empty($options['plot_id'])) {
            $qb->andWhere('plot.gid = :plotId')
                ->setParameter('plotId', $options['plot_id']);
        }
        if (!empty($options['phenophase'])) {
            $qb->join('calculatedRisk.phenophase', 'phenophase')
                ->andWhere('phenophase.slug = :phenophase')
                ->setParameter('phenophase', $options['phenophase']);
        }
        if (!empty($options['pest_disease'])) {
            $qb->join('calculatedRisk.pestDisease', 'pestDisease')
                ->andWhere('pestDisease.slug = :pestDisease')
                ->setParameter('pestDisease', $options['pest_disease']);
        }

        return $qb->getQuery()->getResult();
    }
}
