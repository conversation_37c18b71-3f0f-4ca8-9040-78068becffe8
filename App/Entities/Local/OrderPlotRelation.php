<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class OrderPlotRelation.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="su_satellite_orders_plots_rel")
 */
class OrderPlotRelation
{
    /**
     * @TODO: add all fields
     */

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Order
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Order", inversedBy="orderPlotRelations")
     */
    protected $order;

    /**
     * @var Plot
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Plot")
     *
     * @OR<PERSON>\JoinColumn(name="plot_id", referencedColumnName="gid")
     */
    protected $plot;

    public function getId(): int
    {
        return $this->id;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): OrderPlotRelation
    {
        $this->order = $order;

        return $this;
    }

    public function getPlot(): Plot
    {
        return $this->plot;
    }

    public function setPlot(Plot $plot): OrderPlotRelation
    {
        $this->plot = $plot;

        return $this;
    }
}
