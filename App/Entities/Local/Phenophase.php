<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;

/**
 * Class Phenophase.
 *
 * @ORM\Entity(repositoryClass="App\Entities\Local\PhenophaseRepository")
 *
 * @ORM\Table(name="phenophases")
 */
class Phenophase
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="slug", type="string")
     */
    protected $slug;

    /**
     * @var float
     *
     * @ORM\Column(name="min_gdd", type="float")
     */
    protected $minGdd;

    /**
     * @var float
     *
     * @ORM\Column(name="max_gdd", type="float")
     */
    protected $maxGdd;

    /**
     * @var GddCollection
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\GddCollection")
     *
     * @ORM\JoinColumn(name="gdd_collection_id", referencedColumnName="id")
     */
    protected $gddCollection;

    /**
     * @var float
     *
     * @ORM\Column(name="sum_gdd", type="float")
     */
    protected $sumGdd;

    /**
     * @var int
     *
     * @ORM\Column(name="frontend_array_index", type="integer")
     */
    protected $frontendArrayIndex;

    /**
     * @var PestDisease[]
     *
     * @ORM\ManyToMany(targetEntity="App\Entities\Local\PestDisease", mappedBy="phenophases")
     */
    protected $pestsDiseases;

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): Phenophase
    {
        $this->slug = $slug;

        return $this;
    }

    public function getMinGdd(): float
    {
        return $this->minGdd;
    }

    public function setMinGdd(float $minGdd): Phenophase
    {
        $this->minGdd = $minGdd;

        return $this;
    }

    public function getMaxGdd(): float
    {
        return $this->maxGdd;
    }

    public function setMaxGdd(float $maxGdd): Phenophase
    {
        $this->maxGdd = $maxGdd;

        return $this;
    }

    public function getGddCollection(): GddCollection
    {
        return $this->gddCollection;
    }

    public function setGddCollection(GddCollection $gddCollection): Phenophase
    {
        $this->gddCollection = $gddCollection;

        return $this;
    }

    public function getSumGdd(): float
    {
        return $this->sumGdd;
    }

    public function setSumGdd(float $sumGdd): Phenophase
    {
        $this->sumGdd = $sumGdd;

        return $this;
    }

    public function getFrontendArrayIndex(): int
    {
        return $this->frontendArrayIndex;
    }

    public function setFrontendArrayIndex(int $frontendArrayIndex): Phenophase
    {
        $this->frontendArrayIndex = $frontendArrayIndex;

        return $this;
    }

    /**
     * @return PestDisease[]
     */
    public function getPestsDiseases(): PersistentCollection
    {
        return $this->pestsDiseases;
    }

    /**
     * @param PestDisease[] $pestsDiseases
     */
    public function setPestsDiseases(array $pestsDiseases): Phenophase
    {
        $this->pestsDiseases = $pestsDiseases;

        return $this;
    }
}
