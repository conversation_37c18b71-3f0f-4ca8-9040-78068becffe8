<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class PestDisease.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="pests_diseases")
 *
 * @ORM\InheritanceType("JOINED")
 *
 * @ORM\DiscriminatorColumn(name="type", type="string")
 *
 * @ORM\DiscriminatorMap({"pest" = "Pest", "disease" = "Disease"})
 */
abstract class PestDisease
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="slug", type="string", length=255)
     */
    protected $slug;

    /**
     * @var Phenophase[]
     *
     * @ORM\ManyToMany(targetEntity="App\Entities\Local\Phenophase", inversedBy="pestsDiseases")
     *
     * @ORM\JoinTable(name="pests_diseases_phenophases",
     *     joinColumns={@ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")},
     * inverseJoinColumns={@ORM\JoinColumn(name="phenophases_id", referencedColumnName="id")})
     */
    protected $phenophases;

    /**
     * @var RiskGroup[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\RiskGroup", mappedBy="pestDisease")
     */
    protected $riskGroups;

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): PestDisease
    {
        $this->slug = $slug;

        return $this;
    }

    /**
     * @return Phenophase[]
     */
    public function getPhenophases(): array
    {
        return $this->phenophases;
    }

    /**
     * @param Phenophase[] $phenophases
     */
    public function setPhenophases(array $phenophases): PestDisease
    {
        $this->phenophases = $phenophases;

        return $this;
    }

    /**
     * @return RiskGroup[]
     */
    public function getRiskGroups()
    {
        return $this->riskGroups;
    }

    /**
     * @param RiskGroup[] $riskGroups
     */
    public function setRiskGroups(array $riskGroups): PestDisease
    {
        $this->riskGroups = $riskGroups;

        return $this;
    }
}
