<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class GddCollection.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="gdd_collections")
 */
class GddCollection
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="slug", type="string")
     */
    protected $slug;

    /**
     * @var float
     *
     * @ORM\Column(name="t_base", type="float")
     */
    protected $tBase;

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): GddCollection
    {
        $this->slug = $slug;

        return $this;
    }

    public function getTBase(): float
    {
        return $this->tBase;
    }

    public function setTBase(float $tBase): GddCollection
    {
        $this->tBase = $tBase;

        return $this;
    }
}
