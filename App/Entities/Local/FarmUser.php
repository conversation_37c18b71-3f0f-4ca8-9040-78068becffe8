<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class FarmUser.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="su_farms_users")
 */
class FarmUser
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Farm
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Farm")
     *
     * @ORM\JoinColumn(name="farm_id", referencedColumnName="id")
     */
    protected $farm;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\User")
     *
     * @ORM\Jo<PERSON><PERSON>(name="user_id", referencedColumnName="id")
     */
    protected $user;

    /**
     * @var bool
     *
     * @ORM\Column(name="is_visible", type="boolean")
     */
    protected $isVisible;

    public function getId(): int
    {
        return $this->id;
    }

    public function getFarm(): Farm
    {
        return $this->farm;
    }

    public function setFarm(Farm $farm): FarmUser
    {
        $this->farm = $farm;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): FarmUser
    {
        $this->user = $user;

        return $this;
    }

    public function isVisible(): bool
    {
        return $this->isVisible;
    }

    public function setIsVisible(bool $isVisible): FarmUser
    {
        $this->isVisible = $isVisible;

        return $this;
    }
}
