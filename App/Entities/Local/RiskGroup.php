<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class RiskGroup.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="risk_groups")
 */
class RiskGroup
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string")
     */
    protected $name;

    /**
     * @var int
     *
     * @ORM\Column(name="risk_level", type="integer")
     */
    protected $riskLevel;

    /**
     * @var PestDisease[]
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\PestDisease", inversedBy="riskGroups")
     *
     * @ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")
     */
    protected $pestDisease;

    /**
     * @var Risk[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\Risk", mappedBy="riskGroup")
     */
    protected $risks;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): RiskGroup
    {
        $this->name = $name;

        return $this;
    }

    public function getRiskLevel(): int
    {
        return $this->riskLevel;
    }

    public function setRiskLevel(int $riskLevel): RiskGroup
    {
        $this->riskLevel = $riskLevel;

        return $this;
    }

    public function getPestDisease(): PestDiseasePhenophase
    {
        return $this->pestDisease;
    }

    public function setPestDisease(PestDiseasePhenophase $pestDisease): RiskGroup
    {
        $this->pestDisease = $pestDisease;

        return $this;
    }

    /**
     * @return Risk[]
     */
    public function getRisks()
    {
        return $this->risks;
    }

    /**
     * @param Risk[] $risks
     */
    public function setRisks(array $risks): RiskGroup
    {
        $this->risks = $risks;

        return $this;
    }
}
