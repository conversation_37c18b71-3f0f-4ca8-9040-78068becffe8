<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Order.
 *
 * @ORM\Entity(repositoryClass="App\Entities\Local\OrderRepository")
 *
 * @ORM\Table(name="su_satellite_orders")
 */
class Order
{
    /**
     * @TODO: add all fields
     */

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string")
     */
    protected $status;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string")
     */
    protected $type;

    /**
     * @var DateTime
     *
     * @ORM\Col<PERSON>(name="from_date", type="date")
     */
    protected $fromDate;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="to_date", type="date")
     */
    protected $toDate;

    /**
     * @var Organization
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Organization", inversedBy="orders")
     */
    protected $organization;

    /**
     * @var OrderPlotRelation[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\OrderPlotRelation", mappedBy="order")
     */
    protected $orderPlotRelations;

    public function getId(): int
    {
        return $this->id;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): Order
    {
        $this->status = $status;

        return $this;
    }

    public function getFromDate(): DateTime
    {
        return $this->fromDate;
    }

    public function setFromDate(DateTime $fromDate): Order
    {
        $this->fromDate = $fromDate;

        return $this;
    }

    public function getToDate(): DateTime
    {
        return $this->toDate;
    }

    public function setToDate(DateTime $toDate): Order
    {
        $this->toDate = $toDate;

        return $this;
    }

    public function getOrganization(): Organization
    {
        return $this->organization;
    }

    public function setOrganization(Organization $organization): Order
    {
        $this->organization = $organization;

        return $this;
    }

    /**
     * @return OrderPlotRelation|OrderPlotRelation[]
     */
    public function getOrderPlotRelations()
    {
        return $this->orderPlotRelations;
    }

    /**
     * @param OrderPlotRelation[] $orderPlotRelations
     */
    public function setOrderPlotRelations(array $orderPlotRelations): Order
    {
        $this->orderPlotRelations = $orderPlotRelations;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): Order
    {
        $this->type = $type;

        return $this;
    }
}
