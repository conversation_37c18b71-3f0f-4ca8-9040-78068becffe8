<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class PestDiseasePhenophase.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="pests_diseases_phenophases")
 */
class PestDiseasePhenophase
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var PestDisease
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\PestDisease")
     *
     * @ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")
     */
    protected $pestDisease;

    /**
     * @var Phenophase
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Phenophase")
     *
     * @ORM\JoinColumn(name="phenophases_id", referencedColumnName="id")
     */
    protected $phenophase;

    public function getId(): int
    {
        return $this->id;
    }

    public function getPestDisease(): PestDisease
    {
        return $this->pestDisease;
    }

    public function setPestDisease(PestDisease $pestDisease): PestDiseasePhenophase
    {
        $this->pestDisease = $pestDisease;

        return $this;
    }

    public function getPhenophase(): Phenophase
    {
        return $this->phenophase;
    }

    public function setPhenophase(Phenophase $phenophase): PestDiseasePhenophase
    {
        $this->phenophase = $phenophase;

        return $this;
    }
}
