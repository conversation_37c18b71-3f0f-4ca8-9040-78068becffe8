<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class CalculatedRisk.
 *
 * @ORM\Entity(repositoryClass="App\Entities\Local\CalculatedRiskRepository")
 *
 * @ORM\Table(name="calculated_risks")
 */
class CalculatedRisk
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var PestDisease
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\PestDisease")
     *
     * @ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")
     */
    protected $pestDisease;

    /**
     * @var Plot
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Plot")
     *
     * @ORM\JoinColumn(name="plot_id", referencedColumnName="gid")
     */
    protected $plot;

    /**
     * @var Phenophase
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Phenophase")
     *
     * @ORM\JoinColumn(name="phenophases_id", referencedColumnName="id")
     */
    protected $phenophase;

    /**
     * @var float
     *
     * @ORM\Column(name="risk_level", type="float")
     */
    protected $riskLevel;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="date", type="date")
     */
    protected $date;

    public function getId(): int
    {
        return $this->id;
    }

    public function getPestDisease(): PestDisease
    {
        return $this->pestDisease;
    }

    public function setPestDisease(PestDisease $pestDisease): CalculatedRisk
    {
        $this->pestDisease = $pestDisease;

        return $this;
    }

    public function getPlot(): Plot
    {
        return $this->plot;
    }

    public function setPlot(Plot $plot): CalculatedRisk
    {
        $this->plot = $plot;

        return $this;
    }

    public function getPhenophase(): Phenophase
    {
        return $this->phenophase;
    }

    public function setPhenophase(Phenophase $phenophase): CalculatedRisk
    {
        $this->phenophase = $phenophase;

        return $this;
    }

    public function getRiskLevel(): float
    {
        return $this->riskLevel;
    }

    public function setRiskLevel(float $riskLevel): CalculatedRisk
    {
        $this->riskLevel = $riskLevel;

        return $this;
    }

    public function getDate(): DateTime
    {
        return $this->date;
    }

    public function setDate(DateTime $date): CalculatedRisk
    {
        $this->date = $date;

        return $this;
    }
}
