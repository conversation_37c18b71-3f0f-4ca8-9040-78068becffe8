<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Organization.
 *
 * @ORM\Entity
 *
 * @ORM\Table(name="su_organizations")
 */
class Organization
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     *
     * @ORM\Id;
     *
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    protected $name;

    /**
     * @var string
     *
     * @ORM\Column(name="iso_alpha_2_code", type="string", length=2)
     */
    protected $isoAlpha2Code;

    /**
     * @var string
     *
     * @ORM\Column(name="address", type="string", length=255)
     */
    protected $address;

    /**
     * @var string
     *
     * @ORM\Column(name="email", type="string", length=255)
     */
    protected $email;

    /**
     * @var string
     *
     * @ORM\Column(name="phone", type="string", length=127)
     */
    protected $phone;

    /**
     * @var bool
     *
     * @ORM\Column(name="active", type="boolean")
     */
    protected $isActive;

    /**
     * @var User[]
     *
     * @ORM\ManyToMany(targetEntity="App\Entities\Local\User", inversedBy="organizations")
     *
     * @ORM\JoinTable(name="su_organizations_users")
     */
    protected $users;

    /**
     * @var Order[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\Order", mappedBy="organization")
     */
    protected $orders;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): Organization
    {
        $this->name = $name;

        return $this;
    }

    public function getIsoAlpha2Code(): string
    {
        return $this->isoAlpha2Code;
    }

    public function setIsoAlpha2Code(string $isoAlpha2Code): Organization
    {
        $this->isoAlpha2Code = $isoAlpha2Code;

        return $this;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): Organization
    {
        $this->address = $address;

        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): Organization
    {
        $this->email = $email;

        return $this;
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function setPhone(string $phone): Organization
    {
        $this->phone = $phone;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): Organization
    {
        $this->isActive = $isActive;

        return $this;
    }

    /**
     * @return User[]
     */
    public function getUsers(): array
    {
        return $this->users;
    }

    /**
     * @param User[] $users
     */
    public function setUsers(array $users): Organization
    {
        $this->users = $users;

        return $this;
    }
}
