import argparse
import os
from pprint import pprint
from staticmap import StaticMap, CircleMarker, Line, Polygon, IconMarker
from staticmap.staticmap import _lon_to_x, _lat_to_y, _simplify
import psycopg2
import json
from PIL import Image, ImageDraw, ImageFont


class GSStaticMap(StaticMap):
    def _draw_features(self, image):
        """
        :type image: Image.Image
        """
        # Pillow does not support anti aliasing for lines and circles
        # There is a trick to draw them on an image that is twice the size and resize it at the end before it gets merged with  the base layer

        image_lines = Image.new("RGBA", (self.width * 2, self.height * 2), (255, 0, 0, 0))
        draw = ImageDraw.Draw(image_lines)

        for polygon in self.polygons:
            points = [
                (
                    self._x_to_px(_lon_to_x(coord[0], self.zoom)) * 2,
                    self._y_to_px(_lat_to_y(coord[1], self.zoom)) * 2,
                )
                for coord in polygon.coords
            ]
            if polygon.simplify:
                points = _simplify(points)

            if polygon.fill_color or polygon.outline_color:
                draw.polygon(points, fill=polygon.fill_color, outline=polygon.outline_color)

        for line in self.lines:
            points = [
                (
                    self._x_to_px(_lon_to_x(coord[0], self.zoom)) * 2,
                    self._y_to_px(_lat_to_y(coord[1], self.zoom)) * 2,
                )
                for coord in line.coords
            ]

            if line.simplify:
                points = _simplify(points)

            for point in points:
                # draw extra points to make the connection between lines look nice
                draw.ellipse(
                    (
                        point[0] - line.width + 1,
                        point[1] - line.width + 1,
                        point[0] + line.width - 1,
                        point[1] + line.width - 1,
                    ),
                    fill=line.color,
                )

            draw.line(points, fill=line.color, width=line.width * 2)

        for circle in filter(lambda m: isinstance(m, CircleMarker), self.markers):
            point = [
                self._x_to_px(_lon_to_x(circle.coord[0], self.zoom)) * 2,
                self._y_to_px(_lat_to_y(circle.coord[1], self.zoom)) * 2,
            ]
            draw.ellipse(
                (
                    point[0] - circle.width,
                    point[1] - circle.width,
                    point[0] + circle.width,
                    point[1] + circle.width,
                ),
                fill=circle.color,
            )

        image_lines = image_lines.resize((self.width, self.height), Image.Resampling.LANCZOS)

        # merge lines with base image
        image.paste(image_lines, (0, 0), image_lines)

        # add icon marker
        for icon in filter(lambda m: isinstance(m, IconMarker), self.markers):
            position = (
                self._x_to_px(_lon_to_x(icon.coord[0], self.zoom)) - icon.offset[0],
                self._y_to_px(_lat_to_y(icon.coord[1], self.zoom)) - icon.offset[1],
            )
            image.paste(icon.img, position, icon.img)


def main():

    arg_parser = argparse.ArgumentParser(description="Creating map images with lines, markers and polygons.")
    arg_parser.add_argument("-order_id", "--order_id", help="Order id.", required=True, type=int)
    arg_parser.add_argument("-plot_id", "--plot_id", help="Plot id.", required=True, type=int)
    arg_parser.add_argument("-base_path", "--base_path", help="base path", required=True, type=str)
    arg_parser.add_argument(
        "-dir_email_reports",
        "--dir_email_reports",
        help="dir email reports",
        required=True,
        type=str,
    )
    arg_parser.add_argument("-out_image", "--out_image", help="out image", required=True, type=str)

    arg_parser.add_argument("-d", "--database", help="Database name", metavar="database_name", type=str)
    arg_parser.add_argument(
        "-u",
        "--user",
        help="Database user",
        metavar="username",
        default="postgres",
        nargs="?",
        type=str,
    )
    arg_parser.add_argument(
        "-H",
        "--host",
        help="Database host",
        metavar="address",
        nargs="?",
        default="127.0.0.1",
        type=str,
    )
    arg_parser.add_argument(
        "-p",
        "--port",
        help="Database port",
        metavar="port_number",
        nargs="?",
        default="5432",
        type=str,
    )
    arg_parser.add_argument(
        "-P",
        "--password",
        help="User's password",
        metavar="usr_password",
        nargs="?",
        default="",
        type=str,
    )

    args = arg_parser.parse_args()
    order_id = args.order_id
    plot_id = args.plot_id
    base_path = args.base_path
    dir_email_reports = args.dir_email_reports
    out_image = args.out_image

    database = args.database
    user = args.user
    host = args.host
    port = args.port
    password = args.password

    try:
        conn = psycopg2.connect(dbname=database, user=user, password=password, host=host, port=port)
    except:
        print("Cannot connect to the database")
        return

    m = GSStaticMap(
        320,
        240,
        url_template="https://tile.openstreetmap.org/{z}/{x}/{y}.png",
        headers={
            "User-Agent": "GeoSCAN",
        },
    )
    cur = conn.cursor()

    # plot
    cur.execute(
        """
			SELECT sp.gid, sp.area, sp.name, st_asgeojson(st_transform(sp.geom, 4326)) AS geom, st_geometrytype(sp.geom) AS geom_type
            FROM su_satellite_plots sp
            WHERE sp.gid = %s
		""",
        (plot_id,),
    )
    data = cur.fetchall()

    for row in data:
        gid, area, name, geom, geom_type = row
        geomObj = json.loads(geom)

        if geom_type == "ST_MultiPolygon":
            for poly in geomObj["coordinates"]:
                polygon = Polygon(poly[0], None, "#000000")
                m.add_polygon(polygon)
        elif geom_type == "ST_Polygon":
            polygon = Polygon(geomObj["coordinates"][0], None, "#000000")
            m.add_polygon(polygon)
        else:
            raise Exception("Unknown geometry type")

    # grid
    cur.execute(
        """
			SELECT sg.gid, sg.sopr_id, sg.sample_id, sg.color, st_asgeojson(st_transform(sg.geom, 4326)) AS geom, st_geometrytype(sg.geom) as geom_type
			FROM su_satellite_soil_grid sg
			INNER JOIN su_satellite_orders_plots_rel sopr ON sopr.id=sg.sopr_id
			WHERE sopr.plot_id = '%s' AND sopr.order_id = '%s'
		""",
        (plot_id, order_id),
    )
    data = cur.fetchall()

    for row in data:
        gid, sopr_id, sample_id, color, geom, geom_type = row
        geomObj = json.loads(geom)
        if geom_type == "ST_MultiPolygon":
            for poly in geomObj["coordinates"]:
                polygon = Polygon(poly[0], None, "#000000")
                m.add_polygon(polygon)
        elif geom_type == "ST_Polygon":
            polygon = Polygon(geomObj["coordinates"][0], None, "#000000")
            m.add_polygon(polygon)
        else:
            raise Exception("Unknown geometry type")

    # arcs(tracks)
    cur.execute(
        """
            SELECT sp.gid, sp.sopr_id, sp.sample_id, st_asgeojson(ST_Force2D(st_transform(sp.track, 4326))) AS geom
            FROM su_satellite_soil_points sp
            INNER JOIN su_satellite_orders_plots_rel sopr ON sopr.id=sp.sopr_id
            WHERE sopr.plot_id = '%s' AND sopr.order_id = '%s' AND sp.track NOTNULL
		""",
        (plot_id, order_id),
    )
    data = cur.fetchall()

    for row in data:
        gid, sopr_id, sample_id, geom = row
        geomObj = json.loads(geom)
        line = Line(geomObj["coordinates"], "#CC0000", 2)
        m.add_line(line)

    # points(numbers - sample_id)
    cur.execute(
        """
			SELECT sp.gid, sp.sopr_id, sp.sample_id, st_asgeojson(st_transform(sp.geom, 4326)) AS geom
            FROM su_satellite_soil_points sp
            INNER JOIN su_satellite_orders_plots_rel sopr ON sopr.id=sp.sopr_id
            WHERE sopr.plot_id = '%s' AND sopr.order_id = '%s'
		""",
        (plot_id, order_id),
    )
    data = cur.fetchall()

    for row in data:
        gid, sopr_id, sample_id, geom = row
        geomObj = json.loads(geom)
        imgPath = dir_email_reports + "/sample_numbers/" + str(sample_id) + ".png"
        exists = os.path.isfile(imgPath)
        if not exists:
            generateImageIcon(sample_id, base_path, dir_email_reports)
        icon = IconMarker(geomObj["coordinates"], imgPath, 2, 10)
        m.add_marker(icon)

    # save image
    outDir = dir_email_reports + "/" + str(order_id)
    if not os.path.exists(outDir):
        os.makedirs(outDir)

    image = m.render()
    image.save(out_image)


def generateImageIcon(sample_id, base_path, dir_email_reports):
    if not os.path.exists(dir_email_reports + "/sample_numbers"):
        os.makedirs(dir_email_reports + "/sample_numbers")

    txt = Image.new("RGBA", (36, 14))
    fnt = ImageFont.truetype(base_path + "/public/fonts/arial.ttf", 14)
    d = ImageDraw.Draw(txt)
    d.text((0, 0), str(sample_id), font=fnt, fill=(0, 0, 0))
    txt.save(dir_email_reports + "/sample_numbers/" + str(sample_id) + ".png", "PNG")


if __name__ == "__main__":
    main()
