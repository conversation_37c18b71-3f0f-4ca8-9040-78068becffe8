from sentinelhub import AwsProductRequest, AwsTileRequest, AwsTile, AwsProduct
import os
import shutil
import argparse

def get_sentinel_id(product_id):
	return product_id.split('_')[5]

def download_tile(product_id, bands, metafiles, dst_path):
	request = AwsProductRequest(product_id=product_id, data_folder=dst_path, bands=bands, metafiles=metafiles)
	request.save_data()
	print('Done!')

def change_folder_structure(data_folder, product_id):
	os.chdir(data_folder + '/' + product_id)
	product_folders = os.listdir(data_folder + '/' + product_id)

	for folder in product_folders:
		files = os.listdir(folder)

		for f in files:
			shutil.move(folder + '/' + f, data_folder + '/' + f)
		shutil.move(data_folder + '/qi/MSK_CLOUDS_B00.gml', data_folder + '/MSK_CLOUDS_B00.gml')
	os.chdir(data_folder)

	shutil.rmtree(product_id)
	shutil.rmtree('qi')


def product_exists(data_folder, product_id):
	for path, subdirs, files in os.walk(data_folder):
		if product_id in subdirs:
			return True
		continue
	return False


def main():
	bands = ['B02','B03', 'B04', 'B05', 'B08', 'B11']
	metafiles = ['qi/MSK_CLOUDS_B00']

	arg_parser = argparse.ArgumentParser(description='Downloads files from sentinel using AWS.')
	arg_parser.add_argument('-d', '--dst', help='Directory, where downloaded files are saved. Use full path and destination folder which does not exists.', required=True, type=str)
	arg_parser.add_argument('-p', '--pid', help='Product id.', required=True, type=str)
	arg_parser.add_argument('-b', '--bands', help='Bands to download.Use double quotes!(Example: "B03, B11")', type=str)

	args = arg_parser.parse_args()
	product_id = args.pid
	data_folder = args.dst

	if args.bands is not None:
		bands = args.bands.split(', ')


	if not os.path.exists(data_folder):
		print("Product with id '%s' does not exists => downloading..." % product_id)
		download_tile(product_id, bands, metafiles, data_folder)
		change_folder_structure(data_folder, product_id)

	if os.path.exists(data_folder):
		print("Product with id '%s' exists!" % product_id)


if __name__ == '__main__':
	main()
