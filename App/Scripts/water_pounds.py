from osgeo import gdal, ogr, osr
import numpy as np
import argparse
import os
import psycopg2
import shutil
import tempfile

gdal.UseExceptions()
np.seterr(divide='ignore', invalid='ignore')


def get_output_resolution(green_ds, swir_ds):
    print("Resolutions:")
    print("GREEN [x1: %s, y1: %s], SWIR [x2: %s, y2: %s]" % (
        green_ds.RasterXSize, green_ds.RasterYSize, swir_ds.RasterXSize, swir_ds.RasterYSize))

    if green_ds.RasterXSize < swir_ds.RasterXSize and green_ds.RasterYSize < swir_ds.RasterYSize:
        print("Use GREEN [%s %s]" % (green_ds.RasterXSize, green_ds.RasterYSize))
        return green_ds.RasterXSize, green_ds.RasterYSize

    print("Use SWIR [%s %s]" % (swir_ds.RasterXSize, swir_ds.RasterYSize))
    return swir_ds.RasterXSize, swir_ds.RasterYSize


def ndwi(green, swir):
    return ((green.astype(float) - swir.astype(float)) / (green.astype(float) + swir.astype(float)))


def export_to_database(vector_path, date, sentinel_tile_id, database, table='ndwi', host='127.0.0.1', port='5432', user='postgres', password=''):
    shp_driver = ogr.GetDriverByName('ESRI Shapefile')
    poly_ds = shp_driver.Open(vector_path)
    poly_layer = poly_ds.GetLayer()
    sp_ref = poly_layer.GetSpatialRef()
    epsg = sp_ref.GetAttrValue('AUTHORITY', 1)
    print(poly_ds)

    try:
        conn = psycopg2.connect(
            dbname=database, user=user, password=password, host=host, port=port)
    except:
        print("Unable to connect to the database, you passed:")
        print("-d %s -u %s -P %s -H %s -p %s -t %s" %
              (database, user, password, host, port, table))
        return
    cur = conn.cursor()

    table_exists_sql = "SELECT EXISTS(SELECT * FROM information_schema.tables WHERE table_name='" + table + "');"
    cur.execute(table_exists_sql)
    table_exists = bool(cur.fetchone()[0])

    if not table_exists:
        print("Table '%s' does not exists => creating.." % table)
        create_table_sql = "CREATE TABLE " + table + \
            "(gid SERIAL PRIMARY KEY, date DATE, sentinel_tile_id VARCHAR, geom GEOMETRY(Polygon, 4326));"
        create_index_sql = "CREATE INDEX ON " + table + " USING btree (date);"
        cur.execute(create_table_sql)
        cur.execute(create_index_sql)

    delete_duplicated_rows_sql = "DELETE FROM " + table + \
        " WHERE sentinel_tile_id='" + sentinel_tile_id + "' AND date='" + date + "';"
    cur.execute(delete_duplicated_rows_sql)

    print("Inserting rows...")

    insert_rows_sql = "INSERT INTO " + table + \
        "(date, sentinel_tile_id, geom) " + "VALUES"
    for poly_feature in poly_layer:
        geom = poly_feature.GetGeometryRef()
        wkt = geom.ExportToWkt()
        insert_rows_sql += "('" + str(date) + "', '" + str(sentinel_tile_id) + \
            "', ST_Transform(ST_GeometryFromText('" + wkt + \
            "', " + str(epsg) + "), 4326)),"

    insert_rows_sql = insert_rows_sql[:-1] + ";"
    try:
        cur.execute(insert_rows_sql)
    except psycopg2.Error as e:
        print("Cannot execute sql:\n%s" % insert_rows_sql)
        print(e)

    conn.commit()
    poly_ds = None
    print("Done!")


def generate_water_pounds(work_dir, water_index, green_path, swir_path, clouds_path, tile_date, sentinel_tile_id):
    ndwi_path = work_dir + "/ndwi.tif"
    rasterized_clouds_path = work_dir + "/rasterized_clouds.tif"
    final_tif_path = work_dir + "/final.tif"
    final_vector_path = work_dir + "/poly.shp"

    green_ds = gdal.Open(green_path, gdal.GA_ReadOnly)

    swir_ds = gdal.Open(swir_path, gdal.GA_ReadOnly)
    swir_band = swir_ds.GetRasterBand(1)
    swir_arr = swir_band.ReadAsArray()
    swir_prj = swir_ds.GetProjection()
    swir_geotransform = swir_ds.GetGeoTransform()

    ###Convert files to same resolution###
    output_width, output_height = get_output_resolution(green_ds, swir_ds)
    green_path_converted = work_dir + "/B03-1.jp2"
    gdal.Warp(green_path_converted, green_path,  width=output_width,
              height=output_height, copyMetadata=True)
    green_ds = None
    ###Convert files to same resolution - end###
    green_ds = gdal.Open(green_path_converted, gdal.GA_ReadOnly)
    green_band = green_ds.GetRasterBand(1)
    green_arr = green_band.ReadAsArray()

    ndwi_arr = ndwi(green_arr, swir_arr)
    gtiff_driver = gdal.GetDriverByName('GTiff')
    ndwi_ds = gtiff_driver.Create(
        ndwi_path, output_width, output_height, 1, gdal.GDT_UInt16)
    ndwi_ds.SetProjection(swir_prj)
    ndwi_ds.SetGeoTransform(swir_geotransform)
    ndwi_band = ndwi_ds.GetRasterBand(1)

    ndwi_arr[np.isnan(ndwi_arr)] = -999
    ndwi_arr[ndwi_arr.astype(float) <= 0.0] = 0.0
    ndwi_arr[ndwi_arr.astype(float) > water_index] = 1.0
    ndwi_band.WriteArray(ndwi_arr.astype(int))
    ndwi_ds.FlushCache()

    ndwi_band_arr = ndwi_band.ReadAsArray()
    ndwi_sp_ref = osr.SpatialReference(wkt=swir_prj)
    epsg = ndwi_sp_ref.GetAttrValue('AUTHORITY', 1)
    print("EPSG: %s" % epsg)

    ndwi_x_size = ndwi_ds.RasterXSize
    ndwi_y_size = ndwi_ds.RasterYSize
    ndwi_ds = None

    rasterized_clouds_ds = gtiff_driver.Create(
        rasterized_clouds_path, ndwi_x_size, ndwi_y_size, 1, gdal.GDT_UInt16)
    if rasterized_clouds_ds is None:
        print("Cannot create file %s." % rasterized_clouds_path)
        exit()
    rasterized_clouds_ds.SetProjection(swir_prj)
    rasterized_clouds_ds.SetGeoTransform(swir_geotransform)

    clouds_ds = ogr.Open(clouds_path)
    if clouds_ds is None:
        print("Cannot open file %s." % clouds_path)
        exit()

    clouds_layer = clouds_ds.GetLayer()
    if clouds_layer is None:
        print("Clouds layer not found")
        exit()

    gdal.RasterizeLayer(rasterized_clouds_ds, [
                        1], clouds_layer, burn_values=[1])
    rasterized_clouds_band = rasterized_clouds_ds.GetRasterBand(1)
    rasterized_clouds_band_arr = rasterized_clouds_band.ReadAsArray()
    clouds_ds = None
    rasterized_clouds_ds = None

    final_tif_ds = gtiff_driver.Create(
        final_tif_path, ndwi_x_size, ndwi_y_size, 1, gdal.GDT_UInt16)
    final_tif_ds.SetGeoTransform(swir_geotransform)
    final_tif_ds.SetProjection(swir_prj)
    final_tif_band = final_tif_ds.GetRasterBand(1)
    final_tif_band.SetNoDataValue(0)

    ndwi_band_arr[rasterized_clouds_band_arr != 0] = float(0)

    final_tif_band.WriteArray(ndwi_band_arr)
    final_tif_ds.FlushCache()

    print("Polygonizing...")
    poly_layer_name = "poly"
    field_name = "water"
        
    shp_driver = ogr.GetDriverByName("ESRI Shapefile")
    final_vector_ds = shp_driver.CreateDataSource(final_vector_path)
    final_vector_srs = final_tif_ds.GetSpatialRef()
    final_vector_layer = final_vector_ds.CreateLayer(poly_layer_name, geom_type=ogr.wkbPolygon, srs=final_vector_srs)

    fd = ogr.FieldDefn(field_name, ogr.OFTInteger)
    final_vector_layer.CreateField(fd)
    final_vector_layer_field = 0

    result = gdal.Polygonize(final_tif_band, None, final_vector_layer, final_vector_layer_field)

    for feature in final_vector_layer:

        # Remove the features that do not represent water
        if (feature.GetField("water") == 0):
            final_vector_layer.DeleteFeature(feature.GetFID())

        # Split the multipolygon features to polygon features
        geom = feature.GetGeometryRef()
        if (geom.GetGeometryName() == 'MULTIPOLYGON'):
            for i in range(0, geom.GetGeometryCount()):
                poly_geom = geom.GetGeometryRef(i)

                poly_feature_defn = final_vector_layer.GetLayerDefn()
                poly_feature = ogr.Feature(poly_feature_defn)
                poly_feature.SetGeometry(poly_geom)
                poly_feature.SetField("water", feature.GetField("water"))
                final_vector_layer.CreateFeature(poly_feature)
                poly_feature = None

            final_vector_layer.DeleteFeature(feature.GetFID())

    final_vector_ds.FlushCache()
    final_tif_ds = None
    final_vector_ds = None

    print("Done")
    return final_vector_path


if __name__ == "__main__":
    arg_parser = argparse.ArgumentParser(
        description="This progrem returns NDWI vector file.")
    arg_parser.add_argument("B03", help="Band 03 raster file path", type=str)
    arg_parser.add_argument("B11", help="Band 11 raster file path", type=str)
    arg_parser.add_argument("CLOUDS", help="Clouds vector file path", type=str)
    arg_parser.add_argument("DATE", help="Tile date", type=str)
    arg_parser.add_argument("SENTINEL_TILE_ID", help="", type=str)
    arg_parser.add_argument("-w", "--water_index", help="Water index.",
                            metavar="water_index", default=0.15, type=float)

    arg_parser.add_argument(
        "-d", "--database", help="Database name", metavar="database_name", type=str)
    arg_parser.add_argument("-u", "--user", help="Database user",
                            metavar="username", default="postgres", nargs="?", type=str)
    arg_parser.add_argument("-H", "--host", help="Database host",
                            metavar="address", nargs="?", default="127.0.0.1", type=str)
    arg_parser.add_argument("-p", "--port", help="Database port",
                            metavar="port_number", nargs="?", default="5432", type=str)
    arg_parser.add_argument("-t", "--table", help="Database output table name",
                            metavar="table_name", nargs="?", default="su_satellite_water_pounds", type=str)
    arg_parser.add_argument("-P", "--password", help="User's password",
                            metavar="usr_password", nargs="?", default="", type=str)

    args = arg_parser.parse_args()

    # Parse main arguments
    water_index = args.water_index
    green_path = args.B03
    swir_path = args.B11
    clouds_path = args.CLOUDS
    tile_date = args.DATE
    sentinel_tile_id = args.SENTINEL_TILE_ID

    tmp_path = tempfile.mkdtemp(prefix='water_pounds_')
    try:
        final_vector_path = generate_water_pounds(tmp_path, water_index, green_path,
                                                  swir_path, clouds_path, tile_date, sentinel_tile_id)

        # Parse database arguments and export
        if args.database is not None:
            db_name = args.database
            user = args.user
            host = args.host
            port = args.port
            table = args.table
            password = args.password

            export_to_database(final_vector_path, tile_date, sentinel_tile_id,
                               db_name, table, host, port, user, password)

    except Exception:
        raise
    finally:
        shutil.rmtree(tmp_path)
