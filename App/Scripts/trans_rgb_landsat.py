from osgeo import gdal
import sys
import os
import argparse

gdal_bin_path = ''

def trans_rgb(input, output):
    src_ds = gdal.Open( input )
    a = []
    b = []

    for band in range( src_ds.RasterCount ):
        band += 1
        if band > 3:
            continue

        srcband = src_ds.GetRasterBand(band)
        if srcband is None:
            continue

        srcband.SetNoDataValue(0);
        stats = srcband.ComputeStatistics(0)
        if stats is None:
            continue
        print("[ STATS %.1f ] =  Minimum=%.3f, Maximum=%.3f, Mean=%.3f, StdDev=%.3f" % (band, stats[0], stats[1], stats[2], stats[3]))

        a.append(str(max(0, stats[2] - 2*stats[3])))
        b.append(str(min(65535, stats[2] + 2*stats[3])))

    print(a)
    print(b)
    os.system(gdal_bin_path + "gdal_translate -of VRT -ot Byte -b 1 -b 2 -b 3 -b mask -a_nodata none -scale_1 "+a[0]+" "+b[0]+" 0 255 -scale_2 "+a[1]+" "+b[1]+" 0 255 -scale_3 "+a[2]+" "+b[2]+" 0 255 " + input + " " + output)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Translates landsat image to RGB VRT file.')
    parser.add_argument('input', metavar='input', type=str, help='The source file.')
    parser.add_argument('output', metavar='output', type=str, help='The destination file.')

    parser.add_argument('--gdal_bin_path', metavar='', dest='gdal_bin_path', type=str, default='', help='The path to gdal bin folder.')

    args = parser.parse_args()

    gdal_bin_path = args.gdal_bin_path

    trans_rgb(args.input, args.output)