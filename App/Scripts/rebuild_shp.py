# -*- coding: utf-8 -*-
# Build a new shx index file
import shapefile
import argparse
import os

# Explicitly name the shp and dbf file objects
# so pyshp ignores the missing/corrupt shx
# /var/www/techno/trunk/layers_queue/759_ЕТРЕНЕСАНС- КПДТ- КИРИЛ ЖЕНДОВ_2015_Dobrin/LU_BZZ.SHP
def rebuildSHP(shapeFile, shapeDBF):
	shpFileName = os.path.basename(shapeFile)

	dirName = os.path.dirname(shapeFile)
	name, ext = shpFileName.split('.')

	print(shapeFile)
	print(shapeDBF)

	# Open the files
	myshp = open(shapeFile, "rb")
	mydbf = open(shapeDBF, "rb")

	# Read the shapes, fields and records
	r = shapefile.Reader(shp=myshp, dbf=mydbf)
	shapeRecords = list(r.iterShapeRecords())
	fields = r.fields[1:]	# Get fields without the first (deletion) field

	# Close the files
	myshp.close()
	mydbf.close()

	# Create new files (overwrite the old)
	dstFileName = "%s/%s" % (dirName, name)
	w = shapefile.Writer(dstFileName)

	# Copy the fields
	w.fields = fields

	# Copy records and shapes
	for shaperec in shapeRecords:
		w.record(*shaperec.record)
		w.shape(shaperec.shape)

	# Saving will generate .shx file
	w.close()

	print("REBUILDING DONE")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Rebuilds broken SHP files.')
    parser.add_argument('shape_name', metavar='shape_name', type=str, nargs=1, help='The path to the SHP file.')
    parser.add_argument('shape_dbf', metavar='shape_dbf', type=str, nargs=1, help='The path to the DBF file.')

    args = parser.parse_args()

    rebuildSHP(args.shape_name[0], args.shape_dbf[0])