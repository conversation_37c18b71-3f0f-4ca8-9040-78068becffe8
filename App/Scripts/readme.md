#Scripts Documentation

##water_pounds.py
#####Returns NDWI vector file and inserts its geometry to database.
* Required parameters:
 * B03 - path to Band 03 raster file
 * B11 - path to Band 11 raster file
 * CLOUDS - path to clouds vector file
 * DATE - Tile date
 * SENTINEL\_TILE\_ID -  Sentinel tile id
 * WORK_DIR - Name of work directory
* Optional parameters:
 * -d [ _--database_] - Database name
 * -w - Water index
 * -r [ _--remove_] - If  is present removes work directory before the script completes
 * -u [ _--user_] - Database username
 * -H [ _--host_] - Database host
 * -p [ _--port_] - Database port
 * -P [ _--password_] - User's password
 * -t [ _--table_] - Database table name (Creates new table if does not exists)
* Examples:
```
  python3 water_pounds.py path/to/B03.jp2 path/to/B11.jp2 path/to/clouds.gml "2018-05-08" "34_T_FM" /tmp/workdir
```
```
  python3 water_pounds.py path/to/B03.jp2 path/to/B11.jp2 path/to/clouds.gml "2018-05-08" "34_T_FM" /tmp/workdir -r
```
```
  python3 water_pounds.py path/to/B03.jp2 path/to/B11.jp2 path/to/clouds.gml "2018-05-08" "34_T_FM" /tmp/workdir -r -d dbname
```
```
  python3 water_pounds.py path/to/B03.jp2 path/to/B11.jp2 path/to/clouds.gml "2018-05-08" "34_T_FM" /tmp/workdir -r -d dbname -H 127.0.0.1 -p 5432 -u username -P password -t water_pounds_table
```


##sentinelhub(2.0.3) module (for python) - https://github.com/sentinel-hub/sentinelhub-py
####Instalation requirements:
* python(version >=3.5 , tested on 3.5.2)
* pip (version 9.0.3)
* opencv-pyhton (version *******)
* tifffile (version 0.14.0)
