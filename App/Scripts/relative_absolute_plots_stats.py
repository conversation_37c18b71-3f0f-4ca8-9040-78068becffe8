#! /usr/bin/python -OO
# -*- coding: utf-8 -*-

import numpy as np
np.seterr(divide='ignore')
import matplotlib
matplotlib.use('Agg')
from matplotlib import pyplot as plt
from shapely import wkt
import json
import collections
import argparse
import glob
from globals import *

#Setting a Cyrillic font!
matplotlib.rcParams['font.family'] = 'DejaVu Sans'

numberOfClasses = 11

def calcStats(geoTiff, geoTiffPath, polygon, areaCoef, proj):
    polygon_tiff = geoTiffPath + "_wrap.tif"

    cutTiffByPolygon(geoTiff, polygon_tiff, polygon, proj)
    tif = rasterio.open(polygon_tiff)
    pixels = tif.read()

    x = pixels[~(np.isnan(pixels) | np.equal(pixels, -999))]
    mean = x.mean()
    min = x.min()
    max = x.max()

    totalResult = {
        'absolute': [],
        'relative': []
    }

    for numberClass in range(1, numberOfClasses + 1):
        resultBin = makeLayerBin(min, max, numberClass)
        totalResult['relative'].append(buildObject(resultBin[0], resultBin[1], pixels, polygon))
        totalResult['absolute'].append(buildObject(absoluteSentinelClasses[numberClass - 1], labelsClasses[numberClass - 1], pixels, polygon))

    for filename in glob.glob(tmp_dir + unique_filename + "*"):
        os.remove(filename)

    return json.dumps({
        "max": "%.3f" % max,
        "min": "%.3f" % min,
        "stats": totalResult
    })

def buildObject(binsArray, labels, pixels, polygon):
    result = []
    binLists = []
    for (i, binList) in enumerate(binsArray):
        if i == len(binsArray) - 1:
            break
        binLists.append([round(binList, 5), round(binsArray[i + 1], 5)])

    bins = np.array(binsArray, dtype=np.float)

    hist, edges = np.histogram(pixels, bins=bins, range=(bins.min(), bins.max()))
    Plot = wkt.loads(polygon)

    sum_hist = sum(hist)
    histArea = (Plot.area * hist / sum_hist / 1000.0)
    for (i, histA) in enumerate(histArea):
        histArea[i] = round(histA, 3)

    result.append(binLists);
    result.append(labels);
    result.append(histArea.tolist());
    return result

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Calculates and saves a GeoTIFF image statistics as a plot. The command also returns the statistics as JSON.');
    parser.add_argument('geo_tiff', metavar='geo_tiff', type=str, help='The source GEOTiff.')
    parser.add_argument('geo_tiff_path', metavar='geo_tiff_path', type=str, help='Path.')
    parser.add_argument('polygon_wkt', metavar='polygon_wkt', type=str, help='A polygon represented in WKT format.')

    parser.add_argument('--area_coef', metavar='', dest='area_coef', type=float, default=0.001, help='The plot area in dka.')
    parser.add_argument('--proj', metavar='', dest='proj', type=int, help='EPSG Projection.')

    args = parser.parse_args()

    print(calcStats(args.geo_tiff, args.geo_tiff_path, args.polygon_wkt, args.area_coef, args.proj))
