from sat_downloader import Landsat8
from sat_downloader import Sentinel2
import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Calculates and saves a GeoTIFF image statistics as a plot. The command also returns the statistics as JSON.');
    parser.add_argument('destination_path', metavar='destination_path', type=str, help='The download path.')
    parser.add_argument('scene_id', metavar='scene_id', type=str, help='Scene ID to download.')
    parser.add_argument('type', metavar='type', type=str, help='Satellite type (landsat|sentinel).')

    parser.add_argument('--bands', metavar='', dest='bands', nargs='*', type=int, help='Bands to download.')

    args = parser.parse_args()

    if args.type == 'sentinel':
        s = Sentinel2(download_dir=args.destination_path)
        scenes = s.download([args.scene_id], bands=args.bands)
    if args.type == 'landsat':
        s = Landsat8(download_dir=args.destination_path)
        scenes = s.download([args.scene_id], bands=args.bands)