import os
import logging
from xml.etree import ElementTree

from usgs import api, USGSError

from .download import S3DownloadMixin, Scenes
from .common import (landsat_scene_interpreter, amazon_s3_url_landsat8, check_create_folder,
                     fetch, google_storage_url_landsat8, remote_file_exists)

from .errors import RemoteFileDoesntExist, USGSInventoryAccessMissing

logger = logging.getLogger('sdownloader')


class Landsat8(S3DownloadMixin):
    """ Landsat8 downloader class """

    _bandmap = {
        'coastal': 1,
        'blue': 2,
        'green': 3,
        'red': 4,
        'nir': 5,
        'swir1': 6,
        'swir2': 7,
        'pan': 8,
        'cirrus': 9,
        'quality': 'BQA'
    }

    def __init__(self, download_dir, usgs_user=None, usgs_pass=None):
        self.download_dir = download_dir
        self.usgs_user = usgs_user
        self.usgs_pass = usgs_pass
        self.scene_interpreter = landsat_scene_interpreter
        self.amazon_s3_url = amazon_s3_url_landsat8

        # Make sure download directory exist
        check_create_folder(self.download_dir)

    def _band_converter(self, bands=None):
        if bands:
            for i, b in enumerate(bands):
                try:
                    bands[i] = self._bandmap[b]
                except KeyError:
                    pass
        return bands

    def download(self, scenes, bands):
        """
        Download scenese from Google Storage or Amazon S3 if bands are provided
        :param scenes:
            A list of scene and/or product IDs
        :type scenes:
            List
        :param bands:
            A list of bands. Default value is None.
        :type bands:
            List
        :returns:
            (List) includes downloaded scenes as key and source as value (aws or google)
        """

        bands = self._band_converter(bands)

        if not isinstance(bands, list):
            raise Exception('Expected bands list')

        # Always grab MTL.txt and QA band if bands are specified
        if 'BQA' not in bands:
            bands.append('QA')

        if 'MTL' not in bands:
            bands.append('MTL')

        if isinstance(scenes, list):
            scene_objs = Scenes()

            for scene in scenes:
                # for all scenes first check AWS, if the bands exist
                # download them, otherwise use Google and then USGS.
                try:
                    scene_objs.merge(self.s3([scene], bands))
                except RemoteFileDoesntExist:
                    try:
                        scene_objs.merge(self.google([scene], bands))
                    except RemoteFileDoesntExist:
                        scene_objs.merge(self.usgs([scene]))

            return scene_objs

        else:
            raise Exception('Expected sceneIDs list')

    def usgs(self, scenes):
        """ Downloads the image from USGS """

        if not isinstance(scenes, list):
            raise Exception('Expected sceneIDs list')

        scene_objs = Scenes()

        # download from usgs if login information is provided
        if self.usgs_user and self.usgs_pass:
            try:
                api_key = api.login(self.usgs_user, self.usgs_pass)
            except USGSError as e:
                error_tree = ElementTree.fromstring(str(e.message))
                error_text = error_tree.find("SOAP-ENV:Body/SOAP-ENV:Fault/faultstring", api.NAMESPACES).text
                raise USGSInventoryAccessMissing(error_text)

            for scene in scenes:
                download_urls = api.download('LANDSAT_8', 'EE', [scene], api_key=api_key)
                if download_urls:
                    logger.info('Source: USGS EarthExplorer')
                    scene_objs.add_with_files(scene, fetch(download_urls[0], self.download_dir))

                else:
                    raise RemoteFileDoesntExist('{0} not available on AWS S3, Google or USGS Earth Explorer'.format(
                                                ' - '.join(scene)))

            return scene_objs

        raise RemoteFileDoesntExist('{0} not available on AWS S3 or Google Storage'.format(' - '.join(scenes)))

    def google(self, scenes, bands):
        """
        Google Storage Downloader.
        :param scenes:
            A list of scene and/or product IDs
        :type scenes:
            List
        :param bands:
            A list of bands. Default value is None.
        :type bands:
            List
        :returns:
            (List) includes downloaded scenes as key and source as value (aws or google)
        """

        if not isinstance(scenes, list):
            raise Exception('Expected sceneIDs list')

        scene_objs = Scenes()
        logger.info('Source: Google Storge')

        for scene in scenes:
            files = []

            sat = landsat_scene_interpreter(scene)

            urls = []

            for band in bands:
                # get url for the band
                url = google_storage_url_landsat8(sat, band)

                # make sure it exist
                remote_file_exists(url)
                urls.append(url)

            if '/' in scene:
                scene_file = scene.replace('/', '_')
            else:
                scene_file = scene

            folder = os.path.join(self.download_dir, scene_file)
            # create folder
            check_create_folder(folder)

            for url in urls:
                files.append(fetch(url, folder))

            scene_objs.add_with_files(scene, files)

        return scene_objs
