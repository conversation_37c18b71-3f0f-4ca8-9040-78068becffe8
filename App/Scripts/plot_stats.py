#! /usr/bin/python -OO
# -*- coding: utf-8 -*-

import numpy as np

np.seterr(divide="ignore")
import matplotlib

matplotlib.use("Agg")
from matplotlib import pyplot as plt
from shapely.geometry import shape
from shapely.geometry.polygon import Polygon
import json
import collections
import argparse
import glob
import rasterio
from globals import *

# Setting a Cyrillic font!
matplotlib.rcParams["font.family"] = "DejaVu Sans"

rapideye_bins = np.array(
    [-1.00, 0.25, 0.50, 0.75, 0.90, 1.20, 1.50, 1.80, 2.15, 2.50, 3.00, 7.00],
    dtype=float,
)
landsat_bins = np.array(
    [-1.00, 0.10, 0.20, 0.30, 0.38, 0.44, 0.52, 0.60, 0.68, 0.77, 0.85, 1.00],
    dtype=float,
)

gsBins = np.array(
    [
        0.00,
        10.00,
        20.00,
        30.00,
        40.00,
        50.00,
        60.00,
        70.00,
        80.00,
        90.00,
        100.00,
        110.00,
    ],
    dtype=float,
)
colors = [
    "#d7191c",
    "#f58f53",
    "#ffde9c",
    "#ffffbf",
    "#cbe89c",
    "#a6d97b",
    "#7cc75b",
    "#4ab060",
    "#289148",
    "#067330",
    "#00451a",
]

polygon_tiff = tmp_dir + unique_filename + ".tiff"


def calcStats(
    geoTiff, polygon, numberOfClasses, typeOfClasses, areaCoef, satelliteType, proj
):
    if satelliteType == "sentinel":
        bins = np.array(absoluteSentinelClasses[numberOfClasses - 1], dtype=float)
    elif satelliteType == "sentinel_water":
        bins = np.array(absoluteSentinelWaterClasses[numberOfClasses - 1], dtype=float)
    elif satelliteType == "landsat":
        numberOfClasses = 11
        bins = landsat_bins
    else:
        numberOfClasses = 11
        bins = rapideye_bins

    labels = labelsClasses[numberOfClasses - 1]

    cutTiffByPolygon(geoTiff, polygon_tiff, polygon, proj)
    tif = rasterio.open(polygon_tiff)
    pixels = tif.read()

    x = pixels[~(np.isnan(pixels) | np.equal(pixels, -999))]
    mean = x.mean()
    min = x.min()
    max = x.max()

    if typeOfClasses == "relative" and satelliteType == "sentinel":
        resultBin = makeLayerBin(min, max, numberOfClasses)
        labels = resultBin[1]
        bins = np.array(makeLayerBin(min, max, numberOfClasses)[0])

    gsMean = 0
    idx = findNearestAbove(bins, mean)
    if idx:
        gsMean = round(
            (mean - bins[idx - 1])
            / (bins[idx] - bins[idx - 1])
            * (gsBins[idx] - gsBins[idx - 1])
            + gsBins[idx - 1],
            0,
        )

    hist, edges = np.histogram(pixels, bins=bins, range=(bins.min(), bins.max()))

    Plot: Polygon = shape(json.loads(polygon))

    sum_hist = sum(hist)

    histArea = Plot.area * hist / sum_hist / 1000.0
    histImg = Plot.area * hist / sum_hist * areaCoef

    histWithLabels = collections.OrderedDict(zip(labels, histArea))
    histWithLabelsImg = collections.OrderedDict(zip(labels, histImg))

    for filename in glob.glob(tmp_dir + unique_filename + "*"):
        os.remove(filename)

    for (key, value) in histWithLabels.items():
        histWithLabels[key] = "{0:.3f}".format(round(value, 3))

    return json.dumps(
        {
            "mean": gsMean,
            "max": "%.3f" % max,
            "min": "%.3f" % min,
            "stats": histWithLabels,
        }
    )


def findNearestAbove(my_array, target):
    diff = my_array - target
    mask = np.ma.less_equal(diff, 0)
    # We need to mask the negative differences and zero
    # since we are looking for values above
    if np.all(mask):
        return None  # returns None if target is greater than any value
    masked_diff = np.ma.masked_array(diff, mask)
    return masked_diff.argmin()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Calculates and saves a GeoTIFF image statistics as a plot. The command also returns the statistics as JSON."
    )
    parser.add_argument(
        "geo_tiff", metavar="geo_tiff", type=str, help="The source GEOTiff."
    )
    parser.add_argument(
        "polygon_geojson",
        type=str,
        help="A polygon represented in GeoJSON format.",
    )

    parser.add_argument(
        "--number_of_classes",
        metavar="number_of_classes",
        type=int,
        default=11,
        help="Number of classes. From 1 to 11.",
    )
    parser.add_argument(
        "--type_of_classes",
        metavar="type_of_classes",
        default="absolute",
        type=str,
        help="Type of classes (absolute | relative)",
    )
    parser.add_argument(
        "--area_coef",
        metavar="",
        dest="area_coef",
        type=float,
        default=0.001,
        help="The plot area in dka.",
    )
    parser.add_argument(
        "--satellite_type",
        metavar="",
        dest="satellite_type",
        type=str,
        default="rapideye",
        help="The satellite type (rapideye | sentinel | landsat).",
    )
    parser.add_argument(
        "--proj", metavar="", dest="proj", type=int, help="EPSG Projection."
    )

    args = parser.parse_args()
    print(
        calcStats(
            args.geo_tiff,
            args.polygon_geojson,
            args.number_of_classes,
            args.type_of_classes,
            args.area_coef,
            args.satellite_type,
            args.proj,
        )
    )
