#! /usr/bin/python -OO
# -*- coding: utf-8 -*-

import numpy as np
np.seterr(divide='ignore')
import argparse
import rasterio
import json
from globals import *

def readTiff(geoTiff, polygon_tiff, gdal_bin_path):

    call([(gdal_bin_path + "gdal_translate"), "-of", "GTiff", "-a_nodata", "-127", geoTiff, polygon_tiff])

    tif = rasterio.open(polygon_tiff)
    return tif

def calcStats(geo_tiff, geo_tiff_path, gdal_bin_path):
    polygon_tiff = geo_tiff_path + "_wrap.tif"
    polygon_tiff_remap = geo_tiff_path + "_remap.tif"

    tif = readTiff(geo_tiff, polygon_tiff, gdal_bin_path)
    pixels = tif.read()

    newPixels  = []

    for pixel in pixels:
        newPixel = []
        for each in pixel:
            newEach = []
            for value in each:
                roundedValue = value
                newValue = -127
                if(roundedValue > -1.00 and not np.isnan(roundedValue)):
                    b = [x for x in sentinelReferenceClasses if (roundedValue>=x[2] and roundedValue<x[3])]
                    newValue = int((((b[0][1] - b[0][0])/(b[0][3]-b[0][2]))*(roundedValue-b[0][2])) + b[0][0])
                newEach.append(newValue)
            newPixel.append(newEach)
        newPixels.append(newPixel)

        profile = tif.profile
        profile.update(
                dtype=rasterio.int16,
                count=1,
                compress='lzw')
        with rasterio.open(polygon_tiff_remap, 'w', **profile) as dst:
            dst.write(np.array(newPixels, dtype=np.int8).astype(rasterio.int16))

    tif.close()
    #Remove wrap file
    if os.path.exists(polygon_tiff):
        os.remove(polygon_tiff)

    if os.path.exists(polygon_tiff_remap):
        return json.dumps({'remapedTiffPath': polygon_tiff_remap});

    return 'error_creation';

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Create remap order tiff file with new values(from 0 to 110).');
    parser.add_argument('geo_tiff', metavar='geo_tiff', type=str, help='The source GEOTiff.')
    parser.add_argument('geo_tiff_path', metavar='geo_tiff_path', type=str, help='Path.')
    parser.add_argument('--gdal_bin_path', metavar='', dest='gdal_bin_path', type=str, default='', help='The path to gdal bin folder.')

    args = parser.parse_args()

    print(calcStats(args.geo_tiff, args.geo_tiff_path, args.gdal_bin_path))