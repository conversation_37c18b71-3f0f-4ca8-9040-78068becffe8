import argparse
import uuid
from osgeo import gdal
from osgeo import ogr


def shp2img(
    input: str,
    output: str,
    mask: str,
    color_table: str,
    new_height: float,
    new_width: float,
):

    plot_geojson_path = f"/vsimem/{uuid.uuid1()}.geojson"
    f = gdal.VSIFOpenL(plot_geojson_path, "w")
    gdal.VSIFWriteL(mask, 1, len(mask), f)
    gdal.VSIFCloseL(f)
    json_mask: gdal.Dataset = gdal.OpenEx(plot_geojson_path)
    json_layer: ogr.Layer = json_mask.GetLayerByIndex(0)

    minx, maxx, miny, maxy = json_layer.GetExtent()

    width = maxx - minx
    height = maxy - miny
    ratio = height / width
    calc_height = new_height
    calc_width = new_width
    if new_height and not new_width:
        calc_width = round(new_height * ratio, 3)
    elif new_width and not new_height:
        calc_height = round(calc_width * ratio, 3)

    warp_options = gdal.WarpOptions(
        format="MEM",
        multithread=True,
        dstNodata=-999,
        cutlineDSName=plot_geojson_path,
        cropToCutline=True,
    )

    warp_ds: gdal.Dataset = gdal.Warp(
        "", input, dstSRS="EPSG:3857", options=warp_options
    )

    dem_options = gdal.DEMProcessingOptions(
        format="MEM",
        colorFilename=color_table,
        addAlpha=True,
        colorSelection="linear_interpolation",
    )
    dem_ds: gdal.Dataset = gdal.DEMProcessing(
        "", warp_ds, "color-relief", options=dem_options
    )
    translate_options = gdal.TranslateOptions(
        format="PNG",
        creationOptions="COPYRIGHT=Techno Farm",
        width=calc_width,
        height=calc_height,
    )
    gdal.Translate(output, dem_ds, options=translate_options)


if __name__ == "__main__":
    arg_parser = argparse.ArgumentParser(description="Generate PNG from tiff")
    arg_parser.add_argument("input", help="A tiff file", type=str)
    arg_parser.add_argument("output", help="Outputh path including file name", type=str)
    arg_parser.add_argument(
        "-m", "--mask", help="A geometry in GeoJSON format", type=str
    )
    arg_parser.add_argument("-c", "--color_table", help="A color table file", type=str)
    arg_parser.add_argument(
        "-e",
        "--height",
        help="If only height is set the width will be calculated proportionaly.",
        type=float,
    )
    arg_parser.add_argument(
        "-w",
        "--width",
        help="If only width is set the height will be calculated proportionaly.",
        type=float,
    )

    args = arg_parser.parse_args()

    # Parse arguments
    input: str = args.input
    output: str = args.output
    mask: str = args.mask
    color_table: str = args.color_table
    height: float = args.height
    width: float = args.width

    try:
        shp2img(input, output, mask, color_table, height, width)
    except Exception:
        raise
