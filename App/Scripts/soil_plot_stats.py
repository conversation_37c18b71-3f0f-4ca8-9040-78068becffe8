#! /usr/bin/python -OO
# -*- coding: utf-8 -*-

import numpy as np

np.seterr(divide="ignore")
import json
import collections
import argparse
import rasterio


def calcStats(geo_tiff, scale_type, scale_json, plot_area):
    tif = rasterio.open(geo_tiff)
    pixels = tif.read()
    pixels = pixels[np.nonzero(pixels)]

    if scale_type == "absolute" or scale_type == "summarized":
        scale = json.loads(scale_json)
        labels = scale[:]

        for (key, value) in enumerate(labels):
            value_label = ">= " + str(value)
            if (key + 1) < len(labels):
                value_label = str(value) + " - " + str(labels[key + 1] - 0.01)
            labels[key] = str(value) + "|" + str(value_label)
    else:
        return "{}"

    scale.append(9999)

    hist, edges = np.histogram(pixels, bins=scale)

    sum_hist = sum(hist) or 1

    hist = plot_area * hist / sum_hist

    histWithLabels = collections.OrderedDict(zip(labels, hist))

    for key in list(histWithLabels.keys()):
        if scale_type == "absolute" and value == 0:
            del histWithLabels[key]
            continue

        histWithLabels[key] = "{0:.3f}".format(round(histWithLabels[key], 3))
    return histWithLabels


def calcStatsByRanges(geo_tiff, ranges, plot_area):
    tif = rasterio.open(geo_tiff)
    pixels = tif.read()
    pixels = pixels[np.nonzero(pixels)]

    labels = []
    scale = [-998]  #  Doesn't include no data value pixels
    for r in ranges:
        labels.append(r["label"])
        if r["min"] is None:
            continue
        scale.append(r["min"])

    scale.append(np.inf)
    hist, edges = np.histogram(pixels, bins=scale)
    sum_hist = sum(hist) or 1
    hist = plot_area * hist / sum_hist
    histWithLabels = collections.OrderedDict(zip(labels, hist))

    for key in list(histWithLabels.keys()):
        histWithLabels[key] = "{0:.3f}".format(round(histWithLabels[key], 3))

    return histWithLabels


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Calculates and returns the statistics as JSON."
    )
    parser.add_argument(
        "geo_tiff", metavar="geo_tiff", type=str, nargs=1, help="The plot GEOTiff."
    )
    parser.add_argument(
        "scale_type",
        metavar="scale_type",
        type=str,
        nargs=1,
        help="The scale type for statistic (absolute|summarized).",
    )
    parser.add_argument(
        "scale", metavar="scale", type=str, nargs=1, help="The scale for statistic."
    )
    parser.add_argument(
        "area", metavar="area", type=float, nargs=1, help="The plot area in dka."
    )

    args = parser.parse_args()

    print(
        json.dumps(
            calcStats(args.geo_tiff[0], args.scale_type[0], args.scale[0], args.area[0])
        )
    )
