#! /usr/bin/python -OO
# -*- coding: utf-8 -*-

import numpy as np
import json
import collections
import argparse
import rasterio
from shapely.geometry import shape
from shapely.geometry.polygon import Polygon
from globals import (
    sentinelReferenceClasses,
    cutTiffByPolygon,
)


def calcStats(geo_tiff, geo_tiff_path, polygon, proj, scale_json, plot_area):
    np.seterr(divide="ignore")

    polygon_tiff = geo_tiff_path + "_wrap.tif"
    polygon_tiff_remap = geo_tiff_path + "_wrap_remap.tif"

    cutTiffByPolygon(geo_tiff, polygon_tiff, polygon, proj)
    tif = rasterio.open(polygon_tiff)
    pixels = tif.read()

    newPixels = []

    for pixel in pixels:
        newPixel = []
        for each in pixel:
            newEach = []
            for value in each:
                roundedValue = value
                newValue = -999
                if roundedValue > -1.00 and not np.isnan(roundedValue):
                    b = [x for x in sentinelReferenceClasses if (roundedValue >= x[2] and roundedValue < x[3])]
                    newValue = float((((b[0][1] - b[0][0]) / (b[0][3] - b[0][2])) * (roundedValue - b[0][2])) + b[0][0])
                newEach.append(newValue)
            newPixel.append(newEach)
        newPixels.append(newPixel)

        profile = tif.profile
        profile.update(dtype=rasterio.float32, count=1, compress="lzw")
        with rasterio.open(polygon_tiff_remap, "w", **profile) as dst:
            dst.write(np.array(newPixels, dtype=np.float32).astype(rasterio.float32))

    scale = json.loads(scale_json)

    labels = scale[:]

    for (key, value) in enumerate(labels):
        value_label = ">= " + str(value)
        if (key + 1) < len(labels):
            value_label = str(value) + " - " + str(labels[key + 1] - 0.01)
        labels[key] = str(value) + "|" + str(value_label)

    newScale = []
    for s in scale:
        b = [x for x in sentinelReferenceClasses if (s >= x[0] and s < x[1])]
        newValue = (((b[0][3] - b[0][2]) / (b[0][1] - b[0][0])) * (s - b[0][0])) + b[0][2]
        newScale.append(newValue)
    newScale.append(1.00)
    newScale = np.array(newScale, dtype=np.float)

    hist, edges = np.histogram(pixels, bins=newScale, range=(newScale.min(), newScale.max()))

    Plot: Polygon = shape(json.loads(polygon))

    sum_hist = sum(hist)

    hist = Plot.area * hist / sum_hist / 1000.0

    histWithLabels = collections.OrderedDict(zip(labels, hist))

    for (key, value) in histWithLabels.items():
        histWithLabels[key] = "{0:.3f}".format(round(value, 3))

    return json.dumps(histWithLabels)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="""Calculates and saves a GeoTIFF image statistics as a plot.
        The command also returns the statistics as JSON."""
    )
    parser.add_argument("geo_tiff", metavar="geo_tiff", type=str, help="The source GEOTiff.")
    parser.add_argument("geo_tiff_path", metavar="geo_tiff_path", type=str, help="Path.")
    parser.add_argument(
        "polygon",
        metavar="polygon",
        type=str,
        help="A polygon represented as GeoJSON.",
    )
    parser.add_argument("scale", metavar="scale", type=str, nargs=1, help="The scale for statistic.")
    parser.add_argument("area", metavar="area", type=float, nargs=1, help="The plot area in dka.")

    parser.add_argument("--proj", metavar="", dest="proj", type=int, help="EPSG Projection.")

    args = parser.parse_args()

    print(
        calcStats(
            args.geo_tiff,
            args.geo_tiff_path,
            args.polygon,
            args.proj,
            args.scale[0],
            args.area,
        )
    )
