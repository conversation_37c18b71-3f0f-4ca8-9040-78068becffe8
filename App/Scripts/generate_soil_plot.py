from osgeo import gdal, ogr, osr
import os
import json
import argparse
import math
from soil_plot_stats import calcStatsByRanges
import tempfile
import shutil
from datetime import datetime
from dateutil import parser


def generate_soil_plot(
    server: str,
    service_provider_slug: str,
    tmp_path: str,
    element_interpretations: dict,
    default_proj: int,
    pixel_size,
    plots_geojson: str,
    points_geojson: str,
    dest_path: str,
    stats_type="summarized",
):
    plots_ds, points_ds = get_plots_points_datasets(
        plots_geojson, points_geojson, tmp_path
    )
    if plots_ds is None or points_ds is None:
        raise ValueError("Invalid plots or points datasource!")

    # Transform plots and points to default projection
    transformed_plots_layer_name = "plots_{proj}".format(proj=str(default_proj))
    transformed_plots_path = transform_to_projection(
        plots_ds, tmp_path, transformed_plots_layer_name, 4326, default_proj
    )
    plots_ds: gdal.Dataset = gdal.OpenEx(transformed_plots_path)
    transformed_points_layer_name = "points_{proj}".format(proj=str(default_proj))
    transformed_points_path = transform_to_projection(
        points_ds, tmp_path, transformed_points_layer_name, 4326, default_proj
    )
    points_ds: gdal.Dataset = gdal.OpenEx(transformed_points_path)

    sampling_types = get_sampling_types(points_ds)
    points_fields = get_points_fields(points_ds)

    # Final results
    results = []

    plots_layer: ogr.Layer = plots_ds.GetLayer()
    points_layer: ogr.Layer = points_ds.GetLayer()
    plot_feature: ogr.Feature
    for plot_feature in plots_layer:
        plot_uuid: str = plot_feature.GetField("uuid")
        plot_id: int = plot_feature.GetField("gid")
        order_id: int = points_layer.GetFeature(0).GetField("order_id")
        sopr_id: int = points_layer.GetFeature(0).GetField("sopr_id")

        # Get Envelope returns a tuple (minX, maxX, minY, maxY)
        plot_bbox: tuple[
            float, float, float, float
        ] = plot_feature.GetGeometryRef().GetEnvelope()

        for item in sampling_types:
            date = item["date"]
            # su_satellite_orders_plots_sampling_types.id
            sopst_id = item["sopst_id"]
            sampling_type_name = item["sampling_type_name"]

            files_to_move = []
            for soil_element, element_interpetation in element_interpretations.items():
                # Skip if there are no values for this soil_element
                if soil_element not in points_fields:
                    continue

                # Apply filter to get points by plot and date
                points_filter = "plot_uuid = '{uuid}' AND date = '{date}' AND sopst_id='{sopst_id}'".format(
                    uuid=plot_uuid, date=date, sopst_id=sopst_id
                )
                points_layer.SetAttributeFilter(points_filter)
                if not points_layer.GetFeatureCount():
                    continue

                # Create grid
                out_grid_path = "{tmp_path}/{uuid}_{date}_probe_{st_name}_{element}_grid.tif".format(
                    tmp_path=tmp_path,
                    uuid=plot_uuid,
                    date=date,
                    st_name=sampling_type_name,
                    element=soil_element,
                )
                out_tif_name = "{uuid}_{date}_probe_{st_name}_{element}.tif".format(
                    tmp_path=tmp_path,
                    uuid=plot_uuid,
                    date=date,
                    st_name=sampling_type_name,
                    element=soil_element,
                )
                out_tif_path = "{tmp_path}/{tif_name}".format(
                    tmp_path=tmp_path, tif_name=out_tif_name
                )
                tif_web_path = "{server}/soils/{date}/{tif_name}".format(
                    server=server, date=date, tif_name=out_tif_name
                )

                extent_tif_dts = create_grid(
                    points_ds,
                    out_grid_path,
                    plot_bbox,
                    soil_element,
                    default_proj,
                    pixel_size,
                )

                # Warp img. Returns the dataset of warped img
                tif_dts = warp_img(
                    extent_tif_dts,
                    out_tif_path,
                    transformed_plots_path,
                    transformed_plots_layer_name,
                    plot_uuid,
                )

                out_tif_3857_path = "{tmp_path}/{uuid}_{date}_probe_{st_name}_{element}_3857.tif".format(
                    tmp_path=tmp_path,
                    uuid=plot_uuid,
                    date=date,
                    st_name=sampling_type_name,
                    element=soil_element,
                )
                gdal.Warp(out_tif_3857_path, tif_dts, dstSRS="EPSG:3857")

                files_to_move.append(out_tif_path)

                # Close tif files
                extent_tif_dts = None
                tif_dts = None

                # create png
                out_png_name = (
                    "{uuid}_{date}_probe_{st_name}_{element}_{type}.png".format(
                        uuid=plot_uuid,
                        date=date,
                        st_name=sampling_type_name,
                        element=soil_element,
                        type=stats_type,
                    )
                )
                out_png_path = "{tmp_path}/{png_name}".format(
                    tmp_path=tmp_path, png_name=out_png_name
                )
                make_plot_png(
                    out_tif_3857_path,
                    out_png_path,
                    service_provider_slug,
                    soil_element,
                    element_interpetation,
                    default_proj,
                    plot_bbox,
                )
                files_to_move.append(out_png_path)

                # calculate stats
                plot_area = plot_feature.GetGeometryRef().GetArea() * 0.001
                stats = calcStatsByRanges(
                    out_tif_path,
                    element_interpretations[soil_element]["ranges"],
                    plot_area,
                )

                png_web_path = "{server}/soils/{date}/{png_name}".format(
                    server=server, date=date, png_name=out_png_name
                )

                # Move ready files
                copy_ready_files(dest_path, date, files_to_move)

                layer_name = (
                    "soil_raster_{date}_{s_e}-{s_e_u}_{s_type}_{st_name}".format(
                        date=date,
                        s_e=soil_element,
                        s_e_u=element_interpretations[soil_element]["unit"],
                        s_type=stats_type,
                        st_name=sampling_type_name,
                    )
                )
                result = {
                    "plot_id": plot_id,
                    "layer_name": layer_name,
                    "date": date,
                    "date_time": date,
                    "stats": stats,
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "type": "soil",
                    "order_id": order_id,
                    "sopst_id": sopst_id,
                    "png_path": "{dst_path}/{date}/{png_name}".format(
                        dst_path=dest_path, date=date, png_name=out_png_name
                    ),
                    "tif_path": "{dst_path}/{date}/{tif_name}".format(
                        dst_path=dest_path, date=date, tif_name=out_tif_name
                    ),
                    "png_web_path": png_web_path,
                    "tif_web_path": tif_web_path,
                    "element": soil_element,
                    "unit": element_interpretations[soil_element]["unit"],
                    "stats_type": stats_type,
                    "sopr_id": sopr_id,
                }

                if result not in results:
                    results.append(result)

    # Close plots and points geojson files
    plots_ds = None
    points_ds = None

    print(json.dumps(results))


def get_plots_points_datasets(plots_geojson: str, points_geojson: str, tmp_path: str):
    plots_file = tempfile.NamedTemporaryFile(
        mode="w+", dir=tmp_path, prefix="plots", suffix=".geojson"
    )
    plots_file.write(plots_geojson)
    plots_file.seek(0)
    plots_ds: gdal.Dataset = gdal.OpenEx(plots_file.name)
    plots_file.close()

    points_file = tempfile.NamedTemporaryFile(
        mode="w+", dir=tmp_path, prefix="points", suffix=".geojson"
    )
    points_file.write(points_geojson)
    points_file.seek(0)
    points_ds: gdal.Dataset = gdal.OpenEx(points_file.name)
    points_file.close()

    return plots_ds, points_ds


def copy_ready_files(dst: str, date: str, files: list[str]):
    dst = "{dst}/{date}".format(dst=dst, date=date)

    if not os.path.isdir(dst):
        os.makedirs(dst)

    for file_path in files:
        shutil.copy2(file_path, dst)


def transform_to_projection(
    src_ds: gdal.Dataset, tmp_path: str, layer_name: str, src_proj: str, dst_proj: str
):
    driver = ogr.GetDriverByName("GeoJSON")

    # input SpatialReference
    src_sp_ref = osr.SpatialReference()
    if int(gdal.__version__[0]) >= 3:
        src_sp_ref.SetAxisMappingStrategy(osr.OAMS_TRADITIONAL_GIS_ORDER)
    src_sp_ref.ImportFromEPSG(src_proj)

    # output SpatialReference
    dst_sp_ref = osr.SpatialReference()
    dst_sp_ref.ImportFromEPSG(dst_proj)

    coord_trans = osr.CoordinateTransformation(src_sp_ref, dst_sp_ref)

    # get input layer
    src_layer = src_ds.GetLayer()

    # create the output layer
    dst_file_path = "{tmp_path}/{layer_name}.geojson".format(
        tmp_path=tmp_path, layer_name=layer_name
    )
    if os.path.exists(dst_file_path):
        driver.DeleteDataSource(dst_file_path)
    dst_ds = driver.CreateDataSource(dst_file_path)
    dst_layer = dst_ds.CreateLayer(
        layer_name, dst_sp_ref, geom_type=src_layer.GetLayerDefn().GetGeomType()
    )

    # add fields
    src_layer_defn = src_layer.GetLayerDefn()
    for i in range(0, src_layer_defn.GetFieldCount()):
        field_defn = src_layer_defn.GetFieldDefn(i)
        dst_layer.CreateField(field_defn)

    # get the output layer's feature definition
    dst_layer_defn = dst_layer.GetLayerDefn()

    # loop through the input features
    src_feature = src_layer.GetNextFeature()
    while src_feature:
        # get the input geometry
        geom = src_feature.GetGeometryRef()

        # reproject the geometry
        geom.Transform(coord_trans)

        # create a new feature
        dst_feature = ogr.Feature(dst_layer_defn)

        # set the geometry and attribute
        dst_feature.SetGeometry(geom)
        for i in range(0, dst_layer_defn.GetFieldCount()):
            dst_feature.SetField(
                dst_layer_defn.GetFieldDefn(i).GetNameRef(), src_feature.GetField(i)
            )

        # add the feature to the layer
        dst_layer.CreateFeature(dst_feature)

        # dereference the features and get the next input feature
        dst_feature = None
        src_feature = src_layer.GetNextFeature()

    src_ds = None
    dst_ds = None
    return dst_file_path


def warp_img(
    src_dts: gdal.Dataset,
    out_file_path: str,
    cutline_ds_path: str,
    cutline_layer_name: str,
    plot_uuid: str,
) -> gdal.Dataset:
    plots_filter = "id = '{uuid}'".format(uuid=plot_uuid)
    warp_options = gdal.WarpOptions(
        multithread=True,
        warpMemoryLimit=2000,
        dstNodata=-999,
        cutlineDSName=cutline_ds_path,
        cutlineLayer=cutline_layer_name,
        cutlineWhere=plots_filter,
        cropToCutline=True,
    )

    return gdal.Warp(out_file_path, src_dts, options=warp_options)


def create_grid(
    src_ds: gdal.Dataset,
    out_file_path: str,
    plot_bbox,
    soil_element: str,
    default_proj: str,
    pixel_size: float,
) -> gdal.Dataset:
    img_width = math.ceil((plot_bbox[1] - plot_bbox[0]) / pixel_size)
    img_height = math.ceil((plot_bbox[3] - plot_bbox[2]) / pixel_size)

    new_x_max = plot_bbox[0] + img_width * pixel_size
    new_y_max = plot_bbox[2] + img_height * pixel_size

    grid_options = gdal.GridOptions(
        format="GTiff",
        zfield=soil_element,
        outputSRS=f"EPSG:{default_proj}",
        outputBounds=[plot_bbox[0], new_y_max, new_x_max, plot_bbox[2]],
        algorithm="invdist:power=10.0:smoothing=100.0:radius1=0.0:radius2=0.0:angle=0.0:max_points=0:min_points=0:nodata=-999",
        width=img_width,
        height=img_height,
    )

    return gdal.Grid(out_file_path, src_ds, options=grid_options)


def make_plot_png(
    tif_file,
    out_png_path,
    service_provider_slug,
    soil_element,
    element_interpetation,
    default_proj,
    plot_bbox,
):
    # Get extent, width and height in 3857 projection
    width, height = calculate_width_height(default_proj, 3857, plot_bbox)

    ratio = height / width
    new_width = 200
    new_height = round(200 * ratio, 3)

    color_table_path = create_color_table(
        service_provider_slug, soil_element, element_interpetation
    )
    dem_options = gdal.DEMProcessingOptions(
        format="MEM",
        colorFilename=color_table_path,
        addAlpha=True,
        colorSelection="nearest_color_entry",
    )

    dem_ds = gdal.DEMProcessing(
        "",
        tif_file,
        "color-relief",
        options=dem_options,
    )
    translate_options = gdal.TranslateOptions(
        width=new_width,
        height=new_height,
        format="PNG",
        creationOptions="COPYRIGHT=Techno Farm",
    )
    gdal.Translate(out_png_path, dem_ds, options=translate_options)
    dem_ds = None


def create_color_table(
    service_provider_slug: str, soil_element: str, element_interpetation: dict
):
    ct = ["-999      0   0   0   0"]
    for range in element_interpetation["ranges"]:
        rgbColor = " ".join(
            [str(int(range["color"].lstrip("#")[i : i + 2], 16)) for i in (0, 2, 4)]
        )
        if not range["min"] and range["max"]:
            ct.append(f"-998 {rgbColor} 255")
            ct.append(f"{range['max']} {rgbColor} 255")
        elif not range["max"] and range["min"]:
            ct.append(f"{range['min']} {rgbColor} 255")
        else:
            ct.append(f"{range['min']} {rgbColor} 255")
            ct.append(f"{range['max']} {rgbColor} 255")
    content = "\n".join(ct)

    mem_path = f"/vsimem/{service_provider_slug}/{soil_element}.txt"
    f = gdal.VSIFOpenL(mem_path, "w")
    gdal.VSIFWriteL(content, 1, len(content), f)
    gdal.VSIFCloseL(f)

    return mem_path


def calculate_width_height(src_proj, dst_proj, envelope):
    # Input SpatialReference
    src_sp_ref = osr.SpatialReference()
    src_sp_ref.ImportFromEPSG(src_proj)

    # Output SpatialReference
    dst_sp_ref = osr.SpatialReference()
    dst_sp_ref.ImportFromEPSG(dst_proj)

    coord_trans = osr.CoordinateTransformation(src_sp_ref, dst_sp_ref)

    # upper left point
    ul_point = ogr.Geometry(ogr.wkbPoint)
    ul_point.AddPoint(envelope[0], envelope[3])
    ul_point.Transform(coord_trans)

    # lower right point
    lr_point = ogr.Geometry(ogr.wkbPoint)
    lr_point.AddPoint(envelope[1], envelope[2])
    lr_point.Transform(coord_trans)

    width: float = lr_point.GetX() - ul_point.GetX()
    height: float = ul_point.GetY() - lr_point.GetY()

    return (width, height)


def get_points_fields(points_ds: gdal.Dataset):
    points_layer = points_ds.GetLayer()
    points_layer_def = points_layer.GetLayerDefn()

    fields: set[str] = set()
    for field in range(points_layer_def.GetFieldCount()):
        fields.add(points_layer_def.GetFieldDefn(field).GetName())

    return fields


def get_sampling_types(points_ds: gdal.Dataset):
    points_layer = points_ds.GetLayer()

    sampling_types = []
    for feature in points_layer:
        date = parser.parse(feature.GetField("date")).date().strftime("%Y-%m-%d")
        sampling_type = {
            "date": date,
            "sopst_id": feature.GetField("sopst_id"),
            "sampling_type_name": feature.GetField("sampling_type_name"),
        }

        if sampling_type not in sampling_types:
            sampling_types.append(sampling_type)

    return sampling_types


def parse_number(value):
    try:
        return int(value)
    except ValueError:
        return float(value)


if __name__ == "__main__":
    arg_parser = argparse.ArgumentParser(
        description="This script creates soil map (rgb) for given plot/plots."
    )
    arg_parser.add_argument(
        "server", help="Server (Country iso alpha 2 code)", type=str
    )
    arg_parser.add_argument(
        "service_provider_slug", help="Service provider slug", type=str
    )
    arg_parser.add_argument(
        "plots_geojson", help="Plot GeoJSON (FeatureCollection)", type=str
    )
    arg_parser.add_argument(
        "points_geojson", help="Points GeoJSON (FeatureCollection)", type=str
    )
    arg_parser.add_argument(
        "processed_orders_path",
        help="Processed orders path. Example: /mnt/d/www/geoscan/storage/ordered_plots/",
        type=str,
    )
    arg_parser.add_argument(
        "-d", "--default_proj", help="Default DB projection", type=int, default=32635
    )
    arg_parser.add_argument(
        "-p", "--pixel_size", help="Tile pixel size", type=float, default=2.0
    )
    arg_parser.add_argument(
        "-e",
        "--element_interpretations",
        help="Soil elements unit, range, color",
        type=str,
    )

    args = arg_parser.parse_args()

    # Parse arguments
    server = args.server.upper()
    service_provider_slug = args.service_provider_slug
    default_proj = args.default_proj
    pixel_size = args.pixel_size
    plots_geojson = args.plots_geojson
    points_geojson = args.points_geojson
    dest_path = os.path.join(args.processed_orders_path, server, "soils")
    element_interpretations = json.loads(args.element_interpretations)

    tmp_path = tempfile.mkdtemp(prefix="generate_soil_plot_")
    try:
        generate_soil_plot(
            server,
            service_provider_slug,
            tmp_path,
            element_interpretations,
            default_proj,
            pixel_size,
            plots_geojson,
            points_geojson,
            dest_path,
        )
    except Exception:
        raise
    finally:
        shutil.rmtree(tmp_path)
