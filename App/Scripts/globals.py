import os
import uuid
from osgeo import gdal


absoluteSentinelClasses = [
    [-1.00, 1.00],
    [-1.00, 0.65, 1.00],
    [-1.00, 0.50, 0.75, 1.00],
    [-1.00, 0.33, 0.66, 0.85, 1.00],
    [-1.00, 0.25, 0.50, 0.75, 0.85, 1.00],
    [-1.00, 0.20, 0.40, 0.60, 0.74, 0.85, 1.00],
    [-1.00, 0.16, 0.33, 0.50, 0.66, 0.75, 0.85, 1.00],
    [-1.00, 0.15, 0.28, 0.38, 0.50, 0.60, 0.73, 0.85, 1.00],
    [-1.00, 0.10, 0.30, 0.38, 0.45, 0.55, 0.65, 0.75, 0.85, 1.00],
    [-1.00, 0.10, 0.20, 0.30, 0.40, 0.50, 0.60, 0.70, 0.80, 0.85, 1.00],
    [-1.00, 0.10, 0.20, 0.30, 0.38, 0.44, 0.52, 0.60, 0.68, 0.77, 0.85, 1.00],
]

sentinelReferenceClasses = [
    [0, 10, -1.00, 0.10],
    [10, 20, 0.10, 0.20],
    [20, 30, 0.20, 0.30],
    [30, 40, 0.30, 0.38],
    [40, 50, 0.38, 0.44],
    [50, 60, 0.44, 0.52],
    [60, 70, 0.52, 0.60],
    [70, 80, 0.60, 0.68],
    [80, 90, 0.68, 0.77],
    [90, 100, 0.77, 0.85],
    [100, 110, 0.85, 1.00],
]

absoluteSentinelWaterClasses = [
    [-1.00, 1.00],
    [-1.00, 0.25, 1.00],
    [-1.00, 0.00, 0.35, 1.00],
    [-1.00, -0.33, -0.10, 0.15, 1.00],
    [-1.00, 0.25, 0.50, 0.75, 0.85, 1.00],
    [-1.00, 0.20, 0.40, 0.60, 0.74, 0.85, 1.00],
    [-1.00, 0.16, 0.33, 0.50, 0.66, 0.75, 0.85, 1.00],
    [-1.00, 0.15, 0.28, 0.38, 0.50, 0.60, 0.73, 0.85, 1.00],
    [-1.00, 0.10, 0.30, 0.38, 0.45, 0.55, 0.65, 0.75, 0.85, 1.00],
    [-1.00, 0.10, 0.20, 0.30, 0.40, 0.50, 0.60, 0.70, 0.80, 0.85, 1.00],
    [
        -1.00,
        -0.6533,
        -0.4033,
        -0.3066,
        -0.1599,
        -0.0664,
        0.1564,
        0.2502,
        0.3569,
        0.4536,
        0.5503,
        1.00,
    ],
]

labelsClasses = [
    ["0-110"],
    ["0-76", "76-110"],
    ["0-58", "58-90", "90-110"],
    ["0-34", "34-78", "78-104", "104-110"],
    ["0-25", "25-58", "58-90", "90-104", "104-110"],
    ["0-20", "20-33", "33-70", "70-89", "89-104", "104-110"],
    ["0-15", "15-34", "34-58", "58-68", "68-78", "78-104", "104-110"],
    ["0-15", "15-28", "28-40", "40-58", "58-70", "70-78", "78-104", "104-110"],
    ["0-10", "10-30", "30-40", "40-51", "51-64", "64-76", "76-90", "90-104", "104-110"],
    [
        "0-10",
        "10-20",
        "20-30",
        "30-43",
        "43-58",
        "58-70",
        "70-83",
        "83-98",
        "98-104",
        "104-110",
    ],
    [
        "0-10",
        "10-20",
        "20-30",
        "30-40",
        "40-50",
        "50-60",
        "60-70",
        "70-80",
        "80-90",
        "90-100",
        "100-110",
    ],
]

unique_filename = str(uuid.uuid4())
tmp_dir = "/tmp/stats_tmp/"


def makeLayerBin(min, max, numberOfClasses):
    step = (max - min) / numberOfClasses
    bins = []
    labels = []
    for layer in range(0, numberOfClasses + 1):
        if layer > 0:
            labels.append(layer)
        bins.append(min + step * (layer))
    return [bins, labels]


def cutTiffByPolygon(src_tiff, dst_tiff, polygon_wkt, polygon_proj):
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)

    plot_geojson_path = f"/vsimem/{uuid.uuid1()}.geojson"
    f = gdal.VSIFOpenL(plot_geojson_path, "w")
    gdal.VSIFWriteL(polygon_wkt, 1, len(polygon_wkt), f)
    gdal.VSIFCloseL(f)

    # Cut
    warp_options = gdal.WarpOptions(
        multithread=True,
        warpMemoryLimit=2000,
        dstNodata=-999,
        cutlineDSName=plot_geojson_path,
        cropToCutline=True,
    )

    gdal.Warp(dst_tiff, src_tiff, options=warp_options)
