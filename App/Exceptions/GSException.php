<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Arr;

/**
 * Exception class.
 */
class GSException extends Exception
{
    /**
     * The HTTP status code for this exception that should be sent in the response.
     */
    public $httpStatusCode = 400;

    /**
     * The exception type.
     */
    public $errorType = '';

    public function render()
    {
        return config('app.debug') ? [
            'error' => $this->getErrorType(), // TODO: Remove after proper implementation of the exceptions.
            'error_description' => $this->getMessage(), // TODO: Remove after proper implementation of the exceptions.
            'message' => $this->getMessage(),
            'exception' => get_class($this),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => collect($this->getTrace())->map(function ($trace) {
                return Arr::except($trace, ['args']);
            })->all(),
        ] : response([
            'error' => $this->getErrorType(),
            'error_description' => $this->getMessage(),
        ], $this->getStatusCode());
    }
}
