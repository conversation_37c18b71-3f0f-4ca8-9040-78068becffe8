<?php

namespace App\Exceptions;

class NotFoundException extends GSException
{
    public $errorType = 'not_found';
    public $statusCode = 404;

    public function __construct($message = '')
    {
        parent::__construct($message);
    }

    public function getErrorType()
    {
        return $this->errorType;
    }

    public function getStatusCode()
    {
        return $this->statusCode;
    }
}
