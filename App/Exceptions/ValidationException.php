<?php

namespace App\Exceptions;

class ValidationException extends GSException
{
    public $errorType = 'invalid_params';
    public $statusCode = 400;

    public function __construct($message = '')
    {
        parent::__construct($message);
    }

    public function getErrorType()
    {
        return $this->errorType;
    }

    public function getStatusCode()
    {
        return $this->statusCode;
    }
}
