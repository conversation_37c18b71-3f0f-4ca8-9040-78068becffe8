<?php

namespace Database\Seeders;

use App\Models\ServiceProvider;
use Illuminate\Database\Seeder;

class CouchDBConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        ServiceProvider::where('id', 1)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agrolab_db',
            ],
        ]);

        ServiceProvider::where('id', 2)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agrolab_db',
            ],
        ]);
        ServiceProvider::where('id', 3)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agricost_db',
            ],
        ]);
        ServiceProvider::where('id', 4)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agrolab_db',
            ],
        ]);
        ServiceProvider::where('id', 5)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agrolab_db',
            ],
        ]);
        ServiceProvider::where('id', 6)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agrolab_db',
            ],
        ]);
        ServiceProvider::where('id', 7)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agrolab_db',
            ],
        ]);
        ServiceProvider::where('id', 9)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'agviser_db',
            ],
        ]);
        ServiceProvider::where('id', 10)->update([
            'couchdb_config' => [
                'host' => 'geoscan-couchdb.geoscan.svc',
                'port' => 5984,
                'username' => 'geoscan',
                'password' => 'COUCH_DB_PASSWORD',
                'dbname' => 'ans_db',
            ],
        ]);
    }
}
