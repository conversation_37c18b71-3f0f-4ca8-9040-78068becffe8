<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateForeignKeysWithCascadeDelete extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('su_machine_tasks', function (Blueprint $table) {
            $table->dropForeign(['machine_unit_id']);

            $table->foreign('machine_unit_id')
                ->references('id')
                ->on('su_machine_units')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });

        DB::statement(
            'ALTER TABLE su_machine_task_products DROP CONSTRAINT su_machine_event_products_task_id_foreign;'
        );

        Schema::table('su_machine_task_products', function (Blueprint $table) {
            $table->foreign('task_id')
                ->references('id')
                ->on('su_machine_tasks')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down() {}
}
