<?php

use App\Models\WorkOperation;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddWorkOperationsForRoDb extends Migration
{
    public $withinTransaction = false;

    protected $newWorkOperations = [
        ['name' => 'Subsoiling', 'color' => '#8CAFD1'],
        ['name' => 'Rotary', 'color' => '#A8D5BA'],
        ['name' => 'Chopping', 'color' => '#F6E3B4'],
        ['name' => 'CollectedDripLines', 'color' => '#F2B6A0'],
        ['name' => 'InstallingDripIrrigation', 'color' => '#FFD6A5'],
        ['name' => 'RotaryReRiging', 'color' => '#B7E4C7'],
        ['name' => 'WindroverHarvest', 'color' => '#CDB4DB'],
        ['name' => 'Plowing', 'color' => '#B5B9D9'],
        ['name' => 'Spraying1', 'color' => '#A0CFCF'],
        ['name' => 'Spraying2', 'color' => '#FFD8B1'],
        ['name' => 'Spraying3', 'color' => '#E2A9A9'],
        ['name' => 'Spraying4', 'color' => '#B7D2B7'],
        ['name' => 'Spraying5', 'color' => '#B6B6D9'],
        ['name' => 'Spraying6', 'color' => '#A7C7E7'],
        ['name' => 'Spraying7', 'color' => '#FFE0B2'],
        ['name' => 'Spraying8', 'color' => '#E6EE9C'],
        ['name' => 'Spraying9', 'color' => '#BCAAA4'],
        ['name' => 'Spraying10', 'color' => '#A1887F'],
        ['name' => 'Spraying11', 'color' => '#B0BEC5'],
        ['name' => 'Spraying12', 'color' => '#90CAF9'],
        ['name' => 'Fertilization1', 'color' => '#AED581'],
        ['name' => 'Fertilization2', 'color' => '#FFAB91'],
        ['name' => 'Fertilization3', 'color' => '#FFE082'],
        ['name' => 'Fertilization4', 'color' => '#FFCCBC'],
    ];

    /**
     * This migration is specifically designed to be applied only to the RO (Romania) database.
     */
    public function up()
    {
        $dbName = strtoupper(DB::getName());

        if ('RO' !== $dbName) {
            throw new \Exception('Database migration can only be applied to the RO database.');
        }

        // Add new work operations to the enum type
        foreach ($this->newWorkOperations as $operation) {
            DB::statement("ALTER TYPE work_operations_types_enum ADD VALUE IF NOT EXISTS '{$operation['name']}'");
        }

        // Insert new work operations into the database
        WorkOperation::insertOrIgnore($this->newWorkOperations);
    }

    public function down()
    {
        $dbName = strtoupper(DB::getName());

        if ('RO' !== $dbName) {
            throw new \Exception('Database migration can only be applied to the RO database.');
        }

        WorkOperation::whereIn('name', $this->newWorkOperations)->deleteOrFail();

        // Remove work operations from the enum type
        foreach ($this->newWorkOperations as $operation) {
            DB::statement("ALTER TYPE work_operations_types_enum DROP ATTRIBUTE IF EXISTS '{$operation['name']}'");
        }
    }
}
