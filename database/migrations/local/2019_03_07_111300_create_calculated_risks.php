<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCalculatedRisks extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('calculated_risks', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('pests_diseases_id')->unsigned();
            $table->integer('plot_id')->unsigned();
            $table->integer('phenophases_id')->unsigned();
            $table->float('risk_level')->nullable(false);
            $table->date('date')->nullable(false);

            $table->foreign('pests_diseases_id')->references('id')->on('pests_diseases');
            $table->foreign('plot_id')->references('gid')->on('su_satellite_plots');
            $table->foreign('phenophases_id')->references('id')->on('phenophases');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('calculated_risks');
    }
}
