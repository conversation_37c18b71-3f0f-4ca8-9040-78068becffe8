<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnAppliedAreaInSuMachineTaskProducts extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('su_machine_task_products', function (Blueprint $table) {
            $table->decimal('applied_area', 9, 3)->nullable(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('su_machine_task_products', function (Blueprint $table) {
            $table->dropColumn('applied_area');
        });
    }
}
