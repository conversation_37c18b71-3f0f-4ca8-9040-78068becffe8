<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRiskGroups extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('risk_groups', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 255)->nullable(false);
            $table->integer('risk_level')->unsigned()->nullable(false);
            $table->integer('pests_diseases_id')->unsigned();

            $table->foreign('pests_diseases_id')->references('id')->on('pests_diseases');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('risk_groups');
    }
}
