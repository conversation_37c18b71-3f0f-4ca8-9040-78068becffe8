<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class DropPassportTables extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::dropIfExists('oauth_session_scopes');
        Schema::dropIfExists('oauth_refresh_tokens');
        Schema::dropIfExists('oauth_client_endpoints');
        Schema::dropIfExists('oauth_auth_code_scopes');
        Schema::dropIfExists('oauth_client_grants');
        Schema::dropIfExists('oauth_access_token_scopes');
        Schema::dropIfExists('oauth_client_scopes');
        Schema::dropIfExists('oauth_grant_scopes');
        Schema::dropIfExists('oauth_scopes');
        Schema::dropIfExists('oauth_grants');
        Schema::dropIfExists('oauth_auth_codes');
        Schema::dropIfExists('oauth_access_tokens');
        Schema::dropIfExists('oauth_sessions');
        Schema::dropIfExists('oauth_clients');
    }
}
