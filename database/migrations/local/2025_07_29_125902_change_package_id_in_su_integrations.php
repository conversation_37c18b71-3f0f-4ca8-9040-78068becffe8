<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ChangePackageIdInSuIntegrations extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        $mainDB = Config::get('database.connections.main');
        $connectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname=geoscan_cms password={$mainDB['password']}";

        DB::statement("
            WITH integartions_data AS (
                SELECT 
                    *
                FROM su_integration
            ),
            subscription_package_data AS (
                SELECT
                    integartions_data.id as integration_id,
                    integartions_data.organization_id,
                    integartions_data.package_id as current_package_id,
                    sp.id as subscription_package_id,
                    sp.package_id as actual_package_id
                FROM integartions_data 
                INNER JOIN dblink('" . $connectionString . "',
                    'SELECT sp.id, sp.package_id 
                    FROM subscription_package sp'
                ) AS sp(id int, package_id int) ON integartions_data.package_id = sp.id
            )
            UPDATE su_integration 
            SET package_id = spd.actual_package_id
            FROM subscription_package_data spd
            WHERE 
                su_integration.id = spd.integration_id
            RETURNING su_integration.id, su_integration.package_id as new_package
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
}
