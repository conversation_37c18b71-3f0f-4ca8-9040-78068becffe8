<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixSuMachineEventsDuplicates extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // First, let's identify and remove duplicate records
        // We'll keep the record with the highest ID (most recent) for each group of duplicates

        DB::statement('
            -- Create a temporary table to identify duplicates
            CREATE TEMP TABLE duplicate_machine_events AS
            SELECT 
                id,
                ROW_NUMBER() OVER (
                    PARTITION BY 
                        COALESCE(plot_id, -1), 
                        machine_id, 
                        COALESCE(machine_implement_id, -1), 
                        date, 
                        start_date, 
                        end_date, 
                        type 
                    ORDER BY id DESC
                ) as row_num
            FROM su_machine_events;
        ');

        // Delete duplicate records (keeping only the first one - highest ID)
        DB::statement('
            DELETE FROM su_machine_events
            WHERE id IN (
                SELECT id
                FROM duplicate_machine_events
                WHERE row_num > 1
            );
        ');

        // Drop the current unique constraint
        DB::statement('ALTER TABLE su_machine_events DROP CONSTRAINT IF EXISTS su_machine_events_unique_fields');

        // Create an improved unique index that handles NULL values properly
        // Using COALESCE to treat NULL values as a specific value for uniqueness
        DB::statement('
            CREATE UNIQUE INDEX su_machine_events_unique_fields
            ON su_machine_events (
                COALESCE(plot_id, -1),
                machine_id,
                COALESCE(machine_implement_id, -1),
                date,
                start_date,
                end_date,
                type
            )
        ');

        // No need to create constraint as it's already handled by the unique index
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drop the improved index
        DB::statement('DROP INDEX IF EXISTS su_machine_events_unique_fields');

        // Restore the original constraint (this might fail if duplicates exist again)
        try {
            DB::statement('
                ALTER TABLE su_machine_events
                ADD CONSTRAINT su_machine_events_unique_fields
                UNIQUE (plot_id, machine_id, machine_implement_id, date, start_date, end_date, type)
            ');
        } catch (Exception $e) {
            // If the original constraint can't be restored due to duplicates, just continue
            // The migration rollback will be logged by Laravel automatically
        }
    }
}
