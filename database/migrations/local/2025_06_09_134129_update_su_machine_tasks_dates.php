<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateSuMachineTasksDates extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        DB::statement(
            'UPDATE su_machine_tasks
                    SET 
                        start_date = sme.start_date,
                        end_date = sme.end_date,
                        completion_date = sme.end_date
                    FROM su_machine_events sme
                    WHERE sme.id = su_machine_tasks.machine_event_id;
                    '
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down() {}
}
