<?php

/**
 * Test script to validate the organization-based batching migration logic.
 * 
 * This script can be run to test the migration logic without actually executing
 * the migration. It validates the SQL queries and checks for potential issues.
 */

require_once __DIR__ . '/../../../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class MigrationBatchingTest
{
    /**
     * Test the organization ID retrieval logic.
     */
    public function testGetOrganizationIds(): void
    {
        echo "Testing organization ID retrieval...\n";
        
        try {
            $organizationIds = DB::table('su_integration')
                ->distinct()
                ->pluck('organization_id')
                ->toArray();
            
            $filteredIds = array_filter($organizationIds, function ($id) {
                return !is_null($id);
            });
            
            echo "Found " . count($filteredIds) . " organizations with integrations\n";
            echo "Organization IDs: " . implode(', ', array_slice($filteredIds, 0, 10)) . 
                 (count($filteredIds) > 10 ? '...' : '') . "\n";
            
            if (empty($filteredIds)) {
                echo "WARNING: No organizations found with integrations!\n";
            }
            
        } catch (\Exception $e) {
            echo "ERROR: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test the batch processing SQL queries for syntax and logic.
     */
    public function testBatchQueries(): void
    {
        echo "Testing batch processing SQL queries...\n";
        
        // Get a sample organization ID
        $sampleOrgId = DB::table('su_integration')
            ->whereNotNull('organization_id')
            ->value('organization_id');
            
        if (!$sampleOrgId) {
            echo "SKIP: No sample organization ID found\n\n";
            return;
        }
        
        echo "Using sample organization ID: {$sampleOrgId}\n";
        
        // Test machine events query
        $this->testMachineEventsQuery($sampleOrgId);
        
        // Test machine tasks query
        $this->testMachineTasksQuery($sampleOrgId);
        
        // Test implements query
        $this->testImplementsQuery($sampleOrgId);
        
        // Test integration reports query
        $this->testIntegrationReportsQuery($sampleOrgId);
        
        // Test machine integration references query
        $this->testMachineIntegrationReferencesQuery($sampleOrgId);
        
        echo "\n";
    }
    
    private function testMachineEventsQuery(int $organizationId): void
    {
        echo "  Testing machine events query...\n";
        
        try {
            // Test the SELECT part of the update query
            $result = DB::select('
                SELECT COUNT(*) as count
                FROM su_machine_events sme
                JOIN su_machine_units smu_old ON sme.machine_id = smu_old.id
                JOIN su_integration si_old ON smu_old.integration_id = si_old.id
                WHERE si_old.organization_id = ?
            ', [$organizationId]);
            
            echo "    Found " . ($result[0]->count ?? 0) . " machine events for organization {$organizationId}\n";
            
        } catch (\Exception $e) {
            echo "    ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    private function testMachineTasksQuery(int $organizationId): void
    {
        echo "  Testing machine tasks query...\n";
        
        try {
            $result = DB::select('
                SELECT COUNT(*) as count
                FROM su_machine_tasks smt
                JOIN su_machine_units smu_old ON smt.machine_unit_id = smu_old.id
                JOIN su_integration si_old ON smu_old.integration_id = si_old.id
                WHERE si_old.organization_id = ?
            ', [$organizationId]);
            
            echo "    Found " . ($result[0]->count ?? 0) . " machine tasks for organization {$organizationId}\n";
            
        } catch (\Exception $e) {
            echo "    ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    private function testImplementsQuery(int $organizationId): void
    {
        echo "  Testing implements query...\n";
        
        try {
            $result = DB::select('
                SELECT COUNT(*) as count
                FROM su_machines_implements smi
                JOIN su_integration si_old ON smi.integration_id = si_old.id
                WHERE si_old.organization_id = ?
            ', [$organizationId]);
            
            echo "    Found " . ($result[0]->count ?? 0) . " implements for organization {$organizationId}\n";
            
        } catch (\Exception $e) {
            echo "    ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    private function testIntegrationReportsQuery(int $organizationId): void
    {
        echo "  Testing integration reports query...\n";
        
        try {
            $result = DB::select('
                SELECT COUNT(*) as count
                FROM su_integrations_reports sir
                JOIN su_integration si_old ON sir.integration_id = si_old.id
                WHERE si_old.organization_id = ?
            ', [$organizationId]);
            
            echo "    Found " . ($result[0]->count ?? 0) . " integration reports for organization {$organizationId}\n";
            
        } catch (\Exception $e) {
            echo "    ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    private function testMachineIntegrationReferencesQuery(int $organizationId): void
    {
        echo "  Testing machine integration references query...\n";
        
        try {
            $result = DB::select('
                SELECT COUNT(*) as count
                FROM su_machine_units smu
                JOIN su_integration si ON smu.integration_id = si.id
                WHERE si.organization_id = ?
            ', [$organizationId]);
            
            echo "    Found " . ($result[0]->count ?? 0) . " machine units for organization {$organizationId}\n";
            
        } catch (\Exception $e) {
            echo "    ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test database connection and required tables.
     */
    public function testDatabaseConnection(): void
    {
        echo "Testing database connection and required tables...\n";
        
        $requiredTables = [
            'su_integration',
            'su_machine_units', 
            'su_machine_events',
            'su_machine_tasks',
            'su_machines_implements',
            'su_integrations_reports'
        ];
        
        foreach ($requiredTables as $table) {
            try {
                if (Schema::hasTable($table)) {
                    $count = DB::table($table)->count();
                    echo "  ✓ Table {$table} exists with {$count} records\n";
                } else {
                    echo "  ✗ Table {$table} does not exist\n";
                }
            } catch (\Exception $e) {
                echo "  ✗ Error checking table {$table}: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Run all tests.
     */
    public function runAllTests(): void
    {
        echo "=== Migration Batching Test Suite ===\n\n";
        
        $this->testDatabaseConnection();
        $this->testGetOrganizationIds();
        $this->testBatchQueries();
        
        echo "=== Test Suite Complete ===\n";
    }
}

// Run tests if this script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new MigrationBatchingTest();
    $test->runAllTests();
}
