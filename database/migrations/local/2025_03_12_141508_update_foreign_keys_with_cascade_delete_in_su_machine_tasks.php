<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateForeignKeysWithCascadeDeleteInSuMachineTasks extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('su_machine_tasks', function (Blueprint $table) {
            $table->dropForeign(['machine_event_id']);

            $table->foreign('machine_event_id')
                ->references('id')
                ->on('su_machine_events')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->dropForeign(['plot_id']);

            $table->foreign('plot_id')
                ->references('gid')
                ->on('su_satellite_plots')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down() {}
}
