<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePestsDiseasesPhenophases extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('pests_diseases_phenophases', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('pests_diseases_id')->unsigned();
            $table->integer('phenophases_id')->unsigned();

            $table->foreign('pests_diseases_id')->references('id')->on('pests_diseases');
            $table->foreign('phenophases_id')->references('id')->on('phenophases');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('pests_diseases_phenophases');
    }
}
