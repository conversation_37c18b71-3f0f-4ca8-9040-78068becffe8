<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDiseases extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('diseases', function (Blueprint $table) {
            $table->integer('id')->unsigned()->nullable(false);

            $table->foreign('id')->references('id')->on('pests_diseases');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('diseases');
    }
}
