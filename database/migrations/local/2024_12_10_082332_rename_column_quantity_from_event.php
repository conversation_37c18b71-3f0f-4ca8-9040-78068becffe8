<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameColumnQuantityFromEvent extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('su_machine_task_products', function (Blueprint $table) {
            $table->renameColumn('quantity_from_event', 'has_event_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('su_machine_task_products', function (Blueprint $table) {
            $table->renameColumn('has_event_data', 'quantity_from_event');
        });
    }
}
