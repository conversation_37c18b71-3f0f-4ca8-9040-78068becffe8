<?php

use App\Models\FarmTrackReportsLog;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateConstraintFarmTrackReportsLogStateCheck extends Migration
{
    private $processing = FarmTrackReportsLog::PROCESSING;
    private $failure = FarmTrackReportsLog::FAILURE;
    private $success = FarmTrackReportsLog::SUCCESS;
    private $noData = FarmTrackReportsLog::NO_DATA;

    /**
     * Run the migrations.
     */
    public function up()
    {
        DB::statement('ALTER TABLE farm_track_reports_log DROP CONSTRAINT IF EXISTS farm_track_reports_log_state_check');
        DB::statement("ALTER TABLE public.farm_track_reports_log
            ADD CONSTRAINT farm_track_reports_log_state_check
            CHECK (((state)::text = ANY (ARRAY[
                ('{$this->success}'::character varying)::text,
                ('{$this->failure}'::character varying)::text,
                ('{$this->processing}'::character varying)::text,
                ('{$this->noData}'::character varying)::text
            ])))
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        DB::statement('ALTER TABLE farm_track_reports_log DROP CONSTRAINT IF EXISTS farm_track_reports_log_state_check');

        DB::statement("ALTER TABLE public.farm_track_reports_log
            ADD CONSTRAINT farm_track_reports_log_state_check
            CHECK (((state)::text = ANY (ARRAY[
                ('{$this->success}'::character varying)::text,
                ('{$this->failure}'::character varying)::text,
                ('{$this->processing}'::character varying)::text
            ])))
        ");
    }
}
