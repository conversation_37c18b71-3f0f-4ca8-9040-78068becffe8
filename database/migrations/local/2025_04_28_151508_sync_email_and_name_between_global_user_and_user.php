<?php

use App\Models\Country;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class SyncEmailAndNameBetweenGlobalUserAndUser extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        $mainDatabase = Config::get('database.connections.main');
        $connectionString = "host={$mainDatabase['host']} user={$mainDatabase['username']} dbname={$mainDatabase['database']} password={$mainDatabase['password']}";
        $countryIsoAlpha2Code = strtoupper(DB::getName());
        $country = Country::where('iso_alpha_2_code', $countryIsoAlpha2Code)->get()->first();
        $serviceProviders = $country->serviceProviders()->get();

        $localUsers = DB::select('
            SELECT 
                su_users.id AS id,
                su_users.name AS name,
                su_users.email AS email
            FROM su_users
        ');

        // Iterate over the local users and update the gs main users
        $serviceProviders->each(function ($serviceProvider) use ($connectionString, $localUsers) {
            foreach ($localUsers as $localUser) {
                DB::statement("
                    SELECT dblink_exec('{$connectionString}', '
                        UPDATE su_users
                        SET name = ''{$localUser->name}'',
                            email = ''{$localUser->email}''
                        WHERE old_id = {$localUser->id} and service_provider_id = {$serviceProvider->id}
                    ')
                ");
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // No rollback logic needed as this is a one-time sync
    }
}
