<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePestsDiseases extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('pests_diseases', function (Blueprint $table) {
            $table->increments('id');
            $table->string('slug', 255)->nullable(false);
            $table->string('type')->nullable(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('pests_diseases');
    }
}
