<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ReplaceNikasWithEurofins extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Add the new key to the JSONB column for all records except where slug is 'agricost', 'agviser', or 'ans'
        DB::table('service_providers')->whereIn('slug', ['nikas'])->update([
            'name' => 'EUROFINS AGRO TESTING BULGARIA',
            'slug' => 'eurofins',
            'company_info' => DB::raw("JSON_BUILD_OBJECT(
                        'city', 'София',
                        'address', 'бул. Александър Малинов 6',
                        'manager', 'Артур Лайош Тернеш',
                        'website', 'www.eurofins-agro.bg',
                        'name_latin', 'EUROFINS AGRO TESTING BULGARIA',
                        'vat_number', '*********',
                        'postal_code', '1784',
                        'include_logo', false,
                        'name_cyrillic', 'ЮРОФИНС АГРО ТЕСТИНГ БЪЛГАРИЯ ЕООД',
                        'include_address', false,
                        'show_gs_branding', true,
                        'include_iso_certificates', true,
                        'soil_samples_header_title', 'protocol_title'
            )"),
            'logo' => '<svg xmlns="http://www.w3.org/2000/svg" id="Ebene_2" viewBox="0 0 129.53 25.67">
                        <defs>
                            <style>.cls-1{fill:#f7b473;}.cls-2{fill:#003883;}.cls-3{fill:#ee7d11;}.cls-4{fill:#9fa1c8;}.cls-5{fill:#5964a1;}</style>
                        </defs>
                        <g id="Calque_1">
                            <g>
                            <path class="cls-4" d="M7.79,15.52c0-1.55,1.26-2.81,2.81-2.81s2.81,1.26,2.81,2.81-1.26,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <path class="cls-2" d="M6.14,22.87c0-3.07-.26-3.33-3.33-3.33-1.56,0-2.81-1.25-2.81-2.81s1.26-2.81,2.81-2.81,2.81,1.26,2.81,2.81c0,3.07,.26,3.33,3.33,3.33,1.56,0,2.81,1.25,2.81,2.81s-1.25,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <path class="cls-5" d="M19.24,16.57c0-3.07-.26-3.33-3.33-3.33-1.56,0-2.81-1.25-2.81-2.81s1.25-2.81,2.81-2.81,2.81,1.25,2.81,2.81c0,3.07,.26,3.33,3.33,3.33,1.56,0,2.81,1.25,2.81,2.81s-1.25,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <path class="cls-3" d="M20.72,8.95c0-3.07-.26-3.33-3.33-3.33-1.56,0-2.81-1.25-2.81-2.81s1.25-2.81,2.81-2.81,2.81,1.25,2.81,2.81c0,3.07,.26,3.33,3.33,3.33,1.56,0,2.81,1.26,2.81,2.81s-1.25,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <path class="cls-3" d="M13.48,22.28c0-1.56,1.25-2.81,2.81-2.81s2.81,1.25,2.81,2.81-1.25,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <path class="cls-1" d="M1.21,9.24c0-1.55,1.25-2.81,2.81-2.81s2.81,1.25,2.81,2.81-1.25,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <path class="cls-2" d="M6.53,3.51c0-1.56,1.26-2.81,2.81-2.81s2.81,1.25,2.81,2.81-1.25,2.81-2.81,2.81-2.81-1.25-2.81-2.81"/>
                            <g>
                                <path class="cls-2" d="M44.21,21.14c-1.18,.48-2.23,.76-3.92,.76-3.72,0-6.71-2.2-6.71-7.61,0-3.72,1.49-6.43,5.72-6.43s5.16,2.9,5.16,5.5h-8.03c0,2.65,1.18,7.05,5.21,7.05,.87,0,1.72-.17,2.56-.56v1.3Zm-2.54-9.07c0-1.04-.37-3.02-2.48-3.02s-2.62,2.25-2.68,3.02h5.16Z"/>
                                <path class="cls-2" d="M56.19,18.4h-.06c-.45,.99-1.72,3.49-4.68,3.49-2.37,0-3.38-1.97-3.38-3.92V8.17h2.85v8.4c0,1.69,.25,3.21,1.69,3.21,2.82,0,3.61-4.4,3.61-6.06v-5.55h2.85v13.42h-2.88v-3.18Z"/>
                                <path class="cls-2" d="M63.69,8.17h2.85v3.41h.06c.9-1.8,1.89-3.52,4.34-3.72v2.96c-2.73,.08-4.4,1.01-4.4,4.62v6.14h-2.85V8.17Z"/>
                                <path class="cls-2" d="M72.57,14.88c0-4.54,1.97-7.02,5.92-7.02s5.95,2.48,5.95,7.02-2,7.02-5.95,7.02-5.92-2.48-5.92-7.02Zm5.92,5.83c2.54,0,2.85-3.38,2.85-5.83s-.31-5.83-2.85-5.83-2.82,3.38-2.82,5.83,.31,5.83,2.82,5.83Z"/>
                                <path class="cls-2" d="M89.08,9.64h-2.34v-1.47h2.34v-1.27c0-2.48,.59-5.07,5.1-5.07,1.16,0,2.17,.17,2.85,.31v1.94c-.59-.25-1.35-.62-2.51-.62-2.31,0-2.59,1.66-2.59,3.1v1.61h3.8v1.47h-3.8v11.95h-2.85V9.64Z"/>
                                <path class="cls-2" d="M98.58,2.14h3.07v2.82h-3.07V2.14Zm.11,6.03h2.85v13.42h-2.85V8.17Z"/>
                                <path class="cls-2" d="M106.33,8.17h2.85v3.18h.06c.45-.99,1.75-3.49,4.71-3.49,2.37,0,3.38,1.97,3.38,3.92v9.81h-2.85V13.19c0-1.69-.25-3.21-1.69-3.21-2.82,0-3.61,4.4-3.61,6.06v5.55h-2.85V8.17Z"/>
                                <path class="cls-2" d="M128.68,10.82c-.96-.65-2.03-1.32-3.38-1.32-.9,0-1.92,.39-1.92,1.41,0,2.42,6.14,2.65,6.14,6.93,0,2.88-2.25,4.06-5.1,4.06-1.72,0-2.9-.37-3.58-.59v-2.62c.82,.56,2.14,1.41,3.72,1.41s2.31-.85,2.31-1.69c0-2.37-6.06-2.85-6.06-6.68,0-2.03,1.35-3.86,4.71-3.86,1.44,0,2.68,.39,3.16,.54v2.42Z"/>
                            </g>
                            </g>
                        </g>
                        </svg>
                ',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Add the new key to the JSONB column for all records except where slug is 'agricost', 'agviser', or 'ans'
        DB::table('service_providers')->whereIn('slug', ['eurofins'])->update([
            'name' => 'NIK AgroService',
            'slug' => 'nikas',
            'company_info' => DB::raw("JSON_BUILD_OBJECT(
                        'city', 'София',
                        'address', 'бул. Александър Малинов 6',
                        'manager', 'Костадин Костадинов',
                        'website', 'www.nik.bg',
                        'name_latin', 'NIK Agro Service Ltd.',
                        'vat_number', '*********',
                        'postal_code', '1784',
                        'include_logo', false,
                        'name_cyrillic', 'НИК Агро Сървис ООД',
                        'include_address', false,
                        'show_gs_branding', true,
                        'include_iso_certificates', true,
                        'soil_samples_header_title', 'protocol_title'
            )"),
            'logo' => '<svg xmlns="http://www.w3.org/2000/svg" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" version="1.1" id="svg2" xml:space="preserve" width="249.33333" height="258.66666" viewBox="0 0 249.33333 258.66666" sodipodi:docname="Nik.agro.eps">
                        <metadata id="metadata8">
                            <rdf:RDF>
                            <cc:Work rdf:about="">
                                <dc:format>image/svg+xml</dc:format>
                                <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
                            </cc:Work>
                            </rdf:RDF>
                        </metadata>
                        <defs id="defs6"/>
                        <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="640" inkscape:window-height="480" id="namedview4"/>
                        <g id="g10" inkscape:groupmode="layer" inkscape:label="ink_ext_XXXXXX" transform="matrix(1.3333333,0,0,-1.3333333,0,258.66667)">
                            <g id="g12" transform="scale(0.1)">
                            <path d="m 291.836,1011.81 h -15.914 v 213.42 c 0,45.16 -2.418,74.32 -7.117,87.63 -4.707,13.18 -12.504,23.52 -23.117,30.91 -10.622,7.26 -23.387,11.03 -38.305,11.03 -19.219,0 -36.422,-5.25 -51.613,-15.73 -15.184,-10.62 -25.668,-24.46 -31.309,-41.8 -5.648,-17.33 -8.469,-49.32 -8.469,-96.09 V 1011.81 H 5.38672 v 418.11 H 108.063 v -61.42 c 36.55,47.31 82.515,70.96 137.89,70.96 24.328,0 46.774,-4.43 66.93,-13.17 20.16,-8.87 35.484,-20.02 45.828,-33.73 10.348,-13.71 17.605,-29.16 21.641,-46.51 4.16,-17.33 6.179,-42.06 6.179,-74.32 v -20.78 C 353.113,1176 321.313,1096.14 291.836,1011.81" style="fill:#100f0e;fill-opacity:1;fill-rule:evenodd;stroke:none" id="path14"/>
                            <path d="m 291.836,1011.81 h -15.914 v 213.42 c 0,45.16 -2.418,74.32 -7.117,87.63 -4.707,13.18 -12.504,23.52 -23.117,30.91 -10.622,7.26 -23.387,11.03 -38.305,11.03 -19.219,0 -36.422,-5.25 -51.613,-15.73 -15.184,-10.62 -25.668,-24.46 -31.309,-41.8 -5.648,-17.33 -8.469,-49.32 -8.469,-96.09 V 1011.81 H 5.38672 v 418.11 H 108.063 v -61.42 c 36.55,47.31 82.515,70.96 137.89,70.96 24.328,0 46.774,-4.43 66.93,-13.17 20.16,-8.87 35.484,-20.02 45.828,-33.73 10.348,-13.71 17.605,-29.16 21.641,-46.51 4.16,-17.33 6.179,-42.06 6.179,-74.32 v -20.78 C 353.113,1176 321.313,1096.14 291.836,1011.81 Z" style="fill:none;stroke:#100f0e;stroke-width:2.16;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:22.9256;stroke-dasharray:none;stroke-opacity:1" id="path16"/>
                            <path d="m 509.051,1011.81 v 224.77 c 30.804,68.98 62.297,133.61 94.16,193.34 h 16.449 v -418.11 z m 0,367.28 v 50.83 h 27.312 c -9.172,-16.61 -18.277,-33.51 -27.312,-50.83" style="fill:#100f0e;fill-opacity:1;fill-rule:nonzero;stroke:none" id="path18"/>
                            <path d="m 718.176,1011.81 v 577.24 H 828.918 V 1011.81 H 718.176" style="fill:#100f0e;fill-opacity:1;fill-rule:nonzero;stroke:none" id="path20"/>
                            <path d="M 125.551,1.55078 C 305.859,1262.71 953.77,2323.7 1196.3,1713.86 1064.94,2398.75 195.395,1409.5 125.551,1.55078" style="fill:#ed1c24;fill-opacity:1;fill-rule:evenodd;stroke:none" id="path22"/>
                            <path d="m 536.859,1454.13 c 45.981,-15.18 95.579,9.8 110.762,55.77 15.18,45.98 -9.797,95.58 -55.773,110.76 -45.977,15.18 -95.575,-9.8 -110.758,-55.77 -15.176,-45.98 9.793,-95.58 55.769,-110.76" style="fill:#ed1c24;fill-opacity:1;fill-rule:evenodd;stroke:none" id="path24"/>
                            <path d="m 1061.27,1113.38 c 31.58,-10.43 65.66,6.73 76.08,38.32 10.44,31.58 -6.73,65.65 -38.31,76.08 -31.59,10.43 -65.66,-6.73 -76.09,-38.32 -10.44,-31.58 6.73,-65.66 38.32,-76.08" style="fill:#ed1c24;fill-opacity:1;fill-rule:evenodd;stroke:none" id="path26"/>
                            <path d="m 949.445,1168.01 147.995,256.64 H 978.234 L 830.129,1168.57 h -0.641 l 0.321,-0.56 -0.321,-0.55 h 0.641 L 978.234,911.383 H 1097.44 L 949.445,1168.01" style="fill:#ed1c24;fill-opacity:1;fill-rule:nonzero;stroke:none" id="path28"/>
                            <path d="m 1232.68,1129.96 c 22.43,-7.41 46.62,4.78 54.03,27.21 7.4,22.42 -4.78,46.62 -27.21,54.03 -22.43,7.4 -46.62,-4.78 -54.03,-27.21 -7.41,-22.42 4.78,-46.63 27.21,-54.03" style="fill:#ed1c24;fill-opacity:1;fill-rule:evenodd;stroke:none" id="path30"/>
                            <path d="m 1392.62,1138.25 c 17.85,-5.89 37.11,3.8 43,21.66 5.9,17.84 -3.8,37.1 -21.65,43 -17.85,5.89 -37.11,-3.81 -43,-21.66 -5.9,-17.85 3.8,-37.11 21.65,-43" style="fill:#ed1c24;fill-opacity:1;fill-rule:evenodd;stroke:none" id="path32"/>
                            <path d="m 497.348,556.047 65.996,171.801 h 24.48 l 70.324,-171.801 h -25.921 l -20.039,52.043 h -71.84 l -18.84,-52.043 z m 49.558,70.558 h 58.282 l -17.961,47.563 c -5.438,14.437 -9.52,26.32 -12.161,35.641 -2.199,-11.039 -5.281,-21.961 -9.281,-32.84 z m 122.84,-80.878 20.52,-3.039 c 0.843,-6.321 3.242,-10.918 7.121,-13.84 5.242,-3.879 12.402,-5.84 21.441,-5.84 9.801,0 17.32,1.961 22.641,5.84 5.316,3.918 8.879,9.402 10.758,16.398 1.121,4.32 1.601,13.32 1.523,27.082 -9.203,-10.84 -20.684,-16.281 -34.441,-16.281 -17.121,0 -30.36,6.16 -39.719,18.519 -9.363,12.321 -14.086,27.161 -14.086,44.403 0,11.879 2.16,22.836 6.441,32.879 4.321,10.039 10.563,17.8 18.723,23.281 8.16,5.437 17.762,8.199 28.758,8.199 14.679,0 26.801,-5.961 36.32,-17.84 v 15 h 19.481 V 572.926 c 0,-19.36 -2,-33.117 -5.918,-41.199 -3.961,-8.079 -10.2,-14.481 -18.762,-19.161 -8.559,-4.679 -19.078,-7.039 -31.598,-7.039 -14.84,0 -26.844,3.36 -35.961,10.039 -9.16,6.68 -13.562,16.723 -13.242,30.161 z m 17.442,74.761 c 0,-16.32 3.242,-28.238 9.761,-35.718 6.481,-7.524 14.598,-11.243 24.36,-11.243 9.675,0 17.8,3.719 24.359,11.161 6.559,7.48 9.84,19.16 9.84,35.121 0,15.238 -3.36,26.718 -10.121,34.437 -6.758,7.723 -14.918,11.602 -24.438,11.602 -9.359,0 -17.324,-3.801 -23.922,-11.442 -6.558,-7.597 -9.839,-18.918 -9.839,-33.918 z m 119.679,-64.441 v 124.441 h 18.961 v -18.843 c 4.84,8.804 9.32,14.64 13.438,17.441 4.082,2.805 8.601,4.242 13.523,4.242 7.121,0 14.32,-2.281 21.68,-6.801 l -7.281,-19.601 c -5.118,3.082 -10.282,4.601 -15.442,4.601 -4.637,0 -8.758,-1.402 -12.437,-4.16 -3.68,-2.801 -6.282,-6.64 -7.84,-11.562 -2.36,-7.477 -3.52,-15.676 -3.52,-24.598 v -65.16 z m 72.281,62.242 c 0,23.039 6.399,40.117 19.243,51.199 10.675,9.2 23.718,13.84 39.117,13.84 17.121,0 31.121,-5.601 41.961,-16.84 10.879,-11.199 16.277,-26.679 16.277,-46.441 0,-16.039 -2.394,-28.641 -7.199,-37.801 -4.797,-9.199 -11.801,-16.32 -20.957,-21.398 -9.203,-5.082 -19.199,-7.602 -30.082,-7.602 -17.399,0 -31.481,5.563 -42.238,16.762 -10.762,11.16 -16.122,27.242 -16.122,48.281 z m 21.68,0 c 0,-15.961 3.481,-27.883 10.438,-35.805 6.964,-7.957 15.722,-11.918 26.242,-11.918 10.48,0 19.203,4 26.16,11.961 6.918,7.961 10.402,20.121 10.402,36.442 0,15.398 -3.484,27.078 -10.48,35 -7.004,7.918 -15.684,11.879 -26.082,11.879 -10.52,0 -19.278,-3.961 -26.242,-11.84 -6.957,-7.883 -10.438,-19.801 -10.438,-35.719 z m 181.322,-7.043 21.44,1.883 c 1,-8.602 3.36,-15.641 7.08,-21.16 3.72,-5.524 9.48,-9.961 17.28,-13.36 7.8,-3.402 16.6,-5.082 26.36,-5.082 8.68,0 16.32,1.282 22.96,3.84 6.64,2.598 11.6,6.121 14.84,10.602 3.24,4.519 4.88,9.398 4.88,14.719 0,5.402 -1.56,10.082 -4.72,14.121 -3.12,4.039 -8.28,7.398 -15.44,10.121 -4.64,1.797 -14.8,4.597 -30.6,8.398 -15.76,3.801 -26.84,7.36 -33.16,10.719 -8.2,4.281 -14.32,9.641 -18.36,16 -4,6.359 -6,13.48 -6,21.402 0,8.637 2.44,16.762 7.36,24.281 4.92,7.559 12.12,13.278 21.56,17.196 9.48,3.883 19.96,5.844 31.52,5.844 12.76,0 23.96,-2.043 33.72,-6.165 9.72,-4.078 17.2,-10.117 22.44,-18.078 5.2,-7.961 8.04,-17 8.44,-27.078 l -21.8,-1.64 c -1.2,10.839 -5.16,19.078 -11.92,24.601 -6.76,5.559 -16.72,8.317 -29.92,8.317 -13.76,0 -23.76,-2.52 -30.08,-7.559 -6.28,-5.039 -9.44,-11.121 -9.44,-18.203 0,-6.199 2.24,-11.277 6.68,-15.238 4.4,-4 15.8,-8.082 34.28,-12.239 18.48,-4.203 31.16,-7.84 38.04,-10.961 10,-4.636 17.4,-10.48 22.16,-17.519 4.76,-7.082 7.12,-15.242 7.12,-24.442 0,-9.16 -2.6,-17.761 -7.84,-25.839 -5.24,-8.079 -12.76,-14.36 -22.56,-18.879 -9.8,-4.481 -20.84,-6.719 -33.08,-6.719 -15.56,0 -28.6,2.238 -39.08,6.797 -10.52,4.519 -18.76,11.32 -24.76,20.441 -5.96,9.082 -9.12,19.403 -9.4,30.879 z m 250.28,-15.121 21.8,-2.676 c -3.44,-12.761 -9.8,-22.64 -19.08,-29.683 -9.32,-7 -21.2,-10.52 -35.64,-10.52 -18.2,0 -32.64,5.602 -43.28,16.801 -10.68,11.199 -16,26.922 -16,47.16 0,20.961 5.4,37.199 16.16,48.758 10.8,11.562 24.76,17.363 41.96,17.363 16.64,0 30.24,-5.68 40.76,-17 10.56,-11.32 15.84,-27.281 15.84,-47.801 0,-1.281 -0.04,-3.16 -0.12,-5.64 h -92.8 c 0.76,-13.68 4.64,-24.16 11.6,-31.399 6.96,-7.281 15.6,-10.922 26,-10.922 7.72,0 14.32,2.039 19.8,6.122 5.48,4.039 9.8,10.519 13,19.437 z m -69.24,34.121 h 69.48 c -0.92,10.442 -3.6,18.32 -7.96,23.524 -6.72,8.117 -15.44,12.195 -26.12,12.195 -9.68,0 -17.84,-3.238 -24.44,-9.719 -6.6,-6.48 -10.24,-15.16 -10.96,-26 z m 117.32,-74.199 v 124.441 h 18.96 v -18.843 c 4.84,8.804 9.32,14.64 13.44,17.441 4.08,2.805 8.6,4.242 13.52,4.242 7.12,0 14.32,-2.281 21.68,-6.801 l -7.28,-19.601 c -5.12,3.082 -10.28,4.601 -15.44,4.601 -4.64,0 -8.76,-1.402 -12.44,-4.16 -3.68,-2.801 -6.28,-6.64 -7.84,-11.562 -2.36,-7.477 -3.52,-15.676 -3.52,-24.598 v -65.16 z m 114.72,0 -47.36,124.441 h 22.28 l 26.72,-74.519 c 2.88,-8.043 5.52,-16.403 7.96,-25.082 1.88,6.558 4.48,14.441 7.84,23.679 l 27.68,75.922 h 21.68 l -47.12,-124.441 z m 85.52,147.519 v 24.282 h 21.12 v -24.282 z m 0,-147.519 v 124.441 h 21.12 V 556.047 Z m 134.44,45.601 20.72,-2.722 c -2.24,-14.281 -8.04,-25.481 -17.4,-33.559 -9.32,-8.082 -20.8,-12.121 -34.4,-12.121 -17,0 -30.72,5.563 -41.04,16.684 -10.36,11.117 -15.56,27.078 -15.56,47.879 0,13.437 2.24,25.199 6.68,35.281 4.48,10.078 11.24,17.637 20.36,22.68 9.08,5.039 19,7.558 29.68,7.558 13.52,0 24.6,-3.441 33.16,-10.281 8.6,-6.84 14.12,-16.52 16.56,-29.121 l -20.52,-3.16 c -1.96,8.363 -5.4,14.64 -10.36,18.882 -4.96,4.2 -10.96,6.317 -18,6.317 -10.64,0 -19.28,-3.797 -25.92,-11.438 -6.64,-7.597 -9.96,-19.64 -9.96,-36.121 0,-16.718 3.2,-28.879 9.64,-36.476 6.4,-7.563 14.76,-11.364 25.08,-11.364 8.28,0 15.16,2.559 20.72,7.641 5.56,5.082 9.08,12.879 10.56,23.441 z m 123.97,-5.523 21.79,-2.676 c -3.43,-12.761 -9.8,-22.64 -19.08,-29.683 -9.32,-7 -21.2,-10.52 -35.64,-10.52 -18.19,0 -32.64,5.602 -43.28,16.801 -10.68,11.199 -16,26.922 -16,47.16 0,20.961 5.4,37.199 16.16,48.758 10.8,11.562 24.76,17.363 41.96,17.363 16.64,0 30.24,-5.68 40.76,-17 10.56,-11.32 15.84,-27.281 15.84,-47.801 0,-1.281 -0.04,-3.16 -0.12,-5.64 h -92.8 c 0.76,-13.68 4.64,-24.16 11.6,-31.399 6.96,-7.281 15.6,-10.922 26.01,-10.922 7.71,0 14.31,2.039 19.8,6.122 5.47,4.039 9.79,10.519 13,19.437 z m -69.25,34.121 h 69.48 c -0.92,10.442 -3.6,18.32 -7.96,23.524 -6.72,8.117 -15.44,12.195 -26.12,12.195 -9.68,0 -17.84,-3.238 -24.43,-9.719 -6.61,-6.48 -10.25,-15.16 -10.97,-26" style="fill:#231f20;fill-opacity:1;fill-rule:nonzero;stroke:none" id="path34"/>
                            </g>
                        </g>
                        </svg>
            ',
        ]);
    }
}
