<?php

use Database\Seeders\CouchDBConfigSeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnCouchdbConfig extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        if (!Schema::hasColumn('service_providers', 'couchdb_config')) {
            Schema::table('service_providers', function (Blueprint $table) {
                $table->jsonb('couchdb_config')->nullable();
            });

            $seeder = new CouchDBConfigSeeder();
            $seeder->run();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('service_providers', function (Blueprint $table) {
            $table->dropColumn('couchdb_config');
        });
    }
}
