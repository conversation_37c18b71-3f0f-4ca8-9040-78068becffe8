<?php

use Illuminate\Database\Migrations\Migration;

class AddIncludeIsoCertificatesToServiceProvidersCompanyInfo extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Add the new key to the JSONB column for all records except where slug is 'agricost', 'agviser', or 'ans'
        DB::table('service_providers')->whereNotIn('slug', ['agricost', 'agviser', 'ans'])->update([
            'company_info' => DB::raw("jsonb_set(company_info, '{include_iso_certificates}', 'true'::jsonb)"),
        ]);

        // Add the new key to the JSONB column for records where slug is 'agricost', 'agviser', or 'ans'
        DB::table('service_providers')->whereIn('slug', ['agricost', 'agviser', 'ans'])->update([
            'company_info' => DB::raw("jsonb_set(company_info, '{include_iso_certificates}', 'false'::jsonb)"),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Remove the new key from the JSONB column
        DB::table('service_providers')->update([
            'company_info' => DB::raw("company_info - 'include_iso_certificates'"),
        ]);
    }
}
