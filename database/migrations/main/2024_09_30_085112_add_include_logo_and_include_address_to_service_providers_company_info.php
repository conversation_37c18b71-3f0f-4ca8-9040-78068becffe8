<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIncludeLogoAndIncludeAddressToServiceProvidersCompanyInfo extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add properties 'include_logo' and 'include_address' to the company_info column for 'agviser' with value 'false'
        DB::table('service_providers')->whereIn('slug', ['agviser'])->update([
            'company_info' => DB::raw("
                jsonb_set(company_info, '{include_logo}', 'false'::jsonb) ||
                jsonb_set(company_info, '{include_address}', 'false'::jsonb)
                
            "),
        ]);

        // Add properties 'include_logo' and 'include_address' to the company_info column for all records except 'agviser' with value 'true'
        DB::table('service_providers')->whereNotIn('slug', ['agviser'])->update([
            'company_info' => DB::raw("
                jsonb_set(company_info, '{include_logo}', 'true'::jsonb) ||
                jsonb_set(company_info, '{include_address}', 'true'::jsonb)
                
            "),
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove the new key from the JSONB column
        DB::table('service_providers')->update([
            'company_info' => DB::raw("(company_info - 'include_logo')::jsonb - 'include_address'"),
        ]);
    }
}
