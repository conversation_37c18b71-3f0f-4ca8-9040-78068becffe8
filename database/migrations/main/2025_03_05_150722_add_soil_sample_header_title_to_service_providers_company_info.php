<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddSoilSampleHeaderTitleToServiceProvidersCompanyInfo extends Migration
{
    public function up()
    {
        DB::table('service_providers')->whereNotIn('slug', ['agviser'])->update([
            'company_info' => DB::raw("jsonb_set(company_info, '{soil_samples_header_title}', '\"protocol_title\"'::jsonb)"),
        ]);

        DB::table('service_providers')->whereIn('slug', ['agviser'])->update([
            'company_info' => DB::raw("jsonb_set(company_info, '{soil_samples_header_title}', '\"report_title\"'::jsonb)"),
        ]);
    }

    public function down()
    {
        DB::table('service_providers')->update([
            'company_info' => DB::raw("company_info - 'soil_samples_header_title'"),
        ]);
    }
}
