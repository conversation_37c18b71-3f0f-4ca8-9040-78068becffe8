apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME
  name: REPLACE_CONTAINER_NAME
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: REPLACE_CONTAINER_NAME
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO:REPLACE_TAG
        name: REPLACE_CONTAINER_NAME
        readinessProbe:
            exec:
                command:
                    - php-fpm-healthcheck # a simple ping since this means it's ready to handle traffic
            initialDelaySeconds: 5
            periodSeconds: 5
        livenessProbe:
            exec:
                command:
                    - php-fpm-healthcheck
                    - --listen-queue=10 # fails if there are more than 10 processes waiting in the fpm queue
            initialDelaySeconds: 240
            periodSeconds: 10
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: qnap
          mountPath: /home/<USER>/qnap_storage
        - name: qnap2
          mountPath: /home/<USER>/qnap_storage2
        - name: maps
          mountPath: /home/<USER>/maps
        - name: envfiles
          mountPath: /usr/local/etc/php/php.ini
          subPath: php.ini
          readOnly: false
      volumes:
      - name: qnap
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE
      - name: qnap2
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE2
      - name: maps
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPS
      - name: envfiles
        configMap:
          name: REPLACE_CONFIG_MAP_FILES
      - name: secret
        secret:
          secretName: gs-jwt
      imagePullSecrets:
      - name: docker-technofarm