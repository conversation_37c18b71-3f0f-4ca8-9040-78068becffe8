apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME-scheduler
  name: REPLACE_CONTAINER_NAME-scheduler
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME-scheduler
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: REPLACE_CONTAINER_NAME-scheduler
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO:REPLACE_TAG
        name: REPLACE_CONTAINER_NAME-scheduler
        env:
        - name: CONTAINER_ROLE
          value: "scheduler"
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: qnap
          mountPath: /home/<USER>/qnap_storage
        - name: qnap2
          mountPath: /home/<USER>/qnap_storage2
        - name: maps
          mountPath: /home/<USER>/maps
        - name: envfiles
          mountPath: /usr/local/etc/php/php.ini
          subPath: php.ini
          readOnly: false
      volumes:
      - name: qnap
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE
      - name: qnap2
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE2
      - name: maps
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPS
      - name: envfiles
        configMap:
          name: REPLACE_CONFIG_MAP_FILES
      - name: secret
        secret:
          secretName: gs-jwt
      imagePullSecrets:
      - name: docker-technofarm