apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME-nginx
  name: REPLACE_CONTAINER_NAME-nginx
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME-nginx
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: REPLACE_CONTAINER_NAME-nginx
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO-nginx:REPLACE_TAG
        name: REPLACE_CONTAINER_NAME-nginx
        livenessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 3
          periodSeconds: 3
        readinessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 1
          periodSeconds: 5
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: qnap
          mountPath: /home/<USER>/qnap_storage
        - name: qnap2
          mountPath: /home/<USER>/qnap_storage2
        - name: maps
          mountPath: /home/<USER>/maps
        - name: envfiles
          mountPath: /usr/local/etc/php/php.ini
          subPath: php.ini
          readOnly: false
      volumes:
      - name: qnap
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE
      - name: qnap2
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE2
      - name: maps
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPS
      - name: envfiles
        configMap:
          name: REPLACE_CONFIG_MAP_FILES
      imagePullSecrets:
      - name: docker-technofarm