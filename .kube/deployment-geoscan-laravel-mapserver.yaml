apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: REPLACE_CONTAINER_NAME-mapserver
  name: REPLACE_CONTAINER_NAME-mapserver
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME-mapserver
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: REPLACE_CONTAINER_NAME-mapserver
    spec:
      serviceAccount: bitbucket
      containers:
      - image: technofarm/mapserver:7.6
        name: REPLACE_CONTAINER_NAME-mapserver
        # readinessProbe:
        #   exec:
        #     command:
        #     - curl
        #     - -I
        #     - localhost:5000
        #   initialDelaySeconds: 1
        #   periodSeconds: 5
        # livenessProbe:
        #   exec:
        #     command:
        #     - curl
        #     - -I
        #     - localhost:5000
        #   failureThreshold: 3
        #   initialDelaySeconds: 5
        #   periodSeconds: 5
        #   successThreshold: 1
        #   timeoutSeconds: 10
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: qnap
          mountPath: /home/<USER>/qnap_storage
        - name: qnap2
          mountPath: /home/<USER>/qnap_storage2
        - name: maps
          mountPath: /home/<USER>/maps
      volumes:
      - name: qnap
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE
      - name: qnap2
        nfs:
          server: REPLACE_QNAP_NFS_SERVER
          path: REPLACE_NFS_DIR_STORAGE2
      - name: maps
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPS
      imagePullSecrets:
      - name: docker-technofarm