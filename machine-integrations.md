# Machine Integrations Documentation

## Overview

The GeoScan Laravel application features a comprehensive machine integration system that connects with external machine tracking platforms, primarily **Wialon**, to synchronize and manage agricultural machine data. The system handles machine units (tractors, harvesters, sprayers), implements (attachments), events (work activities), and tasks (planned operations).

## Architecture Overview

The machine integration system follows a service-oriented architecture with the following key components:

- **Integration Management**: Handles connections to external platforms
- **Data Synchronization**: Pulls machine data from external APIs
- **Event Processing**: Processes machine work events and activities
- **Task Management**: Manages planned and executed machine tasks
- **Reporting**: Generates reports and analytics from machine data

## Core Models

### 1. Integration (`App\Models\Integration`)
**Table**: `su_integration`

Manages connections to external machine tracking platforms.

**Key Fields**:
- `token`: Authentication token for external API
- `organization_id`: Associated organization
- `contract_id`: Contract identifier
- `package_slug_short`: Package type (FT, IM)
- `integration_address`: Reference to integration address
- `status`: Active/Inactive

**Constants**:
- `ACTIVE = 'Active'`
- `INACTIVE = 'Inactive'`
- `PACKAGE_SLUG_SHORT_FT = 'FT'` (Farm Track)
- `PACKAGE_SLUG_SHORT_IM = 'IM'` (Implement Management)

### 2. MachineUnit (`App\Models\MachineUnit`)
**Table**: `su_machine_units`

Represents individual machines (tractors, harvesters, sprayers).

**Key Fields**:
- `organization_id`: Owner organization
- `integration_id`: Associated integration
- `name`: Machine name
- `wialon_unit_imei`: Unique identifier from Wialon
- `type`: Machine type enum (Tractor, Harvester, Sprayer, Car, Truck)
- `last_communication`: Last communication timestamp
- `last_position`: PostGIS geometry for last known position
- `wialon_unit_id`: Wialon system ID

### 3. MachineImplement (`App\Models\MachineImplement`)
**Table**: `su_machines_implements`

Represents machine attachments/implements.

**Key Fields**:
- `organization_id`: Owner organization
- `integration_id`: Associated integration
- `wialon_unit_id`: Wialon system ID
- `name`: Implement name
- `width`: Working width in meters
- `status`: Active/Inactive

**Status Constants**:
- `STATUS_ACTIVE = 'Active'`
- `STATUS_INACTIVE = 'Inactive'`

### 4. MachineEvent (`App\Models\MachineEvent`)
**Table**: `su_machine_events`

Records actual machine work activities.

**Key Fields**:
- `plot_id`: Associated field/plot
- `machine_id`: Machine unit ID
- `machine_implement_id`: Implement used
- `date`: Work date
- `start_date`/`end_date`: Activity timestamps
- `type`: Work/Transportation/WorkOutsidePlot
- `stage`: Proposed/Approved
- `geom_cultivated`: PostGIS geometry of worked area
- `geom_track`: PostGIS geometry of machine track
- `work_operation_ids`: Array of work operation IDs
- `driver`: Driver name

### 5. MachineTask (`App\Models\MachineTask`)
**Table**: `su_machine_tasks`

Represents planned machine operations.

**Key Fields**:
- `organization_id`: Owner organization
- `plot_id`: Target field/plot
- `machine_unit_id`: Assigned machine
- `machine_implement_id`: Required implement
- `state`: Planned/Scheduled/Ongoing/Done/Canceled
- `start_date`/`end_date`: Planned timing
- `work_operation_ids`: Array of planned operations
- `guidance_line_ids`: GPS guidance lines
- `vra_order_ids`: Variable rate application orders

## Key Services

### 1. WialonService (`App\Services\Wialon\WialonService`)

Core service for Wialon API communication.

**Key Methods**:
- `login($token, $baseURL)`: Authenticate with Wialon
- `makeRequest($svcType, $jsonParams)`: Generic API request
- `searchItems($itemsType, $propName, $propValueMask, $flags)`: Search for items
- `createUnitGroup($wialonUserId, $groupName, $wialonUnitIds)`: Create unit groups

**Item Types**:
- `ITEMS_TYPE_UNIT = 'avl_unit'`: Machine units
- `ITEMS_TYPE_RESOURCE = 'avl_resource'`: Resources (implements)

### 2. MachineUnitService (`App\Services\Machine\MachineUnitService`)

Manages machine unit synchronization and operations.

**Key Methods**:
- `syncUnitsByIntegration(Integration $integration)`: Sync units from Wialon
- `getUnitsByIntegrationUrlAndToken($url, $token)`: Fetch units from API
- `storeCurrentMachineReport($serverName, $tmpTableName, $organizationId)`: Process current machine data

### 3. MachineImplementService (`App\Services\Implement\MachineImplementService`)

Manages implement synchronization.

**Key Methods**:
- `syncImplementsByIntegration(Integration $integration)`: Sync implements
- `getImplementsByIntegrationUrlAndToken($url, $token)`: Fetch implements from API

### 4. MachineEventService (`App\Services\Machine\MachineEventService`)

Processes machine work events.

**Key Methods**:
- `storeMachineEventsReport($serverName, $tmpTableName, $organizationId)`: Store events from reports
- `deleteEvents($organizationId, $date, $wialonUnitId)`: Clean up old events
- `detachEventsFromTasks($organizationId, $date, $wialonUnitId)`: Unlink events from tasks

### 5. IntegrationService (`App\Services\Integration\IntegrationService`)

Manages integration lifecycle.

**Key Methods**:
- `createIntegration($integrationData, $machineUnitsData, $machineImplementsData)`: Create new integration
- `storeIntegration($integrationData)`: Store integration data
- `storeMachineUnits($integration, $machineUnitsData)`: Store machine units
- `storeMachineImplements($integration, $machineImplementsData)`: Store implements

### 6. Track Processing Services

#### MachineUnitService Track Methods
**Location**: `App\Services\Machine\MachineUnitService`

**Key Method**: `getMachineUnitTrack(MachineUnit $machineUnit, int $organizationId, string $from, string $to, string $format)`

**Process**:
1. Retrieves 'machine_track' report configuration
2. Sets time interval parameters (from/to timestamps)
3. Calls Wialon API for track data
4. Uses factory pattern to format output (GeoJSON/GPX)
5. Returns processed track data

**API Integration**:
- Endpoint: `GET /apigs/machine-units/{unit}/track/{format?}`
- Formats: `geojson` (default), `gpx`
- Time range: `from` and `to` parameters (timestamps)
- Authorization: User must have access to machine's organization

#### Track Format Factory
**Location**: `App\Classes\Machine\MachineUnitTrack\MachineUnitTrackFormatFactory`

**Supported Formats**:
- `MachineUnit::TRACK_FORMAT_GEOJSON` → `MachineUnitTrackGeoJsonFormatter`
- `MachineUnit::TRACK_FORMAT_GPX` → `MachineUnitTrackGPXFormatter`

**Usage Pattern**:
```php
$formatter = MachineUnitTrackFormatFactory::make($format);
return $formatter->format($machineUnitId, $reportData);
```

## API Endpoints

### Integration Management
- `POST /apigs/integration` - **Create new integration** (Main endpoint for inserting into su_integration)
- `PUT /apigs/integration/{integration}` - Update existing integration
- `GET /apigs/integration/contract/{contractId}` - Get integrations by contract
- `GET /apigs/integration/organization/{organizationId}` - Get integrations by organization
- `GET /apigs/integration/history/{integration}` - Get integration history

### Machine Units
- `GET /apigs/machine-units` - List machine units
- `GET /apigs/machine-units/sync` - Sync units from integration
- `GET /apigs/machine-units/types` - Get unit types
- `GET /apigs/machine-units/{unit}/track/{format?}` - Get unit track data
- `PUT /apigs/machine-units/{unit}` - Update unit
- `DELETE /apigs/machine-units/{unit}` - Delete unit

### Machine Implements
- `GET /apigs/machine-implements` - List implements
- `GET /apigs/machine-implements/sync` - Sync implements from integration
- `GET /apigs/machine-implements/statuses` - Get implement statuses
- `PUT /apigs/machine-implements/{implement}` - Update implement

### Machine Events & Data
- `GET /apigs/machine/map` - Get machines for map display
- `GET /apigs/machine/events` - List machine events
- `GET /apigs/machine/events/report` - Generate events report
- `POST /apigs/machine/events/report/{type}` - Export events report
- `GET /apigs/machine/events/drivers` - Get driver list
- `GET /apigs/machine/events/types` - Get event types

### Machine Tasks
- `GET /apigs/machine-tasks` - List machine tasks
- `POST /apigs/machine-tasks` - Create machine task
- `PUT /apigs/machine-tasks/{task}` - Update machine task

### Machine Track Processing
- `GET /apigs/machine-units/{unit}/track/{format?}` - Get unit track data
  - **Formats**: `geojson` (default), `gpx`
  - **Parameters**: `from`, `to` (timestamp range)
  - **Returns**: Processed track data with geometry, speed, fuel consumption

## Database Schema Analysis

Based on the complete database schema (`susi_main_v5.sql`), here's a detailed analysis of the integration system:

### Core Integration Tables

**su_integration**
```sql
- id (serial4, primary key)
- token (varchar(255), not null) - API authentication token
- organization_id (int4, not null, FK to su_organizations)
- contract_id (int4, not null) - Contract identifier
- package_id (int4, not null) - Package identifier
- package_slug_short (varchar(255), not null) - Package type (FT/IM)
- package_period (varchar(255), not null) - Subscription period
- integration_address (int4, not null) - FK to integration addresses
- status (varchar(255), default 'Active') - Active/Inactive
- created_at/updated_at (timestamps)
- remote_username (varchar(127)) - External system username
- remote_user_id (int4) - External system user ID
```

**su_integration_reports_types**
```sql
- id (serial4, primary key)
- name (varchar(63)) - Report type name (e.g., 'machine_events', 'implements_sync')
- execution (varchar(63)) - Execution type ('scheduled', 'on_request')
- period (varchar(63)) - Cron expression for scheduled reports
- params (json) - Report parameters and configuration
- url (varchar(255)) - Base URL for the integration
```

**su_integrations_reports** (Junction Table)
```sql
- id (serial4, primary key)
- integration_id (int8, FK to su_integration)
- integration_reports_types_id (int8, FK to su_integration_reports_types)
```

**farm_track_reports_log**
```sql
- id (bigserial, primary key)
- integration_id (int8, FK to su_integration)
- start_time (timestamp, not null)
- end_time (timestamp)
- request_parameters (json) - Parameters used for the report
- integration_reports_types_id (int8, FK to su_integration_reports_types)
- state (reports_log_state_enum) - processing/failure/success/no_data
- error (json) - Error details if failed
- tmp_table_name (varchar(63)) - Temporary table name for processing
```

### Machine-Related Tables

**su_machine_units**
```sql
- id (bigserial, primary key)
- organization_id (int8, not null, FK to su_organizations)
- integration_id (int4, FK to su_integration)
- name (varchar(255), not null)
- wialon_unit_imei (int8) - IMEI from Wialon system
- type (machine_unit_types_enum, not null) - Tractor/Harvester/Sprayer/Car/Truck
- last_communication (timestamp) - Last communication with tracking system
- last_position (geometry(point, 32635)) - PostGIS point geometry
- wialon_unit_id (int8) - Wialon system identifier
```

**su_machines_implements**
```sql
- id (bigserial, primary key)
- organization_id (int8, not null, FK to su_organizations)
- integration_id (int4, FK to su_integration)
- wialon_unit_id (int4) - Wialon system identifier
- name (varchar(255), not null)
- width (float8, not null) - Working width in meters
- status (machine_implement_status_enum, default 'Active') - Active/Inactive
```

**su_machines_implements_work_operations** (Junction Table)
```sql
- id (int8, primary key)
- implement_id (int8, FK to su_machines_implements)
- work_operation_id (int4, FK to su_work_operations)
```

**su_machine_events**
```sql
- id (bigserial, primary key)
- plot_id (int4, FK to su_satellite_plots)
- machine_id (int4, not null, FK to su_machine_units)
- machine_implement_id (int4, FK to su_machines_implements)
- date (date, not null) - Work date
- start_date/end_date (timestamp, not null) - Activity time range
- max_speed/avg_speed (int4, not null) - Speed metrics
- length_track (int4, not null) - Track length in meters
- fuel_consumed_driving/fuel_consumed_stay (int4, not null) - Fuel consumption
- type (proposed_events_types_enum, not null) - Work/Transportation/WorkOutsidePlot
- stage (proposed_events_stages_enum, not null) - Proposed/Approved
- duration/duration_stay (interval) - Time intervals
- geom_cultivated (geometry(geometry, 32635)) - Worked area geometry
- geom_track (geometry(geometry, 32635), not null) - Machine track geometry
- work_operation_ids (_int4, not null) - Array of work operation IDs
- driver (varchar(127)) - Driver name
- implement_width (float8) - Implement width used
```

**Indexes on su_machine_events:**
- `su_machine_events_machine_id_idx` (btree)
- `su_machine_events_machine_implement_id_idx` (btree)
- `su_machine_events_plot_id_idx` (btree)
- `su_machine_events_work_operation_ids_idx` (gin) - For array queries
- `su_machine_events_unique_fields` (unique) - Prevents duplicates

**su_machine_tasks**
```sql
- id (bigserial, primary key)
- organization_id (int4, not null, FK to su_organizations)
- plot_id (int4, FK to su_satellite_plots)
- machine_unit_id (int4, FK to su_machine_units)
- machine_event_id (int4, FK to su_machine_events) - Links to actual execution
- machine_implement_id (int4, FK to su_machines_implements)
- driver (varchar(255)) - Assigned driver
- covered_area (float8) - Planned/actual covered area
- start_date/end_date (timestamp, not null) - Planned time range
- completion_date (timestamp) - Actual completion time
- state (machine_task_state_enum) - Planned/Scheduled/Ongoing/Done (proposed)/Done (approved)/Canceled
- guidance_line_ids (_int4) - Array of guidance line IDs
- work_operation_ids (_int4, not null) - Array of work operation IDs
- vra_order_ids (_int4) - Array of VRA order IDs
- farm_year (int4, not null) - Farming year
- created_at/updated_at (timestamps)
```

**Indexes on su_machine_tasks:**
- `su_machine_tasks_guidance_line_ids_idx` (gin)
- `su_machine_tasks_work_operation_ids_idx` (gin)
- `su_machine_tasks_order_ids_idx` (gin)

### Supporting Tables

**su_machine_task_products**
```sql
- id (int4, primary key)
- task_id (int4, not null, FK to su_machine_tasks)
- product_id (int4, not null, FK to su_products)
- rate (numeric(16,8), not null) - Application rate
- value (float8, not null) - Total amount
- applied_area (numeric(9,3)) - Area where product was applied
- has_event_data (bool, default false) - Whether data comes from actual events
- pest_name/pest_application/pest_quarantine - Pest control specific fields
- created_at/updated_at (timestamps)
```

**su_current_machines_data**
```sql
- organization_id (int4, primary key, unique)
- geojson (json, not null) - Current machine positions as GeoJSON
```

**su_work_operations**
```sql
- id (int4, primary key)
- name (work_operations_types_enum, not null) - Operation type
- color (varchar(7), not null) - Hex color code for UI display
```

**su_products** (Machine Products/Materials)
```sql
- id (int4, primary key)
- name (varchar(160), not null) - Product name
- organization_id (int4, not null, FK to su_organizations)
- rate (varchar(255)) - Application rate description
- status (varchar(255), default 'Active') - Active/Inactive
- default_price (float8, default 0) - Default price
- unit_id (int4, not null, FK to units of measure)
- type_id (int4, default 1, FK to product types)
- application_rate (application_rates_enum) - per km/per dka/per ha/per 100km
- quarantine_period (int4) - Days before harvest
- information (text) - Additional product information
- created_at/updated_at (timestamps)
```

### Database Enums

**machine_unit_types_enum**
- `'Tractor'` - Agricultural tractors
- `'Harvester'` - Combine harvesters
- `'Sprayer'` - Crop spraying equipment
- `'Car'` - Regular vehicles
- `'Truck'` - Transport trucks

**machine_implement_status_enum**
- `'Active'` - Implement is available for use
- `'Inactive'` - Implement is disabled/unavailable

**machine_task_state_enum**
- `'Planned'` - Task is planned but not scheduled
- `'Scheduled'` - Task is scheduled for execution
- `'Ongoing'` - Task is currently being executed
- `'Done (proposed)'` - Task completed, awaiting approval
- `'Done (approved)'` - Task completed and approved
- `'Canceled'` - Task was canceled

**proposed_events_types_enum**
- `'Work'` - Actual field work (cultivation, spraying, etc.)
- `'Transportation'` - Moving between locations
- `'WorkOutsidePlot'` - Work performed outside registered plots

**proposed_events_stages_enum**
- `'Proposed'` - Event detected but not yet approved
- `'Approved'` - Event has been verified and approved

**reports_log_state_enum**
- `'processing'` - Report is currently being processed
- `'failure'` - Report processing failed
- `'success'` - Report processed successfully
- `'no_data'` - No data available for the requested period

**work_operations_types_enum**
- `'Unknown'` - Unidentified operation
- `'Fertilizing'` - Fertilizer application
- `'SowingAndPlanting'` - Seeding and planting operations
- `'CropProtection'` - Pesticide/herbicide application
- `'Tillage'` - Soil preparation and cultivation
- `'Harvesting'` - Crop harvesting
- `'Transport'` - Material transport
- `'Other'` - Other agricultural operations

**application_rates_enum**
- `'per km'` - Rate per kilometer
- `'per dka'` - Rate per decare (1000 m²)
- `'per ha'` - Rate per hectare
- `'per 100km'` - Rate per 100 kilometers

## Machine Track Processing System

The system includes a sophisticated machine track processing pipeline that transforms raw GPS tracking data into meaningful agricultural work events and analytics.

### Track Data Flow

1. **Raw Data Ingestion**
   - GPS coordinates from Wialon API
   - Speed, fuel consumption, timestamps
   - Machine state (Move/Stop)
   - Driver and implement information

2. **Data Transformation**
   - Convert coordinates to PostGIS geometry (SRID 4326 → 32635)
   - Parse JSON track data into structured format
   - Calculate time intervals and durations
   - Extract sensor data (fuel consumption)

3. **Track Processing**
   - Create line geometries from point sequences
   - Detect work patterns vs transportation
   - Calculate worked areas using implement width
   - Identify field boundaries and plot intersections

4. **Event Detection**
   - Classify activities as Work/Transportation/WorkOutsidePlot
   - Apply business rules for minimum duration and area
   - Detect clustering for work outside registered plots
   - Generate cultivated area geometries

### Track Processing Classes

#### BaseMachineUnitTrackFormatter
**Location**: `App\Classes\Machine\MachineUnitTrack\BaseMachineUnitTrackFormatter`

Core functionality for processing raw track data:

```php
public static function getUnitTrackRawDataQuery(string $machineTrackReportData): Builder
```

**Key Processing Steps**:
1. **JSON Parsing**: Extracts track points from JSON array
2. **Coordinate Transformation**: Creates PostGIS points from lat/lon
3. **Data Cleaning**: Filters valid sensor data
4. **Type Conversion**: Converts timestamps, speeds, fuel consumption

**Raw Data Structure**:
```json
{
  "coordinates": {"x": longitude, "y": latitude},
  "speed": "speed_value",
  "time": timestamp,
  "value": fuel_consumption,
  "wialon_unit_imei": unit_identifier,
  "sensor": "sensor_data"
}
```

#### MachineUnitTrackGeoJsonFormatter
**Location**: `App\Classes\Machine\MachineUnitTrack\MachineUnitTrackGeoJsonFormatter`

Converts track data to GeoJSON format:

**Processing Pipeline**:
1. **Line Creation**: `ST_MakeLine(array_agg(point))` - Creates track geometry
2. **Aggregation**: Groups points by unit, calculates statistics
3. **Distance Calculation**: Uses UTM projection for accurate measurements
4. **GeoJSON Generation**: Creates standard GeoJSON Feature format

**Output Format**:
```json
{
  "type": "Feature",
  "id": unit_id,
  "geometry": {...},
  "properties": {
    "unit_id": 123,
    "wialon_unit_imei": 456789,
    "max_speed": "45 km/h",
    "fuel_consumption": "12.5 l",
    "distance": "15.3 km"
  }
}
```

#### MachineUnitTrackGPXFormatter
**Location**: `App\Classes\Machine\MachineUnitTrack\MachineUnitTrackGPXFormatter`

Generates GPX (GPS Exchange Format) files:

**Features**:
- Standard GPX 1.1 format
- Track points with elevation (speed)
- Extensions for custom data
- XML structure with metadata

### Advanced Track Analysis (MachineEventService)

#### Complex Event Detection Algorithm

The `MachineEventService::getContentQuery()` method implements a sophisticated multi-stage analysis:

**Stage 1: Raw Point Processing**
```sql
-- Extract and clean raw tracking points
-- Apply coordinate transformations
-- Filter by date range and organization
```

**Stage 2: Plot Intersection Analysis**
```sql
-- Intersect track points with field boundaries
-- Identify which plots machines worked in
-- Calculate plot coverage percentages
```

**Stage 3: Line Geometry Creation**
```sql
-- Create line segments between consecutive points
-- Use window functions for temporal ordering
-- Calculate movement duration and distances
```

**Stage 4: Clustering and Grouping**
```sql
-- Apply DBSCAN clustering for work outside plots
-- Group activities by gaps (>30 minutes)
-- Identify continuous work sessions
```

**Stage 5: Work Classification**
```sql
-- Apply business rules for work detection:
-- Minimum duration: 5 minutes
-- Minimum coverage: 70% of plot perimeter
-- Area validation: envelope vs track length ratio
```

#### Work Detection Rules

1. **Field Work Detection**:
   - Track length > 70% of plot perimeter
   - Duration > 5 minutes
   - Area envelope ratio validates actual work

2. **Work Outside Plot Detection**:
   - Uses DBSCAN clustering (eps=100m, minpoints=10)
   - Duration > 5 minutes
   - Area coverage matches implement width

3. **Transportation Detection**:
   - Activities not meeting work criteria
   - Movement between locations
   - No significant area coverage

#### Cultivated Area Calculation

**Buffer Algorithm**:
```sql
ST_Buffer(track_geometry, implement_width/2, 'endcap=flat join=round')
```

**Process**:
1. Take machine track line geometry
2. Apply buffer using half implement width
3. Create polygon representing worked area
4. Validate and clean geometry
5. Store as cultivated area

### Track Export Formats

#### GeoJSON Format
- **Use Case**: Web mapping, GIS analysis
- **Features**: Standard web format, coordinate precision
- **Includes**: Geometry, properties, metadata

#### GPX Format
- **Use Case**: GPS devices, external analysis tools
- **Features**: Industry standard, time-based tracks
- **Includes**: Track points, elevation, extensions

### Performance Optimizations

1. **Spatial Indexing**: PostGIS spatial indexes on geometry columns
2. **Temporal Partitioning**: Efficient queries by date range
3. **Aggregation**: Pre-calculated statistics and summaries
4. **Caching**: Temporary tables for complex processing
5. **Streaming**: Large datasets processed in chunks

### Track Data Quality

#### Validation Rules
- **Coordinate Bounds**: Valid lat/lon ranges
- **Speed Limits**: Realistic speed values
- **Temporal Order**: Chronological point sequence
- **Sensor Data**: Valid fuel consumption values

#### Data Cleaning
- **Outlier Removal**: Filter unrealistic GPS points
- **Gap Handling**: Manage tracking interruptions
- **Duplicate Detection**: Remove redundant points
- **Smoothing**: Optional track smoothing algorithms

## Advanced Integration Features

### Multi-System Support
The database schema reveals support for multiple integration types:

1. **Wialon Integration**: Primary machine tracking platform
   - Token-based authentication
   - Unit and implement synchronization
   - Real-time position tracking
   - Work event detection

2. **Irrigation Systems**: Separate irrigation machine tracking
   - `su_irrigation_units` table for pivot irrigation systems
   - `su_irrigation_events` for irrigation activities
   - `su_current_irrigation_platforms_data` for real-time status

3. **Extensible Architecture**: The system is designed to support additional platforms
   - Generic integration framework
   - Configurable report types
   - Flexible parameter system

### Data Processing Pipeline

1. **Temporary Table Processing**:
   - Reports create temporary tables (e.g., `tmp_report_*`)
   - Data is processed and validated
   - Tables are cleaned up after processing

2. **Duplicate Prevention**:
   - Unique constraints on machine events
   - Conflict resolution with `ON CONFLICT DO NOTHING`
   - Data integrity checks

3. **Spatial Data Handling**:
   - PostGIS geometry types for precise location tracking
   - SRID 32635 (UTM Zone 35N) coordinate system
   - Spatial indexes for performance

4. **Array-Based Relationships**:
   - PostgreSQL integer arrays for many-to-many relationships
   - GIN indexes for efficient array queries
   - Flexible work operation assignments

### Product and Material Management

The system includes comprehensive product tracking:

1. **Product Catalog**: Fertilizers, pesticides, seeds, fuel
2. **Application Rates**: Flexible rate definitions
3. **Inventory Tracking**: Usage tracking per task/event
4. **Cost Management**: Default pricing and cost calculation
5. **Compliance**: Quarantine periods and safety information

### Reporting and Analytics

1. **Scheduled Reports**: Configurable cron-based reporting
2. **On-Demand Reports**: Manual report generation
3. **Error Tracking**: Comprehensive error logging
4. **Performance Monitoring**: Execution time tracking
5. **Data Export**: Multiple export formats supported

## Integration Flow

### 1. Initial Setup

#### Integration Creation Endpoint
**API Endpoint**: `POST /apigs/integration`
**Controller**: `App\Http\Controllers\APIGS\IntegrationController::store()`
**Request Validation**: `App\Http\Requests\Integration\StoreIntegrationRequest`

**Required Parameters**:
```json
{
  "token": "wialon_api_token",
  "organization_id": 123,
  "contract_id": 456,
  "package_id": 789,
  "package_slug_short": "FT", // or "IM"
  "package_period": "monthly",
  "integration_address_id": 1,
  "status": "Active",
  "machine_units": [...], // For FT integrations
  "machine_implements": [...] // For FT integrations
}
```

**Integration Creation Process**:
1. **Create integration record** with Wialon credentials via `POST /apigs/integration`
2. **Sync machine units** from Wialon API (for FT integrations)
3. **Sync implements** from Wialon API (for FT integrations)
4. **Set up scheduled reports** for data synchronization

**Service Methods**:
- **FT Integrations**: `IntegrationService::createFTIntegration()` - Full setup with machines/implements
- **IM Integrations**: `IntegrationService::storeIntegration()` - Basic integration only

**Authorization**:
- **FT Integrations**: Requires `createFTIntegration` permission
- **IM Integrations**: Requires `createIMIntegration` permission
- **Middleware**: `organization-data-access` for data isolation

#### Detailed Integration Creation Flow

**Step 1: API Request Processing**
<augment_code_snippet path="App/Http/Controllers/APIGS/IntegrationController.php" mode="EXCERPT">
````php
public function store(StoreIntegrationRequest $request)
{
    $integrationData = [
        'token' => $request->get('token'),
        'organization_id' => $request->get('organization_id'),
        'contract_id' => $request->get('contract_id'),
        'package_id' => $request->get('package_id'),
        'package_slug_short' => $request->get('package_slug_short'),
        'package_period' => $request->get('package_period'),
        'integration_address' => $request->get('integration_address_id'),
        'status' => $request->get('status'),
    ];

    if (Integration::PACKAGE_SLUG_SHORT_FT === $integrationData['package_slug_short']) {
        // Farm Track integration with machines and implements
        $integration = $this->integrationService->createFTIntegration($integrationData, $machineUnits, $machineImplements);
    } else {
        // Implement Management integration (basic)
        $integration = $this->integrationService->storeIntegration($integrationData);
    }
}
````
</augment_code_snippet>

**Step 2: Database Record Creation**
<augment_code_snippet path="App/Services/Integration/IntegrationService.php" mode="EXCERPT">
````php
public function storeIntegration(array $integrationData): Integration
{
    $integration = new Integration();
    $integration->fill($integrationData);

    // Validate Wialon credentials and get user info
    $integrationAddressBaseUrl = $integration->integrationAddress()->first()->getBaseUrl();
    $wialonData = $this->wialonService->login($integration->token, $integrationAddressBaseUrl);

    $integration->remote_username = $wialonData['user']['nm'];
    $integration->remote_user_id = $wialonData['user']['id'];
    $integration->save(); // INSERT INTO su_integration

    return $integration;
}
````
</augment_code_snippet>

**Step 3: FT Integration Extended Setup**
For Farm Track (FT) integrations, additional setup occurs:

1. **Wialon Resource Discovery**: Find available Wialon resources
2. **Unit Group Creation**: Create machine unit groups in Wialon
3. **Report Template Creation**: Set up predefined report templates
4. **Machine Units Storage**: Store machine units in `su_machine_units`
5. **Implements Storage**: Store implements in `su_machines_implements`
6. **Integration Reports Setup**: Create report configurations

**Step 4: Validation and Error Handling**
- **Token Validation**: Wialon API authentication test
- **User Verification**: Ensure consistent Wialon user
- **Database Transactions**: Rollback on any failure
- **Error Logging**: Comprehensive error tracking

#### Integration Types

**Farm Track (FT) Integration**:
- **Purpose**: Complete machine tracking and management
- **Includes**: Machine units, implements, work events, tasks
- **Setup**: Complex with Wialon resource configuration
- **Database Tables**: `su_integration`, `su_machine_units`, `su_machines_implements`

**Implement Management (IM) Integration**:
- **Purpose**: Basic implement tracking only
- **Includes**: Implement data synchronization
- **Setup**: Simple integration record creation
- **Database Tables**: `su_integration`, `su_machines_implements`

#### Default Values and Constraints

**Database Defaults**:
- **Status**: `'Active'` (from migration default)
- **Created/Updated Timestamps**: Automatic Laravel timestamps
- **Remote User Info**: Populated from Wialon API response

**Validation Rules** (from `StoreIntegrationRequest`):
- **Token**: Required string (Wialon API token)
- **Organization ID**: Required numeric
- **Contract/Package IDs**: Required numeric
- **Package Slug**: Required string ('FT' or 'IM')
- **Integration Address**: Required integer (FK to integration addresses)
- **Status**: Required string ('Active' or 'Inactive')

#### Route Configuration
<augment_code_snippet path="routes/apigs.php" mode="EXCERPT">
````php
Route::post('integration', [IntegrationController::class, 'store'])
    ->middleware('organization-data-access');
````
</augment_code_snippet>

**Middleware Applied**:
- **organization-data-access**: Ensures user can only create integrations for their organization

### 2. Data Synchronization
1. **Scheduled Jobs**: Run via cron every few hours
2. **Report Generation**: Create temporary tables with fresh data
3. **Data Processing**: Transform and validate incoming data
4. **Database Updates**: Insert/update machine events and current positions
5. **Cleanup**: Remove temporary tables and old data

### 3. Event Processing
1. **Raw Data Import**: Import tracking data from Wialon reports
2. **Event Detection**: Identify work activities vs transportation
3. **Geometry Processing**: Calculate worked areas and track paths
4. **Task Matching**: Link events to planned tasks
5. **Validation**: Approve or flag events for review

## Job Classes

### Scheduled Jobs
- `IntegrationReportJob`: Fetch reports from Wialon
- `MachineEventsReport`: Process machine event data
- `CurrentMachineReportJob`: Update current machine positions
- `PrepareScheduledReports`: Prepare and queue report jobs

### Background Processing
- `DeactivateIntegrationJob`: Deactivate failed integrations
- `MatchEventAndTask`: Match events to planned tasks
- `UpdateTaskStateToOngoing`: Update task states based on events

## Configuration

### Wialon Configuration
**File**: `config/wialon/report-templates.php`

Contains predefined report templates for:
- Current fuel levels
- Machine locations
- Sensor data tracing
- Trip information

### Authentication
- **Token-based**: Uses Wialon API tokens
- **Session Management**: Maintains Wialon sessions with caching
- **Error Handling**: Automatic token validation and integration deactivation

### Cron Schedule
**File**: `.docker/crontab/crontab`
```bash
* * * * * php /app/artisan schedule:run
```

**Scheduled Tasks** (defined in `App\Console\Kernel`):
- Integration reports: Every few hours based on report type
- Scheduled reports: Every 15 minutes (6:45-22:00)
- Horizon snapshots: Every 5 minutes

## Security & Authorization

### Policies
- `MachineUnitPolicy`: Controls machine unit access
- `MachineImplementPolicy`: Controls implement access
- `IntegrationPolicy`: Controls integration management

### Authorization Rules
- Users can only access machines from their organizations
- Integration management requires active organization membership
- Machine operations require specific permissions

### Error Handling
- **Wialon Error Codes**: Comprehensive error code handling
- **Token Validation**: Automatic detection of invalid tokens
- **Integration Deactivation**: Automatic deactivation on auth failures
- **Logging**: Comprehensive error logging and monitoring

## Monitoring & Logging

### Farm Track Reports Log
**Table**: `farm_track_reports_log`
- Tracks all integration report executions
- Records success/failure states
- Stores error details and request parameters
- Enables debugging and monitoring

### Current Machine Data
**Table**: `su_current_machines_data`
- Stores real-time machine positions as GeoJSON
- Updated by scheduled jobs
- Used for map displays and current status

## Best Practices

### Development
1. **Service Layer**: Use service classes for business logic
2. **Job Queues**: Process heavy operations asynchronously
3. **Database Transactions**: Ensure data consistency
4. **Error Handling**: Implement comprehensive error handling
5. **Testing**: Write tests for critical integration flows

### Maintenance
1. **Monitor Integration Health**: Check farm track reports log
2. **Token Management**: Regularly validate API tokens
3. **Data Cleanup**: Remove old temporary tables and logs
4. **Performance**: Monitor job queue performance
5. **Backup**: Regular backup of machine data

### Troubleshooting
1. **Check Integration Status**: Verify active integrations
2. **Review Error Logs**: Check farm track reports log
3. **Validate Tokens**: Test Wialon API connectivity
4. **Queue Status**: Monitor job queue health
5. **Database Integrity**: Check foreign key constraints

## Database Schema Insights Summary

### Key Discoveries from Schema Analysis

1. **Comprehensive Integration System**:
   - 13 core integration-related tables
   - Support for multiple external platforms (Wialon primary)
   - Flexible report configuration system
   - Robust error handling and logging

2. **Advanced Machine Management**:
   - 5 machine unit types supported
   - Detailed implement tracking with work operations
   - Real-time position tracking with PostGIS
   - Comprehensive event logging with spatial data

3. **Sophisticated Task Management**:
   - 6 task states from planning to completion
   - Integration with guidance systems (GPS lines)
   - VRA (Variable Rate Application) support
   - Product/material usage tracking

4. **Data Integrity Features**:
   - Unique constraints preventing duplicate events
   - Foreign key relationships with cascade options
   - Check constraints for data validation
   - Comprehensive indexing strategy

5. **Scalability Design**:
   - Array-based relationships for flexibility
   - GIN indexes for array queries
   - Temporary table processing pattern
   - Efficient spatial indexing

### Integration Complexity Assessment

**High Complexity Features**:
- Multi-dimensional spatial data processing
- Real-time synchronization with external APIs
- Complex event detection and classification
- Product application rate calculations
- Multi-tenant data isolation

**Moderate Complexity Features**:
- Task planning and scheduling
- Report generation and export
- User permission management
- Data validation and cleanup

**Standard Features**:
- CRUD operations for machines/implements
- Basic reporting and analytics
- User interface data presentation

## Future Enhancements

### Immediate Opportunities
1. **Performance Optimization**: Implement table partitioning for large event tables
2. **Real-time Features**: Add WebSocket support for live machine tracking
3. **Mobile Support**: Develop mobile APIs for field operators
4. **Advanced Analytics**: Implement machine learning for pattern detection

### Long-term Scalability
1. **Multi-Platform Integration**: Extend beyond Wialon to other tracking systems
2. **IoT Integration**: Direct sensor data ingestion
3. **Predictive Maintenance**: AI-based equipment health monitoring
4. **Blockchain Integration**: Immutable audit trails for compliance
