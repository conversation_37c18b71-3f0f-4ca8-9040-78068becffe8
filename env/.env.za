APP_ENV=production
APP_DEBUG=false
APP_TIMEZONE=Africa/Johannesburg
APP_LOCALE=za

DEFAULT_DB=geoscan

GEOSCAN_DB_HOST=127.0.0.1
GEOSCAN_DB_DATABASE=susi_main_za_v3
GEOSCAN_DB_USERNAME=postgres
GEOSCAN_DB_PASSWORD=6nuk23

USERDB_DB_HOST=127.0.0.1
USERDB_DB_DATABASE=za_new_users_db
USERDB_DB_USERNAME=postgres
USERDB_DB_PASSWORD=6nuk23

CACHE_DRIVER=memcached
SESSION_DRIVER=memcached
QUEUE_DRIVER=sync

RAPIDEYE_TOI_START=2014-11-01
RAPIDEYE_BLK_FILL=100
RAPIDEYE_CC=20
RAPIDEYE_COUNTRY=South Africa

USERDB_PREFIX=za_

DEFAULT_DB_CRS=32735
DEFAULT_UTM_ZONE=35
GEO_POLE=south

MAPCACHE_XML_FILE=/mapcache/za_mapcache.xml
MAPCACHE_DEFAULT_EXTENT=15.3492,34.3626,33.1356,21.3116

DEFAULT_MAX_EXTENT=-520475.997 6084077.748 1180717.164 -674045.983

AREA_UNIT=HA
AREA_UNIT_LABEL=ha

WMS_SERVER=http://apiza.geoscan.bg/cgi-bin/mapserv

PRICE_PER_DKA_INDEX=0.2
PRICE_PER_DKA_SOIL=0.2

METEO_MAX_DIST=4500

SENTINEL_PATH=/mnt/new_disc/satellite_images/za/sentinel/
SENTINEL_SEARCH_QUERY=satellite_name:sentinel-2 AND date:>=2017-10-01 AND cloud_coverage:<=85 AND ((grid_square:LM AND latitude_band:J AND utm_zone:35))

SENTINEL_ESA_SEARCH_QUERY=platformname:Sentinel-2 AND processinglevel:Level-1C AND beginposition:[NOW-10DAYS TO NOW] AND cloudcoverpercentage:[0 TO 85] AND (filename:*_T35JLM_*)

LANDSAT_PATH=/mnt/new_disc/satellite_images/za/landsat/
LANDSAT_SEARCH_QUERY=satellite_name:landsat-8 AND date:>=2017-10-01 AND cloud_coverage:<=85 AND ((path:172 AND row:78))	

EXTENT_WKT_CRS=POLYGON((1708056.913 -2484744.585, 1769283.509 -4181595.958, 3746027.892 -4172849.302, 3728534.579 -2467251.272, 1708056.913 -2484744.585))
EXTENT_WKT_CRS_4326=POLYGON((15.42231 -21.55771, 16.20803 -35.19449, 33.88686 -35.19449, 33.49400 -21.70378, 15.42231 -21.55771))

OSM_CONNECTION=http://130.185.253.250/cgi-bin/mapserv6?map=/var/www/osm-demo/mapserver-utils-svn-imposm/za_osm-outlined,google,usshields_edit.map&

GDAL_BIN_PATH=/opt/gdal2.1.2/bin/

ALLOWED_DEMO_AREA=600