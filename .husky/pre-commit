#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# get the staged .php files
staged_php_files="$(git diff-index --cached --name-only --diff-filter=ACMR HEAD | grep '\.php$')" || exit 0
for staged in ${staged_php_files}; do
    # run php-cs-fixer fix for each of the staged .php files
    tools/php-cs-fixer/vendor/bin/php-cs-fixer --config=.php-cs-fixer.php -q fix ${staged}
    
    php_cs_fixer_result=$?
    # the 'php-cs-fixer fix' command returns 0 for success
    if [ ${php_cs_fixer_result} -eq 0 ]; then
        git add ${staged} # stage formatted file
    fi
done