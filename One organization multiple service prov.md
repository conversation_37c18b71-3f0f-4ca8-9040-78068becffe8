# One organization multiple service providers
When a given service admin creates a new organization and this organization already exists in another service provider, the CMS (Agrimi Contracts Management System) should return a message. This message should give an option for inviting directly the existing organization to the new service provider. There should be 3 options for invitation:
* Directly inviting the organization manager with a notification in Agrimi.
* Sending an email to the organization manager with a link to accept the invitation.
* Generating a QR code for the organization manager to scan and accept the invitation.

Untill the organization manager accepts the invitation, the organization should be vissible only to the service admin who send the invitation with a status "Invitation send". New contracts should not be created for the organization until the invitation is accepted.

In the "Organizations" page grid only the first column organization name should be visible, the rest of the columns should be empy. A new column "Status" should be added to the grid. The status should be "Active" for all organizations that are not invited and "Invitation send" for all organizations that are invited. When the organization manager accepts the invitation, the status should change to "Invited" and the organization should be visible to all users in the new service provider. A new column "Actions" should be add to the grid where the service admin can see a button "Revoke invitation" that will remove the organization from the new service provider and will change the status to "Invitation revoked".

In the "Users" page grid if the service provider admin search by the invited organization, and the organization is in status "Invitation send" the grid should not return the organization users, only after the invitation is accepted the users should be visible. The organization status should be showin in the dropdown filter.

If a new contract is created in the organization dropdown field the invited organization have to be not selectable.

The service provider which created the organization should be the main service provider for the organization. It should be prefered if there is an ambigous situation.